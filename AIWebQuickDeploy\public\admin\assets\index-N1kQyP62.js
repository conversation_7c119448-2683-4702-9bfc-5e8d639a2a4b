
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,r as t,$ as n,b as o,Q as u,a1 as r,c as d,e as i,f as s,w as p,g as m,V as c,W as f,i as v,a2 as b,h as w,a5 as _,t as g,ae as h,Y as y,k as C}from"./index-BERX8Mlm.js";import{j as V}from"./index-gPQwgooA.js";import{u as k}from"./utcFormatTime-BtFjiA-p.js";const x={key:0},I={key:1},M={class:"input-with-text"},j={class:"input-with-text"},U={class:"input-with-text"},z=a({__name:"index",setup(a){const C=t(),z=t(0),T=t(!1),$=t(!1),D=t(!1),S=t(0),Y=t(),q=n({status:"0",id:0}),A=n({model3Count:0,model4Count:0,drawMjCount:0}),F=n({username:"",email:"",status:"",phone:"",nickname:"",page:1,size:15}),L={0:"Inactive",1:"Active",2:"Banned",3:"Suspended"},N=n({model3Count:[{required:!0,message:"请填写调整的基础模型额度",trigger:"blur"}],model4Count:[{required:!0,message:"请填写调整的高级模型额度",trigger:"blur"}],drawMjCount:[{required:!0,message:"请填写调整的绘画积分额度",trigger:"blur"}]}),P=t([]);async function R(){try{$.value=!0;const e=await h.queryAllUser(F),{rows:l,count:a}=e.data;$.value=!1,z.value=a,P.value=l}catch(e){$.value=!1}}async function B(){(await h.updateUserStatus(q)).success&&y({type:"success",message:"变更用户状态成功！"}),T.value=!1,R()}return o((()=>R())),(a,t)=>{const n=l,o=u("el-input"),Q=u("el-form-item"),W=u("el-option"),E=u("el-select"),G=u("el-button"),H=u("el-form"),J=e,K=u("el-avatar"),O=u("el-table-column"),X=u("el-tag"),Z=u("el-popconfirm"),ee=u("el-table"),le=u("el-pagination"),ae=u("el-row"),te=u("el-dialog"),ne=u("el-input-number"),oe=r("loading");return i(),d("div",null,[s(n,null,{title:p((()=>t[17]||(t[17]=[m("div",{class:"flex items-center gap-4"},"用户信息列表",-1)]))),_:1}),s(J,null,{default:p((()=>[s(H,{ref_key:"formRef",ref:C,inline:!0,model:F},{default:p((()=>[s(Q,{label:"用户名称",prop:"username"},{default:p((()=>[s(o,{modelValue:F.username,"onUpdate:modelValue":t[0]||(t[0]=e=>F.username=e),placeholder:"用户姓名[模糊搜索]",clearable:""},null,8,["modelValue"])])),_:1}),s(Q,{label:"用户邮箱",prop:"email"},{default:p((()=>[s(o,{modelValue:F.email,"onUpdate:modelValue":t[1]||(t[1]=e=>F.email=e),placeholder:"用户邮箱[模糊搜索]",clearable:""},null,8,["modelValue"])])),_:1}),s(Q,{label:"手机号码",prop:"phone"},{default:p((()=>[s(o,{modelValue:F.phone,"onUpdate:modelValue":t[2]||(t[2]=e=>F.phone=e),placeholder:"手机号码[模糊搜索]",clearable:""},null,8,["modelValue"])])),_:1}),s(Q,{label:"用户昵称",prop:"nickname"},{default:p((()=>[s(o,{modelValue:F.nickname,"onUpdate:modelValue":t[3]||(t[3]=e=>F.nickname=e),placeholder:"用户昵称[模糊搜索]",clearable:""},null,8,["modelValue"])])),_:1}),s(Q,{label:"用户状态",prop:"status"},{default:p((()=>[s(E,{modelValue:F.status,"onUpdate:modelValue":t[4]||(t[4]=e=>F.status=e),placeholder:"请选择用户状态",style:{width:"160px"},clearable:""},{default:p((()=>[(i(!0),d(c,null,f(v(V),(e=>(i(),b(W,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(Q,null,{default:p((()=>[s(G,{type:"primary",onClick:R},{default:p((()=>t[18]||(t[18]=[w(" 查询 ")]))),_:1}),s(G,{onClick:t[5]||(t[5]=e=>{return null==(l=C.value)||l.resetFields(),void R();var l})},{default:p((()=>t[19]||(t[19]=[w(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),s(J,{style:{width:"100%"}},{default:p((()=>[_((i(),b(ee,{border:"",data:P.value,style:{width:"100%"},size:"large"},{default:p((()=>[s(O,{prop:"avatar",label:"用户头像",fixed:"",width:"120"},{default:p((e=>[s(K,{src:e.row.avatar},null,8,["src"])])),_:1}),s(O,{fixed:"",prop:"username",label:"用户信息",width:"250",align:"center"},{default:p((e=>[e.row.nickname?(i(),d("div",x,g(e.row.username)+"（"+g(e.row.nickname)+"） ",1)):(i(),d("div",I,g(e.row.username),1))])),_:1}),s(O,{prop:"email",label:"用户邮箱",width:"250",align:"left"}),s(O,{prop:"phone",label:"用户手机号",width:"250",align:"left"},{default:p((e=>{var l;return[w(g((null==(l=e.row)?void 0:l.phone)||"未绑定手机号"),1)]})),_:1}),s(O,{prop:"realName",label:"真实姓名",width:"150",align:"center"},{default:p((e=>{var l;return[w(g((null==(l=e.row)?void 0:l.realName)||"未实名认证"),1)]})),_:1}),s(O,{prop:"idCard",label:"身份证号",width:"200",align:"center"},{default:p((e=>{var l;return[w(g((null==(l=e.row)?void 0:l.idCard)||"未实名认证"),1)]})),_:1}),s(O,{prop:"status",label:"用户状态",width:"120",align:"center"},{default:p((({row:e})=>[s(X,{type:"success"},{default:p((()=>[w(g(L[e.status]),1)])),_:2},1024)])),_:1}),s(O,{prop:"balanceInfo.model3Count",label:"基础模型",width:"120",align:"center"}),s(O,{prop:"balanceInfo.model4Count",label:"高级模型",width:"120",align:"center"}),s(O,{prop:"balanceInfo.drawMjCount",label:"绘画余额",width:"120",align:"center"}),t[23]||(t[23]=w("expirationTime ")),s(O,{prop:"balanceInfo.drawMjCount",label:"会员到期时间",width:"170",align:"center"},{default:p((e=>[s(X,{type:"success"},{default:p((()=>{var l,a,t,n;return[w(g((null==(a=null==(l=e.row)?void 0:l.balanceInfo)?void 0:a.expirationTime)?v(k)(new Date(null==(n=null==(t=e.row)?void 0:t.balanceInfo)?void 0:n.expirationTime).toString()):"非会员"),1)]})),_:2},1024)])),_:1}),s(O,{prop:"balanceInfo.memberModel3Count",label:"基础模型[会员]",width:"120",align:"center"}),s(O,{prop:"balanceInfo.memberModel4Count",label:"高级模型[会员]",width:"120",align:"center"}),s(O,{prop:"balanceInfo.memberDrawMjCount",label:"绘画余额[会员]",width:"120",align:"center"}),s(O,{prop:"balanceInfo.useModel3Count",label:"已用基础模型",width:"160",align:"center"},{default:p((e=>{var l,a;return[w(g(`${(null==(l=e.row.balanceInfo)?void 0:l.useModel3Count)||0}次 | ${(null==(a=e.row.balanceInfo)?void 0:a.useModel3Token)||0} Token`),1)]})),_:1}),s(O,{prop:"balanceInfo.useModel4Count",label:"已用高级模型",width:"160",align:"center"},{default:p((e=>{var l,a;return[w(g(`${(null==(l=e.row.balanceInfo)?void 0:l.useModel4Count)||0}次 | ${(null==(a=e.row.balanceInfo)?void 0:a.useModel4Token)||0} Token`),1)]})),_:1}),s(O,{prop:"balanceInfo.useDrawMjToken",label:"已用绘画积分",width:"160",align:"center"},{default:p((e=>{var l;return[w(g(`${(null==(l=e.row.balanceInfo)?void 0:l.useDrawMjToken)||0} Token`),1)]})),_:1}),s(O,{prop:"createdAt",label:"注册时间",width:"200",align:"center"},{default:p((e=>[w(g(v(k)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),s(O,{fixed:"right",label:"操作",width:"250",align:"center"},{default:p((e=>[s(G,{link:"",type:"primary",size:"small",onClick:l=>{return a=e.row,T.value=!0,q.status=a.status.toString(),void(q.id=a.id);var a}},{default:p((()=>t[20]||(t[20]=[w(" 修改状态 ")]))),_:2},1032,["onClick"]),s(Z,{title:"确认重置此用户密码为【123456】?","confirm-button-text":"确认重置",onConfirm:l=>async function(e){const{id:l,email:a}=e;(await h.resetUserPassword({id:l})).success&&y({type:"success",message:`重置用户[${a}密码为初始密码为[123456]完成！`})}(e.row)},{reference:p((()=>[s(G,{link:"",type:"danger"},{default:p((()=>t[21]||(t[21]=[w(" 重置密码 ")]))),_:1})])),_:2},1032,["onConfirm"]),s(G,{link:"",type:"primary",size:"small",onClick:l=>{return a=e.row,D.value=!0,void(S.value=a.id);var a}},{default:p((()=>t[22]||(t[22]=[w(" 调整积分 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[oe,$.value]]),s(ae,{class:"mt-5 flex justify-end"},{default:p((()=>[s(le,{"current-page":F.page,"onUpdate:currentPage":t[6]||(t[6]=e=>F.page=e),"page-size":F.size,"onUpdate:pageSize":t[7]||(t[7]=e=>F.size=e),class:"mr-5","page-sizes":[15,30,50,100],layout:"total, sizes, prev, pager, next, jumper",total:z.value,onSizeChange:R,onCurrentChange:R},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),s(te,{modelValue:T.value,"onUpdate:modelValue":t[9]||(t[9]=e=>T.value=e),title:"变更用户状态",width:"500px"},{default:p((()=>[s(H,{model:q,inline:!0},{default:p((()=>[s(Q,{label:"用户状态","label-width":"90px"},{default:p((()=>[s(E,{modelValue:q.status,"onUpdate:modelValue":t[8]||(t[8]=e=>q.status=e),placeholder:"请选择用户状态",style:{width:"160px"},clearable:""},{default:p((()=>[(i(!0),d(c,null,f(v(V),(e=>(i(),b(W,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(Q,null,{default:p((()=>[s(G,{type:"primary",onClick:B},{default:p((()=>t[24]||(t[24]=[w(" 确认变更 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),s(te,{modelValue:D.value,"onUpdate:modelValue":t[15]||(t[15]=e=>D.value=e),title:"调整用户积分（赠送/扣除）",width:"450px",onClose:t[16]||(t[16]=e=>{return l=Y.value,S.value=0,void(null==l||l.resetFields());var l})},{footer:p((()=>[s(G,{onClick:t[13]||(t[13]=e=>D.value=!1)},{default:p((()=>t[25]||(t[25]=[w(" 取消 ")]))),_:1}),s(G,{type:"primary",onClick:t[14]||(t[14]=e=>async function(e){null==e||e.validate((async e=>{e&&(await h.sendUserCrami({...A,userId:S.value}),y.success("调整成功！"),D.value=!1,R())}))}(Y.value))},{default:p((()=>t[26]||(t[26]=[w(" 确认调整 ")]))),_:1})])),default:p((()=>[s(H,{ref_key:"cramiRef",ref:Y,model:A,rules:N,"label-width":"100px"},{default:p((()=>[s(Q,{label:"基础积分",prop:"modelLimits"},{default:p((()=>[m("div",M,[s(ne,{modelValue:A.model3Count,"onUpdate:modelValue":t[10]||(t[10]=e=>A.model3Count=e),max:99999999,min:-99999999,step:1,"step-strictly":"",class:"input-number",style:{"margin-right":"10px"}},null,8,["modelValue"])])])),_:1}),s(Q,{label:"高级积分",prop:"modelLimits"},{default:p((()=>[m("div",j,[s(ne,{modelValue:A.model4Count,"onUpdate:modelValue":t[11]||(t[11]=e=>A.model4Count=e),max:99999999,min:-99999999,step:1,"step-strictly":"",class:"input-number",style:{"margin-right":"10px"}},null,8,["modelValue"])])])),_:1}),s(Q,{label:"绘画积分",prop:"modelLimits"},{default:p((()=>[m("div",U,[s(ne,{modelValue:A.drawMjCount,"onUpdate:modelValue":t[12]||(t[12]=e=>A.drawMjCount=e),max:99999999,min:-99999999,step:1,"step-strictly":"",class:"input-number",style:{"margin-right":"10px"}},null,8,["modelValue"])])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}});"function"==typeof C&&C(z);export{z as default};
