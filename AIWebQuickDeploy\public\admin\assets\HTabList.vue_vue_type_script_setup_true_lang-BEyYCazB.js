
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,r as a,au as t,bb as l,bc as n,bd as s,be as r,b as u,ai as o,ap as i,P as d,bf as v,aq as c,a0 as p,bg as b,bh as f,V as m,bi as x,bj as g,bk as h,bl as y,bm as I,bn as k,bo as S,bp as T,aS as w,aT as P,a2 as A,e as F,w as O,f as E,i as j,c as D,W as L,g as _,a6 as q,T as B,h as C,_ as G,t as W}from"./index-BERX8Mlm.js";import{s as M}from"./use-resolve-button-type-DnRVrBaM.js";let N=e({props:{onFocus:{type:Function,required:!0}},setup(e){let s=a(!0);return()=>s.value?t(n,{as:"button",type:"button",features:l.Focusable,onFocus(a){a.preventDefault();let t,l=50;t=requestAnimationFrame((function a(){var n;if(!(l--<=0))return null!=(n=e.onFocus)&&n.call(e)?(s.value=!1,void cancelAnimationFrame(t)):void(t=requestAnimationFrame(a));t&&cancelAnimationFrame(t)}))}}):null}});var V,z,$=((z=$||{})[z.Forwards=0]="Forwards",z[z.Backwards=1]="Backwards",z),H=((V=H||{})[V.Less=-1]="Less",V[V.Equal=0]="Equal",V[V.Greater=1]="Greater",V);let R=Symbol("TabsContext");function U(e){let a=i(R,null);if(null===a){let a=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,U),a}return a}let K=Symbol("TabsSSRContext"),J=e({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:l,attrs:n,emit:r}){var o;let i=a(null!=(o=e.selectedIndex)?o:e.defaultIndex),h=a([]),y=a([]),I=d((()=>null!==e.selectedIndex)),k=d((()=>I.value?e.selectedIndex:i.value));function S(e){var a;let t=x(T.tabs.value,v),l=x(T.panels.value,v),n=t.filter((e=>{var a;return!(null!=(a=v(e))&&a.hasAttribute("disabled"))}));if(e<0||e>t.length-1){let a=g(null===i.value?0:Math.sign(e-i.value),{[-1]:()=>1,0:()=>g(Math.sign(e),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),s=g(a,{0:()=>t.indexOf(n[0]),1:()=>t.indexOf(n[n.length-1])});-1!==s&&(i.value=s),T.tabs.value=t,T.panels.value=l}else{let s=t.slice(0,e),r=[...t.slice(e),...s].find((e=>n.includes(e)));if(!r)return;let u=null!=(a=t.indexOf(r))?a:T.selectedIndex.value;-1===u&&(u=T.selectedIndex.value),i.value=u,T.tabs.value=t,T.panels.value=l}}let T={selectedIndex:d((()=>{var a,t;return null!=(t=null!=(a=i.value)?a:e.defaultIndex)?t:null})),orientation:d((()=>e.vertical?"vertical":"horizontal")),activation:d((()=>e.manual?"manual":"auto")),tabs:h,panels:y,setSelectedIndex(e){k.value!==e&&r("change",e),I.value||S(e)},registerTab(e){var a;if(h.value.includes(e))return;let t=h.value[i.value];if(h.value.push(e),h.value=x(h.value,v),!I.value){let e=null!=(a=h.value.indexOf(t))?a:i.value;-1!==e&&(i.value=e)}},unregisterTab(e){let a=h.value.indexOf(e);-1!==a&&h.value.splice(a,1)},registerPanel(e){y.value.includes(e)||(y.value.push(e),y.value=x(y.value,v))},unregisterPanel(e){let a=y.value.indexOf(e);-1!==a&&y.value.splice(a,1)}};c(R,T);let w=a({tabs:[],panels:[]}),P=a(!1);u((()=>{P.value=!0})),c(K,d((()=>P.value?null:w.value)));let A=d((()=>e.selectedIndex));return u((()=>{p([A],(()=>{var a;return S(null!=(a=e.selectedIndex)?a:e.defaultIndex)}),{immediate:!0})})),b((()=>{if(!I.value||null==k.value||T.tabs.value.length<=0)return;let e=x(T.tabs.value,v);e.some(((e,a)=>v(T.tabs.value[a])!==v(e)))&&T.setSelectedIndex(e.findIndex((e=>v(e)===v(T.tabs.value[k.value]))))})),()=>{let a={selectedIndex:i.value};return t(m,[h.value.length<=0&&t(N,{onFocus:()=>{for(let e of h.value){let a=v(e);if(0===(null==a?void 0:a.tabIndex))return a.focus(),!0}return!1}}),s({theirProps:{...n,...f(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])},ourProps:{},slot:a,slots:l,attrs:n,name:"TabGroup"})])}}}),Q=e({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:a,slots:t}){let l=U("TabList");return()=>{let n={selectedIndex:l.selectedIndex.value},r={role:"tablist","aria-orientation":l.orientation.value};return s({ourProps:r,theirProps:e,slot:n,attrs:a,slots:t,name:"TabList"})}}}),X=e({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var c;let p=null!=(c=e.id)?c:`headlessui-tabs-tab-${r()}`,b=U("Tab"),f=a(null);n({el:f,$el:f}),u((()=>b.registerTab(f))),o((()=>b.unregisterTab(f)));let m=i(K),x=d((()=>{if(m.value){let e=m.value.tabs.indexOf(p);return-1===e?m.value.tabs.push(p)-1:e}return-1})),w=d((()=>{let e=b.tabs.value.indexOf(f);return-1===e?x.value:e})),P=d((()=>w.value===b.selectedIndex.value));function A(e){var a;let t=e();if(t===y.Success&&"auto"===b.activation.value){let e=null==(a=k(f))?void 0:a.activeElement,t=b.tabs.value.findIndex((a=>v(a)===e));-1!==t&&b.setSelectedIndex(t)}return t}function F(e){let a=b.tabs.value.map((e=>v(e))).filter(Boolean);if(e.key===h.Space||e.key===h.Enter)return e.preventDefault(),e.stopPropagation(),void b.setSelectedIndex(w.value);switch(e.key){case h.Home:case h.PageUp:return e.preventDefault(),e.stopPropagation(),A((()=>S(a,T.First)));case h.End:case h.PageDown:return e.preventDefault(),e.stopPropagation(),A((()=>S(a,T.Last)))}return A((()=>g(b.orientation.value,{vertical:()=>e.key===h.ArrowUp?S(a,T.Previous|T.WrapAround):e.key===h.ArrowDown?S(a,T.Next|T.WrapAround):y.Error,horizontal:()=>e.key===h.ArrowLeft?S(a,T.Previous|T.WrapAround):e.key===h.ArrowRight?S(a,T.Next|T.WrapAround):y.Error})))===y.Success?e.preventDefault():void 0}let O=a(!1);function E(){var a;O.value||(O.value=!0,!e.disabled&&(null==(a=v(f))||a.focus({preventScroll:!0}),b.setSelectedIndex(w.value),I((()=>{O.value=!1}))))}function j(e){e.preventDefault()}let D=M(d((()=>({as:e.as,type:t.type}))),f);return()=>{var a,n;let r={selected:P.value,disabled:null!=(a=e.disabled)&&a},{...u}=e,o={ref:f,onKeydown:F,onMousedown:j,onClick:E,id:p,role:"tab",type:D.value,"aria-controls":null==(n=v(b.panels.value[w.value]))?void 0:n.id,"aria-selected":P.value,tabIndex:P.value?0:-1,disabled:!!e.disabled||void 0};return s({ourProps:o,theirProps:u,slot:r,attrs:t,slots:l,name:"Tab"})}}});const Y=e({__name:"HTabList",props:w({options:{}},{modelValue:{},modelModifiers:{}}),emits:w(["change"],["update:modelValue"]),setup(e,{emit:a}){const t=e,l=a,n=P(e,"modelValue"),s=d({get:()=>t.options.findIndex((e=>e.value===n.value)),set(e){n.value=t.options[e].value}});function r(e){n.value=t.options[e].value}return p(n,(e=>{e&&l("change",e)})),(e,a)=>{const t=G;return F(),A(j(J),{"selected-index":j(s),onChange:r},{default:O((()=>[E(j(Q),{class:"inline-flex select-none items-center justify-center rounded-md bg-stone-1 p-1 ring-1 ring-stone-2 dark-bg-stone-9 dark-ring-stone-8"},{default:O((()=>[(F(!0),D(m,null,L(e.options,((e,a)=>(F(),A(j(X),{key:a,as:"template"},{default:O((({selected:a})=>[_("button",{class:q(["w-full inline-flex items-center justify-center gap-1 break-keep border-size-0 rounded-md bg-inherit px-2 py-1.5 text-sm text-dark ring-stone-2 ring-inset dark-text-white focus-outline-none focus-ring-2 dark-ring-stone-8",{"cursor-default bg-white dark-bg-dark-9":a,"cursor-pointer opacity-50 hover-opacity-100":!a}])},[e.icon?(F(),A(t,{key:0,name:e.icon,class:"flex-shrink-0"},null,8,["name"])):B("",!0),C(" "+W(e.label),1)],2)])),_:2},1024)))),128))])),_:1})])),_:1},8,["selected-index"])}}});export{Y as _};
