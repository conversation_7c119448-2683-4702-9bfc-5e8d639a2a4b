/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Auth: typeof import('./../components/Auth/index.vue')['default']
    AuthAll: typeof import('./../components/AuthAll/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    FixedActionBar: typeof import('./../components/FixedActionBar/index.vue')['default']
    HButton: typeof import('./../layouts/ui-kit/HButton.vue')['default']
    HCheckList: typeof import('./../layouts/ui-kit/HCheckList.vue')['default']
    HDialog: typeof import('./../layouts/ui-kit/HDialog.vue')['default']
    HDropdown: typeof import('./../layouts/ui-kit/HDropdown.vue')['default']
    HDropdownMenu: typeof import('./../layouts/ui-kit/HDropdownMenu.vue')['default']
    HInput: typeof import('./../layouts/ui-kit/HInput.vue')['default']
    HKbd: typeof import('./../layouts/ui-kit/HKbd.vue')['default']
    HSelect: typeof import('./../layouts/ui-kit/HSelect.vue')['default']
    HSlideover: typeof import('./../layouts/ui-kit/HSlideover.vue')['default']
    HTabList: typeof import('./../layouts/ui-kit/HTabList.vue')['default']
    HToggle: typeof import('./../layouts/ui-kit/HToggle.vue')['default']
    HTooltip: typeof import('./../layouts/ui-kit/HTooltip.vue')['default']
    IconifyIcon: typeof import('./../components/IconifyIcon/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    NotAllowed: typeof import('./../components/NotAllowed/index.vue')['default']
    PageHeader: typeof import('./../components/PageHeader/index.vue')['default']
    PageMain: typeof import('./../components/PageMain/index.vue')['default']
    PcasCascader: typeof import('./../components/PcasCascader/index.vue')['default']
    PromptTemplateEditor: typeof import('./../components/PromptTemplateEditor/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchBar: typeof import('./../components/SearchBar/index.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SystemInfo: typeof import('./../components/SystemInfo/index.vue')['default']
    Trend: typeof import('./../components/Trend/index.vue')['default']
  }
}
