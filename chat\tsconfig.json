{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "removeComments": true, "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node", "vue", "vue-i18n"]}, "exclude": ["node_modules", "dist", "service", "electron"]}