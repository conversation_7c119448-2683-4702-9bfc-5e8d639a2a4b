#!/bin/bash

# 分步构建脚本 - 可以单独执行每个步骤
# 使用方法: ./build-step-by-step.sh [step]
# step可以是: admin, chat, service, package, all

set -e

# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

STEP=${1:-all}

build_admin() {
    echo "🔨 构建管理后台..."
    cd admin/
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "安装依赖..."
        pnpm install
    fi
    
    # 尝试不同的构建方式
    echo "尝试完整构建..."
    if ! pnpm run build; then
        echo "完整构建失败，尝试跳过类型检查..."
        if ! npx vite build; then
            echo "❌ 管理后台构建失败"
            cd ..
            exit 1
        fi
    fi
    
    cd ..
    echo "✅ 管理后台构建完成"
}

build_chat() {
    echo "🔨 构建用户界面..."
    cd chat/
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "安装依赖..."
        pnpm install
    fi
    
    # 尝试构建
    if ! pnpm run build; then
        echo "完整构建失败，尝试跳过类型检查..."
        if ! npx vite build; then
            echo "❌ 用户界面构建失败"
            cd ..
            exit 1
        fi
    fi
    
    cd ..
    echo "✅ 用户界面构建完成"
}

build_service() {
    echo "🔨 构建后端服务..."
    cd service/
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "安装依赖..."
        pnpm install
    fi
    
    # 构建后端
    if ! pnpm run build; then
        echo "❌ 后端服务构建失败"
        cd ..
        exit 1
    fi
    
    cd ..
    echo "✅ 后端服务构建完成"
}

package_files() {
    echo "📦 打包文件..."
    
    # 清理旧文件
    rm -rf ./AIWebQuickDeploy/dist/* ./AIWebQuickDeploy/public/admin/* ./AIWebQuickDeploy/public/chat/* 2>/dev/null || true
    mkdir -p ./AIWebQuickDeploy/dist ./AIWebQuickDeploy/public/admin ./AIWebQuickDeploy/public/chat
    
    # 复制配置文件
    [ -f "service/pm2.conf.json" ] && cp service/pm2.conf.json ./AIWebQuickDeploy/
    [ -f "service/package.json" ] && cp service/package.json ./AIWebQuickDeploy/
    [ -f "service/.env.example" ] && cp service/.env.example ./AIWebQuickDeploy/
    [ -f "service/.env.docker" ] && cp service/.env.docker ./AIWebQuickDeploy/
    [ -f "service/Dockerfile" ] && cp service/Dockerfile ./AIWebQuickDeploy/
    [ -f "service/docker-compose.yml" ] && cp service/docker-compose.yml ./AIWebQuickDeploy/
    [ -f "service/.dockerignore" ] && cp service/.dockerignore ./AIWebQuickDeploy/
    
    # 复制构建产物
    if [ -d "service/dist" ] && [ "$(ls -A service/dist)" ]; then
        cp -a service/dist/* ./AIWebQuickDeploy/dist/
    else
        echo "⚠️  警告: service/dist 目录为空或不存在"
    fi
    
    if [ -d "admin/dist" ] && [ "$(ls -A admin/dist)" ]; then
        cp -r admin/dist/* ./AIWebQuickDeploy/public/admin/
    else
        echo "⚠️  警告: admin/dist 目录为空或不存在"
    fi
    
    if [ -d "chat/dist" ] && [ "$(ls -A chat/dist)" ]; then
        cp -r chat/dist/* ./AIWebQuickDeploy/public/chat/
    else
        echo "⚠️  警告: chat/dist 目录为空或不存在"
    fi
    
    echo "✅ 文件打包完成"
}

# 主逻辑
case $STEP in
    "admin")
        build_admin
        ;;
    "chat")
        build_chat
        ;;
    "service")
        build_service
        ;;
    "package")
        package_files
        ;;
    "all")
        echo "🚀 开始完整构建..."
        build_admin
        build_chat
        build_service
        package_files
        echo "🎉 所有构建步骤完成！"
        ;;
    *)
        echo "用法: $0 [admin|chat|service|package|all]"
        echo "  admin   - 只构建管理后台"
        echo "  chat    - 只构建用户界面"
        echo "  service - 只构建后端服务"
        echo "  package - 只打包文件"
        echo "  all     - 执行所有步骤 (默认)"
        exit 1
        ;;
esac
