# 🚀 99AI -- 一站式 AI 服务平台

<div align="center">

[![GitHub stars](https://img.shields.io/github/stars/vastxie/99AI?style=social)](https://github.com/vastxie/99AI/stargazers)
[![开源协议](https://img.shields.io/badge/license-Apache%202.0-blue.svg)](LICENSE)
[![开发指南](https://img.shields.io/badge/开发指南-orange.svg)](./docs/DEVELOPMENT.md)
[![功能介绍](https://img.shields.io/badge/功能介绍-green.svg)](https://docs.lightai.cloud/introduction/)

> 🎓 新手入门？查看[部署文档](https://docs.lightai.cloud/deployment/)，快速上手项目部署！
>
> 📢 遇到问题？加入[交流群](#交流群)，获取最新动态，与社区伙伴一起学习讨论，共同进步！

</div>

## 🌟 项目介绍

99AI 是一个**可商业化的 AI Web 平台**，提供一站式的人工智能服务解决方案。支持私有化部署，内置多用户管理，适合企业、团队或个人快速构建 AI 服务。

<img width="1510" alt="image" src="https://github.com/user-attachments/assets/b6e7be3e-d754-4420-b088-9c30492adb21" />

### 🚀 核心优势

- **开箱即用**：基于 Node.js 完整打包，支持 Docker 部署
- **功能丰富**：集成主流 AI 能力，覆盖多场景应用
- **安全可控**：支持私有化部署，数据自主管理
- **开发友好**：提供源码级访问，支持二次开发与功能扩展
- **商业支持**：内置多种支付方式，支持商业化运营，助力变现和团队管理

### 💡 主要功能

- 🤖 **AI 对话**：适配主流 AI 模型，支持自定义灵活配置
  
  <img width="1510" alt="image" src="https://github.com/user-attachments/assets/12c47a55-08e7-4cfb-abee-09f3c9c2e9db" />
  
- 💡 **深度思考**：支持深度思考模型，使用全局思考模型赋能普通模型，实现 Deep + Everything
  
  <img width="1510" alt="image" src="https://github.com/user-attachments/assets/8c09455a-2a84-4a6f-a1bd-78d8a1e2c88b" />

- 🔍 **联网搜索**：实时信息获取，突破知识时效性限制
  
  <img width="1510" alt="image" src="https://github.com/user-attachments/assets/0228bb87-9cac-4187-817e-a6e15c321a53" />

- 🗺️ **智能图表**：一句话生成可视化流程图/思维导图，智能匹配 10+ 种 Mermaid 图表类型
  
  <img width="1510" alt="image" src="https://github.com/user-attachments/assets/a348d8c4-0b97-40d6-b441-3654a378732b" />

- 🔧 **应用广场**：自定义应用预设，灵活配置多场景 AI 智能体
  
  <img width="1510" alt="image" src="https://github.com/user-attachments/assets/af1dadcd-6097-46dc-a21b-71d1b07c3bff" />

## 🔥 版本对比

| 特性     | 稳定版                                            | 开发版                                           |
| -------- | ------------------------------------------------- | ------------------------------------------------ |
| 预览地址 | [99ai.lightai.cloud](https://99ai.lightai.cloud/) | [asst.lightai.cloud](https://asst.lightai.cloud) |
| 商用许可 | ✅ 支持                                           | ✅ 支持                                          |
| 源码状态 | 未编译，可修改                                    | 已编译，需授权                                   |
| 获取方式 | 开源免费                                          | 联系作者获取授权                                 |
| 功能特性 | 基础功能                                          | 功能更多，优先更新                               |

可在 [开发版更新日志](https://docs.lightai.cloud/introduction/changelog-dev.html/) 查看或通过预览地址比对体验版本差异，开发版授权详情可参考 [开发版授权说明](https://docs.lightai.cloud/deployment/license.html)

## 💬 交流学习

### 社区参与

- 欢迎提交 [Issue](https://github.com/vastxie/99AI/issues) 反馈问题或 Pull Request 共同维护
- 本项目采用 [Apache 2.0](LICENSE) 开源协议，使用本项目时请保留项目署名和链接
- 版权由原作者所有，项目支持商用，闭源分发需授权许可
- 如果觉得项目不错，欢迎 Star ⭐️ 支持

### 交流群

微信扫一扫备注「99」进群交流，作者不提供私聊技术咨询，进群请先阅读群公告说明

<img src="https://github.com/user-attachments/assets/9fed8343-73ae-43b0-9ce7-dc1a4c30c7a5" width="200">
