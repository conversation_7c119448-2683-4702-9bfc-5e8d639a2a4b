
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,b5 as t,r as l,al as n,a2 as o,e as a,f as s,b6 as i,W as u,w as r,aj as c,aQ as d,aR as v,i as f,av as m,ap as h,b as p,a9 as g,an as b,T as y,c as C,a6 as x,g as k,R as I,t as M,aB as S,au as w,Q as P,V as W,am as O,b7 as R,a0 as L,aq as B,$ as H,ax as T,h as j,b8 as z,b9 as _,ay as E,af as $,u as A,a as F,P as D,at as N,aF as Y,aA as U,ai as Z,a4 as K,aE as V,a5 as X,_ as G,a8 as q,Z as Q}from"./index-BERX8Mlm.js";import{b as J}from"./index-DhWfG07N.js";import{u as ee}from"./useMainPage-Dbp8uSF1.js";let te=null;function le(e){e===te&&(te=null)}function ne(){te&&(te.closeMenu(),te=null)}const oe={defaultDirection:"br",defaultMinWidth:100,defaultMaxWidth:600,defaultZindex:100,defaultZoom:1,defaultAdjustPadding:{x:0,y:10}};function ae(e,t){let l=e.offsetTop;return null!=e.offsetParent&&e.offsetParent!=t&&(l-=e.offsetParent.scrollTop,l+=ae(e.offsetParent,t)),l}function se(e,t){let l=e.offsetLeft;return null!=e.offsetParent&&e.offsetParent!=t&&(l-=e.offsetParent.scrollLeft,l+=se(e.offsetParent,t)),l}const ie="mx-menu-default-container";let ue=0;function re(e){const{getContainer:t,zIndex:l}=e;if(t){const e="function"==typeof t?t():t;if(e){let t=e.getAttribute("id");return t||(t="mx-menu-container-"+ue++,e.setAttribute("id",t)),{eleId:t,container:e,isNew:!1}}}let n=document.getElementById(ie);return n||(n=document.createElement("div"),n.setAttribute("id",ie),n.setAttribute("class","mx-menu-ghost-host fullscreen"),document.body.appendChild(n)),n.style.zIndex=(null==l?void 0:l.toString())||oe.defaultZindex.toString(),{eleId:ie,container:n,isNew:!0}}function ce(e){return"number"==typeof e?`${e}px`:e}const de=e({props:{vnode:{type:null},data:{type:null,default:null}},setup(e){const{vnode:l,data:n}=t(e);return()=>"function"==typeof l.value?l.value(n.value):l.value}});function ve(e,t){const l={...e};return delete l[t],l}var fe=Object.defineProperty,me=(e,t,l)=>((e,t,l)=>t in e?fe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l)(e,"symbol"!=typeof t?t+"":t,l);class he{constructor(e,t){me(this,"x",0),me(this,"y",0),this.x=e||0,this.y=t||0}set(e,t){this.x=e,this.y=t}substract(e){this.x-=e.x,this.y-=e.y}}function pe(e){const{onDown:t,onMove:l,onUp:n}=e,o=new he,a=new he;let s;function i(e){e.stopPropagation(),a.set(e.x,e.y),a.substract(o),l(o,a,e,s)}function u(e){n(e,s),o.set(0,0),document.removeEventListener("mousemove",i),document.removeEventListener("mouseup",u)}return(e,l)=>!!t(e,l)&&(s=l,o.set(e.x,e.y),document.addEventListener("mousemove",i),document.addEventListener("mouseup",u),e.stopPropagation(),!0)}const ge=[],be=function(e,t){let l=0;return{start(){l>0&&clearInterval(l),l=setInterval((()=>{l=0,t()}),e)},stop(){l>0&&(clearInterval(l),l=0)}}}(100,(()=>{for(const e of ge)e()}));const ye=e({__name:"ScrollRect",props:{scroll:{type:String,default:"both"},scrollBarAlwaysShow:{type:Boolean,default:!1},scrollBarBackgroundClickable:{type:Boolean,default:!1},containerClass:{type:String,default:""}},emits:["scroll"],setup(e,{expose:t,emit:n}){const o=e,s=n,i=l(),u=l(),r=l(),d=l(),v=l(),m=l(),h=l(!1),I=H({show:!1,size:0,sizeRaw:0,pos:0}),M=H({show:!1,size:0,sizeRaw:0,pos:0});let S=0,w=0,P=0,W=0,R=null;const L={attributes:!0,childList:!0};function B(){if(i.value){if(I.show){const e=i.value.offsetWidth/i.value.scrollWidth;I.sizeRaw=e*i.value.offsetWidth,I.size=100*e,I.pos=i.value.scrollLeft/(i.value.scrollWidth-i.value.offsetWidth)*(100-I.size),e>=1&&(I.show=!1)}if(M.show){const e=i.value.offsetHeight/i.value.scrollHeight;M.sizeRaw=e*i.value.offsetHeight,M.size=100*e,M.pos=i.value.scrollTop/(i.value.scrollHeight-i.value.offsetHeight)*(100-M.size),e>=1&&(M.show=!1)}s("scroll",i.value.scrollLeft,i.value.scrollTop)}}function T(e=!1){if(!i.value)return;let t="horizontal"===o.scroll||"both"===o.scroll,l="vertical"===o.scroll||"both"===o.scroll;const n=t&&(S!==i.value.scrollWidth||P!==i.value.offsetWidth),a=l&&(w!==i.value.scrollHeight||W!==i.value.offsetHeight);if(!e&&!n&&!a)return;const s=window.getComputedStyle(i.value);("hidden"===s.overflow||"hidden"===s.overflowX)&&(t=!1),("hidden"===s.overflow||"hidden"===s.overflowY)&&(l=!1),I.show=t,M.show=l,B(),P=i.value.offsetWidth,W=i.value.offsetHeight,S=i.value.scrollWidth,w=i.value.scrollHeight}function j(e){var t;"horizontal"==o.scroll&&(0==e.deltaMode&&(null==(t=i.value)||t.scrollTo({left:i.value.scrollLeft+(e.deltaY>0?140:-140),behavior:"smooth"})),e.preventDefault(),e.stopPropagation())}function z(e){var t;0==e.deltaMode&&(null==(t=i.value)||t.scrollTo({left:i.value.scrollLeft+(e.deltaY>0?140:-140),behavior:"smooth"}),e.preventDefault(),e.stopPropagation())}function _(e){var t;0==e.deltaMode&&(null==(t=i.value)||t.scrollTo({top:i.value.scrollTop+(e.deltaY>0?70:-70),behavior:"smooth"}),e.preventDefault(),e.stopPropagation())}let E=0,$=0,A=0,F=0;const D=pe({onDown:e=>!(!r.value||!v.value)&&(E=e.offsetX,$=e.x-e.offsetX-v.value.offsetLeft,e.preventDefault(),h.value=!0,!0),onMove(e,t,l){i.value&&r.value&&(Z(l.x-E-$),l.preventDefault(),l.stopPropagation())},onUp(){h.value=!1}}),N=pe({onDown:e=>!(!d.value||!m.value)&&(A=e.offsetY,F=e.y-e.offsetY-m.value.offsetTop,e.preventDefault(),h.value=!0,!0),onMove(e,t,l){i.value&&d.value&&(K(l.y-A-F),l.preventDefault(),l.stopPropagation())},onUp(){h.value=!1}});function Y(e){i.value&&(i.value.scrollLeft=e/100*(i.value.scrollWidth-i.value.offsetWidth))}function U(e){i.value&&(i.value.scrollLeft=e/100*(i.value.scrollHeight-i.value.offsetHeight))}function Z(e){i.value&&(i.value.scrollLeft=e/(i.value.offsetWidth-I.sizeRaw)*(i.value.scrollWidth-i.value.offsetWidth))}function K(e){i.value&&(i.value.scrollTop=e/(i.value.offsetHeight-M.sizeRaw)*(i.value.scrollHeight-i.value.offsetHeight))}function V(e){o.scrollBarBackgroundClickable&&Z(e.offsetX-I.sizeRaw/2)}function X(e){o.scrollBarBackgroundClickable&&K(e.offsetY-M.sizeRaw/2)}const{startResizeChecker:G,stopResizeChecker:q}=function(e,t,l){let n=0,o=0;function a(){e.value&&(t&&n!==e.value.offsetWidth&&t(e.value.offsetWidth),l&&o!==e.value.offsetHeight&&l(e.value.offsetHeight),n=e.value.offsetWidth,o=e.value.offsetHeight)}return{startResizeChecker(){be.start(),ge.push(a)},stopResizeChecker(){const e=ge.indexOf(a);e>=0&&ge.splice(e,1),0===ge.length&&be.stop()}}}(i,(()=>T()),(()=>T()));return p((()=>{g((()=>{setTimeout((()=>T(!0)),200),T(!0),G(),R=new MutationObserver((()=>T())),R.observe(i.value,L)}))})),b((()=>{q(),R&&(R.disconnect(),R=null)})),t({refreshScrollState(){T(!0)},getScrollContainer:()=>i.value,scrollTo(e,t){var l;null==(l=i.value)||l.scrollTo(e,t)},scrollToTop(){var e;null==(e=i.value)||e.scrollTo(0,0)},scrollToBottom(){var e;null==(e=i.value)||e.scrollTo(i.value.scrollWidth,i.value.scrollHeight)}}),(t,l)=>(a(),C("div",{ref_key:"scrollrect",ref:u,class:x(["vue-scroll-rect",e.scrollBarAlwaysShow?"always-show-scrollbar":"",e.scrollBarBackgroundClickable?"background-clickable":"",h.value?"dragging":""]),onWheel:j},[k("div",{ref_key:"container",ref:i,class:x(["scroll-content",e.scroll,e.containerClass]),onScroll:B},[c(t.$slots,"default")],34),I.show?c(t.$slots,"scrollBarX",{key:0,scrollBarValue:I,onScroll:Y},(()=>[k("div",{ref_key:"scrollBarRefX",ref:r,class:"scrollbar horizontal",onClick:V,onWheel:z},[k("div",{class:"thumb",ref_key:"scrollBarThumbRefX",ref:v,style:O({left:`${I.pos}%`,width:`${I.size}%`}),onMousedown:l[0]||(l[0]=(...e)=>f(D)&&f(D)(...e)),onWheel:z},null,36)],544)])):y("",!0),M.show?c(t.$slots,"scrollBarY",{key:1,scrollBarValue:M,onScroll:U},(()=>[M.show?(a(),C("div",{key:0,ref_key:"scrollBarRefY",ref:d,class:"scrollbar vertical",onClick:X,onWheel:_},[k("div",{class:"thumb",ref_key:"scrollBarThumbRefY",ref:m,style:O({top:`${M.pos}%`,height:`${M.size}%`}),onMousedown:l[1]||(l[1]=(...e)=>f(N)&&f(N)(...e)),onWheel:_},null,36)],544)):y("",!0)])):y("",!0)],34))}}),Ce=(e,t)=>{const l=e.__vccOpts||e;for(const[n,o]of t)l[n]=o;return l},xe={},ke={class:"mx-checked-mark","aria-hidden":"true",viewBox:"0 0 1024 1024"},Ie=[k("path",{d:"M129.3,428.6L52,512l345,372.5l575-620.8l-69.5-75L400.4,718.2L129.3,428.6z"},null,-1)];const Me=Ce(xe,[["render",function(e,t){return a(),C("svg",ke,Ie)}]]),Se={},we={class:"mx-right-arrow","aria-hidden":"true",viewBox:"0 0 1024 1024"},Pe=[k("path",{d:"M307.018 49.445c11.517 0 23.032 4.394 31.819 13.18L756.404 480.18c8.439 8.438 13.181 19.885 13.181 31.82s-4.741 23.38-13.181 31.82L338.838 961.376c-17.574 17.573-46.065 17.573-63.64-0.001-17.573-17.573-17.573-46.065 0.001-63.64L660.944 512 275.198 126.265c-17.574-17.573-17.574-46.066-0.001-63.64C283.985 53.839 295.501 49.445 307.018 49.445z"},null,-1)];const We=Ce(Se,[["render",function(e,t){return a(),C("svg",we,Pe)}]]),Oe={class:"mx-item-row"},Re=["xlink:href"],Le={key:1,class:"label"},Be={class:"mx-item-row"},He={class:"mx-shortcut"},Te=e({__name:"ContextMenuItem",props:{disabled:{type:Boolean,default:!1},hidden:{type:Boolean,default:!1},customRender:{type:Function,default:null},customClass:{type:String,default:""},clickHandler:{type:Function,default:null},label:{type:[String,Object,Function],default:""},icon:{type:[String,Object,Function],default:""},iconFontClass:{type:String,default:"iconfont"},checked:{type:Boolean,default:!1},shortcut:{type:String,default:""},svgIcon:{type:String,default:""},svgProps:{type:Object,default:null},preserveIconWidth:{type:Boolean,default:!0},showRightArrow:{type:Boolean,default:!1},hasChildren:{type:Boolean,default:!1},clickClose:{type:Boolean,default:!0},clickableWhenHasChildren:{type:Boolean,default:!1},rawMenuItem:{type:Object,default:void 0}},emits:["click","subMenuOpen","subMenuClose"],setup(e,{expose:n,emit:i}){const u=e,v=i,{clickHandler:m,clickClose:w,clickableWhenHasChildren:P,disabled:W,hidden:O,label:R,icon:L,iconFontClass:H,showRightArrow:T,shortcut:j,hasChildren:z}=t(u),_=l(!1),E=l(!1),$=l(),A=h("globalOptions"),F=h("globalHasSlot"),D=h("globalRenderSlot"),N=h("globalCloseMenu"),Y=h("menuContext"),U={getSubMenuInstance:()=>{},showSubMenu:()=>_.value?(Y.markActiveMenuItem(U,!0),!0):!!z.value&&(K(),!0),hideSubMenu:()=>{Y.closeOtherSubMenu()},isDisabledOrHidden:()=>W.value||O.value,getElement:()=>$.value,focus:()=>E.value=!0,blur:()=>E.value=!1,click:Z};function Z(e){if(!W.value){if(e){const t=e.target;if(t.classList.contains("mx-context-no-clickable")||A.value.ignoreClickClassName&&t.classList.contains(A.value.ignoreClickClassName))return;if(A.value.clickCloseClassName&&t.classList.contains(A.value.clickCloseClassName))return e.stopPropagation(),void N(u.rawMenuItem)}z.value?P.value?("function"==typeof m.value&&m.value(e),v("click",e)):_.value||K():("function"==typeof m.value&&m.value(e),v("click",e),w.value&&N(u.rawMenuItem))}}function K(e){E.value=!1,Y.checkCloseOtherSubMenuTimeOut()||Y.closeOtherSubMenu(),W.value||(Y.markActiveMenuItem(U),z.value&&(e||Y.markThisOpenedByKeyBoard(),Y.addOpenedSubMenu(V),_.value=!0,g((()=>v("subMenuOpen",U)))))}function V(){E.value=!1,_.value=!1,v("subMenuClose",U)}function X(){return{disabled:W.value,label:R.value,icon:L.value,iconFontClass:H.value,showRightArrow:T.value,clickClose:w.value,clickableWhenHasChildren:P.value,shortcut:j.value,theme:A.value.theme,isOpen:_,hasChildren:z,onClick:Z,onMouseEnter:K,closeMenu:N}}return B("menuItemInstance",U),p((()=>{Y.isMenuItemDataCollectedFlag()?g((()=>{let e=0;const t=Y.getElement();if(t){let l=0;for(let n=0;n<t.children.length;n++){const o=t.children[n];if("ContextMenuItem"===o.getAttribute("data-type")){if(o===$.value){e=l;break}l++}}}Y.addChildMenuItem(U,e)})):Y.addChildMenuItem(U)})),b((()=>{Y.removeChildMenuItem(U)})),n(U),(t,l)=>f(O)?y("",!0):(a(),C("div",{key:0,class:"mx-context-menu-item-wrapper",ref_key:"menuItemRef",ref:$,"data-type":"ContextMenuItem"},[f(F)("itemRender")?(a(),o(f(de),{key:0,vnode:()=>f(D)("itemRender",X())},null,8,["vnode"])):e.customRender?(a(),o(f(de),{key:1,vnode:e.customRender,data:X()},null,8,["vnode","data"])):(a(),C("div",{key:2,class:x(["mx-context-menu-item",f(W)?"disabled":"",E.value?"keyboard-focus":"",e.customClass?" "+e.customClass:"",_.value?"open":""]),onClick:Z,onMouseenter:K},[c(t.$slots,"default",{},(()=>[k("div",Oe,[k("div",{class:x(["mx-icon-placeholder",e.preserveIconWidth?"preserve-width":""])},[c(t.$slots,"icon",{},(()=>[f(F)("itemIconRender")?(a(),o(f(de),{key:0,vnode:()=>f(D)("itemIconRender",X())},null,8,["vnode"])):"string"==typeof e.svgIcon&&e.svgIcon?(a(),C("svg",I({key:1,class:"icon svg"},e.svgProps),[k("use",{"xlink:href":e.svgIcon},null,8,Re)],16)):"string"!=typeof f(L)?(a(),o(f(de),{key:2,vnode:f(L),data:f(L)},null,8,["vnode","data"])):"string"==typeof f(L)&&""!==f(L)?(a(),C("i",{key:3,class:x(f(L)+" icon "+f(H)+" "+f(A).iconFontClass)},null,2)):y("",!0)])),e.checked?c(t.$slots,"check",{key:0},(()=>[f(F)("itemCheckRender")?(a(),o(f(de),{key:0,vnode:()=>f(D)("itemCheckRender",X())},null,8,["vnode"])):y("",!0),s(Me)])):y("",!0)],2),c(t.$slots,"label",{},(()=>[f(F)("itemLabelRender")?(a(),o(f(de),{key:0,vnode:()=>f(D)("itemLabelRender",X())},null,8,["vnode"])):"string"==typeof f(R)?(a(),C("span",Le,M(f(R)),1)):(a(),o(f(de),{key:2,vnode:f(R),data:f(R)},null,8,["vnode","data"]))]))]),k("div",Be,[f(j)||t.$slots.shortcut?c(t.$slots,"shortcut",{key:0},(()=>[f(F)("itemShortcutRender")?(a(),o(f(de),{key:0,vnode:()=>f(D)("itemShortcutRender",X())},null,8,["vnode"])):y("",!0),k("span",He,M(f(j)),1)])):y("",!0),f(T)?c(t.$slots,"rightArrow",{key:1},(()=>[f(F)("itemRightArrowRender")?(a(),o(f(de),{key:0,vnode:()=>f(D)("itemRightArrowRender",X())},null,8,["vnode"])):y("",!0),s(We)])):y("",!0)])]))],34)),f(A).menuTransitionProps?(a(),o(S,d(I({key:3},f(A).menuTransitionProps)),{default:r((()=>[_.value?c(t.$slots,"submenu",{key:0,context:U}):y("",!0)])),_:3},16)):_.value?c(t.$slots,"submenu",{key:4,context:U}):y("",!0)],512))}}),je=e({name:"ContextMenuSperator",components:{VNodeRender:de},setup:()=>({globalHasSlot:h("globalHasSlot"),globalRenderSlot:h("globalRenderSlot")})}),ze={key:1,class:"mx-context-menu-item-sperator mx-context-no-clickable"};const _e=Ce(je,[["render",function(e,t,l,n,s,i){const u=P("VNodeRender");return e.globalHasSlot("separatorRender")?(a(),o(u,{key:0,vnode:()=>e.globalRenderSlot("separatorRender",{})},null,8,["vnode"])):(a(),C("div",ze))}]]),Ee=e({__name:"ContextSubMenu",props:{items:{type:Object,default:null},maxWidth:{type:[String,Number],default:0},minWidth:{type:[String,Number],default:0},adjustPosition:{type:Boolean,default:!0},direction:{type:String,default:"br"},parentMenuItemContext:{type:Object,default:null}},setup(e,{expose:n}){const d=e,v=l(!1),M=h("globalGetMenuHostId",""),S=h("menuContext"),w=h("globalOptions");h("globalHasSlot"),h("globalRenderSlot");const{zIndex:R,getParentWidth:L,getParentHeight:H,getZoom:T}=S,{adjustPosition:j}=t(d),z=l(),_=l(),E=l(),$=[],A=h("globalSetCurrentSubMenu"),F=[];let D=null,N=0;function Y(){D&&D.blur()}function U(e,t){if(e){for(let l=void 0!==t?t:0;l<F.length;l++)if(!F[l].isDisabledOrHidden()){Z(l);break}}else for(let l=void 0!==t?t:F.length-1;l>=0;l--)if(!F[l].isDisabledOrHidden()){Z(l);break}}function Z(e){var t;if(D&&Y(),void 0!==e&&(D=F[Math.max(0,Math.min(e,F.length-1))]),D&&(D.focus(),te.value)){const e=D.getElement();e&&(null==(t=z.value)||t.scrollTo(0,Math.min(Math.max(-ee.value,-e.offsetTop-e.offsetHeight+ne.value),0)))}}function K(){A(V)}const V={isTopLevel:()=>null===S.getParentContext(),closeSelfAndActiveParent:()=>{const e=q.getParentContext();if(e){e.closeOtherSubMenu();const t=e.getSubMenuInstanceContext();if(t)return t.focusCurrentItem(),!0}return!1},closeCurrentSubMenu:()=>{var e;return null==(e=q.getParentContext())?void 0:e.closeOtherSubMenu()},moveCurrentItemFirst:()=>U(!0),moveCurrentItemLast:()=>U(!1),moveCurrentItemDown:()=>U(!0,D?F.indexOf(D)+1:0),moveCurrentItemUp:()=>U(!1,D?F.indexOf(D)-1:0),focusCurrentItem:()=>Z(),openCurrentItemSubMenu:()=>!!D&&(null==D?void 0:D.showSubMenu()),triggerCurrentItemClick:e=>null==D?void 0:D.click(e)};let X=!1,G=!1;const q={zIndex:R+1,container:S.container,adjustPadding:w.value.adjustPadding||oe.defaultAdjustPadding,getParentWidth:()=>{var e;return(null==(e=E.value)?void 0:e.offsetWidth)||0},getParentHeight:()=>{var e;return(null==(e=E.value)?void 0:e.offsetHeight)||0},getPositon:()=>[le.value.x,le.value.y],getZoom:()=>w.value.zoom||oe.defaultZoom,addOpenedSubMenu(e){$.push(e)},closeOtherSubMenu(){$.forEach((e=>e())),$.splice(0,$.length),A(V)},checkCloseOtherSubMenuTimeOut:()=>!!N&&(clearTimeout(N),N=0,!0),closeOtherSubMenuWithTimeOut(){N=setTimeout((()=>{N=0,this.closeOtherSubMenu()}),200)},addChildMenuItem:(e,t)=>{void 0===t?F.push(e):F.splice(t,0,e)},removeChildMenuItem:e=>{F.splice(F.indexOf(e),1),e.getSubMenuInstance=()=>{}},markActiveMenuItem:(e,t=!1)=>{Y(),D=e,t&&Z()},markThisOpenedByKeyBoard:()=>{X=!0},isOpenedByKeyBoardFlag:()=>!!X&&(X=!1,!0),isMenuItemDataCollectedFlag:()=>G,getElement:()=>E.value||null,getParentContext:()=>S,getSubMenuInstanceContext:()=>V};B("menuContext",q);const Q={getChildItem:e=>F[e],getMenuDimensions:()=>_.value?{width:_.value.offsetWidth,height:_.value.offsetHeight}:{width:0,height:0},getSubmenuRoot:()=>_.value,getMenu:()=>E.value,getScrollValue:()=>{var e,t;return(null==(t=null==(e=z.value)?void 0:e.getScrollContainer())?void 0:t.scrollTop)||0},setScrollValue:e=>{var t;return null==(t=z.value)?void 0:t.scrollTo(0,e)},getScrollHeight:()=>ee.value,adjustPosition:()=>{ie()},getMaxHeight:()=>ne.value,getPosition:()=>le.value,setPosition:(e,t)=>{le.value.x=e,le.value.y=t}},J=h("menuItemInstance",void 0);J&&(J.getSubMenuInstance=()=>Q);const ee=l(0),te=l(!1),le=l({x:0,y:0}),ne=l(0);function ie(){g((()=>{var e,t;const l=E.value;if(l&&z.value){const{container:n}=S,o=(null==L?void 0:L())??0,a=(null==H?void 0:H())??0,s="number"==typeof S.adjustPadding?S.adjustPadding:(null==(e=S.adjustPadding)?void 0:e.x)??0,i="number"==typeof S.adjustPadding?S.adjustPadding:(null==(t=S.adjustPadding)?void 0:t.y)??0,u=a>0?i:0,r=document.documentElement.scrollHeight/T(),c=document.documentElement.scrollWidth/T(),v=Math.min(c,n.offsetWidth),f=Math.min(r,n.offsetHeight);let m=se(l,n),h=ae(l,n);d.direction.includes("l")?le.value.x-=l.offsetWidth+s:d.direction.includes("r")?le.value.x+=o+s:(le.value.x+=o/2,le.value.x-=(l.offsetWidth+s)/2),d.direction.includes("t")?le.value.y-=(l.offsetHeight+2*i)/T():d.direction.includes("b")?le.value.y-=i/T():le.value.y-=(l.offsetHeight+i)/2/T(),j.value&&g((()=>{var e,t;m=se(l,n),h=ae(l,n),ee.value=(null==(t=null==(e=z.value)?void 0:e.getScrollContainer())?void 0:t.scrollHeight)||0;const a=m+l.offsetWidth-v,i=h+ee.value+2*u-f;if(te.value=i>0,a>0){const e=o+l.offsetWidth-s,t=m;le.value.x-=e>t?t:e}if(te.value){const e=i,t=h;le.value.y-=e>t?t-u:e-u,ne.value=f-2*u}else ne.value=0}))}null==l||l.focus({preventScroll:!0}),S.isOpenedByKeyBoardFlag()&&U(!0),G=!0}))}return p((()=>{var e;v.value=!0;const t=null==(e=d.parentMenuItemContext)?void 0:e.getElement();if(t){const e=se(t,S.container),l=ae(t,S.container);le.value.x=e,le.value.y=l}else{const[e,t]=S.getPositon();le.value.x=e,le.value.y=t}A(V),ie()})),b((()=>{v.value=!1,J&&(J.getSubMenuInstance=()=>{})})),n(Q),(t,l)=>{const n=P("ContextSubMenu",!0);return v.value?(a(),o(m,{key:0,to:`#${f(M)}`},[k("div",I({ref_key:"submenuRoot",ref:_},t.$attrs,{class:["mx-context-menu",f(w).customClass?f(w).customClass:"",f(w).theme??""],style:{maxWidth:e.maxWidth?f(ce)(e.maxWidth):`${f(oe).defaultMaxWidth}px`,minWidth:e.minWidth?f(ce)(e.minWidth):`${f(oe).defaultMinWidth}px`,maxHeight:te.value&&ne.value>0?`${ne.value}px`:void 0,zIndex:f(R),left:`${le.value.x}px`,top:`${le.value.y}px`},"data-type":"ContextSubMenu",onClick:K}),[s(f(ye),{ref_key:"scrollRectRef",ref:z,scroll:te.value?"vertical":"none",style:O({width:"auto",height:`${te.value?ne.value:ee.value}px`})},{default:r((()=>[k("div",{class:x(["mx-context-menu-items"]),ref_key:"menu",ref:E},[c(t.$slots,"default",{},(()=>[(a(!0),C(W,null,u(e.items,((e,t)=>(a(),C(W,{key:t},[!0!==e.hidden&&"up"===e.divided?(a(),o(_e,{key:0})):y("",!0),!0!==e.hidden&&"self"===e.divided?(a(),o(_e,{key:1})):(a(),o(Te,{key:2,clickHandler:e.onClick?t=>e.onClick(t):void 0,disabled:"object"==typeof e.disabled?e.disabled.value:e.disabled,hidden:"object"==typeof e.hidden?e.hidden.value:e.hidden,icon:e.icon,iconFontClass:e.iconFontClass,svgIcon:e.svgIcon,svgProps:e.svgProps,label:e.label,customRender:e.customRender,customClass:e.customClass,checked:"object"==typeof e.checked?e.checked.value:e.checked,shortcut:e.shortcut,clickClose:e.clickClose,clickableWhenHasChildren:e.clickableWhenHasChildren,preserveIconWidth:void 0!==e.preserveIconWidth?e.preserveIconWidth:f(w).preserveIconWidth,showRightArrow:e.children&&e.children.length>0,hasChildren:e.children&&e.children.length>0,rawMenuItem:e,onSubMenuOpen:t=>{var l;return null==(l=e.onSubMenuOpen)?void 0:l.call(e,t)},onSubMenuClose:t=>{var l;return null==(l=e.onSubMenuClose)?void 0:l.call(e,t)}},i({_:2},[e.children&&e.children.length>0?{name:"submenu",fn:r((({context:t})=>[s(n,{parentMenuItemContext:t,items:e.children,maxWidth:e.maxWidth,minWidth:e.minWidth,adjustPosition:void 0!==e.adjustSubMenuPosition?e.adjustSubMenuPosition:f(w).adjustPosition,direction:void 0!==e.direction?e.direction:f(w).direction},null,8,["parentMenuItemContext","items","maxWidth","minWidth","adjustPosition","direction"])])),key:"0"}:void 0]),1032,["clickHandler","disabled","hidden","icon","iconFontClass","svgIcon","svgProps","label","customRender","customClass","checked","shortcut","clickClose","clickableWhenHasChildren","preserveIconWidth","showRightArrow","hasChildren","rawMenuItem","onSubMenuOpen","onSubMenuClose"])),!0===e.hidden||"down"!==e.divided&&!0!==e.divided?y("",!0):(a(),o(_e,{key:3}))],64)))),128))]))],512)])),_:3},8,["scroll","style"])],16)],8,["to"])):y("",!0)}}}),$e=e({__name:"ContextSubMenuWrapper",props:{options:{type:Object,default:null},show:{type:Boolean,default:null},container:{type:Object,default:null},isFullScreenContainer:{type:Boolean,default:!0}},emits:["close","closeAnimFinished"],setup(e,{expose:s,emit:i}){const u=e,d=i,v=n(),m=l(),{options:h,show:g,container:C}=t(u);p((()=>{g.value&&M()})),b((()=>{W()})),L(g,(e=>{e?M():(le(x),W())}));const x={closeMenu:P,isClosed:function(){return k},getMenuRef:()=>m.value,getMenuDimensions:()=>{var e;return(null==(e=m.value)?void 0:e.getMenuDimensions())??{width:0,height:0}}};let k=!1;function M(){setTimeout((()=>{document.addEventListener("click",T,!0),document.addEventListener("contextmenu",T,!0),document.addEventListener("scroll",H,!0),!u.isFullScreenContainer&&C.value&&C.value.addEventListener("scroll",H,!0),!1!==h.value.keyboardControl&&document.addEventListener("keydown",R,!0)}),50),function(e){te&&ne(),te=e}(x)}function P(e){k=!0,d("close",e),h.value.menuTransitionProps||d("closeAnimFinished"),le(x)}function W(){document.removeEventListener("contextmenu",T,!0),document.removeEventListener("click",T,!0),document.removeEventListener("scroll",H,!0),!u.isFullScreenContainer&&C.value&&C.value.removeEventListener("scroll",H,!0),!1!==h.value.keyboardControl&&document.removeEventListener("keydown",R,!0)}const O=l();function R(e){var t,l,n,o,a,s,i,u,r,c,d,v,f;let m=!0;switch(e.key){case"Escape":!1===(null==(t=O.value)?void 0:t.isTopLevel())?null==(l=O.value)||l.closeCurrentSubMenu():P();break;case"ArrowDown":null==(n=O.value)||n.moveCurrentItemDown();break;case"ArrowUp":null==(o=O.value)||o.moveCurrentItemUp();break;case"Home":null==(a=O.value)||a.moveCurrentItemFirst();break;case"End":null==(s=O.value)||s.moveCurrentItemLast();break;case"ArrowLeft":null!=(i=O.value)&&i.closeSelfAndActiveParent()||null==(r=(u=h.value).onKeyFocusMoveLeft)||r.call(u);break;case"ArrowRight":null!=(c=O.value)&&c.openCurrentItemSubMenu()||null==(v=(d=h.value).onKeyFocusMoveRight)||v.call(d);break;case"Enter":null==(f=O.value)||f.triggerCurrentItemClick(e);break;default:m=!1}m&&O.value&&(e.stopPropagation(),e.preventDefault())}function H(e){!1!==h.value.closeWhenScroll&&j(e.target,null)}function T(e){j(e.target,e)}function j(e,t){for(var l,n;e;){if(e.classList&&e.classList.contains("mx-context-menu"))return;e=e.parentNode}t?!1!==h.value.clickCloseOnOutside?(W(),P()):null==(n=(l=h.value).onClickOnOutside)||n.call(l,t):(W(),P())}return B("globalSetCurrentSubMenu",(e=>O.value=e)),B("globalGetMenuHostId",C.value.id),B("globalOptions",h),B("globalCloseMenu",P),B("globalIsFullScreenContainer",u.isFullScreenContainer),B("globalHasSlot",(e=>void 0!==v[e])),B("globalRenderSlot",((e,t)=>c(v,e,{...t},(()=>[w("span","Render slot failed")]),!1))),B("menuContext",{zIndex:h.value.zIndex||oe.defaultZindex,container:C.value,adjustPadding:{x:0,y:0},getZoom:()=>h.value.zoom||oe.defaultZoom,getParentWidth:()=>0,getParentHeight:()=>0,getPositon:()=>[h.value.x,h.value.y],closeOtherSubMenuWithTimeOut:()=>{},checkCloseOtherSubMenuTimeOut:()=>!1,addOpenedSubMenu:()=>{},closeOtherSubMenu:()=>{},getParentContext:()=>null,getSubMenuInstanceContext:()=>null,getElement:()=>null,addChildMenuItem:()=>{},removeChildMenuItem:()=>{},markActiveMenuItem:()=>{},markThisOpenedByKeyBoard:()=>{},isOpenedByKeyBoardFlag:()=>!1,isMenuItemDataCollectedFlag:()=>!1}),s(x),(e,t)=>f(h).menuTransitionProps?(a(),o(S,I({key:0,appear:""},f(h).menuTransitionProps,{onAfterLeave:t[0]||(t[0]=e=>d("closeAnimFinished"))}),{default:r((()=>[f(g)?(a(),o(Ee,{key:0,ref_key:"submenuInstance",ref:m,items:f(h).items,adjustPosition:f(h).adjustPosition,maxWidth:f(h).maxWidth||f(oe).defaultMaxWidth,minWidth:f(h).minWidth||f(oe).defaultMinWidth,direction:f(h).direction||f(oe).defaultDirection},{default:r((()=>[c(e.$slots,"default")])),_:3},8,["items","adjustPosition","maxWidth","minWidth","direction"])):y("",!0)])),_:3},16)):f(g)?(a(),o(Ee,{key:1,ref_key:"submenuInstance",ref:m,items:f(h).items,adjustPosition:f(h).adjustPosition,maxWidth:f(h).maxWidth||f(oe).defaultMaxWidth,minWidth:f(h).minWidth||f(oe).defaultMinWidth,direction:f(h).direction||f(oe).defaultDirection},{default:r((()=>[c(e.$slots,"default")])),_:3},8,["items","adjustPosition","maxWidth","minWidth","direction"])):y("",!0)}}),Ae=e({__name:"ContextMenu",props:{options:{type:Object,default:null},show:{type:Boolean,default:!1}},emits:["update:show","close"],setup(e,{expose:h,emit:p}){const g=p,b=e,{options:y,show:C}=t(b),{isNew:x,container:k,eleId:I}=re(y.value),M=l(null),S=n();function w(e){var t,l;g("update:show",!1),g("close"),null==(l=(t=y.value).onClose)||l.call(t,e)}return h({closeMenu:()=>g("update:show",!1),isClosed:()=>!C.value,getMenuRef:()=>{var e;return null==(e=M.value)?void 0:e.getMenuRef()},getMenuDimensions:()=>{var e;return(null==(e=M.value)?void 0:e.getMenuDimensions())??{width:0,height:0}}}),(e,t)=>(a(),o(m,{to:`#${f(I)}`},[s($e,{ref_key:"menuRef",ref:M,options:f(y),show:f(C),container:f(k),isFullScreenContainer:!f(x),onClose:w},i({_:2},[u(f(S),((t,l)=>({name:l,fn:r((t=>[c(e.$slots,l,d(v(t)))]))})))]),1032,["options","show","container","isFullScreenContainer"])],8,["to"]))}}),Fe=e({name:"ContextMenuGroup",props:{disabled:{type:Boolean,default:!1},hidden:{type:Boolean,default:!1},clickHandler:{type:Function,default:null},label:{type:String,default:""},icon:{type:String,default:""},iconFontClass:{type:String,default:"iconfont"},checked:{type:Boolean,default:!1},shortcut:{type:String,default:""},svgIcon:{type:String,default:""},svgProps:{type:Object,default:null},preserveIconWidth:{type:Boolean,default:!0},showRightArrow:{type:Boolean,default:!1},clickClose:{type:Boolean,default:!0},adjustSubMenuPosition:{type:Boolean,default:void 0},maxWidth:{type:[String,Number],default:0},minWidth:{type:[String,Number],default:0}},setup(e,n){const o=h("globalOptions"),{adjustSubMenuPosition:a,maxWidth:s,minWidth:i}=t(e),u=typeof a.value<"u"?a.value:o.value.adjustPosition,r=l(),c=l();return n.expose({getSubMenuRef:()=>r.value,getMenuItemRef:()=>c.value}),()=>w(Te,{...e,ref:c,showRightArrow:!0,maxWidth:void 0,minWidth:void 0,adjustSubMenuPosition:void 0,hasChildren:void 0!==typeof n.slots.default},n.slots.default?{submenu:e=>w(Ee,{ref:r,maxWidth:s.value,minWidth:i.value,adjustPosition:u,parentMenuItemContext:e.context},{default:n.slots.default}),...ve(n.slots,"default")}:n.slots)}});function De(e,t){const n=re(e);return function(e,t,n,o){const a=l(!0),s=w($e,{options:e,show:a.value,container:t,isFullScreenContainer:!n,onCloseAnimFinished:()=>{R(null,t)},onClose:t=>{var l;null==(l=e.onClose)||l.call(e,t),a.value=!1}},o);return R(s,t),s.component}(e,n.container,n.isNew,t).exposed}const Ne={install(e){e.config.globalProperties.$contextmenu=De,e.component("ContextMenu",Ae),e.component("ContextMenuItem",Te),e.component("ContextMenuGroup",Fe),e.component("ContextMenuSperator",_e),e.component("ContextMenuSeparator",_e),e.component("ContextSubMenu",Ee)},showContextMenu:(e,t)=>De(e,t),isAnyContextMenuOpen:()=>null!==te,closeContextMenu:ne,transformMenuPosition:function(e,t,l,n){return{x:se(e,n)+t,y:ae(e,n)+l}}};var Ye=Object.defineProperty,Ue=Object.defineProperties,Ze=Object.getOwnPropertyDescriptors,Ke=Object.getOwnPropertySymbols,Ve=Object.prototype.hasOwnProperty,Xe=Object.prototype.propertyIsEnumerable,Ge=(e,t,l)=>t in e?Ye(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,qe=(e,t)=>{for(var l in t||(t={}))Ve.call(t,l)&&Ge(e,l,t[l]);if(Ke)for(var l of Ke(t))Xe.call(t,l)&&Ge(e,l,t[l]);return e},Qe=(e,t)=>Ue(e,Ze(t));const Je={},et=e({name:"Icon",props:{name:String,color:String},computed:{svg(){if(this.name)return Je[this.name]}},render(e){const t=this.svg;if(!t)return s("span",{class:"m-svg-icon"},null);const l={color:this.color?this.color:t.fill?t.fill:null};return s("span",{class:["m-svg-icon","m-svg-icon--"+this.name]},[s("svg",{version:"1.1",xmlns:"http://www.w3.org/2000/svg",viewBox:t.viewBox,style:l,class:t.class},[t.defs&&s("defs",{innerHTML:t.defs},null),t.path&&s("path",{fill:"currentColor",d:t.path},null),t.html&&s("g",{innerHTML:t.html},null),this.$slots.default])])}});et.add=function(e,t){Je[e]=t};const tt={name:"error",fill:"#F56C6C",viewBox:"0 0 1024 1024",path:"M512,952C269,952,72,755,72,512S269,72,512,72s440,197,440,440S755,952,512,952z M579.7,512l101.6-101.6 c18.7-18.7,18.7-49,0-67.7c-18.7-18.7-49-18.7-67.7,0l0,0L512,444.3L410.4,342.7c-18.7-18.7-49-18.7-67.7,0s-18.7,49,0,67.7 L444.3,512L342.7,613.6c-18.7,18.7-18.7,49,0,67.7c18.7,18.7,49,18.7,67.7,0L512,579.7l101.6,101.6c18.7,18.7,49,18.7,67.7,0 c18.7-18.7,18.7-49,0-67.7L579.7,512z"},lt={name:"info",fill:"#1CADF2",viewBox:"0 0 1024 1024",path:"M512,72C269,72,72,269,72,512s197,440,440,440s440-197,440-440S755,72,512,72z M581,673.9 c-33.2,49.9-67,88.3-123.8,88.3c-38.8-6.3-54.7-34.1-46.3-62.4L484,457.6c1.8-5.9-1.2-12.3-6.6-14.2c-5.4-1.9-15.9,5.1-25.1,15.1 l-44.2,53.2c-1.2-8.9-0.1-23.7-0.1-29.6c33.2-49.9,87.8-89.2,124.8-89.2c35.2,3.6,51.8,31.7,45.7,62.6l-73.6,243.3 c-1,5.5,1.9,11.1,6.9,12.8c5.4,1.9,16.8-5.1,26-15.1l44.2-53.1C583,652.3,581,667.9,581,673.9z M571.2,357.6 c-28,0-50.6-20.4-50.6-50.4c0-30,22.7-50.3,50.6-50.3c28,0,50.6,20.4,50.6,50.3C621.8,337.3,599.1,357.6,571.2,357.6z"},nt={name:"success",fill:"#17B77E",viewBox:"0 0 1024 1024",path:"M512,72C269,72,72,269,72,512s197,440,440,440s440-197,440-440S755,72,512,72L512,72z M758.9,374 c-48.5,48.6-81.2,76.9-172.3,186.8c-52.6,63.4-102.3,131.5-102.7,132L462.1,720c-4.6,6.1-13.5,6.8-19.1,1.6L267.9,558.9 c-17.8-16.5-18.8-44.4-2.3-62.2s44.4-18.8,62.2-2.3l104.9,97.5c5.5,5.1,14.1,4.5,18.9-1.3c16.2-20.1,38.4-44.5,62.4-68.6 c90.2-90.9,145.6-139.7,175.2-161.3c36-26.2,77.3-48.6,87.3-36.2C792,343.9,782.5,350.3,758.9,374L758.9,374z"},ot={name:"warning",fill:"#FFC603",viewBox:"0 0 1024 1024",path:"M512,952C269,952,72,755,72,512S269,72,512,72s440,197,440,440S755,952,512,952z M510,770.8 c30.4,0,55-24.6,55-55s-24.6-55-55-55s-55,24.6-55,55S479.6,770.8,510,770.8z M509.8,255.3c-39.3,0-71.2,31.9-71.2,71.2 c0,3.1,0.2,6.2,0.6,9.3L472.4,588c2.5,19.3,18.9,33.7,38.4,33.7c19.4,0,35.8-14.4,38.2-33.7l31.8-252.2c5-39.2-22.8-75-62-79.9 C515.9,255.5,512.8,255.3,509.8,255.3z"},at={name:"loading",viewBox:"0 0 50 50",html:'<g stroke="#f2f2f2" stroke-width="3.5"  stroke-linecap="round" fill="none"><circle cx="25" cy="25" r="20" class="m-loading-icon-bg-path"></circle><circle cx="25" cy="25" r="20" stroke="#20a0ff" stroke-dasharray="90, 150" stroke-dashoffset="0" class="m-loading-icon-active-path"><animate attributeName="stroke-dasharray" dur="1.5s" values="1,200;90,150;90,150" repeatCount="indefinite"/><animate attributeName="stroke-dashoffset" dur="1.5s" values="0;-40px;-120px" repeatCount="indefinite"/><animateTransform attributeName="transform" type="rotate" from="0 25 25" to="360 25 25" dur="2s" repeatCount="indefinite"/></circle></g>'};et.add(tt.name,tt),et.add(lt.name,lt),et.add(nt.name,nt),et.add(ot.name,ot),et.add(at.name,at);var st=(e,t)=>{const l=e.__vccOpts||e;for(const[n,o]of t)l[n]=o;return l};const it=e({components:{Icon:et},name:"m-message",emits:["close","destroy","collapsed"],props:{id:String,type:{type:String,default:"info"},title:String,message:String,iconURL:String,duration:{type:Number,default:3e3},isCollapsed:Boolean,collapsable:Boolean,supportHTML:Boolean,width:String,className:String,wrapperClassName:String,closable:Boolean,stopTimerOnHover:{type:Boolean,default:!0}},setup(e,{expose:t,emit:n}){const o=H({visible:!0,collapsed:e.isCollapsed,timer:null});let a;const s=()=>{e.duration<0||({stop:a}=function(e,t){const n=l(0);return n.value=window.setTimeout(e,t),{stop(){window.clearTimeout(n.value)}}}((()=>{u()}),e.duration))},i=()=>{null==a||a()},u=()=>{o.visible=!1};return z((()=>{i()})),p((()=>{s()})),t({close:u}),{state:o,handleClearTimer:()=>{e.stopTimerOnHover&&i()},handleStartTimer:()=>{e.stopTimerOnHover&&s()},triggerCollapse:()=>{o.collapsed=!o.collapsed,n("collapsed",o.collapsed)},handleClose:()=>{o.visible=!1}}}}),ut=["id"],rt={key:0,class:"m-message-icons"},ct=["src"],dt={class:"m-message-content"},vt={key:0,class:"m-message--title"},ft=["innerHTML"],mt={key:0,class:"m-message--description"},ht={class:"m-message--control"},pt=[k("svg",{viewBox:"0 0 35 35",width:"20",height:"20",version:"1.1",fill:"currentColor"},[k("path",{d:"M9.4,13.9c-0.2,0.2-0.2,0.6,0,0.8l8.1,8.1l0,0l0,0l8.1-8.1c0.2-0.2,0.2-0.6,0-0.8l-1.3-1.3 c-0.2-0.2-0.6-0.2-0.8,0l-5.5,5.5c-0.2,0.2-0.6,0.2-0.8,0l-5.5-5.5c-0.2-0.2-0.6-0.2-0.8,0L9.4,13.9z"})],-1)],gt=[k("svg",{viewBox:"0 0 35 35",width:"20",height:"20",version:"1.1",fill:"currentColor"},[k("path",{d:"M19.5,17.5l5.1,5.1l-2,2l-5.1-5.1l-5.1,5.1l-2-2l5.1-5.1l-5.1-5.1l2-2l5.1,5.1l5.1-5.1l2,2L19.5,17.5z"})],-1)];var bt=st(it,[["render",function(e,t,l,n,s,i){const u=P("icon");return a(),o(S,{name:"m-message-fade",appear:"",mode:"in-out",onBeforeLeave:t[4]||(t[4]=t=>e.$emit("close")),onAfterLeave:t[5]||(t[5]=t=>e.$emit("destroy"))},{default:r((()=>[e.state.visible?(a(),C("div",{key:0,class:x(["m-message-wrapper",e.wrapperClassName]),id:e.id,style:O({width:e.width})},[k("div",{class:x(["m-message",e.className]),onMouseenter:t[2]||(t[2]=(...t)=>e.handleClearTimer&&e.handleClearTimer(...t)),onMouseleave:t[3]||(t[3]=(...t)=>e.handleStartTimer&&e.handleStartTimer(...t))},[e.iconURL||e.type?(a(),C("div",rt,[e.iconURL?(a(),C("img",{key:0,src:e.iconURL,class:"m-message--icon"},null,8,ct)):e.type?(a(),o(u,{key:1,name:e.type,class:"m-message--icon"},null,8,["name"])):y("",!0)])):y("",!0),k("div",dt,[e.title||e.$slots.title?(a(),C("div",vt,[c(e.$slots,"title",{},(()=>[j(M(e.title),1)]))])):y("",!0),e.supportHTML&&e.message?(a(),C(W,{key:1},[e.state.collapsed?y("",!0):(a(),C("div",{key:0,class:"m-message--description",innerHTML:e.message},null,8,ft))],64)):(a(),C(W,{key:2},[e.state.collapsed?y("",!0):(a(),C("div",mt,[c(e.$slots,"default",{},(()=>[j(M(e.message),1)]))]))],64))]),k("div",ht,[e.collapsable&&(e.title||e.$slots.title)?(a(),C("button",{key:0,class:x(["m-message--button m-message--arrow-down",{"is-collapsed":e.state.collapsed}]),onClick:t[0]||(t[0]=(...t)=>e.triggerCollapse&&e.triggerCollapse(...t))},pt,2)):y("",!0),e.closable?(a(),C("button",{key:1,class:"m-message--button m-message--close",onClick:t[1]||(t[1]=(...t)=>e.handleClose&&e.handleClose(...t))},gt)):y("",!0)])],34)],14,ut)):y("",!0)])),_:3})}]]);const yt=[];let Ct=0;const xt={};let kt={};const It={stopTimerOnHover:!0,duration:3e3},Mt=e=>{const t="m-message-"+Ct++,l=Qe(qe(qe(qe({},It),kt),e),{id:t});delete l.hasMask,delete l.position,delete l.zIndex;const n=e.position||"top-center",o=e.hasMask||!1,a=n+(o?"-mask":"");let i=xt[a];i?i.count++:(i=xt[a]={el:document.createElement("div"),count:1},i.el.className=["m-message-container","is-"+n,o?"has-mask":""].filter((function(e){return!!e})).join(" "),document.body.appendChild(i.el)),e.zIndex&&(i.el.style.zIndex=String(e.zIndex));let u=null;T(e.message)?(u={default:()=>e.message},l.message=""):"function"==typeof e.message&&(u={default:e.message},l.message="");const r=s(bt,l,u),c=document.createElement("div");r.appContext=e.ctx||Mt._context||null,r.props.onClose=e.onClose,r.props.onDestroy=()=>{i.count--,0===i.count&&(delete xt[a],i.el.remove()),R(null,c)},R(r,c),0===n.indexOf("bottom")&&i.el.firstChild?i.el.insertBefore(c.firstElementChild,i.el.firstChild):i.el.appendChild(c.firstElementChild);const d={id:t,close(){var e,t;null==(t=null==(e=null==r?void 0:r.component)?void 0:e.exposed)||t.close()}};return yt.push(d),d};Mt.success=(e,t)=>Mt(Qe(qe({},t),{type:"success",message:e})),Mt.info=(e,t)=>Mt(Qe(qe({},t),{type:"info",message:e})),Mt.warning=(e,t)=>Mt(Qe(qe({},t),{type:"warning",message:e})),Mt.error=(e,t)=>Mt(Qe(qe({},t),{type:"error",message:e})),Mt.loading=(e,t)=>Mt(Qe(qe({},t),{type:"loading",message:e})),Mt.closeAll=function(){for(let e=yt.length-1;e>=0;e--)yt[e].close()},Mt.setDefault=e=>{kt=qe({},e)};var St,wt,Pt=(wt=function(e,t={}){Mt._context=e._context,e.config.globalProperties["$"+(t.name||"mmessage")]=Mt,t.defaultOptions&&Mt.setDefault(t.defaultOptions)},(St=Mt).install=wt,St);const Wt=_("tabbar",(()=>{const e=E(),t=l([]),n=l(-1);return{list:t,leaveIndex:n,add:async function(e){var l,o,a;const s=[];e.matched.forEach(((e,t)=>{var l;t>0&&(null==(l=e.components)?void 0:l.default.name)&&s.push(e.components.default.name)}));const i=null==(l=e.matched.at(-1))?void 0:l.meta,u=e.fullPath;if("reload"!==e.name){if(!t.value.find((t=>t.routeName?t.routeName===e.name:t.tabId===u))){const l={tabId:u,fullPath:e.fullPath,routeName:e.name,title:"function"==typeof(null==i?void 0:i.title)?i.title():null==i?void 0:i.title,icon:(null==i?void 0:i.icon)??(null==(a=null==(o=null==i?void 0:i.breadcrumbNeste)?void 0:o.findLast((e=>e.icon)))?void 0:a.icon),name:s};n.value>=0?(t.value.splice(n.value+1,0,l),n.value=-1):t.value.push(l)}}},remove:function(l){const n=[],o=[];t.value.forEach((e=>{e.tabId===l?o.push(...e.name):n.push(...e.name)}));const a=[];o.forEach((e=>{n.includes(e)||a.push(e)})),e.remove(a),t.value=t.value.filter((e=>e.tabId!==l))},removeOtherSide:function(l){const n=[],o=[];t.value.forEach((e=>{e.tabId!==l?o.push(...e.name):n.push(...e.name)}));const a=[];o.forEach((e=>{n.includes(e)||a.push(e)})),e.remove(a),t.value=t.value.filter((e=>e.tabId===l))},removeLeftSide:function(l){const n=t.value.findIndex((e=>e.tabId===l)),o=[],a=[];t.value.forEach(((e,t)=>{t<n?a.push(...e.name):o.push(...e.name)}));const s=[];a.forEach((e=>{o.includes(e)||s.push(e)})),e.remove(s),t.value=t.value.filter(((e,t)=>t>=n))},removeRightSide:function(l){const n=t.value.findIndex((e=>e.tabId===l)),o=[],a=[];t.value.forEach(((e,t)=>{t>n?a.push(...e.name):o.push(...e.name)}));const s=[];a.forEach((e=>{o.includes(e)||s.push(e)})),e.remove(s),t.value=t.value.filter(((e,t)=>t<=n))},clean:function(){t.value=[]}}}));const Ot={class:"tabbar-container"},Rt=["data-index","title","onClick","onContextmenu"],Lt={class:"tab-content"},Bt=["onClick"],Ht=Q(e({name:"Tabbar",__name:"index",setup(e){const t=$(),n=A(),i=F(),c=Wt(),d=function(){const e=$(),t=A(),l=Wt();function n(){return e.fullPath}function o(e){const o=n();t.push(e).then((()=>{l.remove(o)}))}return{getId:n,open:function(e){const o=l.list.findIndex((e=>e.tabId===n()));l.$patch({leaveIndex:o}),t.push(e)},go:function(e){const o=n();t.go(e),l.remove(o)},close:o,closeById:function(e=n()){const t=n();if(l.list.some((t=>t.tabId===e)))if(l.list.length>1)if(e===t){const t=l.list.findIndex((t=>t.tabId===e));t<l.list.length-1?o(l.list[t+1].fullPath):o(l.list[t-1].fullPath)}else l.remove(e);else Pt.error("当前只有一个标签页，已阻止关闭",{zIndex:2e3});else Pt.error("关闭的页面不存在",{zIndex:2e3})},closeOtherSide:function(e=n()){const o=n();if(e!==o){const n=l.list.findIndex((t=>t.tabId===e));t.push(l.list[n].fullPath)}l.removeOtherSide(e)},closeLeftSide:function(e=n()){const o=n();if(e!==o){const n=l.list.findIndex((t=>t.tabId===e));l.list.findIndex((e=>e.tabId===o))<n&&t.push(l.list[n].fullPath)}l.removeLeftSide(e)},closeRightSide:function(e=n()){const o=n();if(e!==o){const n=l.list.findIndex((t=>t.tabId===e));l.list.findIndex((e=>e.tabId===o))>n&&t.push(l.list[n].fullPath)}l.removeRightSide(e)},checkCloseOtherSide:function(e=n()){return l.list.some((t=>t.tabId!==e))},checkCloseLeftSide:function(e=n()){var t;let o=!0;if(e===(null==(t=l.list[0])?void 0:t.tabId))o=!1;else{const t=l.list.findIndex((t=>t.tabId===e));o=l.list.some(((l,n)=>n<t&&l.tabId!==e))}return o},checkCloseRightSide:function(e=n()){var t;let o=!0;if(e===(null==(t=l.list.at(-1))?void 0:t.tabId))o=!1;else{const t=l.list.findIndex((t=>t.tabId===e));o=l.list.some(((l,n)=>n>=t&&l.tabId!==e))}return o}}}(),v=ee(),m=J({reactive:!0}),h=D((()=>d.getId())),g=l(),b=l(),I=N([]);function S(e){g.value.scrollBy({left:e.deltaY||e.detail})}return Y((()=>{I.value=[]})),L((()=>t),(e=>{i.settings.tabbar.enable&&c.add(e).then((()=>{const e=c.list.findIndex((e=>e.tabId===h.value));var t;-1!==e&&(t=I.value[e].offsetLeft,g.value.scrollTo({left:t-50,behavior:"smooth"}),b.value.$el.clientWidth>g.value.clientWidth&&void 0===localStorage.getItem("tabbarScrollTip")&&(localStorage.setItem("tabbarScrollTip",""),Pt.info("标签栏数量超过展示区域范围，可以将鼠标移到标签栏上，通过鼠标滚轮滑动浏览",{title:"温馨提示",duration:5e3,closable:!0,zIndex:2e3})))}))}),{immediate:!0,deep:!0}),p((()=>{U("alt+left,alt+right,alt+w,alt+1,alt+2,alt+3,alt+4,alt+5,alt+6,alt+7,alt+8,alt+9,alt+0",((e,t)=>{var l,o;if(i.settings.tabbar.enable&&i.settings.tabbar.enableHotkeys)switch(e.preventDefault(),t.key){case"alt+left":if(c.list[0].tabId!==h.value){const e=c.list.findIndex((e=>e.tabId===h.value));n.push(c.list[e-1].fullPath)}break;case"alt+right":if((null==(l=c.list.at(-1))?void 0:l.tabId)!==h.value){const e=c.list.findIndex((e=>e.tabId===h.value));n.push(c.list[e+1].fullPath)}break;case"alt+w":d.closeById(h.value);break;case"alt+1":case"alt+2":case"alt+3":case"alt+4":case"alt+5":case"alt+6":case"alt+7":case"alt+8":case"alt+9":{const e=Number(t.key.split("+")[1]);(null==(o=c.list[e-1])?void 0:o.fullPath)&&n.push(c.list[e-1].fullPath);break}case"alt+0":n.push(c.list[c.list.length-1].fullPath)}}))})),Z((()=>{U.unbind("alt+left,alt+right,alt+w,alt+1,alt+2,alt+3,alt+4,alt+5,alt+6,alt+7,alt+8,alt+9,alt+0")})),(e,t)=>{const l=G;return a(),C("div",Ot,[k("div",{ref_key:"tabsRef",ref:g,class:"tabs",onWheel:K(S,["prevent"])},[s(V,{ref_key:"tabContainerRef",ref:b,name:"tabbar",tag:"div",class:"tab-container"},{default:r((()=>[(a(!0),C(W,null,u(f(c).list,((e,u)=>(a(),C("div",{key:e.tabId,ref_for:!0,ref_key:"tabRef",ref:I,"data-index":u,class:x(["tab",{actived:e.tabId===f(h)}]),title:"function"==typeof(null==e?void 0:e.title)?e.title():e.title,onClick:t=>f(n).push(e.fullPath),onContextmenu:t=>{return n=e,(l=t).preventDefault(),void Ne.showContextMenu({x:l.x,y:l.y,zIndex:1050,iconFontClass:"",customClass:"tabbar-contextmenu",items:[{label:"重新加载",icon:"i-ri:refresh-line",disabled:n.tabId!==h.value,onClick:()=>v.reload()},{label:"关闭标签页",icon:"i-ri:close-line",disabled:c.list.length<=1,divided:!0,onClick:()=>{d.closeById(n.tabId)}},{label:"关闭其他标签页",disabled:!d.checkCloseOtherSide(n.tabId),onClick:()=>{d.closeOtherSide(n.tabId)}},{label:"关闭左侧标签页",disabled:!d.checkCloseLeftSide(n.tabId),onClick:()=>{d.closeLeftSide(n.tabId)}},{label:"关闭右侧标签页",disabled:!d.checkCloseRightSide(n.tabId),onClick:()=>{d.closeRightSide(n.tabId)}}]});var l,n}},[t[0]||(t[0]=k("div",{class:"tab-dividers"},null,-1)),t[1]||(t[1]=k("div",{class:"tab-background"},null,-1)),k("div",Lt,[(a(),C("div",{key:e.tabId,class:"title"},[f(i).settings.tabbar.enableIcon&&e.icon?(a(),o(l,{key:0,name:e.icon,class:"icon"},null,8,["name"])):y("",!0),j(" "+M("function"==typeof(null==e?void 0:e.title)?e.title():e.title),1)])),f(c).list.length>1?(a(),C("div",{key:0,class:"action-icon",onClick:K((t=>f(d).closeById(e.tabId)),["stop"])},[s(l,{name:"i-ri:close-fill"})],8,Bt)):y("",!0),X(k("div",{class:"hotkey-number"},M(u+1),513),[[q,f(m).alt&&u<9]])])],42,Rt)))),128))])),_:1},512)],544)])}}}),[["__scopeId","data-v-66f89eec"]]);export{Ht as default};
