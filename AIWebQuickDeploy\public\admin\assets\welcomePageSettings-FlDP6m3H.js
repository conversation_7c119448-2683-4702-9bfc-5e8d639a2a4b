
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as s,Q as o,c as i,e as n,f as r,w as m,j as u,h as d,_ as c,g as f,a2 as g,T as p,Y as _,k as h}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";const v=["srcdoc"],y=l({__name:"welcomePageSettings",setup(l){const h=a({clientHomePath:"",homeHtml:""}),y=t({siteName:[{required:!0,trigger:"blur",message:"请填写网站名称"}]}),b=t();async function H(){const e=await x.queryConfig({keys:["clientHomePath","homeHtml"]});Object.assign(h,e.data)}function w(){var e;null==(e=b.value)||e.validate((async e=>{if(e){try{await x.setConfig({settings:(l=h,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),_.success("变更欢迎页设置成功")}catch(a){}H()}else _.error("请填写完整信息");var l}))}return s((()=>{H()})),(l,a)=>{const t=c,s=u,_=e,x=o("el-switch"),H=o("el-form-item"),V=o("el-col"),P=o("el-row"),j=o("el-input"),k=o("el-form"),C=o("el-card");return n(),i("div",null,[r(_,null,{title:m((()=>a[2]||(a[2]=[f("div",{class:"flex items-center gap-4"},"欢迎页设置",-1)]))),content:m((()=>a[3]||(a[3]=[f("div",{class:"text-sm/6"},[f("div",null," 欢迎页设置支持配置访问首页时的默认显示内容。可以启用欢迎页，或直接跳转到聊天页面。 "),f("div",null,"若启用欢迎页，可以在此处自定义欢迎页面内容。"),f("div",{class:"mt-2 text-gray-500"},[f("strong",null,"推荐："),d(" 您可以在其他专业的 HTML 编辑器（如 VS Code、Sublime Text）中编辑欢迎页面内容并复制粘贴到此处，以获得更好的编辑体验。 ")])],-1)]))),default:m((()=>[r(s,{outline:"",onClick:w},{default:m((()=>[r(t,{name:"i-ri:file-text-line"}),a[4]||(a[4]=d(" 保存设置 "))])),_:1})])),_:1}),r(C,{style:{margin:"20px"}},{default:m((()=>[r(k,{ref_key:"formRef",ref:b,rules:y.value,model:h,"label-width":"150px"},{default:m((()=>[r(P,null,{default:m((()=>[r(V,{xs:24,md:24,lg:24,xl:24},{default:m((()=>[r(H,{label:"开启欢迎页",prop:"clientHomePath"},{default:m((()=>[r(x,{modelValue:h.clientHomePath,"onUpdate:modelValue":a[0]||(a[0]=e=>h.clientHomePath=e),"active-value":"/home","inactive-value":"/chat"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),"/home"===h.clientHomePath?(n(),g(P,{key:0},{default:m((()=>[r(V,{xs:24,md:20,lg:15,xl:12},{default:m((()=>[r(H,{label:"欢迎页（HTML）",prop:"homeHtml"},{default:m((()=>[r(j,{modelValue:h.homeHtml,"onUpdate:modelValue":a[1]||(a[1]=e=>h.homeHtml=e),placeholder:"请输入自定义欢迎页内容",type:"textarea",rows:10,clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})):p("",!0),r(V,{xs:28,md:24,lg:20,xl:12,style:{"margin-top":"20px"}},{default:m((()=>[r(H,{label:"预览"},{default:m((()=>[f("iframe",{class:"w-full h-100 border border-gray-200 rounded-md bg-gray-100",srcdoc:h.homeHtml,sandbox:"allow-same-origin allow-scripts"},null,8,v)])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof h&&h(y);export{y as default};
