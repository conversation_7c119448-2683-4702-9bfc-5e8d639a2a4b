
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,af as t,a as s,ay as i,az as _,P as a,a0 as u,b as p,aA as r,ai as o,Q as n,c as l,e as m,g as d,f as v,T as c,a6 as j,i as g,a2 as y,w as f,aB as x,aC as b,a5 as h,aD as w,a8 as M,Z as k}from"./index-BERX8Mlm.js";import C from"./index-BuhDZ9J1.js";import H from"./index-i8Bdu04L.js";import D from"./index-BWLuTDA6.js";import L from"./index-ChkpeOnk.js";import{_ as P}from"./index.vue_vue_type_script_setup_true_lang-Cp9p1HkJ.js";import{_ as T}from"./index.vue_vue_type_script_setup_true_lang-DOqLWnBB.js";/* empty css                                                              */import S from"./link-Dd6acFu3.js";import q from"./index-Cs7G3EBC.js";import{_ as R}from"./index.vue_vue_type_script_setup_true_lang-DpB-kSh7.js";import{u as V}from"./useMainPage-Dbp8uSF1.js";import{u as $}from"./useMenu-CK91fAX9.js";import"./index.vue_vue_type_script_setup_true_lang-AClYjrVV.js";import"./rightSide.vue_vue_type_script_setup_true_lang-ClZNHSw-.js";import"./HDropdownMenu.vue_vue_type_script_setup_true_lang-Wc6l-Ngn.js";import"./index.vue_vue_type_script_setup_true_lang-PUnUpH4H.js";import"./HDropdown-DFGm5c_S.js";import"./HTabList.vue_vue_type_script_setup_true_lang-BEyYCazB.js";import"./use-resolve-button-type-DnRVrBaM.js";import"./index.vue_vue_type_script_setup_true_lang-DN03WRps.js";import"./index-DhWfG07N.js";import"./index.vue_vue_type_script_setup_true_lang-Do4XPH2t.js";import"./HKbd-LjWkyhwy.js";import"./index.vue_vue_type_script_setup_true_lang-DbfRBGyF.js";import"./index.vue_vue_type_script_setup_true_lang-CReMfaKl.js";import"./sub.vue_vue_type_script_setup_true_lang-SYX2vZ5k.js";import"./item.vue_vue_type_script_setup_true_lang-D5h42eqV.js";import"./HTooltip.vue_vue_type_script_setup_true_lang-T8XkjmIi.js";import"./index-DRUzQ5v3.js";import"./index-CGed-Lii.js";import"./leftSide.vue_vue_type_script_setup_true_lang-BTr4xL8t.js";import"./index-BTtsYu0a.js";import"./index-CZaxHimt.js";import"./item.vue_vue_type_script_setup_true_lang-aMXYc_KV.js";import"./HDialog.vue_vue_type_script_setup_true_lang-BfrjeNSs.js";import"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";const z={class:"layout"},A={id:"app-main"},B={class:"wrapper"},I={class:"main-container"},J={class:"main"},K=k(e({name:"Layout",__name:"index",setup(e){const k=t(),K=s(),Q=i(),U=_(),W=V(),Z=$(),E=a((()=>!!k.meta.link));return u((()=>K.settings.menu.subMenuCollapse),(e=>{"mobile"===K.mode&&(e?document.body.classList.remove("overflow-hidden"):document.body.classList.add("overflow-hidden"))})),u((()=>k.path),(()=>{"mobile"===K.mode&&K.$patch((e=>{e.settings.menu.subMenuCollapse=!0}))})),p((()=>{r("f5",(e=>{K.settings.toolbar.pageReload&&(e.preventDefault(),W.reload())})),r("alt+`",(e=>{K.settings.menu.enableHotkeys&&(e.preventDefault(),Z.switchTo(U.actived+1<U.allMenus.length?U.actived+1:0))}))})),o((()=>{r.unbind("f5"),r.unbind("alt+`")})),(e,t)=>{const s=n("RouterView");return m(),l("div",z,[d("div",A,[v(C),d("div",B,[d("div",{class:j(["sidebar-container",{show:"mobile"===g(K).mode&&!g(K).settings.menu.subMenuCollapse}])},[v(H),v(D)],2),d("div",{class:j(["sidebar-mask",{show:"mobile"===g(K).mode&&!g(K).settings.menu.subMenuCollapse}]),onClick:t[0]||(t[0]=e=>g(K).toggleSidebarCollapse())},null,2),d("div",I,[v(L),d("div",J,[v(s,null,{default:f((({Component:e,route:t})=>[v(x,{name:"slide-right",mode:"out-in",appear:""},{default:f((()=>[(m(),y(b,{include:g(Q).list},[h((m(),y(w(e),{key:t.fullPath})),[[M,!g(E)]])],1032,["include"]))])),_:2},1024)])),_:1}),g(E)?(m(),y(S,{key:0})):c("",!0)]),v(q)])])]),v(P),v(T),c("",!0),v(R)])}}}),[["__scopeId","data-v-dcb72a24"]]);export{K as default};
