
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,af as l,a as s,az as t,r as a,a0 as i,b as n,aG as d,a2 as o,e as u,b6 as m,w as c,i as g,f as b,j as r,h as p,_ as v,g as V,c as _,T as y,a6 as f,ab as h,bq as U,br as k,Z as x}from"./index-BERX8Mlm.js";import{_ as M}from"./HInput.vue_vue_type_script_setup_true_lang-DbpA_N81.js";import{_ as S}from"./HCheckList.vue_vue_type_script_setup_true_lang-Di8Bi58Y.js";import{_ as j}from"./HToggle.vue_vue_type_script_setup_true_lang-qXUln426.js";import{_ as H}from"./HTooltip.vue_vue_type_script_setup_true_lang-T8XkjmIi.js";import{_ as q}from"./HTabList.vue_vue_type_script_setup_true_lang-BEyYCazB.js";import{a as C}from"./index-DhWfG07N.js";/* empty css                                                              */import"./use-resolve-button-type-DnRVrBaM.js";const P={class:"flex items-center justify-center pb-4"},A={key:0,class:"divider"},w={key:1,class:"menu-mode"},O={class:"setting-item"},T={class:"label"},J={class:"setting-item"},I={class:"label"},N={class:"setting-item"},z={key:2,class:"setting-item"},B={class:"setting-item"},D={class:"setting-item"},L={class:"setting-item"},R={class:"setting-item"},G={class:"setting-item"},Z={key:3,class:"setting-item"},E={class:"setting-item"},F={class:"label"},K={key:4,class:"setting-item"},Q={class:"setting-item"},W={class:"label"},X={class:"setting-item"},Y={class:"label"},$={class:"setting-item"},ee={class:"setting-item"},le={class:"setting-item"},se={class:"setting-item"},te={class:"setting-item"},ae={class:"setting-item"},ie={class:"setting-item"},ne={class:"setting-item"},de={class:"label"},oe={class:"setting-item"},ue={class:"label"},me={class:"setting-item"},ce={class:"setting-item"},ge={class:"label"},be={class:"setting-item"},re={class:"label"},pe=x(e({name:"AppSetting",__name:"index",setup(e){const x=l(),pe=s(),ve=t(),Ve=a(!1);i((()=>pe.settings.menu.menuMode),(e=>{"single"===e?ve.setActived(0):ve.setActived(x.fullPath)})),n((()=>{d.on("global-app-setting-toggle",(()=>{Ve.value=!Ve.value}))}));const{copy:_e,isSupported:ye}=C();function fe(e){return"object"==typeof e&&!Array.isArray(e)}function he(e,l){if(!fe(e)||!fe(l))return l;const s={};for(const t in l){const a=e[t],i=l[t];if(JSON.stringify(a)!==JSON.stringify(i))if(fe(a)&&fe(i)){const e=he(a,i);Object.keys(e).length>0&&(s[t]=e)}else s[t]=i}return s}function Ue(){_e(JSON.stringify(he(k,pe.settings),null,2))}return(e,l)=>{const s=q,t=H,a=v,i=j,n=S,d=M,k=r,x=U;return u(),o(x,{modelValue:g(Ve),"onUpdate:modelValue":l[30]||(l[30]=e=>h(Ve)?Ve.value=e:null),title:"应用配置"},m({default:c((()=>[l[62]||(l[62]=V("div",{class:"rounded-2 bg-rose/20 px-4 py-2 text-sm/6 c-rose"},[V("p",{class:"my-1"}," 应用配置可实时预览效果，但只是临时生效，要想真正应用于项目，可以点击下方的「复制配置」按钮，并将配置粘贴到 src/settings.ts 文件中。 "),V("p",{class:"my-1"},"注意：在生产环境中应关闭该模块。")],-1)),l[63]||(l[63]=V("div",{class:"divider"},"颜色主题风格",-1)),V("div",P,[b(s,{modelValue:g(pe).settings.app.colorScheme,"onUpdate:modelValue":l[0]||(l[0]=e=>g(pe).settings.app.colorScheme=e),options:[{icon:"i-ri:sun-line",label:"明亮",value:"light"},{icon:"i-ri:moon-line",label:"暗黑",value:"dark"},{icon:"i-codicon:color-mode",label:"系统",value:""}],class:"w-60"},null,8,["modelValue"])]),"pc"===g(pe).mode?(u(),_("div",A,"导航栏模式")):y("",!0),"pc"===g(pe).mode?(u(),_("div",w,[b(t,{text:"侧边栏模式 (含主导航)",placement:"bottom",delay:500},{default:c((()=>[V("div",{class:f(["mode mode-side",{active:"side"===g(pe).settings.menu.menuMode}]),onClick:l[1]||(l[1]=e=>g(pe).settings.menu.menuMode="side")},l[31]||(l[31]=[V("div",{class:"mode-container"},null,-1)]),2)])),_:1}),b(t,{text:"顶部模式",placement:"bottom",delay:500},{default:c((()=>[V("div",{class:f(["mode mode-head",{active:"head"===g(pe).settings.menu.menuMode}]),onClick:l[2]||(l[2]=e=>g(pe).settings.menu.menuMode="head")},l[32]||(l[32]=[V("div",{class:"mode-container"},null,-1)]),2)])),_:1}),b(t,{text:"侧边栏模式 (不含主导航)",placement:"bottom",delay:500},{default:c((()=>[V("div",{class:f(["mode mode-single",{active:"single"===g(pe).settings.menu.menuMode}]),onClick:l[3]||(l[3]=e=>g(pe).settings.menu.menuMode="single")},l[33]||(l[33]=[V("div",{class:"mode-container"},null,-1)]),2)])),_:1})])):y("",!0),l[64]||(l[64]=V("div",{class:"divider"},"导航栏",-1)),V("div",O,[V("div",T,[l[34]||(l[34]=p(" 主导航切换跳转 ")),b(t,{text:"开启该功能后，切换主导航时，页面自动跳转至该主导航下，次导航里第一个导航"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.menu.switchMainMenuAndPageJump,"onUpdate:modelValue":l[4]||(l[4]=e=>g(pe).settings.menu.switchMainMenuAndPageJump=e),disabled:["single"].includes(g(pe).settings.menu.menuMode)},null,8,["modelValue","disabled"])]),V("div",J,[V("div",I,[l[35]||(l[35]=p(" 次导航保持展开一个 ")),b(t,{text:"开启该功能后，次导航只保持单个菜单的展开"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.menu.subMenuUniqueOpened,"onUpdate:modelValue":l[5]||(l[5]=e=>g(pe).settings.menu.subMenuUniqueOpened=e)},null,8,["modelValue"])]),V("div",N,[l[36]||(l[36]=V("div",{class:"label"},"次导航是否折叠",-1)),b(i,{modelValue:g(pe).settings.menu.subMenuCollapse,"onUpdate:modelValue":l[6]||(l[6]=e=>g(pe).settings.menu.subMenuCollapse=e)},null,8,["modelValue"])]),"pc"===g(pe).mode?(u(),_("div",z,[l[37]||(l[37]=V("div",{class:"label"},"显示次导航折叠按钮",-1)),b(i,{modelValue:g(pe).settings.menu.enableSubMenuCollapseButton,"onUpdate:modelValue":l[7]||(l[7]=e=>g(pe).settings.menu.enableSubMenuCollapseButton=e)},null,8,["modelValue"])])):y("",!0),V("div",B,[l[38]||(l[38]=V("div",{class:"label"},"是否启用快捷键",-1)),b(i,{modelValue:g(pe).settings.menu.enableHotkeys,"onUpdate:modelValue":l[8]||(l[8]=e=>g(pe).settings.menu.enableHotkeys=e),disabled:["single"].includes(g(pe).settings.menu.menuMode)},null,8,["modelValue","disabled"])]),l[65]||(l[65]=V("div",{class:"divider"},"顶栏",-1)),V("div",D,[l[39]||(l[39]=V("div",{class:"label"},"模式",-1)),b(n,{modelValue:g(pe).settings.topbar.mode,"onUpdate:modelValue":l[9]||(l[9]=e=>g(pe).settings.topbar.mode=e),options:[{label:"静止",value:"static"},{label:"固定",value:"fixed"},{label:"粘性",value:"sticky"}]},null,8,["modelValue"])]),V("div",null,[l[43]||(l[43]=V("div",{class:"divider"},"标签栏",-1)),V("div",L,[l[40]||(l[40]=V("div",{class:"label"},"是否启用",-1)),b(i,{modelValue:g(pe).settings.tabbar.enable,"onUpdate:modelValue":l[10]||(l[10]=e=>g(pe).settings.tabbar.enable=e)},null,8,["modelValue"])]),V("div",R,[l[41]||(l[41]=V("div",{class:"label"},"是否显示图标",-1)),b(i,{modelValue:g(pe).settings.tabbar.enableIcon,"onUpdate:modelValue":l[11]||(l[11]=e=>g(pe).settings.tabbar.enableIcon=e),disabled:!g(pe).settings.tabbar.enable},null,8,["modelValue","disabled"])]),V("div",G,[l[42]||(l[42]=V("div",{class:"label"},"是否启用快捷键",-1)),b(i,{modelValue:g(pe).settings.tabbar.enableHotkeys,"onUpdate:modelValue":l[12]||(l[12]=e=>g(pe).settings.tabbar.enableHotkeys=e),disabled:!g(pe).settings.tabbar.enable},null,8,["modelValue","disabled"])])]),l[66]||(l[66]=V("div",{class:"divider"},"工具栏",-1)),"pc"===g(pe).mode?(u(),_("div",Z,[l[44]||(l[44]=V("div",{class:"label"},"面包屑导航",-1)),b(i,{modelValue:g(pe).settings.toolbar.breadcrumb,"onUpdate:modelValue":l[13]||(l[13]=e=>g(pe).settings.toolbar.breadcrumb=e)},null,8,["modelValue"])])):y("",!0),V("div",E,[V("div",F,[l[45]||(l[45]=p(" 导航搜索 ")),b(t,{text:"对导航进行快捷搜索"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.toolbar.navSearch,"onUpdate:modelValue":l[14]||(l[14]=e=>g(pe).settings.toolbar.navSearch=e)},null,8,["modelValue"])]),"pc"===g(pe).mode?(u(),_("div",K,[l[46]||(l[46]=V("div",{class:"label"},"全屏",-1)),b(i,{modelValue:g(pe).settings.toolbar.fullscreen,"onUpdate:modelValue":l[15]||(l[15]=e=>g(pe).settings.toolbar.fullscreen=e)},null,8,["modelValue"])])):y("",!0),V("div",Q,[V("div",W,[l[47]||(l[47]=p(" 页面刷新 ")),b(t,{text:"使用框架内提供的刷新功能进行页面刷新"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.toolbar.pageReload,"onUpdate:modelValue":l[16]||(l[16]=e=>g(pe).settings.toolbar.pageReload=e)},null,8,["modelValue"])]),V("div",X,[V("div",Y,[l[48]||(l[48]=p(" 颜色主题 ")),b(t,{text:"开启后可在明亮/暗黑模式中切换"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.toolbar.colorScheme,"onUpdate:modelValue":l[17]||(l[17]=e=>g(pe).settings.toolbar.colorScheme=e)},null,8,["modelValue"])]),l[67]||(l[67]=V("div",{class:"divider"},"页面",-1)),V("div",$,[l[49]||(l[49]=V("div",{class:"label"},"是否启用快捷键",-1)),b(i,{modelValue:g(pe).settings.mainPage.enableHotkeys,"onUpdate:modelValue":l[18]||(l[18]=e=>g(pe).settings.mainPage.enableHotkeys=e)},null,8,["modelValue"])]),l[68]||(l[68]=V("div",{class:"divider"},"导航搜索",-1)),V("div",ee,[l[50]||(l[50]=V("div",{class:"label"},"是否启用快捷键",-1)),b(i,{modelValue:g(pe).settings.navSearch.enableHotkeys,"onUpdate:modelValue":l[19]||(l[19]=e=>g(pe).settings.navSearch.enableHotkeys=e),disabled:!g(pe).settings.toolbar.navSearch},null,8,["modelValue","disabled"])]),l[69]||(l[69]=V("div",{class:"divider"},"底部版权",-1)),V("div",le,[l[51]||(l[51]=V("div",{class:"label"},"是否启用",-1)),b(i,{modelValue:g(pe).settings.copyright.enable,"onUpdate:modelValue":l[20]||(l[20]=e=>g(pe).settings.copyright.enable=e)},null,8,["modelValue"])]),V("div",se,[l[52]||(l[52]=V("div",{class:"label"},"日期",-1)),b(d,{modelValue:g(pe).settings.copyright.dates,"onUpdate:modelValue":l[21]||(l[21]=e=>g(pe).settings.copyright.dates=e),disabled:!g(pe).settings.copyright.enable},null,8,["modelValue","disabled"])]),V("div",te,[l[53]||(l[53]=V("div",{class:"label"},"公司",-1)),b(d,{modelValue:g(pe).settings.copyright.company,"onUpdate:modelValue":l[22]||(l[22]=e=>g(pe).settings.copyright.company=e),disabled:!g(pe).settings.copyright.enable},null,8,["modelValue","disabled"])]),V("div",ae,[l[54]||(l[54]=V("div",{class:"label"},"网址",-1)),b(d,{modelValue:g(pe).settings.copyright.website,"onUpdate:modelValue":l[23]||(l[23]=e=>g(pe).settings.copyright.website=e),disabled:!g(pe).settings.copyright.enable},null,8,["modelValue","disabled"])]),V("div",ie,[l[55]||(l[55]=V("div",{class:"label"},"备案",-1)),b(d,{modelValue:g(pe).settings.copyright.beian,"onUpdate:modelValue":l[24]||(l[24]=e=>g(pe).settings.copyright.beian=e),disabled:!g(pe).settings.copyright.enable},null,8,["modelValue","disabled"])]),l[70]||(l[70]=V("div",{class:"divider"},"主页",-1)),V("div",ne,[V("div",de,[l[56]||(l[56]=p(" 是否启用 ")),b(t,{text:"该功能开启时，登录成功默认进入主页，反之则默认进入导航栏里第一个导航页面"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.home.enable,"onUpdate:modelValue":l[25]||(l[25]=e=>g(pe).settings.home.enable=e)},null,8,["modelValue"])]),V("div",oe,[V("div",ue,[l[57]||(l[57]=p(" 主页名称 ")),b(t,{text:"开启国际化时，该设置无效"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(d,{modelValue:g(pe).settings.home.title,"onUpdate:modelValue":l[26]||(l[26]=e=>g(pe).settings.home.title=e)},null,8,["modelValue"])]),l[71]||(l[71]=V("div",{class:"divider"},"其它",-1)),V("div",me,[l[58]||(l[58]=V("div",{class:"label"},"是否启用权限",-1)),b(i,{modelValue:g(pe).settings.app.enablePermission,"onUpdate:modelValue":l[27]||(l[27]=e=>g(pe).settings.app.enablePermission=e)},null,8,["modelValue"])]),V("div",ce,[V("div",ge,[l[59]||(l[59]=p(" 载入进度条 ")),b(t,{text:"该功能开启时，跳转路由会看到页面顶部有进度条"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.app.enableProgress,"onUpdate:modelValue":l[28]||(l[28]=e=>g(pe).settings.app.enableProgress=e)},null,8,["modelValue"])]),V("div",be,[V("div",re,[l[60]||(l[60]=p(" 动态标题 ")),b(t,{text:"该功能开启时，页面标题会显示当前路由标题，格式为“页面标题 - 网站名称”；关闭时则显示网站名称，网站名称在项目根目录下 .env.* 文件里配置"},{default:c((()=>[b(a,{name:"i-ri:question-line"})])),_:1})]),b(i,{modelValue:g(pe).settings.app.enableDynamicTitle,"onUpdate:modelValue":l[29]||(l[29]=e=>g(pe).settings.app.enableDynamicTitle=e)},null,8,["modelValue"])])])),_:2},[g(ye)?{name:"footer",fn:c((()=>[b(k,{block:"",onClick:Ue},{default:c((()=>[b(a,{name:"i-ep:document-copy"}),l[61]||(l[61]=p(" 复制配置 "))])),_:1})])),key:"0"}:void 0]),1032,["modelValue"])}}}),[["__scopeId","data-v-3fb48920"]]);export{pe as default};
