#!/bin/bash

# 轻量级构建脚本 - 跳过类型检查，减少内存使用
set -e

# 设置较小的内存限制
export NODE_OPTIONS="--max-old-space-size=2048"

echo "🚀 开始轻量级构建（跳过类型检查）..."

echo "📦 构建管理后台..."
cd admin/
pnpm install --frozen-lockfile
# 直接使用vite构建，跳过格式化和类型检查
npx vite build --mode production
cd ..

echo "📦 构建用户界面..."
cd chat/
pnpm install --frozen-lockfile
# 直接使用vite构建
npx vite build --mode production
cd ..

echo "📦 构建后端服务..."
cd service/
pnpm install --frozen-lockfile
# 只运行nest构建，跳过格式化
npx nest build
cd ..

echo "📁 清理和复制文件..."
rm -rf ./AIWebQuickDeploy/dist/* ./AIWebQuickDeploy/public/admin/* ./AIWebQuickDeploy/public/chat/* 2>/dev/null || true
mkdir -p ./AIWebQuickDeploy/dist ./AIWebQuickDeploy/public/admin ./AIWebQuickDeploy/public/chat

# 复制必要文件
cp service/pm2.conf.json ./AIWebQuickDeploy/ 2>/dev/null || echo "pm2.conf.json not found"
cp service/package.json ./AIWebQuickDeploy/ 2>/dev/null || echo "package.json not found"

# 复制环境配置文件（如果存在）
for file in .env.example .env.docker Dockerfile docker-compose.yml .dockerignore; do
    [ -f "service/$file" ] && cp "service/$file" ./AIWebQuickDeploy/ || echo "$file not found, skipping"
done

# 复制构建产物
[ -d "service/dist" ] && cp -a service/dist/* ./AIWebQuickDeploy/dist/ || echo "service/dist not found"
[ -d "admin/dist" ] && cp -r admin/dist/* ./AIWebQuickDeploy/public/admin/ || echo "admin/dist not found"
[ -d "chat/dist" ] && cp -r chat/dist/* ./AIWebQuickDeploy/public/chat/ || echo "chat/dist not found"

echo "✅ 轻量级构建完成！"
echo "📂 部署文件位于: ./AIWebQuickDeploy/"
