
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as u,Q as d,c as s,e as o,f as r,w as i,j as n,h as _,_ as m,g as S,Y as f,k as R}from"./index-BERX8Mlm.js";import{a as E}from"./config-BrbFL53_.js";const c=l({__name:"email",setup(l){const R=a({noVerifyRegister:"",emailLoginStatus:"",MAILER_HOST:"",MAILER_PORT:"",MAILER_USER:"",MAILER_PASS:"",MAILER_SECURE:""}),c=t({MAILER_HOST:[{required:!0,trigger:"blur",message:"请填写SMTP服务器地址"}],MAILER_PORT:[{required:!0,trigger:"blur",message:"请填写SMTP服务器端口"}],MAILER_USER:[{required:!0,trigger:"blur",message:"请填写SMTP用户名称"}],MAILER_PASS:[{required:!0,trigger:"blur",message:"请填写SMTP用户密码"}],MAILER_SECURE:[{required:!0,trigger:"blur",message:"是否使用SSL"}]}),p=t();async function M(){const e=await E.queryConfig({keys:["noVerifyRegister","emailLoginStatus","MAILER_HOST","MAILER_PORT","MAILER_USER","MAILER_PASS","MAILER_SECURE"]});Object.assign(R,e.data)}function g(){var e;null==(e=p.value)||e.validate((async e=>{if(e){try{await E.setConfig({settings:(l=R,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),f.success("变更配置信息成功")}catch(a){}M()}else f.error("请填写完整信息");var l}))}return u((()=>{M()})),(l,a)=>{const t=m,u=n,f=e,E=d("el-switch"),M=d("el-tooltip"),L=d("el-form-item"),A=d("el-col"),I=d("el-row"),x=d("el-input"),P=d("el-checkbox"),T=d("el-form"),b=d("el-card");return o(),s("div",null,[r(f,null,{title:i((()=>a[6]||(a[6]=[S("div",{class:"flex items-center gap-4"},"邮件登录设置",-1)]))),content:i((()=>a[7]||(a[7]=[S("div",{class:"text-sm/6"},[S("div",null,"邮件设置主要用于发送注册时的激活邮件。"),S("div",null,"是否开启邮箱登录：决定用户是否可以通过邮箱进行登录。"),S("div",null,"是否开启邮箱注册：决定用户是否可以通过邮箱进行注册。"),S("div",null,"SMTP服务器配置，用于发送邮件的 SMTP 相关配置，需自行测试。")],-1)]))),default:i((()=>[r(u,{text:"",outline:"",onClick:g},{default:i((()=>[r(t,{name:"i-ri:file-text-line"}),a[8]||(a[8]=_(" 保存设置 "))])),_:1})])),_:1}),r(b,{style:{margin:"20px"}},{default:i((()=>[r(T,{ref_key:"formRef",ref:p,rules:c.value,model:R,"label-width":"190px"},{default:i((()=>[r(I,null,{default:i((()=>[r(A,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[r(L,{label:"开启邮箱注册/登录",prop:"emailLoginStatus"},{default:i((()=>[r(M,{class:"box-item",effect:"dark",content:"如您启用当前邮箱登录、则用户端可以通过邮箱登录！",placement:"right"},{default:i((()=>[r(E,{modelValue:R.emailLoginStatus,"onUpdate:modelValue":a[0]||(a[0]=e=>R.emailLoginStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),r(I,null,{default:i((()=>[r(A,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[r(L,{label:"SMTP服务器地址",prop:"MAILER_HOST"},{default:i((()=>[r(x,{modelValue:R.MAILER_HOST,"onUpdate:modelValue":a[1]||(a[1]=e=>R.MAILER_HOST=e),placeholder:"示例: smtp.example.com",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(I,null,{default:i((()=>[r(A,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[r(L,{label:"SMTP服务器端口",prop:"MAILER_PORT"},{default:i((()=>[r(x,{modelValue:R.MAILER_PORT,"onUpdate:modelValue":a[2]||(a[2]=e=>R.MAILER_PORT=e),placeholder:"示例: 465",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(I,null,{default:i((()=>[r(A,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[r(L,{label:"SMTP用户名称",prop:"MAILER_USER"},{default:i((()=>[r(x,{modelValue:R.MAILER_USER,"onUpdate:modelValue":a[3]||(a[3]=e=>R.MAILER_USER=e),placeholder:"SMTP认证用户名",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(I,null,{default:i((()=>[r(A,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[r(L,{label:"SMTP用户密码",prop:"MAILER_PASS"},{default:i((()=>[r(x,{modelValue:R.MAILER_PASS,"onUpdate:modelValue":a[4]||(a[4]=e=>R.MAILER_PASS=e),placeholder:"SMTP认证密码",type:"password","show-password":"",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(I,null,{default:i((()=>[r(A,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[r(L,{label:"邮箱SSL配置",prop:"MAILER_SECURE"},{default:i((()=>[r(P,{modelValue:R.MAILER_SECURE,"onUpdate:modelValue":a[5]||(a[5]=e=>R.MAILER_SECURE=e),"true-label":"1","false-label":"0"},{default:i((()=>a[9]||(a[9]=[_(" 启用SSL ")]))),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof R&&R(c);export{c as default};
