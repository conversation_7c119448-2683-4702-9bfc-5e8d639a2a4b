
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,aS as a,aT as t,al as l,r as s,P as o,a2 as r,e as n,w as i,f as p,i as d,aJ as u,g as c,aK as f,R as y,a6 as m,aL as x,c as v,T as g,aU as w,h as b,t as _,_ as h,aN as k,aj as T,aP as j}from"./index-BERX8Mlm.js";const B={class:"fixed inset-0 overflow-y-auto"},C={class:"min-h-full flex items-end justify-center p-4 text-center lg-items-center"},F={flex:"~ items-center justify-between","px-4":"","py-3":"","border-b":"~ solid stone/15","text-6":""},V={key:0,flex:"~ items-center justify-end","px-4":"","py-3":"","border-t":"~ solid stone/15"},P=e({__name:"HDialog",props:a({appear:{type:Boolean,default:!1},title:{},preventClose:{type:Boolean,default:!1},overlay:{type:Boolean,default:!1}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:a(["close"],["update:modelValue"]),setup(e,{emit:a}){const P=a,$=t(e,"modelValue"),z=l(),D=s({enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0"}),H=o((()=>({enter:"ease-out duration-300",enterFrom:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95",enterTo:"opacity-100 translate-y-0 lg-scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-y-0 lg-scale-100",leaveTo:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95"})));function J(){$.value=!1,P("close")}return(e,a)=>{const t=h;return n(),r(d(j),{as:"template",appear:e.appear,show:$.value},{default:i((()=>[p(d(u),{class:"fixed inset-0 z-2000 flex",onClose:a[0]||(a[0]=a=>!e.preventClose&&J())},{default:i((()=>[p(d(f),y({as:"template",appear:e.appear},d(D)),{default:i((()=>[c("div",{class:m(["fixed inset-0 bg-stone-2/75 transition-opacity dark-bg-stone-8/75",{"backdrop-blur-sm":e.overlay}])},null,2)])),_:1},16,["appear"]),c("div",B,[c("div",C,[p(d(f),y({as:"template",appear:e.appear},d(H)),{default:i((()=>[p(d(x),{class:"relative w-full flex flex-col overflow-hidden rounded-xl bg-white text-left shadow-xl lg-my-8 lg-max-w-lg dark-bg-stone-8"},{default:i((()=>[c("div",F,[p(d(w),{"m-0":"","text-lg":"","text-dark":"","dark-text-white":""},{default:i((()=>[b(_(e.title),1)])),_:1}),p(t,{name:"i-carbon:close","cursor-pointer":"",onClick:J})]),p(d(k),{"m-0":"","overflow-y-auto":"","p-4":""},{default:i((()=>[T(e.$slots,"default")])),_:3}),d(z).footer?(n(),v("div",V,[T(e.$slots,"footer")])):g("",!0)])),_:3})])),_:3},16,["appear"])])])])),_:3})])),_:3},8,["appear","show"])}}});export{P as _};
