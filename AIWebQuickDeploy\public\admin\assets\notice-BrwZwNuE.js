
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as t,a as l,$ as a,P as o,r as n,b as i,Q as s,c,e as r,f as u,w as d,j as f,h as m,_ as p,g,i as _,Y as v,k as y}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";import{u as h}from"./upload-DwmqW_vL.js";import{E as w}from"./style-ClrjWzkr.js";const O=t({__name:"notice",setup(t){const y=l(),O=a({isAutoOpenNotice:"",noticeInfo:"",noticeTitle:""});o((()=>y.settings.app.colorScheme));const b=n({noticeTitle:[{required:!0,trigger:"blur",message:"请填写公告标题"}],noticeInfo:[{required:!0,trigger:"blur",message:"请填写公告具体信息"}]}),V=n();async function I(){const e=await x.queryConfig({keys:["noticeInfo","noticeTitle","isAutoOpenNotice"]}),{noticeInfo:t,noticeTitle:l,isAutoOpenNotice:a}=e.data;t&&Object.assign(O,{noticeInfo:t,noticeTitle:l,isAutoOpenNotice:a})}function T(){var e;null==(e=V.value)||e.validate((async e=>{if(e){try{await x.setConfig({settings:(t=O,Object.keys(t).map((e=>({configKey:e,configVal:t[e]}))))}),v.success("变更配置信息成功")}catch(l){}I()}else v.error("请填写完整信息");var t}))}function j(e){}async function A(e,t){t((await Promise.all(Array.from(e).map((e=>new Promise((async(t,l)=>{var a;const o=new FormData;o.append("file",e);try{const e=await h.uploadFile(o,"system/others");(null==(a=null==e?void 0:e.data)?void 0:a.data)||v.error("图片上传失败、请检查您的配置信息！"),t(e.data.data)}catch(n){v.error(n||"图片上传失败、请检查您的配置信息！"),l(n)}})))))).map((e=>e))),v({message:"图片上传成功！",type:"success"})}return i((()=>{I()})),(t,l)=>{const a=p,o=f,n=e,i=s("el-input"),v=s("el-form-item"),y=s("el-col"),x=s("el-switch"),h=s("el-tooltip"),I=s("el-row"),N=s("el-form"),k=s("el-card");return r(),c("div",null,[u(n,null,{title:d((()=>l[3]||(l[3]=[g("div",{class:"flex items-center gap-4"},"公告设置说明",-1)]))),content:d((()=>l[4]||(l[4]=[g("div",{class:"text-sm/6"},[g("div",null," 公告设置用于配置用户端显示的公告页面。支持使用Markdown语法或HTML标签来创建内容，为灵活的内容格式提供便利。 ")],-1)]))),default:d((()=>[u(o,{outline:"",onClick:T},{default:d((()=>[u(a,{name:"i-ri:file-text-line"}),l[5]||(l[5]=m(" 保存设置 "))])),_:1})])),_:1}),u(k,{style:{margin:"20px"}},{default:d((()=>[u(N,{ref_key:"formRef",ref:V,rules:b.value,model:O,"label-width":"120px"},{default:d((()=>[u(I,null,{default:d((()=>[u(y,{xs:24,md:20,lg:15,xl:10},{default:d((()=>[u(v,{label:"公告标题",prop:"noticeTitle"},{default:d((()=>[u(i,{modelValue:O.noticeTitle,"onUpdate:modelValue":l[0]||(l[0]=e=>O.noticeTitle=e),rows:1,placeholder:"公告标题",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),u(y,{xs:24,md:20,lg:15,xl:10},{default:d((()=>[u(v,{label:"自动打开公告",prop:"isAutoOpenNotice"},{default:d((()=>[u(h,{content:"设为自动打开则网站初始化会打开、用户仍可以选择24小时不再查看、选择关闭则不会主动打开！",placement:"top","show-after":500},{default:d((()=>[u(x,{modelValue:O.isAutoOpenNotice,"onUpdate:modelValue":l[1]||(l[1]=e=>O.isAutoOpenNotice=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1}),u(y,{xs:24,md:20,lg:15,xl:12})])),_:1})])),_:1}),u(I,null,{default:d((()=>[u(y,{span:24},{default:d((()=>[u(v,{label:"公告信息",prop:"noticeInfo"},{default:d((()=>[u(_(w),{modelValue:O.noticeInfo,"onUpdate:modelValue":l[2]||(l[2]=e=>O.noticeInfo=e),style:{"min-height":"80vh"},onOnChange:j,onOnUploadImg:A},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof y&&y(O);export{O as default};
