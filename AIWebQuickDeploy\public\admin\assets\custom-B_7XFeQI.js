
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,a as t,r as s,$ as o,b as u,Q as n,a1 as d,c as i,e as r,f as c,a5 as f,w as m,j as p,h as v,_,g as w,i as y,ak as g,a3 as k,a4 as V,a2 as h,V as x,W as b,t as C,ab as W,Y as j,a9 as B,k as U}from"./index-BERX8Mlm.js";import{A as z}from"./badWords-FUVqvqZb.js";const K={class:"dialog-footer"},R=l({__name:"custom",setup(l){t();const U=s(),R=s(0),q=s(!1),A=s(!1),F=s(""),I=s(!1),N=o({word:"",status:"",page:1,size:500}),Q=s();async function Y(){try{q.value=!0;const e=await z.queryBadWords(N),{rows:a,count:l}=e.data;q.value=!1,R.value=l,Q.value=a}catch(e){q.value=!1}}u((()=>{Y()}));const $=s(""),D=s(!1),E=s();function G(){D.value=!0,B((()=>{E.value.input.focus()}))}async function H(){$.value&&(await z.addBadWords({word:$.value}),j.success("添加敏感词成功"),N.status="",await Y()),D.value=!1,$.value=""}async function J(){if(F.value.trim())try{I.value=!0;const e=F.value.split(/[\s\n]+/).filter((e=>e.trim()));if(e.length>1e3)return j.warning("单次最多添加1000个敏感词"),void(I.value=!1);if(0===e.length)return j.warning("请输入有效的敏感词"),void(I.value=!1);for(const a of e)a.trim()&&await z.addBadWords({word:a.trim()});j.success("批量添加成功"),A.value=!1,F.value="",N.status="",await Y()}catch(e){j.error("批量添加失败")}finally{I.value=!1}else j.warning("请输入敏感词")}return(l,t)=>{const s=_,o=p,u=a,B=n("el-form-item"),R=n("el-button"),L=n("el-form"),M=e,O=n("el-tag"),P=n("el-dialog"),S=d("loading");return r(),i("div",null,[c(u,null,{title:m((()=>t[7]||(t[7]=[w("div",{class:"flex items-center gap-4"},"自定义敏感词",-1)]))),default:m((()=>[c(o,{outline:"",onClick:t[0]||(t[0]=e=>A.value=!0)},{default:m((()=>[c(s,{name:"i-ri:file-text-line"}),t[8]||(t[8]=v(" 批量添加 "))])),_:1})])),_:1}),c(M,null,{default:m((()=>[c(L,{ref_key:"formRef",ref:U,inline:!0,model:N},{default:m((()=>[c(B,{label:"敏感词",prop:"word"},{default:m((()=>[c(y(g),{modelValue:N.word,"onUpdate:modelValue":t[1]||(t[1]=e=>N.word=e),placeholder:"敏感词[模糊搜索]",onKeydown:k(V(Y,["prevent"]),["enter"])},null,8,["modelValue","onKeydown"])])),_:1}),c(B,null,{default:m((()=>[c(R,{type:"primary",onClick:Y},{default:m((()=>t[9]||(t[9]=[v(" 查询 ")]))),_:1}),c(R,{onClick:t[2]||(t[2]=e=>{return null==(a=y(U))||a.resetFields(),void Y();var a})},{default:m((()=>t[10]||(t[10]=[v(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),f((r(),h(M,{style:{width:"100%"}},{default:m((()=>[(r(!0),i(x,null,b(y(Q),(e=>(r(),h(O,{key:e.id,type:"warning",class:"mb-3 mr-3",closable:"",hit:"","disable-transitions":!0,onClose:a=>async function(e){await z.delBadWords({id:e}),j.success("删除敏感词成功"),await Y()}(e.id)},{default:m((()=>[v(C(e.word),1)])),_:2},1032,["onClose"])))),128)),y(D)?(r(),h(y(g),{key:0,ref_key:"InputRef",ref:E,modelValue:y($),"onUpdate:modelValue":t[3]||(t[3]=e=>W($)?$.value=e:null),class:"ml-1",style:{width:"80px"},size:"small",onKeyup:k(H,["enter"]),onBlur:H},null,8,["modelValue"])):(r(),h(R,{key:1,class:"ml-1",size:"small",onClick:G},{default:m((()=>t[11]||(t[11]=[v(" + New Word ")]))),_:1}))])),_:1})),[[S,y(q)]]),c(P,{modelValue:y(A),"onUpdate:modelValue":t[6]||(t[6]=e=>W(A)?A.value=e:null),title:"批量添加敏感词",width:"500px","close-on-click-modal":!1},{footer:m((()=>[w("span",K,[c(R,{onClick:t[5]||(t[5]=e=>A.value=!1)},{default:m((()=>t[12]||(t[12]=[v("取消")]))),_:1}),c(R,{type:"primary",loading:y(I),onClick:J},{default:m((()=>t[13]||(t[13]=[v(" 确认添加 ")]))),_:1},8,["loading"])])])),default:m((()=>[c(L,null,{default:m((()=>[c(B,null,{default:m((()=>[c(y(g),{modelValue:y(F),"onUpdate:modelValue":t[4]||(t[4]=e=>W(F)?F.value=e:null),type:"textarea",rows:10,placeholder:"请输入敏感词，多个敏感词可用空格或换行分隔"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}});"function"==typeof U&&U(R);export{R as default};
