
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,b as a,an as s,r as t,a2 as n,e as o,f as r,aB as l,aQ as i,aR as c,w as d,c as u,T as m,i as g,_ as v,av as p}from"./index-BERX8Mlm.js";const y=e({name:"BackTop",__name:"index",setup(e){const y={enterActiveClass:"ease-out duration-300",enterFromClass:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95",enterToClass:"opacity-100 translate-y-0 lg-scale-100",leaveActiveClass:"ease-in duration-200",leaveFromClass:"opacity-100 translate-y-0 lg-scale-100",leaveToClass:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95"};a((()=>{window.addEventListener("scroll",k),k()})),s((()=>{window.removeEventListener("scroll",k)}));const f=t(null);function k(){f.value=document.documentElement.scrollTop}function b(){document.documentElement.scrollTo({top:0,behavior:"smooth"})}return(e,a)=>{const s=v;return o(),n(p,{to:"body"},[r(l,i(c(y)),{default:d((()=>[g(f)&&g(f)>=200?(o(),u("div",{key:0,class:"fixed bottom-4 right-4 z-1000 h-12 w-12 flex cursor-pointer items-center justify-center rounded-full bg-white shadow-lg ring-1 ring-stone-3 ring-inset dark-bg-dark hover-bg-stone-1 dark-ring-stone-7 dark-hover-bg-dark/50",onClick:b},[r(s,{name:"i-icon-park-outline:to-top-one",size:24})])):m("",!0)])),_:1},16)])}}});export{y as _};
