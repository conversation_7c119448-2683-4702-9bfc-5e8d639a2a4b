
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{ac as l,d as t,$ as s,r as u,P as o,b as r,Q as n,a1 as i,c as p,e as d,f as m,w as c,j as f,h as _,_ as g,g as v,a3 as y,a4 as b,V as w,W as h,i as V,a2 as x,a5 as R,t as k,ab as C,Y as z,a9 as A,k as I}from"./index-BERX8Mlm.js";import{c as U,Q as j}from"./index-gPQwgooA.js";const E=e=>l.get("autoReply/query",{params:e}),q=e=>l.post("autoReply/del",e),F=e=>l.post("autoReply/add",e),K=e=>l.post("autoReply/update",e),P={class:"dialog-footer"},Q=t({__name:"autpReply",setup(l){const t=s({prompt:"",status:void 0,page:1,size:10}),I=u(!1),Q=u(!1),S=u(0),O=u(0),W=u(),Y=u(),$=s({status:1,prompt:"",answer:"",isAIReplyEnabled:1}),B=s({status:[{required:!0,message:"请选择开启状态",trigger:"change"}],prompt:[{required:!0,message:"请填写预设问题",trigger:"blur"}],answer:[{required:!0,message:"请填写回复答案",trigger:"blur"}],isAIReplyEnabled:[{required:!0,message:"请选择是否开启AI回复",trigger:"change"}]}),D=u([]);async function G(){try{Q.value=!0;const e=await E(t),{rows:a,count:l}=e.data;Q.value=!1,O.value=l,D.value=a}catch(e){Q.value=!1}}const H=o((()=>0!==S.value));return r((()=>{G()})),(l,s)=>{const u=g,o=f,r=a,E=n("el-input"),J=n("el-form-item"),L=n("el-option"),M=n("el-select"),N=n("el-button"),T=n("el-form"),X=e,Z=n("el-table-column"),ee=n("el-tag"),ae=n("el-popconfirm"),le=n("el-table"),te=n("el-pagination"),se=n("el-row"),ue=n("el-switch"),oe=n("el-dialog"),re=i("loading");return d(),p("div",null,[m(r,null,{title:c((()=>s[14]||(s[14]=[v("div",{class:"flex items-center gap-4"},"自定义知识库说明",-1)]))),content:c((()=>s[15]||(s[15]=[v("div",{class:"text-sm/6"},[v("div",null," 自定义知识库会根据用户提问中的关键词匹配知识库的内容作为预设。同一知识库支持多个关键词，多个关键词用空格隔开。 "),v("div",null,"默认开启 AI 回复，关闭后将直接回复预设答案。")],-1)]))),default:c((()=>[m(o,{outline:"",onClick:s[0]||(s[0]=e=>I.value=!0)},{default:c((()=>[m(u,{name:"i-ri:file-text-line"}),s[16]||(s[16]=_(" 添加知识库 "))])),_:1})])),_:1}),m(X,null,{default:c((()=>[m(T,{ref_key:"formRef",ref:W,inline:!0,model:t},{default:c((()=>[m(J,{label:"关键词",prop:"prompt"},{default:c((()=>[m(E,{modelValue:t.prompt,"onUpdate:modelValue":s[1]||(s[1]=e=>t.prompt=e),placeholder:"关键词[模糊搜索]",onKeydown:y(b(G,["prevent"]),["enter"])},null,8,["modelValue","onKeydown"])])),_:1}),m(J,{label:"启用状态",prop:"status"},{default:c((()=>[m(M,{modelValue:t.status,"onUpdate:modelValue":s[2]||(s[2]=e=>t.status=e),placeholder:"请选择启用状态",clearable:"",style:{width:"160px"}},{default:c((()=>[(d(!0),p(w,null,h(V(U),(e=>(d(),x(L,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(J,null,{default:c((()=>[m(N,{type:"primary",onClick:G},{default:c((()=>s[17]||(s[17]=[_(" 查询 ")]))),_:1}),m(N,{onClick:s[3]||(s[3]=e=>{return null==(a=V(W))||a.resetFields(),void G();var a})},{default:c((()=>s[18]||(s[18]=[_(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),m(X,{style:{width:"100%"}},{default:c((()=>[R((d(),x(le,{border:"",data:V(D),style:{width:"100%"},size:"large"},{default:c((()=>[m(Z,{prop:"prompt",label:"关键词"}),m(Z,{prop:"answer",label:"知识库"}),m(Z,{prop:"status",label:"状态",width:"120"},{default:c((e=>[m(ee,{type:1===e.row.status?"success":"danger"},{default:c((()=>[_(k(V(j)[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),m(Z,{fixed:"right",label:"操作",width:"200"},{default:c((e=>[m(N,{link:"",type:"primary",size:"small",onClick:a=>function(e){S.value=e.id;const{status:a,prompt:l,answer:t,isAIReplyEnabled:s}=e;I.value=!0,A((()=>{Object.assign($,{status:a,prompt:l,answer:t,isAIReplyEnabled:s})}))}(e.row)},{default:c((()=>s[19]||(s[19]=[_(" 编辑 ")]))),_:2},1032,["onClick"]),m(ae,{"confirm-button-text":"确认删除","cancel-button-text":"放弃",type:"danger",width:"230px","icon-color":"red",title:"确定删除次条知识库？",onConfirm:a=>async function(e){await q({id:e}),z.success("删除自定义知识库成功"),await G()}(e.row.id)},{reference:c((()=>[m(N,{link:"",type:"danger",size:"small"},{default:c((()=>s[20]||(s[20]=[_(" 删除 ")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[re,V(Q)]]),m(se,{class:"mt-5 flex justify-end"},{default:c((()=>[m(te,{"current-page":t.page,"onUpdate:currentPage":s[4]||(s[4]=e=>t.page=e),"page-size":t.size,"onUpdate:pageSize":s[5]||(s[5]=e=>t.size=e),class:"mr-5","page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:V(O),onSizeChange:G,onCurrentChange:G},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),m(oe,{modelValue:V(I),"onUpdate:modelValue":s[12]||(s[12]=e=>C(I)?I.value=e:null),title:(V(H)?"编辑":"添加")+"知识库",width:"600px",onClose:s[13]||(s[13]=e=>{return null==(a=V(Y))||a.resetFields(),void(S.value=0);var a})},{footer:c((()=>[v("span",P,[m(N,{onClick:s[10]||(s[10]=e=>I.value=!1)},{default:c((()=>s[21]||(s[21]=[_("取消")]))),_:1}),m(N,{type:"primary",onClick:s[11]||(s[11]=e=>async function(e){null==e||e.validate((async e=>{e?(0===S.value?(await F($),z.success("添加自定义知识库成功")):(await K({id:S.value,...$}),z.success("更新自定义知识库成功")),I.value=!1,G()):z.warning("请按规则填写所有信息！")}))}(V(Y)))},{default:c((()=>[_(k((V(H)?"更新":"新增")+"知识库 "),1)])),_:1})])])),default:c((()=>[m(T,{ref_key:"formAutoReplyRef",ref:Y,"label-position":"right","label-width":"80px",model:$,rules:B},{default:c((()=>[m(J,{label:"开启状态",prop:"status"},{default:c((()=>[m(ue,{modelValue:$.status,"onUpdate:modelValue":s[6]||(s[6]=e=>$.status=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1}),m(J,{label:"AI回复",prop:"isAIReplyEnabled"},{default:c((()=>[m(ue,{modelValue:$.isAIReplyEnabled,"onUpdate:modelValue":s[7]||(s[7]=e=>$.isAIReplyEnabled=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1}),m(J,{label:"关键词",prop:"prompt"},{default:c((()=>[m(E,{modelValue:$.prompt,"onUpdate:modelValue":s[8]||(s[8]=e=>$.prompt=e),type:"textarea",rows:5,placeholder:"请填写关键词，多个关键词用空格隔开"},null,8,["modelValue"])])),_:1}),m(J,{label:"知识库",prop:"answer"},{default:c((()=>[m(E,{modelValue:$.answer,"onUpdate:modelValue":s[9]||(s[9]=e=>$.answer=e),type:"textarea",rows:5,placeholder:"请填写匹配的知识库内容"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}});"function"==typeof I&&I(Q);export{Q as default};
