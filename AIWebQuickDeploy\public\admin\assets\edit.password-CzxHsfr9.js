
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,r as a,b as s,ai as o,c as l,e as t,a6 as r,i as d,aj as n,Z as u,af as p,u as i,ag as c,Q as m,f as w,w as f,h as _,ae as g,Y as h,k as v}from"./index-BERX8Mlm.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as y}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{u as x}from"./useMainPage-Dbp8uSF1.js";const k=u(e({name:"FixedActionBar",__name:"index",setup(e){const u=a(!1);function p(){const e=document.documentElement.scrollTop||document.body.scrollTop,a=document.documentElement.clientHeight||document.body.clientHeight,s=document.documentElement.scrollHeight||document.body.scrollHeight;u.value=Math.ceil(e+a)>=s}return s((()=>{p(),window.addEventListener("scroll",p)})),o((()=>{window.removeEventListener("scroll",p)})),(e,a)=>(t(),l("div",{class:r(["fixed-action-bar bottom-0 z-4 bg-[var(--g-container-bg)] p-5 text-center transition",{shadow:!d(u)}]),"data-fixed-calc-width":""},[n(e.$slots,"default",{},void 0,!0)],2))}}),[["__scopeId","data-v-eb52aec7"]]),V=e({__name:"edit.password",setup(e){const s=p(),o=i(),r=c(),n=a(),u=a({password:"",newpassword:"",checkpassword:""}),v=x(),V=a({password:[{required:!0,message:"请输入原密码",trigger:"blur"}],newpassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,max:18,trigger:"blur",message:"密码长度为6到18位"}],checkpassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:(e,a,s)=>{a!==u.value.newpassword?s(new Error("两次密码不一致！")):s()}}]});function E(){n.value&&n.value.validate((e=>{if(e){const{password:e,newpassword:a}=u.value;g.passwordEdit({oldPassword:e,password:a}).then((()=>{h({type:"success",message:"修改密码成功，请重新登录"}),r.logout().then((()=>{o.push({name:"login",query:{redirect:s.fullPath}})}))}))}}))}function j(){v.reload()}return(e,a)=>{const s=y,o=m("el-input"),r=m("el-form-item"),p=m("el-form"),i=m("el-col"),c=m("el-row"),g=b,h=m("el-button"),v=k;return t(),l("div",null,[w(s,{title:"修改密码",content:"定期修改密码可以提高帐号安全性噢~"}),w(g,null,{default:f((()=>[w(c,null,{default:f((()=>[w(i,{md:24,lg:12},{default:f((()=>[w(p,{ref_key:"formRef",ref:n,model:d(u),rules:d(V),"label-width":"120px"},{default:f((()=>[w(r,{label:"原密码",prop:"password"},{default:f((()=>[w(o,{modelValue:d(u).password,"onUpdate:modelValue":a[0]||(a[0]=e=>d(u).password=e),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])])),_:1}),w(r,{label:"新密码",prop:"newpassword"},{default:f((()=>[w(o,{modelValue:d(u).newpassword,"onUpdate:modelValue":a[1]||(a[1]=e=>d(u).newpassword=e),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])])),_:1}),w(r,{label:"确认新密码",prop:"checkpassword"},{default:f((()=>[w(o,{modelValue:d(u).checkpassword,"onUpdate:modelValue":a[2]||(a[2]=e=>d(u).checkpassword=e),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1})])),_:1})])),_:1}),w(v,null,{default:f((()=>[w(h,{type:"primary",size:"large",onClick:E},{default:f((()=>a[3]||(a[3]=[_(" 提交 ")]))),_:1}),w(h,{type:"default",size:"large",onClick:j},{default:f((()=>a[4]||(a[4]=[_(" 取消 ")]))),_:1})])),_:1})])}}});"function"==typeof v&&v(V);export{V as default};
