
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,$ as t,r as l,b as o,Q as r,c as n,e as i,f as s,w as c,j as u,h as d,_ as f,g as m,a2 as p,T as g,i as h,X as y,a7 as b,Y as v,aa as _,k as x}from"./index-BERX8Mlm.js";import{a as w}from"./config-BrbFL53_.js";import{u as A}from"./upload-DwmqW_vL.js";const P=["src"],L=["src"],V=["src"],U=["src"],k=a({__name:"baseConfiguration",setup(a){const x=t({siteName:"",robotAvatar:"",userDefaultAvatar:"",filingNumber:"",companyName:"",buyCramiAddress:"",siteUrl:"",isShowAppCatIcon:"",clientFaviconPath:"",clientLogoPath:""}),k=l({siteName:[{required:!0,trigger:"blur",message:"请填写网站名称"}]}),j=l();async function F(){const e=await w.queryConfig({keys:["siteName","robotAvatar","userDefaultAvatar","filingNumber","companyName","isShowAppCatIcon","clientLogoPath","clientFaviconPath","siteUrl"]});Object.assign(x,e.data)}function N(){var e;null==(e=j.value)||e.validate((async e=>{if(e){try{await w.setConfig({settings:(a=x,Object.keys(a).map((e=>({configKey:e,configVal:a[e]}))))}),v.success("变更配置信息成功")}catch(t){}F()}else v.error("请填写完整信息");var a}))}async function D(e){const a=await _.get(e,{responseType:"blob"});let t="downloaded_file";const l=a.headers["content-disposition"];if(l){const e=/filename="([^"]+)"/.exec(l);null!=e&&e[1]&&(t=e[1])}else t=function(e){const a=new URL(e),t=a.pathname;return t.substring(t.lastIndexOf("/")+1)}(e);return new File([a.data],t,{type:a.data.type})}function C(e,a,t,l){const o=new FormData;o.append("file",e),A.uploadFile(o,"system/others").then((e=>{a({data:e.data}),l&&v.success(l)})).catch((e=>{v.error("文件上传失败"),t&&a===O?x.clientLogoPath=t:t&&a===E?x.clientFaviconPath=t:t&&a===K?x.robotAvatar=t:t&&a===T&&(x.userDefaultAvatar=t)}))}const I=e=>{const{file:a,onSuccess:t,onError:l}=e,o=new FormData;return o.append("file",a),A.uploadFile(o,"system/others").then((e=>(t&&(t(e),v.success("上传成功")),e))).catch((e=>(l&&l(e),v.error("文件上传失败"),Promise.reject(e))))},O=(e,a)=>{e&&e.data?x.clientLogoPath=e.data:v.error("上传成功但未获取到URL")};async function R(){if(x.clientLogoPath)try{v.info("正在重新上传Logo...");const e=x.clientLogoPath;C(await D(x.clientLogoPath),O,e,"重新上传Logo成功")}catch(e){v.error("重新上传Logo失败，请检查链接是否有效")}}async function q(){if(x.clientFaviconPath)try{v.info("正在重新上传网站图标...");const e=x.clientFaviconPath;C(await D(x.clientFaviconPath),E,e,"重新上传网站图标成功")}catch(e){v.error("重新上传网站图标失败，请检查链接是否有效")}}async function G(){if(x.robotAvatar)try{v.info("正在重新上传AI头像...");const e=x.robotAvatar;C(await D(x.robotAvatar),K,e,"重新上传AI头像成功")}catch(e){v.error("重新上传AI头像失败，请检查链接是否有效")}}async function S(){if(x.userDefaultAvatar)try{v.info("正在重新上传用户默认头像...");const e=x.userDefaultAvatar;C(await D(x.userDefaultAvatar),T,e,"重新上传用户默认头像成功")}catch(e){v.error("重新上传用户默认头像失败，请检查链接是否有效")}}const E=(e,a)=>{e&&e.data?x.clientFaviconPath=e.data:v.error("上传成功但未获取到URL")},K=(e,a)=>{e&&e.data?x.robotAvatar=e.data:v.error("上传成功但未获取到URL")},T=(e,a)=>{e&&e.data?x.userDefaultAvatar=e.data:v.error("上传成功但未获取到URL")},z=e=>{const a=e.name.toLowerCase(),t=a.substring(a.lastIndexOf("."));return["image/png","image/jpeg","image/gif","image/webp","image/x-icon","image/vnd.microsoft.icon"].includes(e.type)||[".png",".jpg",".jpeg",".gif",".webp",".ico"].includes(t)?!(e.size/1024>3e3)||(v.error("当前限制文件最大不超过 3000KB!"),!1):(v.error("当前系统仅支持 PNG、JPEG、GIF、WebP 和 ICO 格式的图片!"),!1)};return o((()=>{F()})),(a,t)=>{const l=f,o=u,v=e,_=r("el-input"),w=r("el-form-item"),A=r("el-col"),F=r("el-row"),D=r("el-icon"),C=r("el-upload"),B=r("el-form"),J=r("el-card");return i(),n("div",null,[s(v,null,{title:c((()=>t[8]||(t[8]=[m("div",{class:"flex items-center gap-4"},"网站基础配置",-1)]))),content:c((()=>t[9]||(t[9]=[m("div",{class:"text-sm/6"},[m("div",null," 网站基础配置支持即时更新网站的主要视觉与功能元素。配置内容包括网站名称、备案号、版权信息、LOGO与ICO、默认AI头像与用户头像，以及首页设置等。 "),m("div",null,"请认真填写各项配置，确保提供给用户的信息准确无误。")],-1)]))),default:c((()=>[s(o,{outline:"",onClick:N},{default:c((()=>[s(l,{name:"i-ri:file-text-line"}),t[10]||(t[10]=d(" 保存设置 "))])),_:1})])),_:1}),s(J,{style:{margin:"20px"}},{default:c((()=>[s(B,{ref_key:"formRef",ref:j,rules:k.value,model:x,"label-width":"150px"},{default:c((()=>[s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"网站名称",prop:"siteName"},{default:c((()=>[s(_,{modelValue:x.siteName,"onUpdate:modelValue":t[0]||(t[0]=e=>x.siteName=e),placeholder:"网站名称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"网站地址",prop:"siteUrl"},{default:c((()=>[s(_,{modelValue:x.siteUrl,"onUpdate:modelValue":t[1]||(t[1]=e=>x.siteUrl=e),placeholder:"网站地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"公司/组织名称",prop:"companyName"},{default:c((()=>[s(_,{modelValue:x.companyName,"onUpdate:modelValue":t[2]||(t[2]=e=>x.companyName=e),placeholder:"填入具体的公司或组织名称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"网站备案号",prop:"filingNumber"},{default:c((()=>[s(_,{modelValue:x.filingNumber,"onUpdate:modelValue":t[3]||(t[3]=e=>x.filingNumber=e),placeholder:"填写网站备案信息的备案号",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"用户端LOGO",prop:"clientLogoPath"},{default:c((()=>[s(_,{modelValue:x.clientLogoPath,"onUpdate:modelValue":t[4]||(t[4]=e=>x.clientLogoPath=e),placeholder:"请填写或上传网站 LOGO 图片 URL",clearable:""},{append:c((()=>[s(C,{class:"avatar-uploader","show-file-list":!1,"http-request":I,"on-success":O,"before-upload":z,style:{display:"flex","align-items":"center","justify-content":"center"}},{default:c((()=>[x.clientLogoPath?(i(),n("img",{key:0,src:x.clientLogoPath,style:{"max-width":"1.5rem","max-height":"1.5rem",margin:"5px 0","object-fit":"contain"}},null,8,P)):(i(),p(D,{key:1,style:{width:"1rem"}},{default:c((()=>[s(h(y))])),_:1}))])),_:1}),x.clientLogoPath?(i(),p(D,{key:0,onClick:R,style:{"margin-left":"35px",width:"1rem"}},{default:c((()=>[s(h(b))])),_:1})):g("",!0)])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"网站 ico",prop:"clientFaviconPath"},{default:c((()=>[s(_,{modelValue:x.clientFaviconPath,"onUpdate:modelValue":t[5]||(t[5]=e=>x.clientFaviconPath=e),placeholder:"请填写或上传网站 ico URL",clearable:""},{append:c((()=>[s(C,{class:"avatar-uploader","show-file-list":!1,"http-request":I,"on-success":E,"before-upload":z,style:{display:"flex","align-items":"center","justify-content":"center"}},{default:c((()=>[x.clientFaviconPath?(i(),n("img",{key:0,src:x.clientFaviconPath,style:{"max-width":"1.5rem","max-height":"1.5rem",margin:"5px 0","object-fit":"contain"}},null,8,L)):(i(),p(D,{key:1,style:{width:"1rem"}},{default:c((()=>[s(h(y))])),_:1}))])),_:1}),x.clientFaviconPath?(i(),p(D,{key:0,onClick:q,style:{"margin-left":"35px",width:"1rem"}},{default:c((()=>[s(h(b))])),_:1})):g("",!0)])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"AI头像",prop:"robotAvatar"},{default:c((()=>[s(_,{modelValue:x.robotAvatar,"onUpdate:modelValue":t[6]||(t[6]=e=>x.robotAvatar=e),placeholder:"请填写或上传网站 AI 头像 URL、为空将根据模型自动显示",clearable:""},{append:c((()=>[s(C,{class:"avatar-uploader","show-file-list":!1,"http-request":I,"on-success":K,"before-upload":z,style:{display:"flex","align-items":"center","justify-content":"center"}},{default:c((()=>[x.robotAvatar?(i(),n("img",{key:0,src:x.robotAvatar,style:{"max-width":"1.5rem","max-height":"1.5rem",margin:"5px 0","object-fit":"contain"}},null,8,V)):(i(),p(D,{key:1,style:{width:"1rem"}},{default:c((()=>[s(h(y))])),_:1}))])),_:1}),x.robotAvatar?(i(),p(D,{key:0,onClick:G,style:{"margin-left":"35px",width:"1rem"}},{default:c((()=>[s(h(b))])),_:1})):g("",!0)])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(F,null,{default:c((()=>[s(A,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[s(w,{label:"用户默认头像",prop:"userDefaultAvatar"},{default:c((()=>[s(_,{modelValue:x.userDefaultAvatar,"onUpdate:modelValue":t[7]||(t[7]=e=>x.userDefaultAvatar=e),placeholder:"请填写或上传网站新用户默认的头像 URL",clearable:""},{append:c((()=>[s(C,{class:"avatar-uploader","show-file-list":!1,"http-request":I,"on-success":T,"before-upload":z,style:{display:"flex","align-items":"center","justify-content":"center"}},{default:c((()=>[x.userDefaultAvatar?(i(),n("img",{key:0,src:x.userDefaultAvatar,style:{"max-width":"1.5rem","max-height":"1.5rem",margin:"5px 0","object-fit":"contain"}},null,8,U)):(i(),p(D,{key:1,style:{width:"1rem"}},{default:c((()=>[s(h(y))])),_:1}))])),_:1}),x.userDefaultAvatar?(i(),p(D,{key:0,onClick:S,style:{"margin-left":"35px",width:"1rem"}},{default:c((()=>[s(h(b))])),_:1})):g("",!0)])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof x&&x(k);export{k as default};
