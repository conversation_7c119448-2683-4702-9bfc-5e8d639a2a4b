
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,at as t,ap as n,P as s,Q as i,c as u,e as l,V as a,f as o,a2 as r,T as p,i as d,av as g,aB as m,R as v,ba as y,w as f,aO as h,a6 as c,W as x,aZ as M,a9 as H}from"./index-BERX8Mlm.js";import{_ as b,r as w}from"./item.vue_vue_type_script_setup_true_lang-D5h42eqV.js";const C=e({name:"SubMenu",__name:"sub",props:{uniqueKey:{},menu:{},level:{default:0}},setup(e){const C=e,k=C.menu.path??JSON.stringify(C.menu),q=t(),_=t(),P=n(w),K=s((()=>P.openedMenus.includes(C.uniqueKey.at(-1)))),R=s((()=>P.isMenuPopup?{enter(e){e.offsetHeight>window.innerHeight&&(e.style.height=`${window.innerHeight}px`)},afterEnter:()=>{},beforeLeave:e=>{e.style.overflow="hidden",e.style.maxHeight=`${e.offsetHeight}px`},leave:e=>{e.style.maxHeight="0"},afterLeave(e){e.style.overflow="",e.style.maxHeight=""}}:{enter(e){const t=e.offsetHeight;e.style.maxHeight="0",e.style.overflow="hidden",e.offsetHeight,e.style.maxHeight=`${t}px`},afterEnter(e){e.style.overflow="",e.style.maxHeight=""},beforeLeave(e){e.style.overflow="hidden",e.style.maxHeight=`${e.offsetHeight}px`},leave(e){e.style.maxHeight="0"},afterLeave(e){e.style.overflow="",e.style.maxHeight=""}})),E=s((()=>P.isMenuPopup?{enterActiveClass:"ease-in-out duration-300",enterFromClass:"opacity-0 translate-x-4",enterToClass:"opacity-100",leaveActiveClass:"ease-in-out duration-300",leaveFromClass:"opacity-100",leaveToClass:"opacity-0"}:{enterActiveClass:"ease-in-out duration-300",enterFromClass:"opacity-0",enterToClass:"opacity-100",leaveActiveClass:"ease-in-out duration-300",leaveFromClass:"opacity-100",leaveToClass:"opacity-0"})),B=s((()=>{let e=!0;return C.menu.children?C.menu.children.every((e=>{var t;return!1===(null==(t=e.meta)?void 0:t.menu)}))&&(e=!1):e=!1,e}));function S(){P.isMenuPopup&&B.value||(B.value?P.handleSubMenuClick(k,C.uniqueKey):P.handleMenuItemClick(k))}let T;function $(){P.isMenuPopup&&(P.mouseInMenu=C.uniqueKey,null==T||T(),({stop:T}=M((()=>{if(B.value)P.openMenu(k,C.uniqueKey),H((()=>{const e=q.value.ref;let t=0,n=0;"vertical"===P.props.mode||0!==C.level?(t=e.getBoundingClientRect().top+e.scrollTop,n=e.getBoundingClientRect().left+e.getBoundingClientRect().width,t+_.value.getElement().offsetHeight>window.innerHeight&&(t=window.innerHeight-_.value.getElement().offsetHeight)):(t=e.getBoundingClientRect().top+e.getBoundingClientRect().height,n=e.getBoundingClientRect().left,t+_.value.getElement().offsetHeight>window.innerHeight&&(_.value.getElement().style.height=window.innerHeight-t+"px")),_.value.getElement().style.top=`${t}px`,_.value.getElement().style.left=`${n}px`}));else{const e=C.menu.children?P.subMenus[k].indexPath.at(-1):P.items[k].indexPath.at(-1);P.openMenu(e,P.subMenus[e].indexPath)}}),300)))}function I(){P.isMenuPopup&&(P.mouseInMenu=[],null==T||T(),({stop:T}=M((()=>{0===P.mouseInMenu.length?P.closeMenu(C.uniqueKey):B.value&&!P.mouseInMenu.includes(C.uniqueKey.at(-1))&&P.closeMenu(C.uniqueKey.at(-1))}),300)))}return(e,t)=>{const n=i("SubMenu");return l(),u(a,null,[o(b,{ref_key:"itemRef",ref:q,"unique-key":e.uniqueKey,item:e.menu,level:e.level,"sub-menu":d(B),expand:d(K),onClick:S,onMouseenter:$,onMouseleave:I},null,8,["unique-key","item","level","sub-menu","expand"]),d(B)?(l(),r(g,{key:0,to:"body",disabled:!d(P).isMenuPopup},[o(m,v(d(E),y(d(R))),{default:f((()=>[d(K)?(l(),r(d(h),{key:0,ref_key:"subMenuRef",ref:_,options:{scrollbars:{visibility:"hidden"}},defer:"",class:c(["sub-menu",{"bg-[var(--g-sub-sidebar-bg)]":d(P).isMenuPopup,"ring-1 ring-stone-2 dark-ring-stone-8 shadow-xl fixed z-3000 w-[200px]":d(P).isMenuPopup,"mx-2":d(P).isMenuPopup&&("vertical"===d(P).props.mode||0!==e.level)}])},{default:f((()=>[(l(!0),u(a,null,x(e.menu.children,(t=>{var s;return l(),u(a,{key:t.path??JSON.stringify(t)},[!1!==(null==(s=t.meta)?void 0:s.menu)?(l(),r(n,{key:0,"unique-key":[...e.uniqueKey,t.path??JSON.stringify(t)],menu:t,level:e.level+1},null,8,["unique-key","menu","level"])):p("",!0)],64)})),128))])),_:1},8,["class"])):p("",!0)])),_:1},16)],8,["disabled"])):p("",!0)],64)}}});export{C as _};
