
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,aS as a,aT as l,r as d,c as s,e as o,a5 as r,aM as t,g as i}from"./index-BERX8Mlm.js";const n={class:"relative w-full lg-w-48"},p=["placeholder","disabled"],u=e({__name:"HInput",props:a({placeholder:{},disabled:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(e,{expose:a}){const u=l(e,"modelValue");return a({ref:d()}),(e,a)=>(o(),s("div",n,[r(i("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),type:"text",placeholder:e.placeholder,disabled:e.disabled,class:"relative block w-full border-0 rounded-md bg-white px-2.5 py-1.5 text-sm shadow-sm ring-1 ring-stone-2 ring-inset disabled-cursor-not-allowed dark-bg-dark disabled-opacity-50 focus-outline-none focus-ring-2 dark-ring-stone-8 focus-ring-ui-primary placeholder-stone-4 dark-placeholder-stone-5"},null,8,p),[[t,u.value]])]))}});export{u as _};
