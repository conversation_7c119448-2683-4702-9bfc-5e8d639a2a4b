
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

var e=["and","as","block","endblock","by","cycle","debug","else","elif","extends","filter","endfilter","firstof","do","for","endfor","if","endif","ifchanged","endifchanged","ifequal","endifequal","ifnotequal","set","raw","endraw","endifnotequal","in","include","load","not","now","or","parsed","regroup","reversed","spaceless","call","endcall","macro","endmacro","endspaceless","ssi","templatetag","openblock","closeblock","openvariable","closevariable","without","context","openbrace","closebrace","opencomment","closecomment","widthratio","url","with","endwith","get_current_language","trans","endtrans","noop","blocktrans","endblocktrans","get_available_languages","get_current_language_bidi","pluralize","autoescape","endautoescape"],n=/^[+\-*&%=<>!?|~^]/,t=/^[:\[\(\{]/,a=["true","false"],i=/^(\d[+\-\*\/])?\d+(\.\d+)?/;function r(r,o){var c=r.peek();if(o.incomment)return r.skipTo("#}")?(r.eatWhile(/\#|}/),o.incomment=!1):r.skipToEnd(),"comment";if(o.intag){if(o.operator){if(o.operator=!1,r.match(a))return"atom";if(r.match(i))return"number"}if(o.sign){if(o.sign=!1,r.match(a))return"atom";if(r.match(i))return"number"}if(o.instring)return c==o.instring&&(o.instring=!1),r.next(),"string";if("'"==c||'"'==c)return o.instring=c,r.next(),"string";if(o.inbraces>0&&")"==c)r.next(),o.inbraces--;else if("("==c)r.next(),o.inbraces++;else if(o.inbrackets>0&&"]"==c)r.next(),o.inbrackets--;else if("["==c)r.next(),o.inbrackets++;else{if(!o.lineTag&&(r.match(o.intag+"}")||r.eat("-")&&r.match(o.intag+"}")))return o.intag=!1,"tag";if(r.match(n))return o.operator=!0,"operator";if(r.match(t))o.sign=!0;else{if(1==r.column()&&o.lineTag&&r.match(e))return"keyword";if(r.eat(" ")||r.sol()){if(r.match(e))return"keyword";if(r.match(a))return"atom";if(r.match(i))return"number";r.sol()&&r.next()}else r.next()}}return"variable"}if(r.eat("{")){if(r.eat("#"))return o.incomment=!0,r.skipTo("#}")?(r.eatWhile(/\#|}/),o.incomment=!1):r.skipToEnd(),"comment";if(c=r.eat(/\{|%/))return o.intag=c,o.inbraces=0,o.inbrackets=0,"{"==c&&(o.intag="}"),r.eat("-"),"tag"}else if(r.eat("#")){if("#"==r.peek())return r.skipToEnd(),"comment";if(!r.eol())return o.intag=!0,o.lineTag=!0,o.inbraces=0,o.inbrackets=0,"tag"}r.next()}e=new RegExp("(("+e.join(")|(")+"))\\b"),a=new RegExp("(("+a.join(")|(")+"))\\b");const o={name:"jinja2",startState:function(){return{tokenize:r,inbrackets:0,inbraces:0}},token:function(e,n){var t=n.tokenize(e,n);return e.eol()&&n.lineTag&&!n.instring&&0==n.inbraces&&0==n.inbrackets&&(n.intag=!1,n.lineTag=!1),t},languageData:{commentTokens:{block:{open:"{#",close:"#}",line:"##"}}}};export{o as jinja2};
