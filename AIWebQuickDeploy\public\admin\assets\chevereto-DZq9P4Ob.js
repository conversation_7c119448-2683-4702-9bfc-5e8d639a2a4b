
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as t,r as a,P as o,b as s,Q as r,c as u,e as c,f as d,w as n,j as i,h as f,_ as p,g as v,i as h,Y as m,k as _}from"./index-BERX8Mlm.js";import{a as y}from"./config-BrbFL53_.js";const g={class:"flex justify-between"},x=l({__name:"chevereto",setup(l){const _=t({cheveretoStatus:"",cheveretoUploadPath:"",cheveretoKey:""}),x=a();async function b(){const e=await y.queryConfig({keys:["cheveretoKey","cheveretoUploadPath","cheveretoStatus"]});Object.assign(_,e.data)}function w(){var e;null==(e=x.value)||e.validate((async e=>{if(e){try{await y.setConfig({settings:(l=_,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),m.success("变更配置信息成功")}catch(t){}b()}else m.error("请填写完整信息");var l}))}const S=o((()=>[{required:1===Number(_.cheveretoStatus),message:"开启配置后请填写此项",trigger:"change"}]));return s((()=>{b()})),(l,t)=>{const a=p,o=i,s=e,m=r("el-button"),y=r("el-switch"),b=r("el-form-item"),V=r("el-col"),K=r("el-row"),U=r("el-input"),j=r("el-form"),k=r("el-card");return c(),u("div",null,[d(s,null,{title:n((()=>t[3]||(t[3]=[v("div",{class:"flex items-center gap-4"},"Chevereto图床设置",-1)]))),content:n((()=>t[4]||(t[4]=[v("div",{class:"text-sm/6"},[v("div",null,[f(" 详细搭建及配置请参考 "),v("a",{href:"https://v4-docs.chevereto.com/developer/api/api-v1.html",target:"_blank"},"Chevereto图床文档"),f(" 。如果同时开启多个存储服务，服务优先级：本地存储 > S3存储 > 腾讯云COS > 阿里云OSS。 ")])],-1)]))),default:n((()=>[d(o,{outline:"",onClick:w},{default:n((()=>[d(a,{name:"i-ri:file-text-line"}),t[5]||(t[5]=f(" 保存设置 "))])),_:1})])),_:1}),d(k,{style:{margin:"20px"}},{header:n((()=>[v("div",g,[t[7]||(t[7]=v("b",null,"chevereto图床参数设置",-1)),d(m,{class:"button",text:"",onClick:w},{default:n((()=>t[6]||(t[6]=[f(" 保存设置 ")]))),_:1})])])),default:n((()=>[d(j,{ref_key:"formRef",ref:x,model:_,"label-width":"100px"},{default:n((()=>[d(K,null,{default:n((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[d(b,{label:"服务启用状态",prop:"cheveretoStatus"},{default:n((()=>[d(y,{modelValue:_.cheveretoStatus,"onUpdate:modelValue":t[0]||(t[0]=e=>_.cheveretoStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(K,null,{default:n((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[d(b,{label:"上传地址",prop:"cheveretoUploadPath",rules:h(S)},{default:n((()=>[d(U,{modelValue:_.cheveretoUploadPath,"onUpdate:modelValue":t[1]||(t[1]=e=>_.cheveretoUploadPath=e),placeholder:"请填写您的图床上传地址",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),d(K,null,{default:n((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[d(b,{label:"ApiKey",prop:"cheveretoKey",rules:h(S)},{default:n((()=>[d(U,{modelValue:_.cheveretoKey,"onUpdate:modelValue":t[2]||(t[2]=e=>_.cheveretoKey=e),placeholder:"请填写ApiKey",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})])}}});"function"==typeof _&&_(x);export{x as default};
