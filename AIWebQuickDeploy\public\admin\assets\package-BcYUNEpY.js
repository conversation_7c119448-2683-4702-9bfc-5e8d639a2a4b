
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{ac as a}from"./index-BERX8Mlm.js";const e={queryAllPackage:e=>a.get("crami/queryAllPackage",{params:e}),updatePackage:e=>a.post("crami/updatePackage",e),createPackage:e=>a.post("crami/createPackage",e),delPackage:e=>a.post("crami/delPackage",e),queryAllCrami:e=>a.get("crami/queryAllCrami",{params:e}),delCrami:e=>a.post("crami/delCrami",e),createCrami:e=>a.post("crami/createCrami",e),batchDelCrami:e=>a.post("crami/batchDelCrami",e)};export{e as A};
