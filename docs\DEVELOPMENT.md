# 99AI 项目开发文档

## 项目概述

99AI 是一个基于 Vue.js 和 NestJS 构建的现代化 AI 对话平台，本文档旨在帮助开发者快速理解和参与项目开发。

## 核心模块

### 1. 用户对话界面（chat）

- **技术栈：** Vue.js
- **功能特性：**
  - AI 智能对话

### 2. 管理后台（admin）

- **技术栈：** [Fantastic Admin Basic](https://github.com/fantastic-admin/basic)
- **功能特性：**
  - 多级管理员权限控制
  - 积分系统管理
  - AI 模型配置
  - 用户管理与数据分析

### 3. 后端服务（service）

- **技术栈：** NestJS
- **服务地址：** `http://localhost:9520`
- **功能特性：**
  - RESTful API 接口
  - 数据库交互

## 开发指南

### 项目构建

项目提供了便捷的一键构建脚本：

```bash
./build.sh
```

执行后，脚本将自动完成所有模块的构建，构建产物将保存在项目根目录的对应文件夹中。

### 开发建议

1. 遵循项目既定的代码规范和提交规范
2. 确保新功能有完整的测试覆盖
3. 提交代码前进行本地测试
4. 及时更新文档

## 问题反馈

如果您在开发过程中遇到任何问题，或有改进建议，欢迎通过以下方式反馈：

- 提交 [Issue](https://github.com/vastxie/99AI/issues)
- 参与项目讨论
- 提交 Pull Request

## 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 功能改进
- Bug 修复
- 文档完善
- 性能优化
- 代码重构

请确保您的贡献符合项目的开发规范和质量标准。
