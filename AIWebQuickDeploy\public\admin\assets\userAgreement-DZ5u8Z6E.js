
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,a as t,$ as l,P as n,r,b as s,Q as o,c as i,e as m,f as u,w as d,j as f,h as g,_ as c,g as p,i as _,Y as v,k as y}from"./index-BERX8Mlm.js";import{a as A}from"./config-BrbFL53_.js";import{u as x}from"./upload-DwmqW_vL.js";import{E as h}from"./style-ClrjWzkr.js";const w=a({__name:"userAgreement",setup(a){const y=t(),w=l({isAutoOpenAgreement:"",agreementInfo:"",agreementTitle:""});n((()=>y.settings.app.colorScheme));const O=r({agreementTitle:[{required:!0,trigger:"blur",message:"请填写用户协议标题"}],agreementInfo:[{required:!0,trigger:"blur",message:"请填写用户协议具体内容"}]}),b=r();async function V(){const e=await A.queryConfig({keys:["agreementInfo","agreementTitle","isAutoOpenAgreement"]}),{agreementInfo:a,agreementTitle:t,isAutoOpenAgreement:l}=e.data;a&&Object.assign(w,{agreementInfo:a,agreementTitle:t,isAutoOpenAgreement:l})}function I(){var e;null==(e=b.value)||e.validate((async e=>{if(e){try{await A.setConfig({settings:(a=w,Object.keys(a).map((e=>({configKey:e,configVal:a[e]}))))}),v.success("变更用户协议信息成功")}catch(t){}V()}else v.error("请填写完整信息");var a}))}function T(e){}async function j(e,a){a((await Promise.all(Array.from(e).map((e=>new Promise((async(a,t)=>{var l;const n=new FormData;n.append("file",e);try{const e=await x.uploadFile(n,"system/others");(null==(l=null==e?void 0:e.data)?void 0:l.data)||v.error("图片上传失败，请检查您的配置信息！"),a(e.data.data)}catch(r){v.error(r||"图片上传失败，请检查您的配置信息！"),t(r)}})))))).map((e=>e))),v({message:"图片上传成功！",type:"success"})}return s((()=>{V()})),(a,t)=>{const l=c,n=f,r=e,s=o("el-input"),v=o("el-form-item"),y=o("el-col"),A=o("el-switch"),x=o("el-tooltip"),V=o("el-row"),k=o("el-form"),C=o("el-card");return m(),i("div",null,[u(r,null,{title:d((()=>t[3]||(t[3]=[p("div",{class:"flex items-center gap-4"},"用户协议设置",-1)]))),content:d((()=>t[4]||(t[4]=[p("div",{class:"text-sm/6"},[p("div",null," 用户协议设置用于配置用户端显示的用户协议页面。支持使用Markdown语法或HTML标签来创建内容，为灵活的内容格式提供便利。 ")],-1)]))),default:d((()=>[u(n,{outline:"",onClick:I},{default:d((()=>[u(l,{name:"i-ri:file-text-line"}),t[5]||(t[5]=g(" 保存设置 "))])),_:1})])),_:1}),u(C,{style:{margin:"20px"}},{default:d((()=>[u(k,{ref_key:"formRef",ref:b,rules:O.value,model:w,"label-width":"120px"},{default:d((()=>[u(V,null,{default:d((()=>[u(y,{xs:24,md:20,lg:15,xl:10},{default:d((()=>[u(v,{label:"用户协议标题",prop:"agreementTitle"},{default:d((()=>[u(s,{modelValue:w.agreementTitle,"onUpdate:modelValue":t[0]||(t[0]=e=>w.agreementTitle=e),rows:1,placeholder:"用户协议标题",clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),u(y,{xs:24,md:20,lg:15,xl:10},{default:d((()=>[u(v,{label:"开启用户协议",prop:"isAutoOpenAgreement"},{default:d((()=>[u(x,{content:"开启后，用户在注册时将会弹出用户协议页面",placement:"top","show-after":500},{default:d((()=>[u(A,{modelValue:w.isAutoOpenAgreement,"onUpdate:modelValue":t[1]||(t[1]=e=>w.isAutoOpenAgreement=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1}),u(y,{xs:24,md:20,lg:15,xl:12})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(y,{span:24},{default:d((()=>[u(v,{label:"用户协议内容",prop:"agreementInfo"},{default:d((()=>[u(_(h),{modelValue:w.agreementInfo,"onUpdate:modelValue":t[2]||(t[2]=e=>w.agreementInfo=e),style:{"min-height":"80vh"},onOnChange:T,onOnUploadImg:j},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof y&&y(w);export{w as default};
