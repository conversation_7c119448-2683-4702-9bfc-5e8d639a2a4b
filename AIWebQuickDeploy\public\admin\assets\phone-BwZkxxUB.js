
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as s,b as t,Q as o,c as n,e as i,f as d,w as c,j as r,h as u,_ as m,g as p,Y as f,k as g}from"./index-BERX8Mlm.js";import{a as h}from"./config-BrbFL53_.js";const y=l({__name:"phone",setup(l){const g=a({phoneLoginStatus:"",aliPhoneAccessKeyId:"",aliPhoneAccessKeySecret:"",aliPhoneSignName:"",aliPhoneTemplateCode:""}),y=s({phoneLoginStatus:[{required:!1,trigger:"blur",message:"请选择是否开启手机号登录"}],aliPhoneAccessKeyId:[{required:!1,trigger:"blur",message:"请填写阿里云短信服务accessKeyId"}],aliPhoneAccessKeySecret:[{required:!1,trigger:"blur",message:"请填写阿里云短信服务accessKeySecret"}],aliPhoneSignName:[{required:!1,trigger:"blur",message:"请填写阿里云短信服务的模板签名"}],aliPhoneTemplateCode:[{required:!1,trigger:"blur",message:"请填写阿里云短信服务的模板ID"}]}),_=s();async function P(){const e=await h.queryConfig({keys:["phoneLoginStatus","aliPhoneAccessKeyId","aliPhoneAccessKeySecret","aliPhoneSignName","aliPhoneTemplateCode"]});Object.assign(g,e.data)}function S(){var e;null==(e=_.value)||e.validate((async e=>{if(e){try{await h.setConfig({settings:(l=g,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),f.success("变更配置信息成功")}catch(a){}P()}else f.error("请填写完整信息");var l}))}return t((()=>{P()})),(l,a)=>{const s=m,t=r,f=e,h=o("el-switch"),P=o("el-tooltip"),b=o("el-form-item"),x=o("el-col"),K=o("el-row"),v=o("el-input"),A=o("el-form"),V=o("el-card");return i(),n("div",null,[d(f,null,{title:c((()=>a[5]||(a[5]=[p("div",{class:"flex items-center gap-4"},"手机验证码登录设置",-1)]))),content:c((()=>a[6]||(a[6]=[p("div",{class:"text-sm/6"},[p("div",null,[u(" 手机验证使用"),p("a",{href:"https://dysms.console.aliyun.com/overview",target:"_blank"},"阿里云短信服务"),u("，请先申请好签名模板以及获取到您的秘钥信息。 ")]),p("div",null,"当您配置并开启后则表示开启用户端手机号注册的行为！")],-1)]))),default:c((()=>[d(t,{text:"",outline:"",onClick:S},{default:c((()=>[d(s,{name:"i-ri:file-text-line"}),a[7]||(a[7]=u(" 保存设置 "))])),_:1})])),_:1}),d(V,{style:{margin:"20px"}},{default:c((()=>[d(A,{ref_key:"formRef",ref:_,rules:y.value,model:g,"label-width":"170px"},{default:c((()=>[d(K,null,{default:c((()=>[d(x,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[d(b,{label:"开启手机号注册/登录",prop:"phoneLoginStatus"},{default:c((()=>[d(P,{class:"box-item",effect:"dark",content:"如您启用短信登录、则用户端则可以通过手机号的方式登录！",placement:"right"},{default:c((()=>[d(h,{modelValue:g.phoneLoginStatus,"onUpdate:modelValue":a[0]||(a[0]=e=>g.phoneLoginStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),d(K,null,{default:c((()=>[d(x,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[d(b,{label:"AccessKeyId",prop:"aliPhoneAccessKeyId"},{default:c((()=>[d(v,{modelValue:g.aliPhoneAccessKeyId,"onUpdate:modelValue":a[1]||(a[1]=e=>g.aliPhoneAccessKeyId=e),placeholder:"请填写AccessKeyId",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(K,null,{default:c((()=>[d(x,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[d(b,{label:"AccessKeySecret",prop:"aliPhoneAccessKeySecret"},{default:c((()=>[d(v,{modelValue:g.aliPhoneAccessKeySecret,"onUpdate:modelValue":a[2]||(a[2]=e=>g.aliPhoneAccessKeySecret=e),placeholder:"请填写AccessKeySecret",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(K,null,{default:c((()=>[d(x,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[d(b,{label:"短信签名",prop:"aliPhoneSignName"},{default:c((()=>[d(v,{modelValue:g.aliPhoneSignName,"onUpdate:modelValue":a[3]||(a[3]=e=>g.aliPhoneSignName=e),placeholder:"请填写您申请的短信签名",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(K,null,{default:c((()=>[d(x,{xs:24,md:20,lg:15,xl:12},{default:c((()=>[d(b,{label:"短信模板ID",prop:"aliPhoneTemplateCode"},{default:c((()=>[d(v,{modelValue:g.aliPhoneTemplateCode,"onUpdate:modelValue":a[4]||(a[4]=e=>g.aliPhoneTemplateCode=e),placeholder:"请填写短信模板ID",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof g&&g(y);export{y as default};
