
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{Z as r,Q as e,a2 as s,e as o,w as t,aj as a,R as d}from"./index-BERX8Mlm.js";const p=r({},[["render",function(r,p){const n=e("VDropdown");return o(),s(n,d({"show-triggers":["hover"],"hide-triggers":["hover"],"auto-hide":!1,"popper-triggers":["hover"],delay:200},r.$attrs),{popper:t((()=>[a(r.$slots,"dropdown")])),default:t((()=>[a(r.$slots,"default")])),_:3},16)}]]);export{p as default};
