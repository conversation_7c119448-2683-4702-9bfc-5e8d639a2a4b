var Fe=Object.defineProperty;var ie=Object.getOwnPropertySymbols;var Pe=Object.prototype.hasOwnProperty,ve=Object.prototype.propertyIsEnumerable;var ae=(t,a,o)=>a in t?Fe(t,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[a]=o,U=(t,a)=>{for(var o in a||(a={}))Pe.call(a,o)&&ae(t,o,a[o]);if(ie)for(var o of ie(a))ve.call(a,o)&&ae(t,o,a[o]);return t};import{_ as l,P as Ce,D,l as Ft,Q as ne,d as Rt,i as Le,o as se,s as Ee,g as De,n as ze,b as Ve,c as Ie,t as we,j as It,k as Be}from"./chart-vendor-e1d59b84.js";import"./utils-vendor-c35799af.js";import"./vue-vendor-d751b0f5.js";var wt=function(){var t=l(function(j,r,h,g){for(h=h||{},g=j.length;g--;h[j[g]]=r);return h},"o"),a=[1,3],o=[1,4],u=[1,5],c=[1,6],p=[1,7],y=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],S=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],n=[55,56,57],A=[2,36],x=[1,37],m=[1,36],b=[1,38],T=[1,35],q=[1,43],d=[1,41],N=[1,14],Y=[1,23],G=[1,18],qt=[1,19],mt=[1,20],ht=[1,21],vt=[1,22],ct=[1,24],dt=[1,25],ut=[1,26],xt=[1,27],ft=[1,28],gt=[1,29],B=[1,32],e=[1,33],k=[1,34],F=[1,39],P=[1,40],v=[1,42],C=[1,44],H=[1,62],X=[1,61],L=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],Nt=[1,65],Wt=[1,66],Qt=[1,67],Ut=[1,68],Ot=[1,69],Ht=[1,70],Xt=[1,71],Mt=[1,72],Yt=[1,73],jt=[1,74],Gt=[1,75],Kt=[1,76],I=[4,5,6,7,8,9,10,11,12,13,14,15,18],J=[1,90],$=[1,91],tt=[1,92],et=[1,99],it=[1,93],at=[1,96],nt=[1,94],st=[1,95],rt=[1,97],ot=[1,98],Ct=[1,102],Zt=[10,55,56,57],W=[4,5,6,8,10,11,13,17,18,19,20,55,56,57],Lt={trace:l(function(){},"trace"),yy:{},symbols_:{error:2,idStringToken:3,ALPHA:4,NUM:5,NODE_STRING:6,DOWN:7,MINUS:8,DEFAULT:9,COMMA:10,COLON:11,AMP:12,BRKT:13,MULT:14,UNICODE_TEXT:15,styleComponent:16,UNIT:17,SPACE:18,STYLE:19,PCT:20,idString:21,style:22,stylesOpt:23,classDefStatement:24,CLASSDEF:25,start:26,eol:27,QUADRANT:28,document:29,line:30,statement:31,axisDetails:32,quadrantDetails:33,points:34,title:35,title_value:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,section:42,text:43,point_start:44,point_x:45,point_y:46,class_name:47,"X-AXIS":48,"AXIS-TEXT-DELIMITER":49,"Y-AXIS":50,QUADRANT_1:51,QUADRANT_2:52,QUADRANT_3:53,QUADRANT_4:54,NEWLINE:55,SEMI:56,EOF:57,alphaNumToken:58,textNoTagsToken:59,STR:60,MD_STR:61,alphaNum:62,PUNCTUATION:63,PLUS:64,EQUALS:65,DOT:66,UNDERSCORE:67,$accept:0,$end:1},terminals_:{2:"error",4:"ALPHA",5:"NUM",6:"NODE_STRING",7:"DOWN",8:"MINUS",9:"DEFAULT",10:"COMMA",11:"COLON",12:"AMP",13:"BRKT",14:"MULT",15:"UNICODE_TEXT",17:"UNIT",18:"SPACE",19:"STYLE",20:"PCT",25:"CLASSDEF",28:"QUADRANT",35:"title",36:"title_value",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"section",44:"point_start",45:"point_x",46:"point_y",47:"class_name",48:"X-AXIS",49:"AXIS-TEXT-DELIMITER",50:"Y-AXIS",51:"QUADRANT_1",52:"QUADRANT_2",53:"QUADRANT_3",54:"QUADRANT_4",55:"NEWLINE",56:"SEMI",57:"EOF",60:"STR",61:"MD_STR",63:"PUNCTUATION",64:"PLUS",65:"EQUALS",66:"DOT",67:"UNDERSCORE"},productions_:[0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],performAction:l(function(r,h,g,f,_,i,pt){var s=i.length-1;switch(_){case 23:this.$=i[s];break;case 24:this.$=i[s-1]+""+i[s];break;case 26:this.$=i[s-1]+i[s];break;case 27:this.$=[i[s].trim()];break;case 28:i[s-2].push(i[s].trim()),this.$=i[s-2];break;case 29:this.$=i[s-4],f.addClass(i[s-2],i[s]);break;case 37:this.$=[];break;case 42:this.$=i[s].trim(),f.setDiagramTitle(this.$);break;case 43:this.$=i[s].trim(),f.setAccTitle(this.$);break;case 44:case 45:this.$=i[s].trim(),f.setAccDescription(this.$);break;case 46:f.addSection(i[s].substr(8)),this.$=i[s].substr(8);break;case 47:f.addPoint(i[s-3],"",i[s-1],i[s],[]);break;case 48:f.addPoint(i[s-4],i[s-3],i[s-1],i[s],[]);break;case 49:f.addPoint(i[s-4],"",i[s-2],i[s-1],i[s]);break;case 50:f.addPoint(i[s-5],i[s-4],i[s-2],i[s-1],i[s]);break;case 51:f.setXAxisLeftText(i[s-2]),f.setXAxisRightText(i[s]);break;case 52:i[s-1].text+=" ⟶ ",f.setXAxisLeftText(i[s-1]);break;case 53:f.setXAxisLeftText(i[s]);break;case 54:f.setYAxisBottomText(i[s-2]),f.setYAxisTopText(i[s]);break;case 55:i[s-1].text+=" ⟶ ",f.setYAxisBottomText(i[s-1]);break;case 56:f.setYAxisBottomText(i[s]);break;case 57:f.setQuadrant1Text(i[s]);break;case 58:f.setQuadrant2Text(i[s]);break;case 59:f.setQuadrant3Text(i[s]);break;case 60:f.setQuadrant4Text(i[s]);break;case 64:this.$={text:i[s],type:"text"};break;case 65:this.$={text:i[s-1].text+""+i[s],type:i[s-1].type};break;case 66:this.$={text:i[s],type:"text"};break;case 67:this.$={text:i[s],type:"markdown"};break;case 68:this.$=i[s];break;case 69:this.$=i[s-1]+""+i[s];break}},"anonymous"),table:[{18:a,26:1,27:2,28:o,55:u,56:c,57:p},{1:[3]},{18:a,26:8,27:2,28:o,55:u,56:c,57:p},{18:a,26:9,27:2,28:o,55:u,56:c,57:p},t(y,[2,33],{29:10}),t(S,[2,61]),t(S,[2,62]),t(S,[2,63]),{1:[2,30]},{1:[2,31]},t(n,A,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:x,5:m,10:b,12:T,13:q,14:d,18:N,25:Y,35:G,37:qt,39:mt,41:ht,42:vt,48:ct,50:dt,51:ut,52:xt,53:ft,54:gt,60:B,61:e,63:k,64:F,65:P,66:v,67:C}),t(y,[2,34]),{27:45,55:u,56:c,57:p},t(n,[2,37]),t(n,A,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:x,5:m,10:b,12:T,13:q,14:d,18:N,25:Y,35:G,37:qt,39:mt,41:ht,42:vt,48:ct,50:dt,51:ut,52:xt,53:ft,54:gt,60:B,61:e,63:k,64:F,65:P,66:v,67:C}),t(n,[2,39]),t(n,[2,40]),t(n,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},t(n,[2,45]),t(n,[2,46]),{18:[1,50]},{4:x,5:m,10:b,12:T,13:q,14:d,43:51,58:31,60:B,61:e,63:k,64:F,65:P,66:v,67:C},{4:x,5:m,10:b,12:T,13:q,14:d,43:52,58:31,60:B,61:e,63:k,64:F,65:P,66:v,67:C},{4:x,5:m,10:b,12:T,13:q,14:d,43:53,58:31,60:B,61:e,63:k,64:F,65:P,66:v,67:C},{4:x,5:m,10:b,12:T,13:q,14:d,43:54,58:31,60:B,61:e,63:k,64:F,65:P,66:v,67:C},{4:x,5:m,10:b,12:T,13:q,14:d,43:55,58:31,60:B,61:e,63:k,64:F,65:P,66:v,67:C},{4:x,5:m,10:b,12:T,13:q,14:d,43:56,58:31,60:B,61:e,63:k,64:F,65:P,66:v,67:C},{4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,44:[1,57],47:[1,58],58:60,59:59,63:k,64:F,65:P,66:v,67:C},t(L,[2,64]),t(L,[2,66]),t(L,[2,67]),t(L,[2,70]),t(L,[2,71]),t(L,[2,72]),t(L,[2,73]),t(L,[2,74]),t(L,[2,75]),t(L,[2,76]),t(L,[2,77]),t(L,[2,78]),t(L,[2,79]),t(L,[2,80]),t(y,[2,35]),t(n,[2,38]),t(n,[2,42]),t(n,[2,43]),t(n,[2,44]),{3:64,4:Nt,5:Wt,6:Qt,7:Ut,8:Ot,9:Ht,10:Xt,11:Mt,12:Yt,13:jt,14:Gt,15:Kt,21:63},t(n,[2,53],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,49:[1,77],63:k,64:F,65:P,66:v,67:C}),t(n,[2,56],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,49:[1,78],63:k,64:F,65:P,66:v,67:C}),t(n,[2,57],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,63:k,64:F,65:P,66:v,67:C}),t(n,[2,58],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,63:k,64:F,65:P,66:v,67:C}),t(n,[2,59],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,63:k,64:F,65:P,66:v,67:C}),t(n,[2,60],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,63:k,64:F,65:P,66:v,67:C}),{45:[1,79]},{44:[1,80]},t(L,[2,65]),t(L,[2,81]),t(L,[2,82]),t(L,[2,83]),{3:82,4:Nt,5:Wt,6:Qt,7:Ut,8:Ot,9:Ht,10:Xt,11:Mt,12:Yt,13:jt,14:Gt,15:Kt,18:[1,81]},t(I,[2,23]),t(I,[2,1]),t(I,[2,2]),t(I,[2,3]),t(I,[2,4]),t(I,[2,5]),t(I,[2,6]),t(I,[2,7]),t(I,[2,8]),t(I,[2,9]),t(I,[2,10]),t(I,[2,11]),t(I,[2,12]),t(n,[2,52],{58:31,43:83,4:x,5:m,10:b,12:T,13:q,14:d,60:B,61:e,63:k,64:F,65:P,66:v,67:C}),t(n,[2,55],{58:31,43:84,4:x,5:m,10:b,12:T,13:q,14:d,60:B,61:e,63:k,64:F,65:P,66:v,67:C}),{46:[1,85]},{45:[1,86]},{4:J,5:$,6:tt,8:et,11:it,13:at,16:89,17:nt,18:st,19:rt,20:ot,22:88,23:87},t(I,[2,24]),t(n,[2,51],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,63:k,64:F,65:P,66:v,67:C}),t(n,[2,54],{59:59,58:60,4:x,5:m,8:H,10:b,12:T,13:q,14:d,18:X,63:k,64:F,65:P,66:v,67:C}),t(n,[2,47],{22:88,16:89,23:100,4:J,5:$,6:tt,8:et,11:it,13:at,17:nt,18:st,19:rt,20:ot}),{46:[1,101]},t(n,[2,29],{10:Ct}),t(Zt,[2,27],{16:103,4:J,5:$,6:tt,8:et,11:it,13:at,17:nt,18:st,19:rt,20:ot}),t(W,[2,25]),t(W,[2,13]),t(W,[2,14]),t(W,[2,15]),t(W,[2,16]),t(W,[2,17]),t(W,[2,18]),t(W,[2,19]),t(W,[2,20]),t(W,[2,21]),t(W,[2,22]),t(n,[2,49],{10:Ct}),t(n,[2,48],{22:88,16:89,23:104,4:J,5:$,6:tt,8:et,11:it,13:at,17:nt,18:st,19:rt,20:ot}),{4:J,5:$,6:tt,8:et,11:it,13:at,16:89,17:nt,18:st,19:rt,20:ot,22:105},t(W,[2,26]),t(n,[2,50],{10:Ct}),t(Zt,[2,28],{16:103,4:J,5:$,6:tt,8:et,11:it,13:at,17:nt,18:st,19:rt,20:ot})],defaultActions:{8:[2,30],9:[2,31]},parseError:l(function(r,h){if(h.recoverable)this.trace(r);else{var g=new Error(r);throw g.hash=h,g}},"parseError"),parse:l(function(r){var h=this,g=[0],f=[],_=[null],i=[],pt=this.table,s="",St=0,Jt=0,Se=2,$t=1,_e=i.slice.call(arguments,1),E=Object.create(this.lexer),K={yy:{}};for(var Et in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Et)&&(K.yy[Et]=this.yy[Et]);E.setInput(r,K.yy),K.yy.lexer=E,K.yy.parser=this,typeof E.yylloc=="undefined"&&(E.yylloc={});var Dt=E.yylloc;i.push(Dt);var Ae=E.options&&E.options.ranges;typeof K.yy.parseError=="function"?this.parseError=K.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ke(R){g.length=g.length-2*R,_.length=_.length-R,i.length=i.length-R}l(ke,"popStack");function te(){var R;return R=f.pop()||E.lex()||$t,typeof R!="number"&&(R instanceof Array&&(f=R,R=f.pop()),R=h.symbols_[R]||R),R}l(te,"lex");for(var w,Z,Q,zt,lt={},_t,M,ee,At;;){if(Z=g[g.length-1],this.defaultActions[Z]?Q=this.defaultActions[Z]:((w===null||typeof w=="undefined")&&(w=te()),Q=pt[Z]&&pt[Z][w]),typeof Q=="undefined"||!Q.length||!Q[0]){var Vt="";At=[];for(_t in pt[Z])this.terminals_[_t]&&_t>Se&&At.push("'"+this.terminals_[_t]+"'");E.showPosition?Vt="Parse error on line "+(St+1)+`:
`+E.showPosition()+`
Expecting `+At.join(", ")+", got '"+(this.terminals_[w]||w)+"'":Vt="Parse error on line "+(St+1)+": Unexpected "+(w==$t?"end of input":"'"+(this.terminals_[w]||w)+"'"),this.parseError(Vt,{text:E.match,token:this.terminals_[w]||w,line:E.yylineno,loc:Dt,expected:At})}if(Q[0]instanceof Array&&Q.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Z+", token: "+w);switch(Q[0]){case 1:g.push(w),_.push(E.yytext),i.push(E.yylloc),g.push(Q[1]),w=null,Jt=E.yyleng,s=E.yytext,St=E.yylineno,Dt=E.yylloc;break;case 2:if(M=this.productions_[Q[1]][1],lt.$=_[_.length-M],lt._$={first_line:i[i.length-(M||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(M||1)].first_column,last_column:i[i.length-1].last_column},Ae&&(lt._$.range=[i[i.length-(M||1)].range[0],i[i.length-1].range[1]]),zt=this.performAction.apply(lt,[s,Jt,St,K.yy,Q[1],_,i].concat(_e)),typeof zt!="undefined")return zt;M&&(g=g.slice(0,-1*M*2),_=_.slice(0,-1*M),i=i.slice(0,-1*M)),g.push(this.productions_[Q[1]][0]),_.push(lt.$),i.push(lt._$),ee=pt[g[g.length-2]][g[g.length-1]],g.push(ee);break;case 3:return!0}}return!0},"parse")},be=function(){var j={EOF:1,parseError:l(function(h,g){if(this.yy.parser)this.yy.parser.parseError(h,g);else throw new Error(h)},"parseError"),setInput:l(function(r,h){return this.yy=h||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:l(function(){var r=this._input[0];this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r;var h=r.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:l(function(r){var h=r.length,g=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var f=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var _=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===f.length?this.yylloc.first_column:0)+f[f.length-g.length].length-g[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[_[0],_[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:l(function(){return this._more=!0,this},"more"),reject:l(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:l(function(r){this.unput(this.match.slice(r))},"less"),pastInput:l(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:l(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:l(function(){var r=this.pastInput(),h=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:l(function(r,h){var g,f,_;if(this.options.backtrack_lexer&&(_={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(_.yylloc.range=this.yylloc.range.slice(0))),f=r[0].match(/(?:\r\n?|\n).*/g),f&&(this.yylineno+=f.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:f?f[f.length-1].length-f[f.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],g=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var i in _)this[i]=_[i];return!1}return!1},"test_match"),next:l(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var r,h,g,f;this._more||(this.yytext="",this.match="");for(var _=this._currentRules(),i=0;i<_.length;i++)if(g=this._input.match(this.rules[_[i]]),g&&(!h||g[0].length>h[0].length)){if(h=g,f=i,this.options.backtrack_lexer){if(r=this.test_match(g,_[i]),r!==!1)return r;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(r=this.test_match(h,_[f]),r!==!1?r:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:l(function(){var h=this.next();return h||this.lex()},"lex"),begin:l(function(h){this.conditionStack.push(h)},"begin"),popState:l(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:l(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:l(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:l(function(h){this.begin(h)},"pushState"),stateStackSize:l(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:l(function(h,g,f,_){switch(f){case 0:break;case 1:break;case 2:return 55;case 3:break;case 4:return this.begin("title"),35;case 5:return this.popState(),"title_value";case 6:return this.begin("acc_title"),37;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),39;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 48;case 14:return 50;case 15:return 49;case 16:return 51;case 17:return 52;case 18:return 53;case 19:return 54;case 20:return 25;case 21:this.begin("md_string");break;case 22:return"MD_STR";case 23:this.popState();break;case 24:this.begin("string");break;case 25:this.popState();break;case 26:return"STR";case 27:this.begin("class_name");break;case 28:return this.popState(),47;case 29:return this.begin("point_start"),44;case 30:return this.begin("point_x"),45;case 31:this.popState();break;case 32:this.popState(),this.begin("point_y");break;case 33:return this.popState(),46;case 34:return 28;case 35:return 4;case 36:return 11;case 37:return 64;case 38:return 10;case 39:return 65;case 40:return 65;case 41:return 14;case 42:return 13;case 43:return 67;case 44:return 66;case 45:return 12;case 46:return 8;case 47:return 5;case 48:return 18;case 49:return 56;case 50:return 63;case 51:return 57}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\b)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?::::)/i,/^(?:^\w+)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{class_name:{rules:[28],inclusive:!1},point_y:{rules:[33],inclusive:!1},point_x:{rules:[32],inclusive:!1},point_start:{rules:[30,31],inclusive:!1},acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},title:{rules:[5],inclusive:!1},md_string:{rules:[22,23],inclusive:!1},string:{rules:[25,26],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],inclusive:!0}}};return j}();Lt.lexer=be;function bt(){this.yy={}}return l(bt,"Parser"),bt.prototype=Lt,Lt.Parser=bt,new bt}();wt.parser=wt;var Re=wt,V=Ce(),yt,Ne=(yt=class{constructor(){this.classes=new Map,this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData()}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){var a,o,u,c,p,y,S,n,A,x,m,b,T,q,d,N,Y,G;return{showXAxis:!0,showYAxis:!0,showTitle:!0,chartHeight:((a=D.quadrantChart)==null?void 0:a.chartWidth)||500,chartWidth:((o=D.quadrantChart)==null?void 0:o.chartHeight)||500,titlePadding:((u=D.quadrantChart)==null?void 0:u.titlePadding)||10,titleFontSize:((c=D.quadrantChart)==null?void 0:c.titleFontSize)||20,quadrantPadding:((p=D.quadrantChart)==null?void 0:p.quadrantPadding)||5,xAxisLabelPadding:((y=D.quadrantChart)==null?void 0:y.xAxisLabelPadding)||5,yAxisLabelPadding:((S=D.quadrantChart)==null?void 0:S.yAxisLabelPadding)||5,xAxisLabelFontSize:((n=D.quadrantChart)==null?void 0:n.xAxisLabelFontSize)||16,yAxisLabelFontSize:((A=D.quadrantChart)==null?void 0:A.yAxisLabelFontSize)||16,quadrantLabelFontSize:((x=D.quadrantChart)==null?void 0:x.quadrantLabelFontSize)||16,quadrantTextTopPadding:((m=D.quadrantChart)==null?void 0:m.quadrantTextTopPadding)||5,pointTextPadding:((b=D.quadrantChart)==null?void 0:b.pointTextPadding)||5,pointLabelFontSize:((T=D.quadrantChart)==null?void 0:T.pointLabelFontSize)||12,pointRadius:((q=D.quadrantChart)==null?void 0:q.pointRadius)||5,xAxisPosition:((d=D.quadrantChart)==null?void 0:d.xAxisPosition)||"top",yAxisPosition:((N=D.quadrantChart)==null?void 0:N.yAxisPosition)||"left",quadrantInternalBorderStrokeWidth:((Y=D.quadrantChart)==null?void 0:Y.quadrantInternalBorderStrokeWidth)||1,quadrantExternalBorderStrokeWidth:((G=D.quadrantChart)==null?void 0:G.quadrantExternalBorderStrokeWidth)||2}}getDefaultThemeConfig(){return{quadrant1Fill:V.quadrant1Fill,quadrant2Fill:V.quadrant2Fill,quadrant3Fill:V.quadrant3Fill,quadrant4Fill:V.quadrant4Fill,quadrant1TextFill:V.quadrant1TextFill,quadrant2TextFill:V.quadrant2TextFill,quadrant3TextFill:V.quadrant3TextFill,quadrant4TextFill:V.quadrant4TextFill,quadrantPointFill:V.quadrantPointFill,quadrantPointTextFill:V.quadrantPointTextFill,quadrantXAxisTextFill:V.quadrantXAxisTextFill,quadrantYAxisTextFill:V.quadrantYAxisTextFill,quadrantTitleFill:V.quadrantTitleFill,quadrantInternalBorderStrokeFill:V.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:V.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData(),this.classes=new Map,Ft.info("clear called")}setData(a){this.data=U(U({},this.data),a)}addPoints(a){this.data.points=[...a,...this.data.points]}addClass(a,o){this.classes.set(a,o)}setConfig(a){Ft.trace("setConfig called with: ",a),this.config=U(U({},this.config),a)}setThemeConfig(a){Ft.trace("setThemeConfig called with: ",a),this.themeConfig=U(U({},this.themeConfig),a)}calculateSpace(a,o,u,c){const p=this.config.xAxisLabelPadding*2+this.config.xAxisLabelFontSize,y={top:a==="top"&&o?p:0,bottom:a==="bottom"&&o?p:0},S=this.config.yAxisLabelPadding*2+this.config.yAxisLabelFontSize,n={left:this.config.yAxisPosition==="left"&&u?S:0,right:this.config.yAxisPosition==="right"&&u?S:0},A=this.config.titleFontSize+this.config.titlePadding*2,x={top:c?A:0},m=this.config.quadrantPadding+n.left,b=this.config.quadrantPadding+y.top+x.top,T=this.config.chartWidth-this.config.quadrantPadding*2-n.left-n.right,q=this.config.chartHeight-this.config.quadrantPadding*2-y.top-y.bottom-x.top,d=T/2,N=q/2;return{xAxisSpace:y,yAxisSpace:n,titleSpace:x,quadrantSpace:{quadrantLeft:m,quadrantTop:b,quadrantWidth:T,quadrantHalfWidth:d,quadrantHeight:q,quadrantHalfHeight:N}}}getAxisLabels(a,o,u,c){const{quadrantSpace:p,titleSpace:y}=c,{quadrantHalfHeight:S,quadrantHeight:n,quadrantLeft:A,quadrantHalfWidth:x,quadrantTop:m,quadrantWidth:b}=p,T=!!this.data.xAxisRightText,q=!!this.data.yAxisTopText,d=[];return this.data.xAxisLeftText&&o&&d.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:A+(T?x/2:0),y:a==="top"?this.config.xAxisLabelPadding+y.top:this.config.xAxisLabelPadding+m+n+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:T?"center":"left",horizontalPos:"top",rotation:0}),this.data.xAxisRightText&&o&&d.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:A+x+(T?x/2:0),y:a==="top"?this.config.xAxisLabelPadding+y.top:this.config.xAxisLabelPadding+m+n+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:T?"center":"left",horizontalPos:"top",rotation:0}),this.data.yAxisBottomText&&u&&d.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+A+b+this.config.quadrantPadding,y:m+n-(q?S/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:q?"center":"left",horizontalPos:"top",rotation:-90}),this.data.yAxisTopText&&u&&d.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+A+b+this.config.quadrantPadding,y:m+S-(q?S/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:q?"center":"left",horizontalPos:"top",rotation:-90}),d}getQuadrants(a){const{quadrantSpace:o}=a,{quadrantHalfHeight:u,quadrantLeft:c,quadrantHalfWidth:p,quadrantTop:y}=o,S=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c+p,y,width:p,height:u,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c,y,width:p,height:u,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c,y:y+u,width:p,height:u,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:c+p,y:y+u,width:p,height:u,fill:this.themeConfig.quadrant4Fill}];for(const n of S)n.text.x=n.x+n.width/2,this.data.points.length===0?(n.text.y=n.y+n.height/2,n.text.horizontalPos="middle"):(n.text.y=n.y+this.config.quadrantTextTopPadding,n.text.horizontalPos="top");return S}getQuadrantPoints(a){const{quadrantSpace:o}=a,{quadrantHeight:u,quadrantLeft:c,quadrantTop:p,quadrantWidth:y}=o,S=ne().domain([0,1]).range([c,y+c]),n=ne().domain([0,1]).range([u+p,p]);return this.data.points.map(x=>{var T,q,d,N;const m=this.classes.get(x.className);return m&&(x=U(U({},m),x)),{x:S(x.x),y:n(x.y),fill:(T=x.color)!=null?T:this.themeConfig.quadrantPointFill,radius:(q=x.radius)!=null?q:this.config.pointRadius,text:{text:x.text,fill:this.themeConfig.quadrantPointTextFill,x:S(x.x),y:n(x.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0},strokeColor:(d=x.strokeColor)!=null?d:this.themeConfig.quadrantPointFill,strokeWidth:(N=x.strokeWidth)!=null?N:"0px"}})}getBorders(a){const o=this.config.quadrantExternalBorderStrokeWidth/2,{quadrantSpace:u}=a,{quadrantHalfHeight:c,quadrantHeight:p,quadrantLeft:y,quadrantHalfWidth:S,quadrantTop:n,quadrantWidth:A}=u;return[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y-o,y1:n,x2:y+A+o,y2:n},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y+A,y1:n+o,x2:y+A,y2:n+p-o},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y-o,y1:n+p,x2:y+A+o,y2:n+p},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y,y1:n+o,x2:y,y2:n+p-o},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:y+S,y1:n+o,x2:y+S,y2:n+p-o},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:y+o,y1:n+c,x2:y+A-o,y2:n+c}]}getTitle(a){if(a)return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}build(){const a=this.config.showXAxis&&!!(this.data.xAxisLeftText||this.data.xAxisRightText),o=this.config.showYAxis&&!!(this.data.yAxisTopText||this.data.yAxisBottomText),u=this.config.showTitle&&!!this.data.titleText,c=this.data.points.length>0?"bottom":this.config.xAxisPosition,p=this.calculateSpace(c,a,o,u);return{points:this.getQuadrantPoints(p),quadrants:this.getQuadrants(p),axisLabels:this.getAxisLabels(c,a,o,p),borderLines:this.getBorders(p),title:this.getTitle(u)}}},l(yt,"QuadrantBuilder"),yt),Tt,kt=(Tt=class extends Error{constructor(a,o,u){super(`value for ${a} ${o} is invalid, please use a valid ${u}`),this.name="InvalidStyleError"}},l(Tt,"InvalidStyleError"),Tt);function Bt(t){return!/^#?([\dA-Fa-f]{6}|[\dA-Fa-f]{3})$/.test(t)}l(Bt,"validateHexCode");function re(t){return!/^\d+$/.test(t)}l(re,"validateNumber");function oe(t){return!/^\d+px$/.test(t)}l(oe,"validateSizeInPixels");var We=Rt();function O(t){return Le(t.trim(),We)}l(O,"textSanitizer");var z=new Ne;function le(t){z.setData({quadrant1Text:O(t.text)})}l(le,"setQuadrant1Text");function he(t){z.setData({quadrant2Text:O(t.text)})}l(he,"setQuadrant2Text");function ce(t){z.setData({quadrant3Text:O(t.text)})}l(ce,"setQuadrant3Text");function de(t){z.setData({quadrant4Text:O(t.text)})}l(de,"setQuadrant4Text");function ue(t){z.setData({xAxisLeftText:O(t.text)})}l(ue,"setXAxisLeftText");function xe(t){z.setData({xAxisRightText:O(t.text)})}l(xe,"setXAxisRightText");function fe(t){z.setData({yAxisTopText:O(t.text)})}l(fe,"setYAxisTopText");function ge(t){z.setData({yAxisBottomText:O(t.text)})}l(ge,"setYAxisBottomText");function Pt(t){const a={};for(const o of t){const[u,c]=o.trim().split(/\s*:\s*/);if(u==="radius"){if(re(c))throw new kt(u,c,"number");a.radius=parseInt(c)}else if(u==="color"){if(Bt(c))throw new kt(u,c,"hex code");a.color=c}else if(u==="stroke-color"){if(Bt(c))throw new kt(u,c,"hex code");a.strokeColor=c}else if(u==="stroke-width"){if(oe(c))throw new kt(u,c,"number of pixels (eg. 10px)");a.strokeWidth=c}else throw new Error(`style named ${u} is not supported.`)}return a}l(Pt,"parseStyles");function pe(t,a,o,u,c){const p=Pt(c);z.addPoints([U({x:o,y:u,text:O(t.text),className:a},p)])}l(pe,"addPoint");function ye(t,a){z.addClass(t,Pt(a))}l(ye,"addClass");function Te(t){z.setConfig({chartWidth:t})}l(Te,"setWidth");function qe(t){z.setConfig({chartHeight:t})}l(qe,"setHeight");function me(){const t=Rt(),{themeVariables:a,quadrantChart:o}=t;return o&&z.setConfig(o),z.setThemeConfig({quadrant1Fill:a.quadrant1Fill,quadrant2Fill:a.quadrant2Fill,quadrant3Fill:a.quadrant3Fill,quadrant4Fill:a.quadrant4Fill,quadrant1TextFill:a.quadrant1TextFill,quadrant2TextFill:a.quadrant2TextFill,quadrant3TextFill:a.quadrant3TextFill,quadrant4TextFill:a.quadrant4TextFill,quadrantPointFill:a.quadrantPointFill,quadrantPointTextFill:a.quadrantPointTextFill,quadrantXAxisTextFill:a.quadrantXAxisTextFill,quadrantYAxisTextFill:a.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:a.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:a.quadrantInternalBorderStrokeFill,quadrantTitleFill:a.quadrantTitleFill}),z.setData({titleText:se()}),z.build()}l(me,"getQuadrantData");var Qe=l(function(){z.clear(),we()},"clear"),Ue={setWidth:Te,setHeight:qe,setQuadrant1Text:le,setQuadrant2Text:he,setQuadrant3Text:ce,setQuadrant4Text:de,setXAxisLeftText:ue,setXAxisRightText:xe,setYAxisTopText:fe,setYAxisBottomText:ge,parseStyles:Pt,addPoint:pe,addClass:ye,getQuadrantData:me,clear:Qe,setAccTitle:Ee,getAccTitle:De,setDiagramTitle:ze,getDiagramTitle:se,getAccDescription:Ve,setAccDescription:Ie},Oe=l((t,a,o,u)=>{var dt,ut,xt,ft,gt,B;function c(e){return e==="top"?"hanging":"middle"}l(c,"getDominantBaseLine");function p(e){return e==="left"?"start":"middle"}l(p,"getTextAnchor");function y(e){return`translate(${e.x}, ${e.y}) rotate(${e.rotation||0})`}l(y,"getTransformation");const S=Rt();Ft.debug(`Rendering quadrant chart
`+t);const n=S.securityLevel;let A;n==="sandbox"&&(A=It("#i"+a));const m=(n==="sandbox"?It(A.nodes()[0].contentDocument.body):It("body")).select(`[id="${a}"]`),b=m.append("g").attr("class","main"),T=(ut=(dt=S.quadrantChart)==null?void 0:dt.chartWidth)!=null?ut:500,q=(ft=(xt=S.quadrantChart)==null?void 0:xt.chartHeight)!=null?ft:500;Be(m,q,T,(B=(gt=S.quadrantChart)==null?void 0:gt.useMaxWidth)!=null?B:!0),m.attr("viewBox","0 0 "+T+" "+q),u.db.setHeight(q),u.db.setWidth(T);const d=u.db.getQuadrantData(),N=b.append("g").attr("class","quadrants"),Y=b.append("g").attr("class","border"),G=b.append("g").attr("class","data-points"),qt=b.append("g").attr("class","labels"),mt=b.append("g").attr("class","title");d.title&&mt.append("text").attr("x",0).attr("y",0).attr("fill",d.title.fill).attr("font-size",d.title.fontSize).attr("dominant-baseline",c(d.title.horizontalPos)).attr("text-anchor",p(d.title.verticalPos)).attr("transform",y(d.title)).text(d.title.text),d.borderLines&&Y.selectAll("line").data(d.borderLines).enter().append("line").attr("x1",e=>e.x1).attr("y1",e=>e.y1).attr("x2",e=>e.x2).attr("y2",e=>e.y2).style("stroke",e=>e.strokeFill).style("stroke-width",e=>e.strokeWidth);const ht=N.selectAll("g.quadrant").data(d.quadrants).enter().append("g").attr("class","quadrant");ht.append("rect").attr("x",e=>e.x).attr("y",e=>e.y).attr("width",e=>e.width).attr("height",e=>e.height).attr("fill",e=>e.fill),ht.append("text").attr("x",0).attr("y",0).attr("fill",e=>e.text.fill).attr("font-size",e=>e.text.fontSize).attr("dominant-baseline",e=>c(e.text.horizontalPos)).attr("text-anchor",e=>p(e.text.verticalPos)).attr("transform",e=>y(e.text)).text(e=>e.text.text),qt.selectAll("g.label").data(d.axisLabels).enter().append("g").attr("class","label").append("text").attr("x",0).attr("y",0).text(e=>e.text).attr("fill",e=>e.fill).attr("font-size",e=>e.fontSize).attr("dominant-baseline",e=>c(e.horizontalPos)).attr("text-anchor",e=>p(e.verticalPos)).attr("transform",e=>y(e));const ct=G.selectAll("g.data-point").data(d.points).enter().append("g").attr("class","data-point");ct.append("circle").attr("cx",e=>e.x).attr("cy",e=>e.y).attr("r",e=>e.radius).attr("fill",e=>e.fill).attr("stroke",e=>e.strokeColor).attr("stroke-width",e=>e.strokeWidth),ct.append("text").attr("x",0).attr("y",0).text(e=>e.text.text).attr("fill",e=>e.text.fill).attr("font-size",e=>e.text.fontSize).attr("dominant-baseline",e=>c(e.text.horizontalPos)).attr("text-anchor",e=>p(e.text.verticalPos)).attr("transform",e=>y(e.text))},"draw"),He={draw:Oe},Ge={parser:Re,db:Ue,renderer:He,styles:l(()=>"","styles")};export{Ge as diagram};
