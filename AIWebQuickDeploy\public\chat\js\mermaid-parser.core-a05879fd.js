var mu=Object.defineProperty;var gu=(n,e,t)=>e in n?mu(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var Ze=(n,e,t)=>(gu(n,typeof e!="symbol"?e+"":e,t),t);var w=(n,e,t)=>new Promise((r,i)=>{var s=l=>{try{o(t.next(l))}catch(c){i(c)}},a=l=>{try{o(t.throw(l))}catch(c){i(c)}},o=l=>l.done?r(l.value):Promise.resolve(l.value).then(s,a);o((t=t.apply(n,e)).next())});import{bx as yu,by as Tu,aX as Al,be as Ru,a$ as vu,aY as te,b5 as El,ap as Au,aq as Ea,aU as kl,b4 as Eu,b7 as xl,b8 as Sl,bj as ka,as as yt,at as F,aZ as xa,aT as ku,bu as St}from"./chart-vendor-e1d59b84.js";import{k as _t,j as js,g as Gt,l as Il,S as xu,w as Su,x as Iu,y as $u,c as $l,v as q,z as Cl,A as Cu,B as Nu,C as wu,D as _u,a as Nl,d as C,i as Ye,r as le,f as Se,E as X}from"./_baseUniq-5ee25ed9.js";import{j as gi,m as S,d as Lu,f as we,g as Lt,i as zs,h as N,l as Ot,e as Ou}from"./_basePickBy-a1ec2f81.js";import{c as re}from"./clone-92746810.js";var bu=Object.prototype,Pu=bu.hasOwnProperty,Mu=yu(function(n,e){if(Tu(e)||Al(e)){Ru(e,_t(e),n);return}for(var t in e)Pu.call(e,t)&&vu(n,t,e[t])});const xe=Mu;function wl(n,e,t){var r=-1,i=n.length;e<0&&(e=-e>i?0:i+e),t=t>i?i:t,t<0&&(t+=i),i=e>t?0:t-e>>>0,e>>>=0;for(var s=Array(i);++r<i;)s[r]=n[r+e];return s}function Qn(n){for(var e=-1,t=n==null?0:n.length,r=0,i=[];++e<t;){var s=n[e];s&&(i[r++]=s)}return i}function Du(n,e,t,r){for(var i=-1,s=n==null?0:n.length;++i<s;){var a=n[i];e(r,a,t(a),n)}return r}function Fu(n,e,t,r){return js(n,function(i,s,a){e(r,i,t(i),a)}),r}function Gu(n,e){return function(t,r){var i=te(t)?Du:Fu,s=e?e():{};return i(t,n,Gt(r),s)}}var Uu=200;function Bu(n,e,t,r){var i=-1,s=Su,a=!0,o=n.length,l=[],c=e.length;if(!o)return l;t&&(e=Il(e,El(t))),r?(s=Iu,a=!1):e.length>=Uu&&(s=$u,a=!1,e=new xu(e));e:for(;++i<o;){var u=n[i],d=t==null?u:t(u);if(u=r||u!==0?u:0,a&&d===d){for(var h=c;h--;)if(e[h]===d)continue e;l.push(u)}else s(e,d,r)||l.push(u)}return l}var Vu=Au(function(n,e){return Ea(n)?Bu(n,$l(e,1,Ea,!0)):[]});const yi=Vu;function Q(n,e,t){var r=n==null?0:n.length;return r?(e=t||e===void 0?1:gi(e),wl(n,e<0?0:e,r)):[]}function On(n,e,t){var r=n==null?0:n.length;return r?(e=t||e===void 0?1:gi(e),e=r-e,wl(n,0,e<0?0:e)):[]}function Wu(n,e){for(var t=-1,r=n==null?0:n.length;++t<r;)if(!e(n[t],t,n))return!1;return!0}function Ku(n,e){var t=!0;return js(n,function(r,i,s){return t=!!e(r,i,s),t}),t}function Pe(n,e,t){var r=te(n)?Wu:Ku;return t&&kl(n,e,t)&&(e=void 0),r(n,Gt(e))}function Me(n){return n&&n.length?n[0]:void 0}function ke(n,e){return $l(S(n,e),1)}var Hu=Object.prototype,ju=Hu.hasOwnProperty,zu=Gu(function(n,e,t){ju.call(n,t)?n[t].push(e):Eu(n,t,[e])});const qu=zu;var Yu="[object String]";function he(n){return typeof n=="string"||!te(n)&&xl(n)&&Sl(n)==Yu}var Xu=Math.max;function de(n,e,t,r){n=Al(n)?n:q(n),t=t&&!r?gi(t):0;var i=n.length;return t<0&&(t=Xu(i+t,0)),he(n)?t<=i&&n.indexOf(e,t)>-1:!!i&&Cl(n,e,t)>-1}var Ju=Math.max;function Sa(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=t==null?0:gi(t);return i<0&&(i=Ju(r+i,0)),Cl(n,e,i)}var Qu="[object RegExp]";function Zu(n){return xl(n)&&Sl(n)==Qu}var Ia=ka&&ka.isRegExp,ed=Ia?El(Ia):Zu;const Xe=ed;var td="Expected a function";function nd(n){if(typeof n!="function")throw new TypeError(td);return function(){var e=arguments;switch(e.length){case 0:return!n.call(this);case 1:return!n.call(this,e[0]);case 2:return!n.call(this,e[0],e[1]);case 3:return!n.call(this,e[0],e[1],e[2])}return!n.apply(this,e)}}function De(n,e){if(n==null)return{};var t=Il(Cu(n),function(r){return[r]});return e=Gt(e),Lu(n,t,function(r,i){return e(r,i[0])})}function Ti(n,e){var t=te(n)?Nu:wu;return t(n,nd(Gt(e)))}function rd(n,e){var t;return js(n,function(r,i,s){return t=e(r,i,s),!t}),!!t}function _l(n,e,t){var r=te(n)?_u:rd;return t&&kl(n,e,t)&&(e=void 0),r(n,Gt(e))}function qs(n){return n&&n.length?Nl(n):[]}function id(n,e){return n&&n.length?Nl(n,Gt(e)):[]}function oe(n){return typeof n=="object"&&n!==null&&typeof n.$type=="string"}function Be(n){return typeof n=="object"&&n!==null&&typeof n.$refText=="string"}function sd(n){return typeof n=="object"&&n!==null&&typeof n.name=="string"&&typeof n.type=="string"&&typeof n.path=="string"}function wr(n){return typeof n=="object"&&n!==null&&oe(n.container)&&Be(n.reference)&&typeof n.message=="string"}class Ll{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return oe(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});const i=r[t];if(i!==void 0)return i;{const s=this.computeIsSubtype(e,t);return r[t]=s,s}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const r=this.getAllTypes(),i=[];for(const s of r)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}}function bn(n){return typeof n=="object"&&n!==null&&Array.isArray(n.content)}function Ol(n){return typeof n=="object"&&n!==null&&typeof n.tokenType=="object"}function bl(n){return bn(n)&&typeof n.fullText=="string"}class Z{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const e=this.iterator();let t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){const e=[],t=this.iterator();let r;do r=t.next(),r.value!==void 0&&e.push(r.value);while(!r.done);return e}toSet(){return new Set(this)}toMap(e,t){const r=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(r)}toString(){return this.join()}concat(e){return new Z(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),t=>{let r;if(!t.firstDone){do if(r=this.nextFn(t.first),!r.done)return r;while(!r.done);t.firstDone=!0}do if(r=t.iterator.next(),!r.done)return r;while(!r.done);return Ae})}join(e=","){const t=this.iterator();let r="",i,s=!1;do i=t.next(),i.done||(s&&(r+=e),r+=ad(i.value)),s=!0;while(!i.done);return r}indexOf(e,t=0){const r=this.iterator();let i=0,s=r.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=r.next(),i++}return-1}every(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;)e(i.value,r),i=t.next(),r++}map(e){return new Z(this.startFn,t=>{const{done:r,value:i}=this.nextFn(t);return r?Ae:{done:!1,value:e(i)}})}filter(e){return new Z(this.startFn,t=>{let r;do if(r=this.nextFn(t),!r.done&&e(r.value))return r;while(!r.done);return Ae})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){const r=this.iterator();let i=t,s=r.next();for(;!s.done;)i===void 0?i=s.value:i=e(i,s.value),s=r.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){const i=e.next();if(i.done)return r;const s=this.recursiveReduce(e,t,r);return s===void 0?i.value:t(s,i.value)}find(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;){if(e(i.value))return r;i=t.next(),r++}return-1}includes(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new Z(()=>({this:this.startFn()}),t=>{do{if(t.iterator){const s=t.iterator.next();if(s.done)t.iterator=void 0;else return s}const{done:r,value:i}=this.nextFn(t.this);if(!r){const s=e(i);if(Hr(s))t.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}}while(t.iterator);return Ae})}flat(e){if(e===void 0&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new Z(()=>({this:t.startFn()}),r=>{do{if(r.iterator){const a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}const{done:i,value:s}=t.nextFn(r.this);if(!i)if(Hr(s))r.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}while(r.iterator);return Ae})}head(){const t=this.iterator().next();if(!t.done)return t.value}tail(e=1){return new Z(()=>{const t=this.startFn();for(let r=0;r<e;r++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new Z(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?Ae:this.nextFn(t.state)))}distinct(e){return new Z(()=>({set:new Set,internalState:this.startFn()}),t=>{let r;do if(r=this.nextFn(t.internalState),!r.done){const i=e?e(r.value):r.value;if(!t.set.has(i))return t.set.add(i),r}while(!r.done);return Ae})}exclude(e,t){const r=new Set;for(const i of e){const s=t?t(i):i;r.add(s)}return this.filter(i=>{const s=t?t(i):i;return!r.has(s)})}}function ad(n){return typeof n=="string"?n:typeof n=="undefined"?"undefined":typeof n.toString=="function"?n.toString():Object.prototype.toString.call(n)}function Hr(n){return!!n&&typeof n[Symbol.iterator]=="function"}const od=new Z(()=>{},()=>Ae),Ae=Object.freeze({done:!0,value:void 0});function ee(...n){if(n.length===1){const e=n[0];if(e instanceof Z)return e;if(Hr(e))return new Z(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new Z(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:Ae)}return n.length>1?new Z(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<n.length){const t=n[e.collIndex++];Hr(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<n.length);return Ae}):od}class jr extends Z{constructor(e,t,r){super(()=>({iterators:r!=null&&r.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){const a=i.iterators[i.iterators.length-1].next();if(a.done)i.iterators.pop();else return i.iterators.push(t(a.value)[Symbol.iterator]()),a}return Ae})}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}var os;(function(n){function e(s){return s.reduce((a,o)=>a+o,0)}n.sum=e;function t(s){return s.reduce((a,o)=>a*o,0)}n.product=t;function r(s){return s.reduce((a,o)=>Math.min(a,o))}n.min=r;function i(s){return s.reduce((a,o)=>Math.max(a,o))}n.max=i})(os||(os={}));function ls(n){return new jr(n,e=>bn(e)?e.content:[],{includeRoot:!0})}function ld(n,e){for(;n.container;)if(n=n.container,n===e)return!0;return!1}function cs(n){return{start:{character:n.startColumn-1,line:n.startLine-1},end:{character:n.endColumn,line:n.endLine-1}}}function zr(n){if(!n)return;const{offset:e,end:t,range:r}=n;return{range:r,offset:e,end:t,length:t-e}}var je;(function(n){n[n.Before=0]="Before",n[n.After=1]="After",n[n.OverlapFront=2]="OverlapFront",n[n.OverlapBack=3]="OverlapBack",n[n.Inside=4]="Inside",n[n.Outside=5]="Outside"})(je||(je={}));function cd(n,e){if(n.end.line<e.start.line||n.end.line===e.start.line&&n.end.character<=e.start.character)return je.Before;if(n.start.line>e.end.line||n.start.line===e.end.line&&n.start.character>=e.end.character)return je.After;const t=n.start.line>e.start.line||n.start.line===e.start.line&&n.start.character>=e.start.character,r=n.end.line<e.end.line||n.end.line===e.end.line&&n.end.character<=e.end.character;return t&&r?je.Inside:t?je.OverlapBack:r?je.OverlapFront:je.Outside}function ud(n,e){return cd(n,e)>je.After}const dd=/^[\w\p{L}]$/u;function fd(n,e){if(n){const t=hd(n,!0);if(t&&$a(t,e))return t;if(bl(n)){const r=n.content.findIndex(i=>!i.hidden);for(let i=r-1;i>=0;i--){const s=n.content[i];if($a(s,e))return s}}}}function $a(n,e){return Ol(n)&&e.includes(n.tokenType.name)}function hd(n,e=!0){for(;n.container;){const t=n.container;let r=t.content.indexOf(n);for(;r>0;){r--;const i=t.content[r];if(e||!i.hidden)return i}n=t}}class Pl extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function Zn(n){throw new Error("Error! The input value was not handled.")}const ar="AbstractRule",or="AbstractType",Pi="Condition",Ca="TypeDefinition",Mi="ValueLiteral",Yt="AbstractElement";function pd(n){return D.isInstance(n,Yt)}const lr="ArrayLiteral",cr="ArrayType",Xt="BooleanLiteral";function md(n){return D.isInstance(n,Xt)}const Jt="Conjunction";function gd(n){return D.isInstance(n,Jt)}const Qt="Disjunction";function yd(n){return D.isInstance(n,Qt)}const ur="Grammar",Di="GrammarImport",Zt="InferredType";function Ml(n){return D.isInstance(n,Zt)}const en="Interface";function Dl(n){return D.isInstance(n,en)}const Fi="NamedArgument",tn="Negation";function Td(n){return D.isInstance(n,tn)}const dr="NumberLiteral",fr="Parameter",nn="ParameterReference";function Rd(n){return D.isInstance(n,nn)}const rn="ParserRule";function _e(n){return D.isInstance(n,rn)}const hr="ReferenceType",_r="ReturnType";function vd(n){return D.isInstance(n,_r)}const sn="SimpleType";function Ad(n){return D.isInstance(n,sn)}const pr="StringLiteral",$t="TerminalRule";function Tt(n){return D.isInstance(n,$t)}const an="Type";function Fl(n){return D.isInstance(n,an)}const Gi="TypeAttribute",mr="UnionType",on="Action";function Ri(n){return D.isInstance(n,on)}const ln="Alternatives";function Gl(n){return D.isInstance(n,ln)}const cn="Assignment";function dt(n){return D.isInstance(n,cn)}const un="CharacterRange";function Ed(n){return D.isInstance(n,un)}const dn="CrossReference";function Ys(n){return D.isInstance(n,dn)}const fn="EndOfFile";function kd(n){return D.isInstance(n,fn)}const hn="Group";function Xs(n){return D.isInstance(n,hn)}const pn="Keyword";function ft(n){return D.isInstance(n,pn)}const mn="NegatedToken";function xd(n){return D.isInstance(n,mn)}const gn="RegexToken";function Sd(n){return D.isInstance(n,gn)}const yn="RuleCall";function ht(n){return D.isInstance(n,yn)}const Tn="TerminalAlternatives";function Id(n){return D.isInstance(n,Tn)}const Rn="TerminalGroup";function $d(n){return D.isInstance(n,Rn)}const vn="TerminalRuleCall";function Ul(n){return D.isInstance(n,vn)}const An="UnorderedGroup";function Bl(n){return D.isInstance(n,An)}const En="UntilToken";function Cd(n){return D.isInstance(n,En)}const kn="Wildcard";function Nd(n){return D.isInstance(n,kn)}class Vl extends Ll{getAllTypes(){return[Yt,ar,or,on,ln,lr,cr,cn,Xt,un,Pi,Jt,dn,Qt,fn,ur,Di,hn,Zt,en,pn,Fi,mn,tn,dr,fr,nn,rn,hr,gn,_r,yn,sn,pr,Tn,Rn,$t,vn,an,Gi,Ca,mr,An,En,Mi,kn]}computeIsSubtype(e,t){switch(e){case on:case ln:case cn:case un:case dn:case fn:case hn:case pn:case mn:case gn:case yn:case Tn:case Rn:case vn:case An:case En:case kn:return this.isSubtype(Yt,t);case lr:case dr:case pr:return this.isSubtype(Mi,t);case cr:case hr:case sn:case mr:return this.isSubtype(Ca,t);case Xt:return this.isSubtype(Pi,t)||this.isSubtype(Mi,t);case Jt:case Qt:case tn:case nn:return this.isSubtype(Pi,t);case Zt:case en:case an:return this.isSubtype(or,t);case rn:return this.isSubtype(ar,t)||this.isSubtype(or,t);case $t:return this.isSubtype(ar,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return or;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return ar;case"Grammar:usedGrammars":return ur;case"NamedArgument:parameter":case"ParameterReference:parameter":return fr;case"TerminalRuleCall:rule":return $t;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Yt:return{name:Yt,properties:[{name:"cardinality"},{name:"lookahead"}]};case lr:return{name:lr,properties:[{name:"elements",defaultValue:[]}]};case cr:return{name:cr,properties:[{name:"elementType"}]};case Xt:return{name:Xt,properties:[{name:"true",defaultValue:!1}]};case Jt:return{name:Jt,properties:[{name:"left"},{name:"right"}]};case Qt:return{name:Qt,properties:[{name:"left"},{name:"right"}]};case ur:return{name:ur,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case Di:return{name:Di,properties:[{name:"path"}]};case Zt:return{name:Zt,properties:[{name:"name"}]};case en:return{name:en,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case Fi:return{name:Fi,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case tn:return{name:tn,properties:[{name:"value"}]};case dr:return{name:dr,properties:[{name:"value"}]};case fr:return{name:fr,properties:[{name:"name"}]};case nn:return{name:nn,properties:[{name:"parameter"}]};case rn:return{name:rn,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case hr:return{name:hr,properties:[{name:"referenceType"}]};case _r:return{name:_r,properties:[{name:"name"}]};case sn:return{name:sn,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case pr:return{name:pr,properties:[{name:"value"}]};case $t:return{name:$t,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case an:return{name:an,properties:[{name:"name"},{name:"type"}]};case Gi:return{name:Gi,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case mr:return{name:mr,properties:[{name:"types",defaultValue:[]}]};case on:return{name:on,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case ln:return{name:ln,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case cn:return{name:cn,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case un:return{name:un,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case dn:return{name:dn,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case fn:return{name:fn,properties:[{name:"cardinality"},{name:"lookahead"}]};case hn:return{name:hn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case pn:return{name:pn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case mn:return{name:mn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case gn:return{name:gn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case yn:return{name:yn,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Tn:return{name:Tn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Rn:return{name:Rn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case vn:return{name:vn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case An:return{name:An,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case En:return{name:En,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case kn:return{name:kn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}const D=new Vl;function wd(n){for(const[e,t]of Object.entries(n))e.startsWith("$")||(Array.isArray(t)?t.forEach((r,i)=>{oe(r)&&(r.$container=n,r.$containerProperty=e,r.$containerIndex=i)}):oe(t)&&(t.$container=n,t.$containerProperty=e))}function vi(n,e){let t=n;for(;t;){if(e(t))return t;t=t.$container}}function tt(n){const t=us(n).$document;if(!t)throw new Error("AST node has no document.");return t}function us(n){for(;n.$container;)n=n.$container;return n}function Js(n,e){if(!n)throw new Error("Node must be an AstNode.");const t=e==null?void 0:e.range;return new Z(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),r=>{for(;r.keyIndex<r.keys.length;){const i=r.keys[r.keyIndex];if(!i.startsWith("$")){const s=n[i];if(oe(s)){if(r.keyIndex++,ds(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;r.arrayIndex<s.length;){const a=r.arrayIndex++,o=s[a];if(oe(o)&&ds(o,t))return{done:!1,value:o}}r.arrayIndex=0}}r.keyIndex++}return Ae})}function er(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new jr(n,t=>Js(t,e))}function Nt(n,e){if(n){if(e!=null&&e.range&&!ds(n,e.range))return new jr(n,()=>[])}else throw new Error("Root node must be an AstNode.");return new jr(n,t=>Js(t,e),{includeRoot:!0})}function ds(n,e){var t;if(!e)return!0;const r=(t=n.$cstNode)===null||t===void 0?void 0:t.range;return r?ud(r,e):!1}function Wl(n){return new Z(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){const t=e.keys[e.keyIndex];if(!t.startsWith("$")){const r=n[t];if(Be(r))return e.keyIndex++,{done:!1,value:{reference:r,container:n,property:t}};if(Array.isArray(r)){for(;e.arrayIndex<r.length;){const i=e.arrayIndex++,s=r[i];if(Be(s))return{done:!1,value:{reference:s,container:n,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Ae})}function _d(n,e){const t=n.getTypeMetaData(e.$type),r=e;for(const i of t.properties)i.defaultValue!==void 0&&r[i.name]===void 0&&(r[i.name]=Kl(i.defaultValue))}function Kl(n){return Array.isArray(n)?[...n.map(Kl)]:n}function _(n){return n.charCodeAt(0)}function Ui(n,e){Array.isArray(n)?n.forEach(function(t){e.push(t)}):e.push(n)}function jt(n,e){if(n[e]===!0)throw"duplicate flag "+e;n[e],n[e]=!0}function It(n){if(n===void 0)throw Error("Internal Error - Should never get here!");return!0}function Ld(){throw Error("Internal Error - Should never get here!")}function Na(n){return n.type==="Character"}const qr=[];for(let n=_("0");n<=_("9");n++)qr.push(n);const Yr=[_("_")].concat(qr);for(let n=_("a");n<=_("z");n++)Yr.push(n);for(let n=_("A");n<=_("Z");n++)Yr.push(n);const wa=[_(" "),_("\f"),_(`
`),_("\r"),_("	"),_("\v"),_("	"),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_(" "),_("\u2028"),_("\u2029"),_(" "),_(" "),_("　"),_("\uFEFF")],Od=/[0-9a-fA-F]/,gr=/[0-9]/,bd=/[1-9]/;class Hl{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":jt(r,"global");break;case"i":jt(r,"ignoreCase");break;case"m":jt(r,"multiLine");break;case"u":jt(r,"unicode");break;case"y":jt(r,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}It(t);const r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return Ld()}quantifier(e=!1){let t;const r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&t===void 0)return;It(t);break}if(!(e===!0&&t===void 0)&&It(t))return this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}if(e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),It(e))return e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[_(`
`),_("\r"),_("\u2028"),_("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=qr;break;case"D":e=qr,t=!0;break;case"s":e=wa;break;case"S":e=wa,t=!0;break;case"w":e=Yr;break;case"W":e=Yr,t=!0;break}if(It(e))return{type:"Set",value:e,complement:t}}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=_("\f");break;case"n":e=_(`
`);break;case"r":e=_("\r");break;case"t":e=_("	");break;case"v":e=_("\v");break}if(It(e))return{type:"Character",value:e}}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:_("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){const e=this.popChar();return{type:"Character",value:_(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const e=this.popChar();return{type:"Character",value:_(e)}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const r=this.classAtom();if(r.type,Na(r)&&this.isRangeDash()){this.consumeChar("-");const i=this.classAtom();if(i.type,Na(i)){if(i.value<r.value)throw Error("Range out of order in character class");e.push({from:r.value,to:i.value})}else Ui(r.value,e),e.push(_("-")),Ui(i.value,e)}else Ui(r.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:_("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}const t=this.disjunction();this.consumeChar(")");const r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(bd.test(e)===!1)throw Error("Expecting a positive integer");for(;gr.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(gr.test(e)===!1)throw Error("Expecting an integer");for(;gr.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:_(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return gr.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const e=this.saveState();try{return this.quantifier(!0)!==void 0}catch(t){return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let i=0;i<e;i++){const s=this.popChar();if(Od.test(s)===!1)throw Error("Expecting a HexDecimal digits");t+=s}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class Ai{visitChildren(e){for(const t in e){const r=e[t];e.hasOwnProperty(t)&&(r.type!==void 0?this.visit(r):Array.isArray(r)&&r.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}const Pd=/\r?\n/gm,Md=new Hl;class Dd extends Ai{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){const t=String.fromCharCode(e.value);if(!this.multiline&&t===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const r=Ei(t);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end),r=new RegExp(t);this.multiline=!!`
`.match(r)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}}const Bi=new Dd;function Fd(n){try{return typeof n=="string"&&(n=new RegExp(n)),n=n.toString(),Bi.reset(n),Bi.visit(Md.pattern(n)),Bi.multiline}catch(e){return!1}}const Gd=`\f
\r	\v              \u2028\u2029  　\uFEFF`.split("");function fs(n){const e=typeof n=="string"?new RegExp(n):n;return Gd.some(t=>e.test(t))}function Ei(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Ud(n){return Array.prototype.map.call(n,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:Ei(e)).join("")}function Bd(n,e){const t=Vd(n),r=e.match(t);return!!r&&r[0].length>0}function Vd(n){typeof n=="string"&&(n=new RegExp(n));const e=n,t=n.source;let r=0;function i(){let s="",a;function o(c){s+=t.substr(r,c),r+=c}function l(c){s+="(?:"+t.substr(r,c)+"|$)",r+=c}for(;r<t.length;)switch(t[r]){case"\\":switch(t[r+1]){case"c":l(3);break;case"x":l(4);break;case"u":e.unicode?t[r+2]==="{"?l(t.indexOf("}",r)-r+1):l(6):l(2);break;case"p":case"P":e.unicode?l(t.indexOf("}",r)-r+1):l(2);break;case"k":l(t.indexOf(">",r)-r+1);break;default:l(2);break}break;case"[":a=/\[(?:\\.|.)*?\]/g,a.lastIndex=r,a=a.exec(t)||[],l(a[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":o(1);break;case"{":a=/\{\d+,?\d*\}/g,a.lastIndex=r,a=a.exec(t),a?o(a[0].length):l(1);break;case"(":if(t[r+1]==="?")switch(t[r+2]){case":":s+="(?:",r+=3,s+=i()+"|$)";break;case"=":s+="(?=",r+=3,s+=i()+")";break;case"!":a=r,r+=3,i(),s+=t.substr(a,r-a);break;case"<":switch(t[r+3]){case"=":case"!":a=r,r+=4,i(),s+=t.substr(a,r-a);break;default:o(t.indexOf(">",r)-r+1),s+=i()+"|$)";break}break}else o(1),s+=i()+"|$)";break;case")":return++r,s;default:l(1);break}return s}return new RegExp(i(),n.flags)}function Wd(n){return n.rules.find(e=>_e(e)&&e.entry)}function Kd(n){return n.rules.filter(e=>Tt(e)&&e.hidden)}function jl(n,e){const t=new Set,r=Wd(n);if(!r)return new Set(n.rules);const i=[r].concat(Kd(n));for(const a of i)zl(a,t,e);const s=new Set;for(const a of n.rules)(t.has(a.name)||Tt(a)&&a.hidden)&&s.add(a);return s}function zl(n,e,t){e.add(n.name),er(n).forEach(r=>{if(ht(r)||t&&Ul(r)){const i=r.rule.ref;i&&!e.has(i.name)&&zl(i,e,t)}})}function Hd(n){if(n.terminal)return n.terminal;if(n.type.ref){const e=Yl(n.type.ref);return e==null?void 0:e.terminal}}function jd(n){return n.hidden&&!fs(ta(n))}function zd(n,e){return!n||!e?[]:Qs(n,e,n.astNode,!0)}function ql(n,e,t){if(!n||!e)return;const r=Qs(n,e,n.astNode,!0);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function Qs(n,e,t,r){if(!r){const i=vi(n.grammarSource,dt);if(i&&i.feature===e)return[n]}return bn(n)&&n.astNode===t?n.content.flatMap(i=>Qs(i,e,t,!1)):[]}function qd(n,e,t){if(!n)return;const r=Yd(n,e,n==null?void 0:n.astNode);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function Yd(n,e,t){if(n.astNode!==t)return[];if(ft(n.grammarSource)&&n.grammarSource.value===e)return[n];const r=ls(n).iterator();let i;const s=[];do if(i=r.next(),!i.done){const a=i.value;a.astNode===t?ft(a.grammarSource)&&a.grammarSource.value===e&&s.push(a):r.prune()}while(!i.done);return s}function Xd(n){var e;const t=n.astNode;for(;t===((e=n.container)===null||e===void 0?void 0:e.astNode);){const r=vi(n.grammarSource,dt);if(r)return r;n=n.container}}function Yl(n){let e=n;return Ml(e)&&(Ri(e.$container)?e=e.$container.$container:_e(e.$container)?e=e.$container:Zn(e.$container)),Xl(n,e,new Map)}function Xl(n,e,t){var r;function i(s,a){let o;return vi(s,dt)||(o=Xl(a,a,t)),t.set(n,o),o}if(t.has(n))return t.get(n);t.set(n,void 0);for(const s of er(e)){if(dt(s)&&s.feature.toLowerCase()==="name")return t.set(n,s),s;if(ht(s)&&_e(s.rule.ref))return i(s,s.rule.ref);if(Ad(s)&&(!((r=s.typeRef)===null||r===void 0)&&r.ref))return i(s,s.typeRef.ref)}}function Jl(n){return Ql(n,new Set)}function Ql(n,e){if(e.has(n))return!0;e.add(n);for(const t of er(n))if(ht(t)){if(!t.rule.ref||_e(t.rule.ref)&&!Ql(t.rule.ref,e))return!1}else{if(dt(t))return!1;if(Ri(t))return!1}return!!n.definition}function Zs(n){if(n.inferredType)return n.inferredType.name;if(n.dataType)return n.dataType;if(n.returnType){const e=n.returnType.ref;if(e){if(_e(e))return e.name;if(Dl(e)||Fl(e))return e.name}}}function ea(n){var e;if(_e(n))return Jl(n)?n.name:(e=Zs(n))!==null&&e!==void 0?e:n.name;if(Dl(n)||Fl(n)||vd(n))return n.name;if(Ri(n)){const t=Jd(n);if(t)return t}else if(Ml(n))return n.name;throw new Error("Cannot get name of Unknown Type")}function Jd(n){var e;if(n.inferredType)return n.inferredType.name;if(!((e=n.type)===null||e===void 0)&&e.ref)return ea(n.type.ref)}function Qd(n){var e,t,r;return Tt(n)?(t=(e=n.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":(r=Zs(n))!==null&&r!==void 0?r:n.name}function ta(n){const e={s:!1,i:!1,u:!1},t=Ut(n.definition,e),r=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,r)}const na=/[\s\S]/.source;function Ut(n,e){if(Id(n))return Zd(n);if($d(n))return ef(n);if(Ed(n))return rf(n);if(Ul(n)){const t=n.rule.ref;if(!t)throw new Error("Missing rule reference.");return qe(Ut(t.definition),{cardinality:n.cardinality,lookahead:n.lookahead})}else{if(xd(n))return nf(n);if(Cd(n))return tf(n);if(Sd(n)){const t=n.regex.lastIndexOf("/"),r=n.regex.substring(1,t),i=n.regex.substring(t+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),qe(r,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}else{if(Nd(n))return qe(na,{cardinality:n.cardinality,lookahead:n.lookahead});throw new Error(`Invalid terminal element: ${n==null?void 0:n.$type}`)}}}function Zd(n){return qe(n.elements.map(e=>Ut(e)).join("|"),{cardinality:n.cardinality,lookahead:n.lookahead})}function ef(n){return qe(n.elements.map(e=>Ut(e)).join(""),{cardinality:n.cardinality,lookahead:n.lookahead})}function tf(n){return qe(`${na}*?${Ut(n.terminal)}`,{cardinality:n.cardinality,lookahead:n.lookahead})}function nf(n){return qe(`(?!${Ut(n.terminal)})${na}*?`,{cardinality:n.cardinality,lookahead:n.lookahead})}function rf(n){return n.right?qe(`[${Vi(n.left)}-${Vi(n.right)}]`,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1}):qe(Vi(n.left),{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}function Vi(n){return Ei(n.value)}function qe(n,e){var t;return(e.wrap!==!1||e.lookahead)&&(n=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${n})`),e.cardinality?`${n}${e.cardinality}`:n}function sf(n){const e=[],t=n.Grammar;for(const r of t.rules)Tt(r)&&jd(r)&&Fd(ta(r))&&e.push(r.name);return{multilineCommentRules:e,nameRegexp:dd}}function Zl(n){const e=new Date().getTime(),t=n();return{time:new Date().getTime()-e,value:t}}function ec(n){function e(){}e.prototype=n;const t=new e;function r(){return typeof t.bar}return r(),r(),n}function af(n){return of(n)?n.LABEL:n.name}function of(n){return he(n.LABEL)&&n.LABEL!==""}class Ve{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),C(this.definition,t=>{t.accept(e)})}}class ce extends Ve{constructor(e){super([]),this.idx=1,xe(this,De(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class Bt extends Ve{constructor(e){super(e.definition),this.orgText="",xe(this,De(e,t=>t!==void 0))}}class pe extends Ve{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,xe(this,De(e,t=>t!==void 0))}}let ne=class extends Ve{constructor(e){super(e.definition),this.idx=1,xe(this,De(e,t=>t!==void 0))}};class Ie extends Ve{constructor(e){super(e.definition),this.idx=1,xe(this,De(e,t=>t!==void 0))}}class $e extends Ve{constructor(e){super(e.definition),this.idx=1,xe(this,De(e,t=>t!==void 0))}}class H extends Ve{constructor(e){super(e.definition),this.idx=1,xe(this,De(e,t=>t!==void 0))}}class ge extends Ve{constructor(e){super(e.definition),this.idx=1,xe(this,De(e,t=>t!==void 0))}}class ye extends Ve{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,xe(this,De(e,t=>t!==void 0))}}class U{constructor(e){this.idx=1,xe(this,De(e,t=>t!==void 0))}accept(e){e.visit(this)}}function lf(n){return S(n,Lr)}function Lr(n){function e(t){return S(t,Lr)}if(n instanceof ce){const t={type:"NonTerminal",name:n.nonTerminalName,idx:n.idx};return he(n.label)&&(t.label=n.label),t}else{if(n instanceof pe)return{type:"Alternative",definition:e(n.definition)};if(n instanceof ne)return{type:"Option",idx:n.idx,definition:e(n.definition)};if(n instanceof Ie)return{type:"RepetitionMandatory",idx:n.idx,definition:e(n.definition)};if(n instanceof $e)return{type:"RepetitionMandatoryWithSeparator",idx:n.idx,separator:Lr(new U({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof ge)return{type:"RepetitionWithSeparator",idx:n.idx,separator:Lr(new U({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof H)return{type:"Repetition",idx:n.idx,definition:e(n.definition)};if(n instanceof ye)return{type:"Alternation",idx:n.idx,definition:e(n.definition)};if(n instanceof U){const t={type:"Terminal",name:n.terminalType.name,label:af(n.terminalType),idx:n.idx};he(n.label)&&(t.terminalLabel=n.label);const r=n.terminalType.PATTERN;return n.terminalType.PATTERN&&(t.pattern=Xe(r)?r.source:r),t}else{if(n instanceof Bt)return{type:"Rule",name:n.name,orgText:n.orgText,definition:e(n.definition)};throw Error("non exhaustive match")}}}class Vt{visit(e){const t=e;switch(t.constructor){case ce:return this.visitNonTerminal(t);case pe:return this.visitAlternative(t);case ne:return this.visitOption(t);case Ie:return this.visitRepetitionMandatory(t);case $e:return this.visitRepetitionMandatoryWithSeparator(t);case ge:return this.visitRepetitionWithSeparator(t);case H:return this.visitRepetition(t);case ye:return this.visitAlternation(t);case U:return this.visitTerminal(t);case Bt:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}function cf(n){return n instanceof pe||n instanceof ne||n instanceof H||n instanceof Ie||n instanceof $e||n instanceof ge||n instanceof U||n instanceof Bt}function Xr(n,e=[]){return n instanceof ne||n instanceof H||n instanceof ge?!0:n instanceof ye?_l(n.definition,r=>Xr(r,e)):n instanceof ce&&de(e,n)?!1:n instanceof Ve?(n instanceof ce&&e.push(n),Pe(n.definition,r=>Xr(r,e))):!1}function uf(n){return n instanceof ye}function Ue(n){if(n instanceof ce)return"SUBRULE";if(n instanceof ne)return"OPTION";if(n instanceof ye)return"OR";if(n instanceof Ie)return"AT_LEAST_ONE";if(n instanceof $e)return"AT_LEAST_ONE_SEP";if(n instanceof ge)return"MANY_SEP";if(n instanceof H)return"MANY";if(n instanceof U)return"CONSUME";throw Error("non exhaustive match")}class ki{walk(e,t=[]){C(e.definition,(r,i)=>{const s=Q(e.definition,i+1);if(r instanceof ce)this.walkProdRef(r,s,t);else if(r instanceof U)this.walkTerminal(r,s,t);else if(r instanceof pe)this.walkFlat(r,s,t);else if(r instanceof ne)this.walkOption(r,s,t);else if(r instanceof Ie)this.walkAtLeastOne(r,s,t);else if(r instanceof $e)this.walkAtLeastOneSep(r,s,t);else if(r instanceof ge)this.walkManySep(r,s,t);else if(r instanceof H)this.walkMany(r,s,t);else if(r instanceof ye)this.walkOr(r,s,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){const i=t.concat(r);this.walk(e,i)}walkOption(e,t,r){const i=t.concat(r);this.walk(e,i)}walkAtLeastOne(e,t,r){const i=[new ne({definition:e.definition})].concat(t,r);this.walk(e,i)}walkAtLeastOneSep(e,t,r){const i=_a(e,t,r);this.walk(e,i)}walkMany(e,t,r){const i=[new ne({definition:e.definition})].concat(t,r);this.walk(e,i)}walkManySep(e,t,r){const i=_a(e,t,r);this.walk(e,i)}walkOr(e,t,r){const i=t.concat(r);C(e.definition,s=>{const a=new pe({definition:[s]});this.walk(a,i)})}}function _a(n,e,t){return[new ne({definition:[new U({terminalType:n.separator})].concat(n.definition)})].concat(e,t)}function tr(n){if(n instanceof ce)return tr(n.referencedRule);if(n instanceof U)return hf(n);if(cf(n))return df(n);if(uf(n))return ff(n);throw Error("non exhaustive match")}function df(n){let e=[];const t=n.definition;let r=0,i=t.length>r,s,a=!0;for(;i&&a;)s=t[r],a=Xr(s),e=e.concat(tr(s)),r=r+1,i=t.length>r;return qs(e)}function ff(n){const e=S(n.definition,t=>tr(t));return qs(we(e))}function hf(n){return[n.terminalType]}const tc="_~IN~_";class pf extends ki{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){const i=gf(e.referencedRule,e.idx)+this.topProd.name,s=t.concat(r),a=new pe({definition:s}),o=tr(a);this.follows[i]=o}}function mf(n){const e={};return C(n,t=>{const r=new pf(t).startWalking();xe(e,r)}),e}function gf(n,e){return n.name+e+tc}let Or={};const yf=new Hl;function xi(n){const e=n.toString();if(Or.hasOwnProperty(e))return Or[e];{const t=yf.pattern(e);return Or[e]=t,t}}function Tf(){Or={}}const nc="Complement Sets are not supported for first char optimization",Jr=`Unable to use "first char" lexer optimizations:
`;function Rf(n,e=!1){try{const t=xi(n);return hs(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===nc)e&&(`${Jr}${n.toString()}`,void 0);else{let r="";e&&(r=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),`${Jr}
	Failed parsing: < ${n.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r}}return[]}function hs(n,e,t){switch(n.type){case"Disjunction":for(let i=0;i<n.value.length;i++)hs(n.value[i],e,t);break;case"Alternative":const r=n.value;for(let i=0;i<r.length;i++){const s=r[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":yr(a.value,e,t);break;case"Set":if(a.complement===!0)throw Error(nc);C(a.value,l=>{if(typeof l=="number")yr(l,e,t);else{const c=l;if(t===!0)for(let u=c.from;u<=c.to;u++)yr(u,e,t);else{for(let u=c.from;u<=c.to&&u<Sn;u++)yr(u,e,t);if(c.to>=Sn){const u=c.from>=Sn?c.from:Sn,d=c.to,h=nt(u),f=nt(d);for(let m=h;m<=f;m++)e[m]=m}}}});break;case"Group":hs(a.value,e,t);break;default:throw Error("Non Exhaustive Match")}const o=a.quantifier!==void 0&&a.quantifier.atLeast===0;if(a.type==="Group"&&ps(a)===!1||a.type!=="Group"&&o===!1)break}break;default:throw Error("non exhaustive match!")}return q(e)}function yr(n,e,t){const r=nt(n);e[r]=r,t===!0&&vf(n,e)}function vf(n,e){const t=String.fromCharCode(n),r=t.toUpperCase();if(r!==t){const i=nt(r.charCodeAt(0));e[i]=i}else{const i=t.toLowerCase();if(i!==t){const s=nt(i.charCodeAt(0));e[s]=s}}}function La(n,e){return Lt(n.value,t=>{if(typeof t=="number")return de(e,t);{const r=t;return Lt(e,i=>r.from<=i&&i<=r.to)!==void 0}})}function ps(n){const e=n.quantifier;return e&&e.atLeast===0?!0:n.value?te(n.value)?Pe(n.value,ps):ps(n.value):!1}class Af extends Ai{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){de(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?La(e,this.targetCharCodes)===void 0&&(this.found=!0):La(e,this.targetCharCodes)!==void 0&&(this.found=!0)}}function ra(n,e){if(e instanceof RegExp){const t=xi(e),r=new Af(n);return r.visit(t),r.found}else return Lt(e,t=>de(n,t.charCodeAt(0)))!==void 0}const pt="PATTERN",xn="defaultMode",Tr="modes";let rc=typeof new RegExp("(?:)").sticky=="boolean";function Ef(n,e){e=zs(e,{useSticky:rc,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(A,R)=>R()});const t=e.tracer;t("initCharCodeToOptimizedIndexMap",()=>{Hf()});let r;t("Reject Lexer.NA",()=>{r=Ti(n,A=>A[pt]===fe.NA)});let i=!1,s;t("Transform Patterns",()=>{i=!1,s=S(r,A=>{const R=A[pt];if(Xe(R)){const $=R.source;return $.length===1&&$!=="^"&&$!=="$"&&$!=="."&&!R.ignoreCase?$:$.length===2&&$[0]==="\\"&&!de(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],$[1])?$[1]:e.useSticky?ba(R):Oa(R)}else{if(yt(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{const $=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),G=new RegExp($);return e.useSticky?ba(G):Oa(G)}}else throw Error("non exhaustive match")}})});let a,o,l,c,u;t("misc mapping",()=>{a=S(r,A=>A.tokenTypeIdx),o=S(r,A=>{const R=A.GROUP;if(R!==fe.SKIPPED){if(he(R))return R;if(Ye(R))return!1;throw Error("non exhaustive match")}}),l=S(r,A=>{const R=A.LONGER_ALT;if(R)return te(R)?S(R,G=>Sa(r,G)):[Sa(r,R)]}),c=S(r,A=>A.PUSH_MODE),u=S(r,A=>N(A,"POP_MODE"))});let d;t("Line Terminator Handling",()=>{const A=ac(e.lineTerminatorCharacters);d=S(r,R=>!1),e.positionTracking!=="onlyOffset"&&(d=S(r,R=>N(R,"LINE_BREAKS")?!!R.LINE_BREAKS:sc(R,A)===!1&&ra(A,R.PATTERN)))});let h,f,m,g;t("Misc Mapping #2",()=>{h=S(r,ic),f=S(s,Vf),m=le(r,(A,R)=>{const $=R.GROUP;return he($)&&$!==fe.SKIPPED&&(A[$]=[]),A},{}),g=S(s,(A,R)=>({pattern:s[R],longerAlt:l[R],canLineTerminator:d[R],isCustom:h[R],short:f[R],group:o[R],push:c[R],pop:u[R],tokenTypeIdx:a[R],tokenType:r[R]}))});let v=!0,y=[];return e.safeMode||t("First Char Optimization",()=>{y=le(r,(A,R,$)=>{if(typeof R.PATTERN=="string"){const G=R.PATTERN.charCodeAt(0),ie=nt(G);Wi(A,ie,g[$])}else if(te(R.START_CHARS_HINT)){let G;C(R.START_CHARS_HINT,ie=>{const Le=typeof ie=="string"?ie.charCodeAt(0):ie,Te=nt(Le);G!==Te&&(G=Te,Wi(A,Te,g[$]))})}else if(Xe(R.PATTERN))if(R.PATTERN.unicode)v=!1,e.ensureOptimizations&&(`${Jr}${R.PATTERN.toString()}`,void 0);else{const G=Rf(R.PATTERN,e.ensureOptimizations);F(G)&&(v=!1),C(G,ie=>{Wi(A,ie,g[$])})}else e.ensureOptimizations&&(`${Jr}${R.name}`,void 0),v=!1;return A},[])}),{emptyGroups:m,patternIdxToConfig:g,charCodeToPatternIdxToConfig:y,hasCustom:i,canBeOptimized:v}}function kf(n,e){let t=[];const r=Sf(n);t=t.concat(r.errors);const i=If(r.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(xf(s)),t=t.concat(bf(s)),t=t.concat(Pf(s,e)),t=t.concat(Mf(s)),t}function xf(n){let e=[];const t=Se(n,r=>Xe(r[pt]));return e=e.concat(Cf(t)),e=e.concat(_f(t)),e=e.concat(Lf(t)),e=e.concat(Of(t)),e=e.concat(Nf(t)),e}function Sf(n){const e=Se(n,i=>!N(i,pt)),t=S(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:j.MISSING_PATTERN,tokenTypes:[i]})),r=yi(n,e);return{errors:t,valid:r}}function If(n){const e=Se(n,i=>{const s=i[pt];return!Xe(s)&&!yt(s)&&!N(s,"exec")&&!he(s)}),t=S(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:j.INVALID_PATTERN,tokenTypes:[i]})),r=yi(n,e);return{errors:t,valid:r}}const $f=/[^\\][$]/;function Cf(n){class e extends Ai{constructor(){super(...arguments),this.found=!1}visitEndAnchor(s){this.found=!0}}const t=Se(n,i=>{const s=i.PATTERN;try{const a=xi(s),o=new e;return o.visit(a),o.found}catch(a){return $f.test(s.source)}});return S(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}function Nf(n){const e=Se(n,r=>r.PATTERN.test(""));return S(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' must not match an empty string",type:j.EMPTY_MATCH_PATTERN,tokenTypes:[r]}))}const wf=/[^\\[][\^]|^\^/;function _f(n){class e extends Ai{constructor(){super(...arguments),this.found=!1}visitStartAnchor(s){this.found=!0}}const t=Se(n,i=>{const s=i.PATTERN;try{const a=xi(s),o=new e;return o.visit(a),o.found}catch(a){return wf.test(s.source)}});return S(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:j.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}function Lf(n){const e=Se(n,r=>{const i=r[pt];return i instanceof RegExp&&(i.multiline||i.global)});return S(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:j.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[r]}))}function Of(n){const e=[];let t=S(n,s=>le(n,(a,o)=>(s.PATTERN.source===o.PATTERN.source&&!de(e,o)&&o.PATTERN!==fe.NA&&(e.push(o),a.push(o)),a),[]));t=Qn(t);const r=Se(t,s=>s.length>1);return S(r,s=>{const a=S(s,l=>l.name);return{message:`The same RegExp pattern ->${Me(s).PATTERN}<-has been used in all of the following Token Types: ${a.join(", ")} <-`,type:j.DUPLICATE_PATTERNS_FOUND,tokenTypes:s}})}function bf(n){const e=Se(n,r=>{if(!N(r,"GROUP"))return!1;const i=r.GROUP;return i!==fe.SKIPPED&&i!==fe.NA&&!he(i)});return S(e,r=>({message:"Token Type: ->"+r.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:j.INVALID_GROUP_TYPE_FOUND,tokenTypes:[r]}))}function Pf(n,e){const t=Se(n,i=>i.PUSH_MODE!==void 0&&!de(e,i.PUSH_MODE));return S(t,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:j.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}function Mf(n){const e=[],t=le(n,(r,i,s)=>{const a=i.PATTERN;return a===fe.NA||(he(a)?r.push({str:a,idx:s,tokenType:i}):Xe(a)&&Ff(a)&&r.push({str:a.source,idx:s,tokenType:i})),r},[]);return C(n,(r,i)=>{C(t,({str:s,idx:a,tokenType:o})=>{if(i<a&&Df(s,r.PATTERN)){const l=`Token: ->${o.name}<- can never be matched.
Because it appears AFTER the Token Type ->${r.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:l,type:j.UNREACHABLE_PATTERN,tokenTypes:[r,o]})}})}),e}function Df(n,e){if(Xe(e)){const t=e.exec(n);return t!==null&&t.index===0}else{if(yt(e))return e(n,0,[],{});if(N(e,"exec"))return e.exec(n,0,[],{});if(typeof e=="string")return e===n;throw Error("non exhaustive match")}}function Ff(n){return Lt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],t=>n.source.indexOf(t)!==-1)===void 0}function Oa(n){const e=n.ignoreCase?"i":"";return new RegExp(`^(?:${n.source})`,e)}function ba(n){const e=n.ignoreCase?"iy":"y";return new RegExp(`${n.source}`,e)}function Gf(n,e,t){const r=[];return N(n,xn)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+xn+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),N(n,Tr)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+Tr+`> property in its definition
`,type:j.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),N(n,Tr)&&N(n,xn)&&!N(n.modes,n.defaultMode)&&r.push({message:`A MultiMode Lexer cannot be initialized with a ${xn}: <${n.defaultMode}>which does not exist
`,type:j.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),N(n,Tr)&&C(n.modes,(i,s)=>{C(i,(a,o)=>{if(Ye(a))r.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${s}> at index: <${o}>
`,type:j.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(N(a,"LONGER_ALT")){const l=te(a.LONGER_ALT)?a.LONGER_ALT:[a.LONGER_ALT];C(l,c=>{!Ye(c)&&!de(i,c)&&r.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${c.name}> on token <${a.name}> outside of mode <${s}>
`,type:j.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),r}function Uf(n,e,t){const r=[];let i=!1;const s=Qn(we(q(n.modes))),a=Ti(s,l=>l[pt]===fe.NA),o=ac(t);return e&&C(a,l=>{const c=sc(l,o);if(c!==!1){const d={message:Kf(l,c),type:c.issue,tokenType:l};r.push(d)}else N(l,"LINE_BREAKS")?l.LINE_BREAKS===!0&&(i=!0):ra(o,l.PATTERN)&&(i=!0)}),e&&!i&&r.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:j.NO_LINE_BREAKS_FLAGS}),r}function Bf(n){const e={},t=_t(n);return C(t,r=>{const i=n[r];if(te(i))e[r]=[];else throw Error("non exhaustive match")}),e}function ic(n){const e=n.PATTERN;if(Xe(e))return!1;if(yt(e))return!0;if(N(e,"exec"))return!0;if(he(e))return!1;throw Error("non exhaustive match")}function Vf(n){return he(n)&&n.length===1?n.charCodeAt(0):!1}const Wf={test:function(n){const e=n.length;for(let t=this.lastIndex;t<e;t++){const r=n.charCodeAt(t);if(r===10)return this.lastIndex=t+1,!0;if(r===13)return n.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},lastIndex:0};function sc(n,e){if(N(n,"LINE_BREAKS"))return!1;if(Xe(n.PATTERN)){try{ra(e,n.PATTERN)}catch(t){return{issue:j.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}else{if(he(n.PATTERN))return!1;if(ic(n))return{issue:j.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}function Kf(n,e){if(e.issue===j.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${n.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===j.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${n.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}function ac(n){return S(n,t=>he(t)?t.charCodeAt(0):t)}function Wi(n,e,t){n[e]===void 0?n[e]=[t]:n[e].push(t)}const Sn=256;let br=[];function nt(n){return n<Sn?n:br[n]}function Hf(){if(F(br)){br=new Array(65536);for(let n=0;n<65536;n++)br[n]=n>255?255+~~(n/255):n}}function nr(n,e){const t=n.tokenTypeIdx;return t===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[t]===!0}function Qr(n,e){return n.tokenTypeIdx===e.tokenTypeIdx}let Pa=1;const oc={};function rr(n){const e=jf(n);zf(e),Yf(e),qf(e),C(e,t=>{t.isParent=t.categoryMatches.length>0})}function jf(n){let e=re(n),t=n,r=!0;for(;r;){t=Qn(we(S(t,s=>s.CATEGORIES)));const i=yi(t,e);e=e.concat(i),F(i)?r=!1:t=i}return e}function zf(n){C(n,e=>{cc(e)||(oc[Pa]=e,e.tokenTypeIdx=Pa++),Ma(e)&&!te(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),Ma(e)||(e.CATEGORIES=[]),Xf(e)||(e.categoryMatches=[]),Jf(e)||(e.categoryMatchesMap={})})}function qf(n){C(n,e=>{e.categoryMatches=[],C(e.categoryMatchesMap,(t,r)=>{e.categoryMatches.push(oc[r].tokenTypeIdx)})})}function Yf(n){C(n,e=>{lc([],e)})}function lc(n,e){C(n,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),C(e.CATEGORIES,t=>{const r=n.concat(e);de(r,t)||lc(r,t)})}function cc(n){return N(n,"tokenTypeIdx")}function Ma(n){return N(n,"CATEGORIES")}function Xf(n){return N(n,"categoryMatches")}function Jf(n){return N(n,"categoryMatchesMap")}function Qf(n){return N(n,"tokenTypeIdx")}const ms={buildUnableToPopLexerModeMessage(n){return`Unable to pop Lexer Mode after encountering Token ->${n.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(n,e,t,r,i){return`unexpected character: ->${n.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`}};var j;(function(n){n[n.MISSING_PATTERN=0]="MISSING_PATTERN",n[n.INVALID_PATTERN=1]="INVALID_PATTERN",n[n.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",n[n.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",n[n.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",n[n.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",n[n.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",n[n.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",n[n.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",n[n.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",n[n.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",n[n.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",n[n.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",n[n.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",n[n.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",n[n.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",n[n.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",n[n.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(j||(j={}));const In={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:ms,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(In);class fe{constructor(e,t=In){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const a=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent;const{time:o,value:l}=Zl(s),c=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&c(`${a}<-- <${i}> time: ${o}ms`),this.traceInitIndent--,l}else return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=xe({},In,t);const r=this.config.traceInitPerf;r===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof r=="number"&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===In.lineTerminatorsPattern)this.config.lineTerminatorsPattern=Wf;else if(this.config.lineTerminatorCharacters===In.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),te(e)?i={modes:{defaultMode:re(e)},defaultMode:xn}:(s=!1,i=re(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Gf(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Uf(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},C(i.modes,(o,l)=>{i.modes[l]=Ti(o,c=>Ye(c))});const a=_t(i.modes);if(C(i.modes,(o,l)=>{this.TRACE_INIT(`Mode: <${l}> processing`,()=>{if(this.modes.push(l),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(kf(o,a))}),F(this.lexerDefinitionErrors)){rr(o);let c;this.TRACE_INIT("analyzeTokenTypes",()=>{c=Ef(o,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[l]=c.patternIdxToConfig,this.charCodeToPatternIdxToConfig[l]=c.charCodeToPatternIdxToConfig,this.emptyGroups=xe({},this.emptyGroups,c.emptyGroups),this.hasCustom=c.hasCustom||this.hasCustom,this.canModeBeOptimized[l]=c.canBeOptimized}})}),this.defaultMode=i.defaultMode,!F(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const l=S(this.lexerDefinitionErrors,c=>c.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+l)}C(this.lexerDefinitionWarning,o=>{o.message}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(rc?(this.chopInput=xa,this.match=this.matchWithTest):(this.updateLastIndex=X,this.match=this.matchWithExec),s&&(this.handleModes=X),this.trackStartLines===!1&&(this.computeNewColumn=xa),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=X),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const o=le(this.canModeBeOptimized,(l,c,u)=>(c===!1&&l.push(u),l),[]);if(t.ensureOptimizations&&!F(o))throw Error(`Lexer Modes: < ${o.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Tf()}),this.TRACE_INIT("toFastProperties",()=>{ec(this)})})}tokenize(e,t=this.defaultMode){if(!F(this.lexerDefinitionErrors)){const i=S(this.lexerDefinitionErrors,s=>s.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,i,s,a,o,l,c,u,d,h,f,m,g,v,y;const A=e,R=A.length;let $=0,G=0;const ie=this.hasCustom?0:Math.floor(e.length/10),Le=new Array(ie),Te=[];let Ge=this.trackStartLines?1:void 0,Ce=this.trackStartLines?1:void 0;const x=Bf(this.emptyGroups),T=this.trackStartLines,E=this.config.lineTerminatorsPattern;let I=0,b=[],O=[];const L=[],Re=[];Object.freeze(Re);let Y;function K(){return b}function lt(se){const Ne=nt(se),xt=O[Ne];return xt===void 0?Re:xt}const pu=se=>{if(L.length===1&&se.tokenType.PUSH_MODE===void 0){const Ne=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(se);Te.push({offset:se.startOffset,line:se.startLine,column:se.startColumn,length:se.image.length,message:Ne})}else{L.pop();const Ne=Ot(L);b=this.patternIdxToConfig[Ne],O=this.charCodeToPatternIdxToConfig[Ne],I=b.length;const xt=this.canModeBeOptimized[Ne]&&this.config.safeMode===!1;O&&xt?Y=lt:Y=K}};function Ra(se){L.push(se),O=this.charCodeToPatternIdxToConfig[se],b=this.patternIdxToConfig[se],I=b.length,I=b.length;const Ne=this.canModeBeOptimized[se]&&this.config.safeMode===!1;O&&Ne?Y=lt:Y=K}Ra.call(this,t);let Oe;const va=this.config.recoveryEnabled;for(;$<R;){l=null;const se=A.charCodeAt($),Ne=Y(se),xt=Ne.length;for(r=0;r<xt;r++){Oe=Ne[r];const ve=Oe.pattern;c=null;const We=Oe.short;if(We!==!1?se===We&&(l=ve):Oe.isCustom===!0?(y=ve.exec(A,$,Le,x),y!==null?(l=y[0],y.payload!==void 0&&(c=y.payload)):l=null):(this.updateLastIndex(ve,$),l=this.match(ve,e,$)),l!==null){if(o=Oe.longerAlt,o!==void 0){const Qe=o.length;for(s=0;s<Qe;s++){const Ke=b[o[s]],ct=Ke.pattern;if(u=null,Ke.isCustom===!0?(y=ct.exec(A,$,Le,x),y!==null?(a=y[0],y.payload!==void 0&&(u=y.payload)):a=null):(this.updateLastIndex(ct,$),a=this.match(ct,e,$)),a&&a.length>l.length){l=a,c=u,Oe=Ke;break}}}break}}if(l!==null){if(d=l.length,h=Oe.group,h!==void 0&&(f=Oe.tokenTypeIdx,m=this.createTokenInstance(l,$,f,Oe.tokenType,Ge,Ce,d),this.handlePayload(m,c),h===!1?G=this.addToken(Le,G,m):x[h].push(m)),e=this.chopInput(e,d),$=$+d,Ce=this.computeNewColumn(Ce,d),T===!0&&Oe.canLineTerminator===!0){let ve=0,We,Qe;E.lastIndex=0;do We=E.test(l),We===!0&&(Qe=E.lastIndex-1,ve++);while(We===!0);ve!==0&&(Ge=Ge+ve,Ce=d-Qe,this.updateTokenEndLineColumnLocation(m,h,Qe,ve,Ge,Ce,d))}this.handleModes(Oe,pu,Ra,m)}else{const ve=$,We=Ge,Qe=Ce;let Ke=va===!1;for(;Ke===!1&&$<R;)for(e=this.chopInput(e,1),$++,i=0;i<I;i++){const ct=b[i],bi=ct.pattern,Aa=ct.short;if(Aa!==!1?A.charCodeAt($)===Aa&&(Ke=!0):ct.isCustom===!0?Ke=bi.exec(A,$,Le,x)!==null:(this.updateLastIndex(bi,$),Ke=bi.exec(e)!==null),Ke===!0)break}if(g=$-ve,Ce=this.computeNewColumn(Ce,g),v=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(A,ve,g,We,Qe),Te.push({offset:ve,line:We,column:Qe,length:g,message:v}),va===!1)break}}return this.hasCustom||(Le.length=G),{tokens:Le,groups:x,errors:Te}}handleModes(e,t,r,i){if(e.pop===!0){const s=e.push;t(i),s!==void 0&&r.call(this,s)}else e.push!==void 0&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,i,s,a,o){let l,c;t!==void 0&&(l=r===o-1,c=l?-1:0,i===1&&l===!0||(e.endLine=s+c,e.endColumn=a-1+-c))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,i){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:i}}createStartOnlyToken(e,t,r,i,s,a){return{image:e,startOffset:t,startLine:s,startColumn:a,tokenTypeIdx:r,tokenType:i}}createFullToken(e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:t+o-1,startLine:s,endLine:s,startColumn:a,endColumn:a+o-1,tokenTypeIdx:r,tokenType:i}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,t++,t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,r){return e.test(t)===!0?t.substring(r,e.lastIndex):null}matchWithExec(e,t){const r=e.exec(t);return r!==null?r[0]:null}}fe.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";fe.NA=/NOT_APPLICABLE/;function wt(n){return uc(n)?n.LABEL:n.name}function uc(n){return he(n.LABEL)&&n.LABEL!==""}const Zf="parent",Da="categories",Fa="label",Ga="group",Ua="push_mode",Ba="pop_mode",Va="longer_alt",Wa="line_breaks",Ka="start_chars_hint";function dc(n){return eh(n)}function eh(n){const e=n.pattern,t={};if(t.name=n.name,Ye(e)||(t.PATTERN=e),N(n,Zf))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return N(n,Da)&&(t.CATEGORIES=n[Da]),rr([t]),N(n,Fa)&&(t.LABEL=n[Fa]),N(n,Ga)&&(t.GROUP=n[Ga]),N(n,Ba)&&(t.POP_MODE=n[Ba]),N(n,Ua)&&(t.PUSH_MODE=n[Ua]),N(n,Va)&&(t.LONGER_ALT=n[Va]),N(n,Wa)&&(t.LINE_BREAKS=n[Wa]),N(n,Ka)&&(t.START_CHARS_HINT=n[Ka]),t}const rt=dc({name:"EOF",pattern:fe.NA});rr([rt]);function ia(n,e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:n.tokenTypeIdx,tokenType:n}}function fc(n,e){return nr(n,e)}const Ct={buildMismatchTokenMessage({expected:n,actual:e,previous:t,ruleName:r}){return`Expecting ${uc(n)?`--> ${wt(n)} <--`:`token of type --> ${n.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:n,ruleName:e}){return"Redundant input, expecting EOF but found: "+n.image},buildNoViableAltMessage({expectedPathsPerAlt:n,actual:e,previous:t,customUserDescription:r,ruleName:i}){const s="Expecting: ",o=`
but found: '`+Me(e).image+"'";if(r)return s+r+o;{const l=le(n,(h,f)=>h.concat(f),[]),c=S(l,h=>`[${S(h,f=>wt(f)).join(", ")}]`),d=`one of these possible Token sequences:
${S(c,(h,f)=>`  ${f+1}. ${h}`).join(`
`)}`;return s+d+o}},buildEarlyExitMessage({expectedIterationPaths:n,actual:e,customUserDescription:t,ruleName:r}){const i="Expecting: ",a=`
but found: '`+Me(e).image+"'";if(t)return i+t+a;{const l=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${S(n,c=>`[${S(c,u=>wt(u)).join(",")}]`).join(" ,")}>`;return i+l+a}}};Object.freeze(Ct);const th={buildRuleNotFoundError(n,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+n.name+"<-"}},ut={buildDuplicateFoundError(n,e){function t(u){return u instanceof U?u.terminalType.name:u instanceof ce?u.nonTerminalName:""}const r=n.name,i=Me(e),s=i.idx,a=Ue(i),o=t(i),l=s>0;let c=`->${a}${l?s:""}<- ${o?`with argument: ->${o}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${r}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return c=c.replace(/[ \t]+/g," "),c=c.replace(/\s\s+/g,`
`),c},buildNamespaceConflictError(n){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${n.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(n){const e=S(n.prefixPath,i=>wt(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;return`Ambiguous alternatives: <${n.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(n){const e=S(n.prefixPath,i=>wt(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r},buildEmptyRepetitionError(n){let e=Ue(n.repetition);return n.repetition.idx!==0&&(e+=n.repetition.idx),`The repetition <${e}> within Rule <${n.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(n){return"deprecated"},buildEmptyAlternationError(n){return`Ambiguous empty alternative: <${n.emptyChoiceIdx+1}> in <OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(n){return`An Alternation cannot have more than 256 alternatives:
<OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
 has ${n.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(n){const e=n.topLevelRule.name,t=S(n.leftRecursionPath,s=>s.name),r=`${e} --> ${t.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${r}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(n){return"deprecated"},buildDuplicateRuleNameError(n){let e;return n.topLevelRule instanceof Bt?e=n.topLevelRule.name:e=n.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${n.grammarName}<-`}};function nh(n,e){const t=new rh(n,e);return t.resolveRefs(),t.errors}class rh extends Vt{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){C(q(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const r=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:r,type:ue.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}class ih extends ki{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=re(this.path.ruleStack).reverse(),this.occurrenceStack=re(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const i=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){F(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class sh extends ih{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const i=t.concat(r),s=new pe({definition:i});this.possibleTokTypes=tr(s),this.found=!0}}}class Si extends ki{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class ah extends Si{walkMany(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof U&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,r)}}class Ha extends Si{walkManySep(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof U&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,r)}}class oh extends Si{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof U&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,r)}}class ja extends Si{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){const i=Me(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof U&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,r)}}function gs(n,e,t=[]){t=re(t);let r=[],i=0;function s(o){return o.concat(Q(n,i+1))}function a(o){const l=gs(s(o),e,t);return r.concat(l)}for(;t.length<e&&i<n.length;){const o=n[i];if(o instanceof pe)return a(o.definition);if(o instanceof ce)return a(o.definition);if(o instanceof ne)r=a(o.definition);else if(o instanceof Ie){const l=o.definition.concat([new H({definition:o.definition})]);return a(l)}else if(o instanceof $e){const l=[new pe({definition:o.definition}),new H({definition:[new U({terminalType:o.separator})].concat(o.definition)})];return a(l)}else if(o instanceof ge){const l=o.definition.concat([new H({definition:[new U({terminalType:o.separator})].concat(o.definition)})]);r=a(l)}else if(o instanceof H){const l=o.definition.concat([new H({definition:o.definition})]);r=a(l)}else{if(o instanceof ye)return C(o.definition,l=>{F(l.definition)===!1&&(r=a(l.definition))}),r;if(o instanceof U)t.push(o.terminalType);else throw Error("non exhaustive match")}i++}return r.push({partialPath:t,suffixDef:Q(n,i)}),r}function hc(n,e,t,r){const i="EXIT_NONE_TERMINAL",s=[i],a="EXIT_ALTERNATIVE";let o=!1;const l=e.length,c=l-r-1,u=[],d=[];for(d.push({idx:-1,def:n,ruleStack:[],occurrenceStack:[]});!F(d);){const h=d.pop();if(h===a){o&&Ot(d).idx<=c&&d.pop();continue}const f=h.def,m=h.idx,g=h.ruleStack,v=h.occurrenceStack;if(F(f))continue;const y=f[0];if(y===i){const A={idx:m,def:Q(f),ruleStack:On(g),occurrenceStack:On(v)};d.push(A)}else if(y instanceof U)if(m<l-1){const A=m+1,R=e[A];if(t(R,y.terminalType)){const $={idx:A,def:Q(f),ruleStack:g,occurrenceStack:v};d.push($)}}else if(m===l-1)u.push({nextTokenType:y.terminalType,nextTokenOccurrence:y.idx,ruleStack:g,occurrenceStack:v}),o=!0;else throw Error("non exhaustive match");else if(y instanceof ce){const A=re(g);A.push(y.nonTerminalName);const R=re(v);R.push(y.idx);const $={idx:m,def:y.definition.concat(s,Q(f)),ruleStack:A,occurrenceStack:R};d.push($)}else if(y instanceof ne){const A={idx:m,def:Q(f),ruleStack:g,occurrenceStack:v};d.push(A),d.push(a);const R={idx:m,def:y.definition.concat(Q(f)),ruleStack:g,occurrenceStack:v};d.push(R)}else if(y instanceof Ie){const A=new H({definition:y.definition,idx:y.idx}),R=y.definition.concat([A],Q(f)),$={idx:m,def:R,ruleStack:g,occurrenceStack:v};d.push($)}else if(y instanceof $e){const A=new U({terminalType:y.separator}),R=new H({definition:[A].concat(y.definition),idx:y.idx}),$=y.definition.concat([R],Q(f)),G={idx:m,def:$,ruleStack:g,occurrenceStack:v};d.push(G)}else if(y instanceof ge){const A={idx:m,def:Q(f),ruleStack:g,occurrenceStack:v};d.push(A),d.push(a);const R=new U({terminalType:y.separator}),$=new H({definition:[R].concat(y.definition),idx:y.idx}),G=y.definition.concat([$],Q(f)),ie={idx:m,def:G,ruleStack:g,occurrenceStack:v};d.push(ie)}else if(y instanceof H){const A={idx:m,def:Q(f),ruleStack:g,occurrenceStack:v};d.push(A),d.push(a);const R=new H({definition:y.definition,idx:y.idx}),$=y.definition.concat([R],Q(f)),G={idx:m,def:$,ruleStack:g,occurrenceStack:v};d.push(G)}else if(y instanceof ye)for(let A=y.definition.length-1;A>=0;A--){const R=y.definition[A],$={idx:m,def:R.definition.concat(Q(f)),ruleStack:g,occurrenceStack:v};d.push($),d.push(a)}else if(y instanceof pe)d.push({idx:m,def:y.definition.concat(Q(f)),ruleStack:g,occurrenceStack:v});else if(y instanceof Bt)d.push(lh(y,m,g,v));else throw Error("non exhaustive match")}return u}function lh(n,e,t,r){const i=re(t);i.push(n.name);const s=re(r);return s.push(1),{idx:e,def:n.definition,ruleStack:i,occurrenceStack:s}}var V;(function(n){n[n.OPTION=0]="OPTION",n[n.REPETITION=1]="REPETITION",n[n.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",n[n.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",n[n.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",n[n.ALTERNATION=5]="ALTERNATION"})(V||(V={}));function sa(n){if(n instanceof ne||n==="Option")return V.OPTION;if(n instanceof H||n==="Repetition")return V.REPETITION;if(n instanceof Ie||n==="RepetitionMandatory")return V.REPETITION_MANDATORY;if(n instanceof $e||n==="RepetitionMandatoryWithSeparator")return V.REPETITION_MANDATORY_WITH_SEPARATOR;if(n instanceof ge||n==="RepetitionWithSeparator")return V.REPETITION_WITH_SEPARATOR;if(n instanceof ye||n==="Alternation")return V.ALTERNATION;throw Error("non exhaustive match")}function za(n){const{occurrence:e,rule:t,prodType:r,maxLookahead:i}=n,s=sa(r);return s===V.ALTERNATION?Ii(e,t,i):$i(e,t,s,i)}function ch(n,e,t,r,i,s){const a=Ii(n,e,t),o=gc(a)?Qr:nr;return s(a,r,o,i)}function uh(n,e,t,r,i,s){const a=$i(n,e,i,t),o=gc(a)?Qr:nr;return s(a[0],o,r)}function dh(n,e,t,r){const i=n.length,s=Pe(n,a=>Pe(a,o=>o.length===1));if(e)return function(a){const o=S(a,l=>l.GATE);for(let l=0;l<i;l++){const c=n[l],u=c.length,d=o[l];if(!(d!==void 0&&d.call(this)===!1))e:for(let h=0;h<u;h++){const f=c[h],m=f.length;for(let g=0;g<m;g++){const v=this.LA(g+1);if(t(v,f[g])===!1)continue e}return l}}};if(s&&!r){const a=S(n,l=>we(l)),o=le(a,(l,c,u)=>(C(c,d=>{N(l,d.tokenTypeIdx)||(l[d.tokenTypeIdx]=u),C(d.categoryMatches,h=>{N(l,h)||(l[h]=u)})}),l),{});return function(){const l=this.LA(1);return o[l.tokenTypeIdx]}}else return function(){for(let a=0;a<i;a++){const o=n[a],l=o.length;e:for(let c=0;c<l;c++){const u=o[c],d=u.length;for(let h=0;h<d;h++){const f=this.LA(h+1);if(t(f,u[h])===!1)continue e}return a}}}}function fh(n,e,t){const r=Pe(n,s=>s.length===1),i=n.length;if(r&&!t){const s=we(n);if(s.length===1&&F(s[0].categoryMatches)){const o=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===o}}else{const a=le(s,(o,l,c)=>(o[l.tokenTypeIdx]=!0,C(l.categoryMatches,u=>{o[u]=!0}),o),[]);return function(){const o=this.LA(1);return a[o.tokenTypeIdx]===!0}}}else return function(){e:for(let s=0;s<i;s++){const a=n[s],o=a.length;for(let l=0;l<o;l++){const c=this.LA(l+1);if(e(c,a[l])===!1)continue e}return!0}return!1}}class hh extends ki{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,i){return e.idx===this.targetOccurrence&&this.targetProdType===t?(this.restDef=r.concat(i),!0):!1}walkOption(e,t,r){this.checkIsTarget(e,V.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,V.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,V.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,V.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,V.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class pc extends Vt{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,V.OPTION)}visitRepetition(e){this.checkIsTarget(e,V.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,V.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,V.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,V.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,V.ALTERNATION)}}function qa(n){const e=new Array(n);for(let t=0;t<n;t++)e[t]=[];return e}function Ki(n){let e=[""];for(let t=0;t<n.length;t++){const r=n[t],i=[];for(let s=0;s<e.length;s++){const a=e[s];i.push(a+"_"+r.tokenTypeIdx);for(let o=0;o<r.categoryMatches.length;o++){const l="_"+r.categoryMatches[o];i.push(a+l)}}e=i}return e}function ph(n,e,t){for(let r=0;r<n.length;r++){if(r===t)continue;const i=n[r];for(let s=0;s<e.length;s++){const a=e[s];if(i[a]===!0)return!1}}return!0}function mc(n,e){const t=S(n,a=>gs([a],1)),r=qa(t.length),i=S(t,a=>{const o={};return C(a,l=>{const c=Ki(l.partialPath);C(c,u=>{o[u]=!0})}),o});let s=t;for(let a=1;a<=e;a++){const o=s;s=qa(o.length);for(let l=0;l<o.length;l++){const c=o[l];for(let u=0;u<c.length;u++){const d=c[u].partialPath,h=c[u].suffixDef,f=Ki(d);if(ph(i,f,l)||F(h)||d.length===e){const g=r[l];if(ys(g,d)===!1){g.push(d);for(let v=0;v<f.length;v++){const y=f[v];i[l][y]=!0}}}else{const g=gs(h,a+1,d);s[l]=s[l].concat(g),C(g,v=>{const y=Ki(v.partialPath);C(y,A=>{i[l][A]=!0})})}}}}return r}function Ii(n,e,t,r){const i=new pc(n,V.ALTERNATION,r);return e.accept(i),mc(i.result,t)}function $i(n,e,t,r){const i=new pc(n,t);e.accept(i);const s=i.result,o=new hh(e,n,t).startWalking(),l=new pe({definition:s}),c=new pe({definition:o});return mc([l,c],r)}function ys(n,e){e:for(let t=0;t<n.length;t++){const r=n[t];if(r.length===e.length){for(let i=0;i<r.length;i++){const s=e[i],a=r[i];if((s===a||a.categoryMatchesMap[s.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}function mh(n,e){return n.length<e.length&&Pe(n,(t,r)=>{const i=e[r];return t===i||i.categoryMatchesMap[t.tokenTypeIdx]})}function gc(n){return Pe(n,e=>Pe(e,t=>Pe(t,r=>F(r.categoryMatches))))}function gh(n){const e=n.lookaheadStrategy.validate({rules:n.rules,tokenTypes:n.tokenTypes,grammarName:n.grammarName});return S(e,t=>Object.assign({type:ue.CUSTOM_LOOKAHEAD_VALIDATION},t))}function yh(n,e,t,r){const i=ke(n,l=>Th(l,t)),s=wh(n,e,t),a=ke(n,l=>Ih(l,t)),o=ke(n,l=>Ah(l,n,r,t));return i.concat(s,a,o)}function Th(n,e){const t=new vh;n.accept(t);const r=t.allProductions,i=qu(r,Rh),s=De(i,o=>o.length>1);return S(q(s),o=>{const l=Me(o),c=e.buildDuplicateFoundError(n,o),u=Ue(l),d={message:c,type:ue.DUPLICATE_PRODUCTIONS,ruleName:n.name,dslName:u,occurrence:l.idx},h=yc(l);return h&&(d.parameter=h),d})}function Rh(n){return`${Ue(n)}_#_${n.idx}_#_${yc(n)}`}function yc(n){return n instanceof U?n.terminalType.name:n instanceof ce?n.nonTerminalName:""}class vh extends Vt{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function Ah(n,e,t,r){const i=[];if(le(e,(a,o)=>o.name===n.name?a+1:a,0)>1){const a=r.buildDuplicateRuleNameError({topLevelRule:n,grammarName:t});i.push({message:a,type:ue.DUPLICATE_RULE_NAME,ruleName:n.name})}return i}function Eh(n,e,t){const r=[];let i;return de(e,n)||(i=`Invalid rule override, rule: ->${n}<- cannot be overridden in the grammar: ->${t}<-as it is not defined in any of the super grammars `,r.push({message:i,type:ue.INVALID_RULE_OVERRIDE,ruleName:n})),r}function Tc(n,e,t,r=[]){const i=[],s=Pr(e.definition);if(F(s))return[];{const a=n.name;de(s,n)&&i.push({message:t.buildLeftRecursionError({topLevelRule:n,leftRecursionPath:r}),type:ue.LEFT_RECURSION,ruleName:a});const l=yi(s,r.concat([n])),c=ke(l,u=>{const d=re(r);return d.push(u),Tc(n,u,t,d)});return i.concat(c)}}function Pr(n){let e=[];if(F(n))return e;const t=Me(n);if(t instanceof ce)e.push(t.referencedRule);else if(t instanceof pe||t instanceof ne||t instanceof Ie||t instanceof $e||t instanceof ge||t instanceof H)e=e.concat(Pr(t.definition));else if(t instanceof ye)e=we(S(t.definition,s=>Pr(s.definition)));else if(!(t instanceof U))throw Error("non exhaustive match");const r=Xr(t),i=n.length>1;if(r&&i){const s=Q(n);return e.concat(Pr(s))}else return e}class aa extends Vt{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function kh(n,e){const t=new aa;n.accept(t);const r=t.alternations;return ke(r,s=>{const a=On(s.definition);return ke(a,(o,l)=>{const c=hc([o],[],nr,1);return F(c)?[{message:e.buildEmptyAlternationError({topLevelRule:n,alternation:s,emptyChoiceIdx:l}),type:ue.NONE_LAST_EMPTY_ALT,ruleName:n.name,occurrence:s.idx,alternative:l+1}]:[]})})}function xh(n,e,t){const r=new aa;n.accept(r);let i=r.alternations;return i=Ti(i,a=>a.ignoreAmbiguities===!0),ke(i,a=>{const o=a.idx,l=a.maxLookahead||e,c=Ii(o,n,l,a),u=Ch(c,a,n,t),d=Nh(c,a,n,t);return u.concat(d)})}class Sh extends Vt{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function Ih(n,e){const t=new aa;n.accept(t);const r=t.alternations;return ke(r,s=>s.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:n,alternation:s}),type:ue.TOO_MANY_ALTS,ruleName:n.name,occurrence:s.idx}]:[])}function $h(n,e,t){const r=[];return C(n,i=>{const s=new Sh;i.accept(s);const a=s.allProductions;C(a,o=>{const l=sa(o),c=o.maxLookahead||e,u=o.idx,h=$i(u,i,l,c)[0];if(F(we(h))){const f=t.buildEmptyRepetitionError({topLevelRule:i,repetition:o});r.push({message:f,type:ue.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),r}function Ch(n,e,t,r){const i=[],s=le(n,(o,l,c)=>(e.definition[c].ignoreAmbiguities===!0||C(l,u=>{const d=[c];C(n,(h,f)=>{c!==f&&ys(h,u)&&e.definition[f].ignoreAmbiguities!==!0&&d.push(f)}),d.length>1&&!ys(i,u)&&(i.push(u),o.push({alts:d,path:u}))}),o),[]);return S(s,o=>{const l=S(o.alts,u=>u+1);return{message:r.buildAlternationAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:l,prefixPath:o.path}),type:ue.AMBIGUOUS_ALTS,ruleName:t.name,occurrence:e.idx,alternatives:o.alts}})}function Nh(n,e,t,r){const i=le(n,(a,o,l)=>{const c=S(o,u=>({idx:l,path:u}));return a.concat(c)},[]);return Qn(ke(i,a=>{if(e.definition[a.idx].ignoreAmbiguities===!0)return[];const l=a.idx,c=a.path,u=Se(i,h=>e.definition[h.idx].ignoreAmbiguities!==!0&&h.idx<l&&mh(h.path,c));return S(u,h=>{const f=[h.idx+1,l+1],m=e.idx===0?"":e.idx;return{message:r.buildAlternationPrefixAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:f,prefixPath:h.path}),type:ue.AMBIGUOUS_PREFIX_ALTS,ruleName:t.name,occurrence:m,alternatives:f}})}))}function wh(n,e,t){const r=[],i=S(e,s=>s.name);return C(n,s=>{const a=s.name;if(de(i,a)){const o=t.buildNamespaceConflictError(s);r.push({message:o,type:ue.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:a})}}),r}function _h(n){const e=zs(n,{errMsgProvider:th}),t={};return C(n.rules,r=>{t[r.name]=r}),nh(t,e.errMsgProvider)}function Lh(n){return n=zs(n,{errMsgProvider:ut}),yh(n.rules,n.tokenTypes,n.errMsgProvider,n.grammarName)}const Rc="MismatchedTokenException",vc="NoViableAltException",Ac="EarlyExitException",Ec="NotAllInputParsedException",kc=[Rc,vc,Ac,Ec];Object.freeze(kc);function Zr(n){return de(kc,n.name)}class Ci extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class xc extends Ci{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Rc}}class Oh extends Ci{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=vc}}class bh extends Ci{constructor(e,t){super(e,t),this.name=Ec}}class Ph extends Ci{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=Ac}}const Hi={},Sc="InRuleRecoveryException";class Mh extends Error{constructor(e){super(e),this.name=Sc}}class Dh{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=N(e,"recoveryEnabled")?e.recoveryEnabled:Je.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=Fh)}getTokenToInsert(e){const t=ia(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,r,i){const s=this.findReSyncTokenType(),a=this.exportLexerState(),o=[];let l=!1;const c=this.LA(1);let u=this.LA(1);const d=()=>{const h=this.LA(0),f=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:c,previous:h,ruleName:this.getCurrRuleFullName()}),m=new xc(f,c,this.LA(0));m.resyncedTokens=On(o),this.SAVE_ERROR(m)};for(;!l;)if(this.tokenMatcher(u,i)){d();return}else if(r.call(this)){d(),e.apply(this,t);return}else this.tokenMatcher(u,s)?l=!0:(u=this.SKIP_TOKEN(),this.addToResyncTokens(u,o));this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(e,t,r){return!(r===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){const r=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(r)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){const r=this.SKIP_TOKEN();return this.consumeToken(),r}throw new Mh("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||F(t))return!1;const r=this.LA(1);return Lt(t,s=>this.tokenMatcher(r,s))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey(),r=this.getFollowSetFromFollowKey(t);return de(r,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1),r=2;for(;;){const i=Lt(e,s=>fc(t,s));if(i!==void 0)return i;t=this.LA(r),r++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return Hi;const e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(r)}}buildFullFollowKeyStack(){const e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return S(e,(r,i)=>i===0?Hi:{ruleName:this.shortRuleNameToFullName(r),idxInCallingRule:t[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){const e=S(this.buildFullFollowKeyStack(),t=>this.getFollowSetFromFollowKey(t));return we(e)}getFollowSetFromFollowKey(e){if(e===Hi)return[rt];const t=e.ruleName+e.idxInCallingRule+tc+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,rt)||t.push(e),t}reSyncTo(e){const t=[];let r=this.LA(1);for(;this.tokenMatcher(r,e)===!1;)r=this.SKIP_TOKEN(),this.addToResyncTokens(r,t);return On(t)}attemptInRepetitionRecovery(e,t,r,i,s,a,o){}getCurrentGrammarPath(e,t){const r=this.getHumanReadableRuleStack(),i=re(this.RULE_OCCURRENCE_STACK);return{ruleStack:r,occurrenceStack:i,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return S(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}}function Fh(n,e,t,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let l=this.firstAfterRepMap[o];if(l===void 0){const h=this.getCurrRuleFullName(),f=this.getGAstProductions()[h];l=new s(f,i).startWalking(),this.firstAfterRepMap[o]=l}let c=l.token,u=l.occurrence;const d=l.isEndOfRule;this.RULE_STACK.length===1&&d&&c===void 0&&(c=rt,u=1),!(c===void 0||u===void 0)&&this.shouldInRepetitionRecoveryBeTried(c,u,a)&&this.tryInRepetitionRecovery(n,e,t,c)}const Gh=4,at=8,Ic=1<<at,$c=2<<at,Ts=3<<at,Rs=4<<at,vs=5<<at,Mr=6<<at;function ji(n,e,t){return t|e|n}class oa{constructor(e){var t;this.maxLookahead=(t=e==null?void 0:e.maxLookahead)!==null&&t!==void 0?t:Je.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if(F(t)){const r=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...r,...i,...s]}return t}validateNoLeftRecursion(e){return ke(e,t=>Tc(t,t,ut))}validateEmptyOrAlternatives(e){return ke(e,t=>kh(t,ut))}validateAmbiguousAlternationAlternatives(e,t){return ke(e,r=>xh(r,t,ut))}validateSomeNonEmptyLookaheadPath(e,t){return $h(e,t,ut)}buildLookaheadForAlternation(e){return ch(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,dh)}buildLookaheadForOptional(e){return uh(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,sa(e.prodType),fh)}}class Uh{initLooksAhead(e){this.dynamicTokensEnabled=N(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Je.dynamicTokensEnabled,this.maxLookahead=N(e,"maxLookahead")?e.maxLookahead:Je.maxLookahead,this.lookaheadStrategy=N(e,"lookaheadStrategy")?e.lookaheadStrategy:new oa({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){C(e,t=>{this.TRACE_INIT(`${t.name} Rule Lookahead`,()=>{const{alternation:r,repetition:i,option:s,repetitionMandatory:a,repetitionMandatoryWithSeparator:o,repetitionWithSeparator:l}=Vh(t);C(r,c=>{const u=c.idx===0?"":c.idx;this.TRACE_INIT(`${Ue(c)}${u}`,()=>{const d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:c.idx,rule:t,maxLookahead:c.maxLookahead||this.maxLookahead,hasPredicates:c.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),h=ji(this.fullRuleNameToShort[t.name],Ic,c.idx);this.setLaFuncCache(h,d)})}),C(i,c=>{this.computeLookaheadFunc(t,c.idx,Ts,"Repetition",c.maxLookahead,Ue(c))}),C(s,c=>{this.computeLookaheadFunc(t,c.idx,$c,"Option",c.maxLookahead,Ue(c))}),C(a,c=>{this.computeLookaheadFunc(t,c.idx,Rs,"RepetitionMandatory",c.maxLookahead,Ue(c))}),C(o,c=>{this.computeLookaheadFunc(t,c.idx,Mr,"RepetitionMandatoryWithSeparator",c.maxLookahead,Ue(c))}),C(l,c=>{this.computeLookaheadFunc(t,c.idx,vs,"RepetitionWithSeparator",c.maxLookahead,Ue(c))})})})}computeLookaheadFunc(e,t,r,i,s,a){this.TRACE_INIT(`${a}${t===0?"":t}`,()=>{const o=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:s||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),l=ji(this.fullRuleNameToShort[e.name],r,t);this.setLaFuncCache(l,o)})}getKeyForAutomaticLookahead(e,t){const r=this.getLastExplicitRuleShortName();return ji(r,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}}class Bh extends Vt{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}}const Rr=new Bh;function Vh(n){Rr.reset(),n.accept(Rr);const e=Rr.dslMethods;return Rr.reset(),e}function Ya(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.endOffset=e.endOffset):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset)}function Xa(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.startColumn=e.startColumn,n.startLine=e.startLine,n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine)}function Wh(n,e,t){n.children[t]===void 0?n.children[t]=[e]:n.children[t].push(e)}function Kh(n,e,t){n.children[e]===void 0?n.children[e]=[t]:n.children[e].push(t)}const Hh="name";function Cc(n,e){Object.defineProperty(n,Hh,{enumerable:!1,configurable:!0,writable:!1,value:e})}function jh(n,e){const t=_t(n),r=t.length;for(let i=0;i<r;i++){const s=t[i],a=n[s],o=a.length;for(let l=0;l<o;l++){const c=a[l];c.tokenTypeIdx===void 0&&this[c.name](c.children,e)}}}function zh(n,e){const t=function(){};Cc(t,n+"BaseSemantics");const r={visit:function(i,s){if(te(i)&&(i=i[0]),!Ye(i))return this[i.name](i.children,s)},validateVisitor:function(){const i=Yh(this,e);if(!F(i)){const s=S(i,a=>a.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}}};return t.prototype=r,t.prototype.constructor=t,t._RULE_NAMES=e,t}function qh(n,e,t){const r=function(){};Cc(r,n+"BaseSemanticsWithDefaults");const i=Object.create(t.prototype);return C(e,s=>{i[s]=jh}),r.prototype=i,r.prototype.constructor=r,r}var As;(function(n){n[n.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",n[n.MISSING_METHOD=1]="MISSING_METHOD"})(As||(As={}));function Yh(n,e){return Xh(n,e)}function Xh(n,e){const t=Se(e,i=>yt(n[i])===!1),r=S(t,i=>({msg:`Missing visitor method: <${i}> on ${n.constructor.name} CST Visitor.`,type:As.MISSING_METHOD,methodName:i}));return Qn(r)}class Jh{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=N(e,"nodeLocationTracking")?e.nodeLocationTracking:Je.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=X,this.cstFinallyStateUpdate=X,this.cstPostTerminal=X,this.cstPostNonTerminal=X,this.cstPostRule=X;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Xa,this.setNodeLocationFromNode=Xa,this.cstPostRule=X,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=X,this.setNodeLocationFromNode=X,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Ya,this.setNodeLocationFromNode=Ya,this.cstPostRule=X,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=X,this.setNodeLocationFromNode=X,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=X,this.setNodeLocationFromNode=X,this.cstPostRule=X,this.setInitialNodeLocation=X;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?(r.endOffset=t.endOffset,r.endLine=t.endLine,r.endColumn=t.endColumn):(r.startOffset=NaN,r.startLine=NaN,r.startColumn=NaN)}cstPostRuleOnlyOffset(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?r.endOffset=t.endOffset:r.startOffset=NaN}cstPostTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];Wh(r,t,e),this.setNodeLocationFromToken(r.location,t)}cstPostNonTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];Kh(r,t,e),this.setNodeLocationFromNode(r.location,e.location)}getBaseCstVisitorConstructor(){if(Ye(this.baseCstVisitorConstructor)){const e=zh(this.className,_t(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(Ye(this.baseCstVisitorWithDefaultsConstructor)){const e=qh(this.className,_t(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}}class Qh{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):ti}LA(e){const t=this.currIdx+e;return t<0||this.tokVectorLength<=t?ti:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}}class Zh{ACTION(e){return e.call(this)}consume(e,t,r){return this.consumeInternal(t,e,r)}subrule(e,t,r){return this.subruleInternal(t,e,r)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,r=ni){if(de(this.definedRulesNames,e)){const a={message:ut.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:ue.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(a)}this.definedRulesNames.push(e);const i=this.defineRule(e,t,r);return this[e]=i,i}OVERRIDE_RULE(e,t,r=ni){const i=Eh(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);const s=this.defineRule(e,t,r);return this[e]=s,s}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const r=this.saveRecogState();try{return e.apply(this,t),!0}catch(i){if(Zr(i))return!1;throw i}finally{this.reloadRecogState(r),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return lf(q(this.gastProductionsCache))}}class ep{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Qr,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},N(t,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(te(e)){if(F(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(te(e))this.tokensMap=le(e,(s,a)=>(s[a.name]=a,s),{});else if(N(e,"modes")&&Pe(we(q(e.modes)),Qf)){const s=we(q(e.modes)),a=qs(s);this.tokensMap=le(a,(o,l)=>(o[l.name]=l,o),{})}else if(ku(e))this.tokensMap=re(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=rt;const r=N(e,"modes")?we(q(e.modes)):q(e),i=Pe(r,s=>F(s.categoryMatches));this.tokenMatcher=i?Qr:nr,rr(q(this.tokensMap))}defineRule(e,t,r){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const i=N(r,"resyncEnabled")?r.resyncEnabled:ni.resyncEnabled,s=N(r,"recoveryValueFunc")?r.recoveryValueFunc:ni.recoveryValueFunc,a=this.ruleShortNameIdx<<Gh+at;this.ruleShortNameIdx++,this.shortRuleNameToFull[a]=e,this.fullRuleNameToShort[e]=a;let o;return this.outputCst===!0?o=function(...u){try{this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,u);const d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}}:o=function(...u){try{return this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,u)}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}},Object.assign(o,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,r){const i=this.RULE_STACK.length===1,s=t&&!this.isBackTracking()&&this.recoveryEnabled;if(Zr(e)){const a=e;if(s){const o=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(o))if(a.resyncedTokens=this.reSyncTo(o),this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];return l.recoveredNode=!0,l}else return r(e);else{if(this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];l.recoveredNode=!0,a.partialCstResult=l}throw a}}else{if(i)return this.moveToTerminatedState(),r(e);throw a}}else throw e}optionInternal(e,t){const r=this.getKeyForAutomaticLookahead($c,t);return this.optionInternalLogic(e,t,r)}optionInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof e!="function"){s=e.DEF;const a=e.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=e;if(i.call(this)===!0)return s.call(this)}atLeastOneInternal(e,t){const r=this.getKeyForAutomaticLookahead(Rs,e);return this.atLeastOneInternalLogic(e,t,r)}atLeastOneInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const a=t.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=t;if(i.call(this)===!0){let a=this.doSingleRepetition(s);for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s)}else throw this.raiseEarlyExitException(e,V.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,Rs,e,oh)}atLeastOneSepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(Mr,e);this.atLeastOneSepFirstInternalLogic(e,t,r)}atLeastOneSepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,ja],o,Mr,e,ja)}else throw this.raiseEarlyExitException(e,V.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){const r=this.getKeyForAutomaticLookahead(Ts,e);return this.manyInternalLogic(e,t,r)}manyInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const o=t.GATE;if(o!==void 0){const l=i;i=()=>o.call(this)&&l.call(this)}}else s=t;let a=!0;for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,Ts,e,ah,a)}manySepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(vs,e);this.manySepFirstInternalLogic(e,t,r)}manySepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,Ha],o,vs,e,Ha)}}repetitionSepSecondInternal(e,t,r,i,s){for(;r();)this.CONSUME(t),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,r,i,s],r,Mr,e,s)}doSingleRepetition(e){const t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){const r=this.getKeyForAutomaticLookahead(Ic,t),i=te(e)?e:e.DEF,a=this.getLaFuncFromCache(r).call(this,i);if(a!==void 0)return i[a].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new bh(t,e))}}subruleInternal(e,t,r){let i;try{const s=r!==void 0?r.ARGS:void 0;return this.subruleIdx=t,i=e.apply(this,s),this.cstPostNonTerminal(i,r!==void 0&&r.LABEL!==void 0?r.LABEL:e.ruleName),i}catch(s){throw this.subruleInternalError(s,r,e.ruleName)}}subruleInternalError(e,t,r){throw Zr(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,t!==void 0&&t.LABEL!==void 0?t.LABEL:r),delete e.partialCstResult),e}consumeInternal(e,t,r){let i;try{const s=this.LA(1);this.tokenMatcher(s,e)===!0?(this.consumeToken(),i=s):this.consumeInternalError(e,s,r)}catch(s){i=this.consumeInternalRecovery(e,t,s)}return this.cstPostTerminal(r!==void 0&&r.LABEL!==void 0?r.LABEL:e.name,i),i}consumeInternalError(e,t,r){let i;const s=this.LA(0);throw r!==void 0&&r.ERR_MSG?i=r.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:s,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new xc(i,t,s))}consumeInternalRecovery(e,t,r){if(this.recoveryEnabled&&r.name==="MismatchedTokenException"&&!this.isBackTracking()){const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(s){throw s.name===Sc?r:s}}else throw r}saveRecogState(){const e=this.errors,t=re(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,r){this.RULE_OCCURRENCE_STACK.push(r),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),rt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}}class tp{initErrorHandler(e){this._errors=[],this.errorMessageProvider=N(e,"errorMessageProvider")?e.errorMessageProvider:Je.errorMessageProvider}SAVE_ERROR(e){if(Zr(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:re(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return re(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,r){const i=this.getCurrRuleFullName(),s=this.getGAstProductions()[i],o=$i(e,s,t,this.maxLookahead)[0],l=[];for(let u=1;u<=this.maxLookahead;u++)l.push(this.LA(u));const c=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:o,actual:l,previous:this.LA(0),customUserDescription:r,ruleName:i});throw this.SAVE_ERROR(new Ph(c,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const r=this.getCurrRuleFullName(),i=this.getGAstProductions()[r],s=Ii(e,i,this.maxLookahead),a=[];for(let c=1;c<=this.maxLookahead;c++)a.push(this.LA(c));const o=this.LA(0),l=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:s,actual:a,previous:o,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new Oh(l,this.LA(1),o))}}class np{initContentAssist(){}computeContentAssist(e,t){const r=this.gastProductionsCache[e];if(Ye(r))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return hc([r],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=Me(e.ruleStack),i=this.getGAstProductions()[t];return new sh(i,e).startWalking()}}const Ni={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(Ni);const Ja=!0,Qa=Math.pow(2,at)-1,Nc=dc({name:"RECORDING_PHASE_TOKEN",pattern:fe.NA});rr([Nc]);const wc=ia(Nc,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(wc);const rp={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}};class ip{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(r,i){return this.consumeInternalRecord(r,e,i)},this[`SUBRULE${t}`]=function(r,i){return this.subruleInternalRecord(r,e,i)},this[`OPTION${t}`]=function(r){return this.optionInternalRecord(r,e)},this[`OR${t}`]=function(r){return this.orInternalRecord(r,e)},this[`MANY${t}`]=function(r){this.manyInternalRecord(e,r)},this[`MANY_SEP${t}`]=function(r){this.manySepFirstInternalRecord(e,r)},this[`AT_LEAST_ONE${t}`]=function(r){this.atLeastOneInternalRecord(e,r)},this[`AT_LEAST_ONE_SEP${t}`]=function(r){this.atLeastOneSepFirstInternalRecord(e,r)}}this.consume=function(e,t,r){return this.consumeInternalRecord(t,e,r)},this.subrule=function(e,t,r){return this.subruleInternalRecord(t,e,r)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const e=this;for(let t=0;t<10;t++){const r=t>0?t:"";delete e[`CONSUME${r}`],delete e[`SUBRULE${r}`],delete e[`OPTION${r}`],delete e[`OR${r}`],delete e[`MANY${r}`],delete e[`MANY_SEP${r}`],delete e[`AT_LEAST_ONE${r}`],delete e[`AT_LEAST_ONE_SEP${r}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return ti}topLevelRuleRecord(e,t){try{const r=new Bt({definition:[],name:e});return r.name=e,this.recordingProdStack.push(r),t.call(this),this.recordingProdStack.pop(),r}catch(r){if(r.KNOWN_RECORDER_ERROR!==!0)try{r.message=r.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch(i){throw r}throw r}}optionInternalRecord(e,t){return zt.call(this,ne,e,t)}atLeastOneInternalRecord(e,t){zt.call(this,Ie,t,e)}atLeastOneSepFirstInternalRecord(e,t){zt.call(this,$e,t,e,Ja)}manyInternalRecord(e,t){zt.call(this,H,t,e)}manySepFirstInternalRecord(e,t){zt.call(this,ge,t,e,Ja)}orInternalRecord(e,t){return sp.call(this,e,t)}subruleInternalRecord(e,t,r){if(ei(t),!e||N(e,"ruleName")===!1){const o=new Error(`<SUBRULE${Za(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}const i=Ot(this.recordingProdStack),s=e.ruleName,a=new ce({idx:t,nonTerminalName:s,label:r==null?void 0:r.LABEL,referencedRule:void 0});return i.definition.push(a),this.outputCst?rp:Ni}consumeInternalRecord(e,t,r){if(ei(t),!cc(e)){const a=new Error(`<CONSUME${Za(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw a.KNOWN_RECORDER_ERROR=!0,a}const i=Ot(this.recordingProdStack),s=new U({idx:t,terminalType:e,label:r==null?void 0:r.LABEL});return i.definition.push(s),wc}}function zt(n,e,t,r=!1){ei(t);const i=Ot(this.recordingProdStack),s=yt(e)?e:e.DEF,a=new n({definition:[],idx:t});return r&&(a.separator=e.SEP),N(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(a),s.call(this),i.definition.push(a),this.recordingProdStack.pop(),Ni}function sp(n,e){ei(e);const t=Ot(this.recordingProdStack),r=te(n)===!1,i=r===!1?n:n.DEF,s=new ye({definition:[],idx:e,ignoreAmbiguities:r&&n.IGNORE_AMBIGUITIES===!0});N(n,"MAX_LOOKAHEAD")&&(s.maxLookahead=n.MAX_LOOKAHEAD);const a=_l(i,o=>yt(o.GATE));return s.hasPredicates=a,t.definition.push(s),C(i,o=>{const l=new pe({definition:[]});s.definition.push(l),N(o,"IGNORE_AMBIGUITIES")?l.ignoreAmbiguities=o.IGNORE_AMBIGUITIES:N(o,"GATE")&&(l.ignoreAmbiguities=!0),this.recordingProdStack.push(l),o.ALT.call(this),this.recordingProdStack.pop()}),Ni}function Za(n){return n===0?"":`${n}`}function ei(n){if(n<0||n>Qa){const e=new Error(`Invalid DSL Method idx value: <${n}>
	Idx value must be a none negative value smaller than ${Qa+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}class ap{initPerformanceTracer(e){if(N(e,"traceInitPerf")){const t=e.traceInitPerf,r=typeof t=="number";this.traceInitMaxIdent=r?t:1/0,this.traceInitPerf=r?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=Je.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===!0){this.traceInitIndent++;const r=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent;const{time:i,value:s}=Zl(t),a=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,s}else return t()}}function op(n,e){e.forEach(t=>{const r=t.prototype;Object.getOwnPropertyNames(r).forEach(i=>{if(i==="constructor")return;const s=Object.getOwnPropertyDescriptor(r,i);s&&(s.get||s.set)?Object.defineProperty(n.prototype,i,s):n.prototype[i]=t.prototype[i]})})}const ti=ia(rt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(ti);const Je=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Ct,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),ni=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var ue;(function(n){n[n.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",n[n.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",n[n.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",n[n.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",n[n.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",n[n.LEFT_RECURSION=5]="LEFT_RECURSION",n[n.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",n[n.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",n[n.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",n[n.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",n[n.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",n[n.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",n[n.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",n[n.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(ue||(ue={}));function eo(n=void 0){return function(){return n}}class ir{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",()=>{ec(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),C(this.definedRulesNames,i=>{const a=this[i].originalGrammarAction;let o;this.TRACE_INIT(`${i} Rule`,()=>{o=this.topLevelRuleRecord(i,a)}),this.gastProductionsCache[i]=o})}finally{this.disableRecording()}});let r=[];if(this.TRACE_INIT("Grammar Resolving",()=>{r=_h({rules:q(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(r)}),this.TRACE_INIT("Grammar Validations",()=>{if(F(r)&&this.skipValidations===!1){const i=Lh({rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),errMsgProvider:ut,grammarName:t}),s=gh({lookaheadStrategy:this.lookaheadStrategy,rules:q(this.gastProductionsCache),tokenTypes:q(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(i,s)}}),F(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const i=mf(q(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:q(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(q(this.gastProductionsCache))})),!ir.DEFER_DEFINITION_ERRORS_HANDLING&&!F(this.definitionErrors))throw e=S(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const r=this;if(r.initErrorHandler(t),r.initLexerAdapter(),r.initLooksAhead(t),r.initRecognizerEngine(e,t),r.initRecoverable(t),r.initTreeBuilder(t),r.initContentAssist(),r.initGastRecorder(t),r.initPerformanceTracer(t),N(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=N(t,"skipValidations")?t.skipValidations:Je.skipValidations}}ir.DEFER_DEFINITION_ERRORS_HANDLING=!1;op(ir,[Dh,Uh,Jh,Qh,ep,Zh,tp,np,ip,ap]);class lp extends ir{constructor(e,t=Je){const r=re(t);r.outputCst=!1,super(e,r)}}function bt(n,e,t){return`${n.name}_${e}_${t}`}const it=1,cp=2,_c=4,Lc=5,sr=7,up=8,dp=9,fp=10,hp=11,Oc=12;class la{constructor(e){this.target=e}isEpsilon(){return!1}}class ca extends la{constructor(e,t){super(e),this.tokenType=t}}class bc extends la{constructor(e){super(e)}isEpsilon(){return!0}}class ua extends la{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function pp(n){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};mp(e,n);const t=n.length;for(let r=0;r<t;r++){const i=n[r],s=Rt(e,i,i);s!==void 0&&Ip(e,i,s)}return e}function mp(n,e){const t=e.length;for(let r=0;r<t;r++){const i=e[r],s=J(n,i,void 0,{type:cp}),a=J(n,i,void 0,{type:sr});s.stop=a,n.ruleToStartState.set(i,s),n.ruleToStopState.set(i,a)}}function Pc(n,e,t){return t instanceof U?da(n,e,t.terminalType,t):t instanceof ce?Sp(n,e,t):t instanceof ye?vp(n,e,t):t instanceof ne?Ap(n,e,t):t instanceof H?gp(n,e,t):t instanceof ge?yp(n,e,t):t instanceof Ie?Tp(n,e,t):t instanceof $e?Rp(n,e,t):Rt(n,e,t)}function gp(n,e,t){const r=J(n,e,t,{type:Lc});ot(n,r);const i=Wt(n,e,r,t,Rt(n,e,t));return Dc(n,e,t,i)}function yp(n,e,t){const r=J(n,e,t,{type:Lc});ot(n,r);const i=Wt(n,e,r,t,Rt(n,e,t)),s=da(n,e,t.separator,t);return Dc(n,e,t,i,s)}function Tp(n,e,t){const r=J(n,e,t,{type:_c});ot(n,r);const i=Wt(n,e,r,t,Rt(n,e,t));return Mc(n,e,t,i)}function Rp(n,e,t){const r=J(n,e,t,{type:_c});ot(n,r);const i=Wt(n,e,r,t,Rt(n,e,t)),s=da(n,e,t.separator,t);return Mc(n,e,t,i,s)}function vp(n,e,t){const r=J(n,e,t,{type:it});ot(n,r);const i=S(t.definition,a=>Pc(n,e,a));return Wt(n,e,r,t,...i)}function Ap(n,e,t){const r=J(n,e,t,{type:it});ot(n,r);const i=Wt(n,e,r,t,Rt(n,e,t));return Ep(n,e,t,i)}function Rt(n,e,t){const r=Se(S(t.definition,i=>Pc(n,e,i)),i=>i!==void 0);return r.length===1?r[0]:r.length===0?void 0:xp(n,r)}function Mc(n,e,t,r,i){const s=r.left,a=r.right,o=J(n,e,t,{type:hp});ot(n,o);const l=J(n,e,t,{type:Oc});return s.loopback=o,l.loopback=o,n.decisionMap[bt(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=o,z(a,o),i===void 0?(z(o,s),z(o,l)):(z(o,l),z(o,i.left),z(i.right,s)),{left:s,right:l}}function Dc(n,e,t,r,i){const s=r.left,a=r.right,o=J(n,e,t,{type:fp});ot(n,o);const l=J(n,e,t,{type:Oc}),c=J(n,e,t,{type:dp});return o.loopback=c,l.loopback=c,z(o,s),z(o,l),z(a,c),i!==void 0?(z(c,l),z(c,i.left),z(i.right,s)):z(c,o),n.decisionMap[bt(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=o,{left:o,right:l}}function Ep(n,e,t,r){const i=r.left,s=r.right;return z(i,s),n.decisionMap[bt(e,"Option",t.idx)]=i,r}function ot(n,e){return n.decisionStates.push(e),e.decision=n.decisionStates.length-1,e.decision}function Wt(n,e,t,r,...i){const s=J(n,e,r,{type:up,start:t});t.end=s;for(const o of i)o!==void 0?(z(t,o.left),z(o.right,s)):z(t,s);const a={left:t,right:s};return n.decisionMap[bt(e,kp(r),r.idx)]=t,a}function kp(n){if(n instanceof ye)return"Alternation";if(n instanceof ne)return"Option";if(n instanceof H)return"Repetition";if(n instanceof ge)return"RepetitionWithSeparator";if(n instanceof Ie)return"RepetitionMandatory";if(n instanceof $e)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}function xp(n,e){const t=e.length;for(let s=0;s<t-1;s++){const a=e[s];let o;a.left.transitions.length===1&&(o=a.left.transitions[0]);const l=o instanceof ua,c=o,u=e[s+1].left;a.left.type===it&&a.right.type===it&&o!==void 0&&(l&&c.followState===a.right||o.target===a.right)?(l?c.followState=u:o.target=u,$p(n,a.right)):z(a.right,u)}const r=e[0],i=e[t-1];return{left:r.left,right:i.right}}function da(n,e,t,r){const i=J(n,e,r,{type:it}),s=J(n,e,r,{type:it});return fa(i,new ca(s,t)),{left:i,right:s}}function Sp(n,e,t){const r=t.referencedRule,i=n.ruleToStartState.get(r),s=J(n,e,t,{type:it}),a=J(n,e,t,{type:it}),o=new ua(i,r,a);return fa(s,o),{left:s,right:a}}function Ip(n,e,t){const r=n.ruleToStartState.get(e);z(r,t.left);const i=n.ruleToStopState.get(e);return z(t.right,i),{left:r,right:i}}function z(n,e){const t=new bc(e);fa(n,t)}function J(n,e,t,r){const i=Object.assign({atn:n,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:n.states.length},r);return n.states.push(i),i}function fa(n,e){n.transitions.length===0&&(n.epsilonOnlyTransitions=e.isEpsilon()),n.transitions.push(e)}function $p(n,e){n.states.splice(n.states.indexOf(e),1)}const ri={};class Es{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=Fc(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return S(this.configs,e=>e.alt)}get key(){let e="";for(const t in this.map)e+=t+":";return e}}function Fc(n,e=!0){return`${e?`a${n.alt}`:""}s${n.state.stateNumber}:${n.stack.map(t=>t.stateNumber.toString()).join("_")}`}function Cp(n,e){const t={};return r=>{const i=r.toString();let s=t[i];return s!==void 0||(s={atnStartState:n,decision:e,states:{}},t[i]=s),s}}class Gc{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let r=0;r<t;r++)e+=this.predicates[r]===!0?"1":"0";return e}}const to=new Gc;class Np extends oa{constructor(e){var t;super(),this.logging=(t=e==null?void 0:e.logging)!==null&&t!==void 0?t:r=>{}}initialize(e){this.atn=pp(e.rules),this.dfas=wp(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:r,hasPredicates:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=bt(r,"Alternation",t),u=this.atn.decisionMap[l].decision,d=S(za({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),h=>S(h,f=>f[0]));if(no(d,!1)&&!s){const h=le(d,(f,m,g)=>(C(m,v=>{v&&(f[v.tokenTypeIdx]=g,C(v.categoryMatches,y=>{f[y]=g}))}),f),{});return i?function(f){var m;const g=this.LA(1),v=h[g.tokenTypeIdx];if(f!==void 0&&v!==void 0){const y=(m=f[v])===null||m===void 0?void 0:m.GATE;if(y!==void 0&&y.call(this)===!1)return}return v}:function(){const f=this.LA(1);return h[f.tokenTypeIdx]}}else return i?function(h){const f=new Gc,m=h===void 0?0:h.length;for(let v=0;v<m;v++){const y=h==null?void 0:h[v].GATE;f.set(v,y===void 0||y.call(this))}const g=zi.call(this,a,u,f,o);return typeof g=="number"?g:void 0}:function(){const h=zi.call(this,a,u,to,o);return typeof h=="number"?h:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:r,prodType:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=bt(r,i,t),u=this.atn.decisionMap[l].decision,d=S(za({maxLookahead:1,occurrence:t,prodType:i,rule:r}),h=>S(h,f=>f[0]));if(no(d)&&d[0][0]&&!s){const h=d[0],f=we(h);if(f.length===1&&F(f[0].categoryMatches)){const g=f[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===g}}else{const m=le(f,(g,v)=>(v!==void 0&&(g[v.tokenTypeIdx]=!0,C(v.categoryMatches,y=>{g[y]=!0})),g),{});return function(){const g=this.LA(1);return m[g.tokenTypeIdx]===!0}}}return function(){const h=zi.call(this,a,u,to,o);return typeof h=="object"?!1:h===0}}}function no(n,e=!0){const t=new Set;for(const r of n){const i=new Set;for(const s of r){if(s===void 0){if(e)break;return!1}const a=[s.tokenTypeIdx].concat(s.categoryMatches);for(const o of a)if(t.has(o)){if(!i.has(o))return!1}else t.add(o),i.add(o)}}return!0}function wp(n){const e=n.decisionStates.length,t=Array(e);for(let r=0;r<e;r++)t[r]=Cp(n.decisionStates[r],r);return t}function zi(n,e,t,r){const i=n[e](t);let s=i.start;if(s===void 0){const o=Bp(i.atnStartState);s=Bc(i,Uc(o)),i.start=s}return _p.apply(this,[i,s,t,r])}function _p(n,e,t,r){let i=e,s=1;const a=[];let o=this.LA(s++);for(;;){let l=Dp(i,o);if(l===void 0&&(l=Lp.apply(this,[n,i,o,s,t,r])),l===ri)return Mp(a,i,o);if(l.isAcceptState===!0)return l.prediction;i=l,a.push(o),o=this.LA(s++)}}function Lp(n,e,t,r,i,s){const a=Fp(e.configs,t,i);if(a.size===0)return ro(n,e,t,ri),ri;let o=Uc(a);const l=Up(a,i);if(l!==void 0)o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l;else if(Hp(a)){const c=Ou(a.alts);o.isAcceptState=!0,o.prediction=c,o.configs.uniqueAlt=c,Op.apply(this,[n,r,a.alts,s])}return o=ro(n,e,t,o),o}function Op(n,e,t,r){const i=[];for(let c=1;c<=e;c++)i.push(this.LA(c).tokenType);const s=n.atnStartState,a=s.rule,o=s.production,l=bp({topLevelRule:a,ambiguityIndices:t,production:o,prefixPath:i});r(l)}function bp(n){const e=S(n.prefixPath,i=>wt(i)).join(", "),t=n.production.idx===0?"":n.production.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(", ")}> in <${Pp(n.production)}${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r}function Pp(n){if(n instanceof ce)return"SUBRULE";if(n instanceof ne)return"OPTION";if(n instanceof ye)return"OR";if(n instanceof Ie)return"AT_LEAST_ONE";if(n instanceof $e)return"AT_LEAST_ONE_SEP";if(n instanceof ge)return"MANY_SEP";if(n instanceof H)return"MANY";if(n instanceof U)return"CONSUME";throw Error("non exhaustive match")}function Mp(n,e,t){const r=ke(e.configs.elements,s=>s.state.transitions),i=id(r.filter(s=>s instanceof ca).map(s=>s.tokenType),s=>s.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:i,tokenPath:n}}function Dp(n,e){return n.edges[e.tokenTypeIdx]}function Fp(n,e,t){const r=new Es,i=[];for(const a of n.elements){if(t.is(a.alt)===!1)continue;if(a.state.type===sr){i.push(a);continue}const o=a.state.transitions.length;for(let l=0;l<o;l++){const c=a.state.transitions[l],u=Gp(c,e);u!==void 0&&r.add({state:u,alt:a.alt,stack:a.stack})}}let s;if(i.length===0&&r.size===1&&(s=r),s===void 0){s=new Es;for(const a of r.elements)ii(a,s)}if(i.length>0&&!Wp(s))for(const a of i)s.add(a);return s}function Gp(n,e){if(n instanceof ca&&fc(e,n.tokenType))return n.target}function Up(n,e){let t;for(const r of n.elements)if(e.is(r.alt)===!0){if(t===void 0)t=r.alt;else if(t!==r.alt)return}return t}function Uc(n){return{configs:n,edges:{},isAcceptState:!1,prediction:-1}}function ro(n,e,t,r){return r=Bc(n,r),e.edges[t.tokenTypeIdx]=r,r}function Bc(n,e){if(e===ri)return e;const t=e.configs.key,r=n.states[t];return r!==void 0?r:(e.configs.finalize(),n.states[t]=e,e)}function Bp(n){const e=new Es,t=n.transitions.length;for(let r=0;r<t;r++){const s={state:n.transitions[r].target,alt:r,stack:[]};ii(s,e)}return e}function ii(n,e){const t=n.state;if(t.type===sr){if(n.stack.length>0){const i=[...n.stack],a={state:i.pop(),alt:n.alt,stack:i};ii(a,e)}else e.add(n);return}t.epsilonOnlyTransitions||e.add(n);const r=t.transitions.length;for(let i=0;i<r;i++){const s=t.transitions[i],a=Vp(n,s);a!==void 0&&ii(a,e)}}function Vp(n,e){if(e instanceof bc)return{state:e.target,alt:n.alt,stack:n.stack};if(e instanceof ua){const t=[...n.stack,e.followState];return{state:e.target,alt:n.alt,stack:t}}}function Wp(n){for(const e of n.elements)if(e.state.type===sr)return!0;return!1}function Kp(n){for(const e of n.elements)if(e.state.type!==sr)return!1;return!0}function Hp(n){if(Kp(n))return!0;const e=jp(n.elements);return zp(e)&&!qp(e)}function jp(n){const e=new Map;for(const t of n){const r=Fc(t,!1);let i=e.get(r);i===void 0&&(i={},e.set(r,i)),i[t.alt]=!0}return e}function zp(n){for(const e of Array.from(n.values()))if(Object.keys(e).length>1)return!0;return!1}function qp(n){for(const e of Array.from(n.values()))if(Object.keys(e).length===1)return!0;return!1}var io;(function(n){function e(t){return typeof t=="string"}n.is=e})(io||(io={}));var ks;(function(n){function e(t){return typeof t=="string"}n.is=e})(ks||(ks={}));var so;(function(n){n.MIN_VALUE=-2147483648,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(so||(so={}));var si;(function(n){n.MIN_VALUE=0,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(si||(si={}));var M;(function(n){function e(r,i){return r===Number.MAX_VALUE&&(r=si.MAX_VALUE),i===Number.MAX_VALUE&&(i=si.MAX_VALUE),{line:r,character:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&p.uinteger(i.line)&&p.uinteger(i.character)}n.is=t})(M||(M={}));var P;(function(n){function e(r,i,s,a){if(p.uinteger(r)&&p.uinteger(i)&&p.uinteger(s)&&p.uinteger(a))return{start:M.create(r,i),end:M.create(s,a)};if(M.is(r)&&M.is(i))return{start:r,end:i};throw new Error(`Range#create called with invalid arguments[${r}, ${i}, ${s}, ${a}]`)}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&M.is(i.start)&&M.is(i.end)}n.is=t})(P||(P={}));var ai;(function(n){function e(r,i){return{uri:r,range:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&P.is(i.range)&&(p.string(i.uri)||p.undefined(i.uri))}n.is=t})(ai||(ai={}));var ao;(function(n){function e(r,i,s,a){return{targetUri:r,targetRange:i,targetSelectionRange:s,originSelectionRange:a}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&P.is(i.targetRange)&&p.string(i.targetUri)&&P.is(i.targetSelectionRange)&&(P.is(i.originSelectionRange)||p.undefined(i.originSelectionRange))}n.is=t})(ao||(ao={}));var xs;(function(n){function e(r,i,s,a){return{red:r,green:i,blue:s,alpha:a}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.numberRange(i.red,0,1)&&p.numberRange(i.green,0,1)&&p.numberRange(i.blue,0,1)&&p.numberRange(i.alpha,0,1)}n.is=t})(xs||(xs={}));var oo;(function(n){function e(r,i){return{range:r,color:i}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&P.is(i.range)&&xs.is(i.color)}n.is=t})(oo||(oo={}));var lo;(function(n){function e(r,i,s){return{label:r,textEdit:i,additionalTextEdits:s}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.undefined(i.textEdit)||Mt.is(i))&&(p.undefined(i.additionalTextEdits)||p.typedArray(i.additionalTextEdits,Mt.is))}n.is=t})(lo||(lo={}));var co;(function(n){n.Comment="comment",n.Imports="imports",n.Region="region"})(co||(co={}));var uo;(function(n){function e(r,i,s,a,o,l){const c={startLine:r,endLine:i};return p.defined(s)&&(c.startCharacter=s),p.defined(a)&&(c.endCharacter=a),p.defined(o)&&(c.kind=o),p.defined(l)&&(c.collapsedText=l),c}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.uinteger(i.startLine)&&p.uinteger(i.startLine)&&(p.undefined(i.startCharacter)||p.uinteger(i.startCharacter))&&(p.undefined(i.endCharacter)||p.uinteger(i.endCharacter))&&(p.undefined(i.kind)||p.string(i.kind))}n.is=t})(uo||(uo={}));var Ss;(function(n){function e(r,i){return{location:r,message:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&ai.is(i.location)&&p.string(i.message)}n.is=t})(Ss||(Ss={}));var fo;(function(n){n.Error=1,n.Warning=2,n.Information=3,n.Hint=4})(fo||(fo={}));var ho;(function(n){n.Unnecessary=1,n.Deprecated=2})(ho||(ho={}));var po;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&p.string(r.href)}n.is=e})(po||(po={}));var oi;(function(n){function e(r,i,s,a,o,l){let c={range:r,message:i};return p.defined(s)&&(c.severity=s),p.defined(a)&&(c.code=a),p.defined(o)&&(c.source=o),p.defined(l)&&(c.relatedInformation=l),c}n.create=e;function t(r){var i;let s=r;return p.defined(s)&&P.is(s.range)&&p.string(s.message)&&(p.number(s.severity)||p.undefined(s.severity))&&(p.integer(s.code)||p.string(s.code)||p.undefined(s.code))&&(p.undefined(s.codeDescription)||p.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(p.string(s.source)||p.undefined(s.source))&&(p.undefined(s.relatedInformation)||p.typedArray(s.relatedInformation,Ss.is))}n.is=t})(oi||(oi={}));var Pt;(function(n){function e(r,i,...s){let a={title:r,command:i};return p.defined(s)&&s.length>0&&(a.arguments=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.title)&&p.string(i.command)}n.is=t})(Pt||(Pt={}));var Mt;(function(n){function e(s,a){return{range:s,newText:a}}n.replace=e;function t(s,a){return{range:{start:s,end:s},newText:a}}n.insert=t;function r(s){return{range:s,newText:""}}n.del=r;function i(s){const a=s;return p.objectLiteral(a)&&p.string(a.newText)&&P.is(a.range)}n.is=i})(Mt||(Mt={}));var Is;(function(n){function e(r,i,s){const a={label:r};return i!==void 0&&(a.needsConfirmation=i),s!==void 0&&(a.description=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(p.string(i.description)||i.description===void 0)}n.is=t})(Is||(Is={}));var Dt;(function(n){function e(t){const r=t;return p.string(r)}n.is=e})(Dt||(Dt={}));var mo;(function(n){function e(s,a,o){return{range:s,newText:a,annotationId:o}}n.replace=e;function t(s,a,o){return{range:{start:s,end:s},newText:a,annotationId:o}}n.insert=t;function r(s,a){return{range:s,newText:"",annotationId:a}}n.del=r;function i(s){const a=s;return Mt.is(a)&&(Is.is(a.annotationId)||Dt.is(a.annotationId))}n.is=i})(mo||(mo={}));var $s;(function(n){function e(r,i){return{textDocument:r,edits:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&Ls.is(i.textDocument)&&Array.isArray(i.edits)}n.is=t})($s||($s={}));var Cs;(function(n){function e(r,i,s){let a={kind:"create",uri:r};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="create"&&p.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Dt.is(i.annotationId))}n.is=t})(Cs||(Cs={}));var Ns;(function(n){function e(r,i,s,a){let o={kind:"rename",oldUri:r,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(o.options=s),a!==void 0&&(o.annotationId=a),o}n.create=e;function t(r){let i=r;return i&&i.kind==="rename"&&p.string(i.oldUri)&&p.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Dt.is(i.annotationId))}n.is=t})(Ns||(Ns={}));var ws;(function(n){function e(r,i,s){let a={kind:"delete",uri:r};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="delete"&&p.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||p.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||p.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||Dt.is(i.annotationId))}n.is=t})(ws||(ws={}));var _s;(function(n){function e(t){let r=t;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(i=>p.string(i.kind)?Cs.is(i)||Ns.is(i)||ws.is(i):$s.is(i)))}n.is=e})(_s||(_s={}));var go;(function(n){function e(r){return{uri:r}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)}n.is=t})(go||(go={}));var yo;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.integer(i.version)}n.is=t})(yo||(yo={}));var Ls;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&(i.version===null||p.integer(i.version))}n.is=t})(Ls||(Ls={}));var To;(function(n){function e(r,i,s,a){return{uri:r,languageId:i,version:s,text:a}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.string(i.languageId)&&p.integer(i.version)&&p.string(i.text)}n.is=t})(To||(To={}));var Os;(function(n){n.PlainText="plaintext",n.Markdown="markdown";function e(t){const r=t;return r===n.PlainText||r===n.Markdown}n.is=e})(Os||(Os={}));var Pn;(function(n){function e(t){const r=t;return p.objectLiteral(t)&&Os.is(r.kind)&&p.string(r.value)}n.is=e})(Pn||(Pn={}));var Ro;(function(n){n.Text=1,n.Method=2,n.Function=3,n.Constructor=4,n.Field=5,n.Variable=6,n.Class=7,n.Interface=8,n.Module=9,n.Property=10,n.Unit=11,n.Value=12,n.Enum=13,n.Keyword=14,n.Snippet=15,n.Color=16,n.File=17,n.Reference=18,n.Folder=19,n.EnumMember=20,n.Constant=21,n.Struct=22,n.Event=23,n.Operator=24,n.TypeParameter=25})(Ro||(Ro={}));var vo;(function(n){n.PlainText=1,n.Snippet=2})(vo||(vo={}));var Ao;(function(n){n.Deprecated=1})(Ao||(Ao={}));var Eo;(function(n){function e(r,i,s){return{newText:r,insert:i,replace:s}}n.create=e;function t(r){const i=r;return i&&p.string(i.newText)&&P.is(i.insert)&&P.is(i.replace)}n.is=t})(Eo||(Eo={}));var ko;(function(n){n.asIs=1,n.adjustIndentation=2})(ko||(ko={}));var xo;(function(n){function e(t){const r=t;return r&&(p.string(r.detail)||r.detail===void 0)&&(p.string(r.description)||r.description===void 0)}n.is=e})(xo||(xo={}));var So;(function(n){function e(t){return{label:t}}n.create=e})(So||(So={}));var Io;(function(n){function e(t,r){return{items:t||[],isIncomplete:!!r}}n.create=e})(Io||(Io={}));var li;(function(n){function e(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}n.fromPlainText=e;function t(r){const i=r;return p.string(i)||p.objectLiteral(i)&&p.string(i.language)&&p.string(i.value)}n.is=t})(li||(li={}));var $o;(function(n){function e(t){let r=t;return!!r&&p.objectLiteral(r)&&(Pn.is(r.contents)||li.is(r.contents)||p.typedArray(r.contents,li.is))&&(t.range===void 0||P.is(t.range))}n.is=e})($o||($o={}));var Co;(function(n){function e(t,r){return r?{label:t,documentation:r}:{label:t}}n.create=e})(Co||(Co={}));var No;(function(n){function e(t,r,...i){let s={label:t};return p.defined(r)&&(s.documentation=r),p.defined(i)?s.parameters=i:s.parameters=[],s}n.create=e})(No||(No={}));var wo;(function(n){n.Text=1,n.Read=2,n.Write=3})(wo||(wo={}));var _o;(function(n){function e(t,r){let i={range:t};return p.number(r)&&(i.kind=r),i}n.create=e})(_o||(_o={}));var Lo;(function(n){n.File=1,n.Module=2,n.Namespace=3,n.Package=4,n.Class=5,n.Method=6,n.Property=7,n.Field=8,n.Constructor=9,n.Enum=10,n.Interface=11,n.Function=12,n.Variable=13,n.Constant=14,n.String=15,n.Number=16,n.Boolean=17,n.Array=18,n.Object=19,n.Key=20,n.Null=21,n.EnumMember=22,n.Struct=23,n.Event=24,n.Operator=25,n.TypeParameter=26})(Lo||(Lo={}));var Oo;(function(n){n.Deprecated=1})(Oo||(Oo={}));var bo;(function(n){function e(t,r,i,s,a){let o={name:t,kind:r,location:{uri:s,range:i}};return a&&(o.containerName=a),o}n.create=e})(bo||(bo={}));var Po;(function(n){function e(t,r,i,s){return s!==void 0?{name:t,kind:r,location:{uri:i,range:s}}:{name:t,kind:r,location:{uri:i}}}n.create=e})(Po||(Po={}));var Mo;(function(n){function e(r,i,s,a,o,l){let c={name:r,detail:i,kind:s,range:a,selectionRange:o};return l!==void 0&&(c.children=l),c}n.create=e;function t(r){let i=r;return i&&p.string(i.name)&&p.number(i.kind)&&P.is(i.range)&&P.is(i.selectionRange)&&(i.detail===void 0||p.string(i.detail))&&(i.deprecated===void 0||p.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}n.is=t})(Mo||(Mo={}));var Do;(function(n){n.Empty="",n.QuickFix="quickfix",n.Refactor="refactor",n.RefactorExtract="refactor.extract",n.RefactorInline="refactor.inline",n.RefactorRewrite="refactor.rewrite",n.Source="source",n.SourceOrganizeImports="source.organizeImports",n.SourceFixAll="source.fixAll"})(Do||(Do={}));var ci;(function(n){n.Invoked=1,n.Automatic=2})(ci||(ci={}));var Fo;(function(n){function e(r,i,s){let a={diagnostics:r};return i!=null&&(a.only=i),s!=null&&(a.triggerKind=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.typedArray(i.diagnostics,oi.is)&&(i.only===void 0||p.typedArray(i.only,p.string))&&(i.triggerKind===void 0||i.triggerKind===ci.Invoked||i.triggerKind===ci.Automatic)}n.is=t})(Fo||(Fo={}));var Go;(function(n){function e(r,i,s){let a={title:r},o=!0;return typeof i=="string"?(o=!1,a.kind=i):Pt.is(i)?a.command=i:a.edit=i,o&&s!==void 0&&(a.kind=s),a}n.create=e;function t(r){let i=r;return i&&p.string(i.title)&&(i.diagnostics===void 0||p.typedArray(i.diagnostics,oi.is))&&(i.kind===void 0||p.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Pt.is(i.command))&&(i.isPreferred===void 0||p.boolean(i.isPreferred))&&(i.edit===void 0||_s.is(i.edit))}n.is=t})(Go||(Go={}));var Uo;(function(n){function e(r,i){let s={range:r};return p.defined(i)&&(s.data=i),s}n.create=e;function t(r){let i=r;return p.defined(i)&&P.is(i.range)&&(p.undefined(i.command)||Pt.is(i.command))}n.is=t})(Uo||(Uo={}));var Bo;(function(n){function e(r,i){return{tabSize:r,insertSpaces:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.uinteger(i.tabSize)&&p.boolean(i.insertSpaces)}n.is=t})(Bo||(Bo={}));var Vo;(function(n){function e(r,i,s){return{range:r,target:i,data:s}}n.create=e;function t(r){let i=r;return p.defined(i)&&P.is(i.range)&&(p.undefined(i.target)||p.string(i.target))}n.is=t})(Vo||(Vo={}));var Wo;(function(n){function e(r,i){return{range:r,parent:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&P.is(i.range)&&(i.parent===void 0||n.is(i.parent))}n.is=t})(Wo||(Wo={}));var Ko;(function(n){n.namespace="namespace",n.type="type",n.class="class",n.enum="enum",n.interface="interface",n.struct="struct",n.typeParameter="typeParameter",n.parameter="parameter",n.variable="variable",n.property="property",n.enumMember="enumMember",n.event="event",n.function="function",n.method="method",n.macro="macro",n.keyword="keyword",n.modifier="modifier",n.comment="comment",n.string="string",n.number="number",n.regexp="regexp",n.operator="operator",n.decorator="decorator"})(Ko||(Ko={}));var Ho;(function(n){n.declaration="declaration",n.definition="definition",n.readonly="readonly",n.static="static",n.deprecated="deprecated",n.abstract="abstract",n.async="async",n.modification="modification",n.documentation="documentation",n.defaultLibrary="defaultLibrary"})(Ho||(Ho={}));var jo;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&(r.resultId===void 0||typeof r.resultId=="string")&&Array.isArray(r.data)&&(r.data.length===0||typeof r.data[0]=="number")}n.is=e})(jo||(jo={}));var zo;(function(n){function e(r,i){return{range:r,text:i}}n.create=e;function t(r){const i=r;return i!=null&&P.is(i.range)&&p.string(i.text)}n.is=t})(zo||(zo={}));var qo;(function(n){function e(r,i,s){return{range:r,variableName:i,caseSensitiveLookup:s}}n.create=e;function t(r){const i=r;return i!=null&&P.is(i.range)&&p.boolean(i.caseSensitiveLookup)&&(p.string(i.variableName)||i.variableName===void 0)}n.is=t})(qo||(qo={}));var Yo;(function(n){function e(r,i){return{range:r,expression:i}}n.create=e;function t(r){const i=r;return i!=null&&P.is(i.range)&&(p.string(i.expression)||i.expression===void 0)}n.is=t})(Yo||(Yo={}));var Xo;(function(n){function e(r,i){return{frameId:r,stoppedLocation:i}}n.create=e;function t(r){const i=r;return p.defined(i)&&P.is(r.stoppedLocation)}n.is=t})(Xo||(Xo={}));var bs;(function(n){n.Type=1,n.Parameter=2;function e(t){return t===1||t===2}n.is=e})(bs||(bs={}));var Ps;(function(n){function e(r){return{value:r}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&(i.tooltip===void 0||p.string(i.tooltip)||Pn.is(i.tooltip))&&(i.location===void 0||ai.is(i.location))&&(i.command===void 0||Pt.is(i.command))}n.is=t})(Ps||(Ps={}));var Jo;(function(n){function e(r,i,s){const a={position:r,label:i};return s!==void 0&&(a.kind=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&M.is(i.position)&&(p.string(i.label)||p.typedArray(i.label,Ps.is))&&(i.kind===void 0||bs.is(i.kind))&&i.textEdits===void 0||p.typedArray(i.textEdits,Mt.is)&&(i.tooltip===void 0||p.string(i.tooltip)||Pn.is(i.tooltip))&&(i.paddingLeft===void 0||p.boolean(i.paddingLeft))&&(i.paddingRight===void 0||p.boolean(i.paddingRight))}n.is=t})(Jo||(Jo={}));var Qo;(function(n){function e(t){return{kind:"snippet",value:t}}n.createSnippet=e})(Qo||(Qo={}));var Zo;(function(n){function e(t,r,i,s){return{insertText:t,filterText:r,range:i,command:s}}n.create=e})(Zo||(Zo={}));var el;(function(n){function e(t){return{items:t}}n.create=e})(el||(el={}));var tl;(function(n){n.Invoked=0,n.Automatic=1})(tl||(tl={}));var nl;(function(n){function e(t,r){return{range:t,text:r}}n.create=e})(nl||(nl={}));var rl;(function(n){function e(t,r){return{triggerKind:t,selectedCompletionInfo:r}}n.create=e})(rl||(rl={}));var il;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&ks.is(r.uri)&&p.string(r.name)}n.is=e})(il||(il={}));var sl;(function(n){function e(s,a,o,l){return new Yp(s,a,o,l)}n.create=e;function t(s){let a=s;return!!(p.defined(a)&&p.string(a.uri)&&(p.undefined(a.languageId)||p.string(a.languageId))&&p.uinteger(a.lineCount)&&p.func(a.getText)&&p.func(a.positionAt)&&p.func(a.offsetAt))}n.is=t;function r(s,a){let o=s.getText(),l=i(a,(u,d)=>{let h=u.range.start.line-d.range.start.line;return h===0?u.range.start.character-d.range.start.character:h}),c=o.length;for(let u=l.length-1;u>=0;u--){let d=l[u],h=s.offsetAt(d.range.start),f=s.offsetAt(d.range.end);if(f<=c)o=o.substring(0,h)+d.newText+o.substring(f,o.length);else throw new Error("Overlapping edit");c=h}return o}n.applyEdits=r;function i(s,a){if(s.length<=1)return s;const o=s.length/2|0,l=s.slice(0,o),c=s.slice(o);i(l,a),i(c,a);let u=0,d=0,h=0;for(;u<l.length&&d<c.length;)a(l[u],c[d])<=0?s[h++]=l[u++]:s[h++]=c[d++];for(;u<l.length;)s[h++]=l[u++];for(;d<c.length;)s[h++]=c[d++];return s}})(sl||(sl={}));let Yp=class{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,r=!0;for(let i=0;i<t.length;i++){r&&(e.push(i),r=!1);let s=t.charAt(i);r=s==="\r"||s===`
`,s==="\r"&&i+1<t.length&&t.charAt(i+1)===`
`&&i++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,i=t.length;if(i===0)return M.create(0,e);for(;r<i;){let a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}let s=r-1;return M.create(s,e-t[s])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,i),r)}get lineCount(){return this.getLineOffsets().length}};var p;(function(n){const e=Object.prototype.toString;function t(f){return typeof f!="undefined"}n.defined=t;function r(f){return typeof f=="undefined"}n.undefined=r;function i(f){return f===!0||f===!1}n.boolean=i;function s(f){return e.call(f)==="[object String]"}n.string=s;function a(f){return e.call(f)==="[object Number]"}n.number=a;function o(f,m,g){return e.call(f)==="[object Number]"&&m<=f&&f<=g}n.numberRange=o;function l(f){return e.call(f)==="[object Number]"&&-2147483648<=f&&f<=2147483647}n.integer=l;function c(f){return e.call(f)==="[object Number]"&&0<=f&&f<=2147483647}n.uinteger=c;function u(f){return e.call(f)==="[object Function]"}n.func=u;function d(f){return f!==null&&typeof f=="object"}n.objectLiteral=d;function h(f,m){return Array.isArray(f)&&f.every(m)}n.typedArray=h})(p||(p={}));class Xp{constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new Wc(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new ha;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const r=new Ms(e.startOffset,e.image.length,cs(e),e.tokenType,!t);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){const t=e.container;if(t){const r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}addHiddenNodes(e){const t=[];for(const s of e){const a=new Ms(s.startOffset,s.image.length,cs(s),s.tokenType,!0);a.root=this.rootNode,t.push(a)}let r=this.current,i=!1;if(r.content.length>0){r.content.push(...t);return}for(;r.container;){const s=r.container.content.indexOf(r);if(s>0){r.container.content.splice(s,0,...t),i=!0;break}r=r.container}i||this.rootNode.content.unshift(...t)}construct(e){const t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;const r=this.nodeStack.pop();(r==null?void 0:r.content.length)===0&&this.removeNode(r)}}class Vc{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const r=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!r)throw new Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class Ms extends Vc{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=r}}class ha extends Vc{constructor(){super(...arguments),this.content=new pa(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){const{range:r}=e,{range:i}=t;this._rangeCache={start:r.start,end:i.end.line<r.start.line?r.start:i.end}}return this._rangeCache}else return{start:M.create(0,0),end:M.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class pa extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,pa.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(const t of e)t.container=this.parent}}class Wc extends ha{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e!=null?e:""}}const Ds=Symbol("Datatype");function qi(n){return n.$type===Ds}const al="​",Kc=n=>n.endsWith(al)?n:n+al;class Hc{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition,r=e.LanguageMetaData.mode==="production";this.wrapper=new tm(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:r,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class Jp extends Hc{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Xp,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const r=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(Kc(e.name),this.startImplementation(r,t).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(Jl(e))return Ds;{const t=Zs(e);return t!=null?t:e.name}}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);const r=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=r.tokens;const i=t.rule?this.allRules.get(t.rule):this.mainRule;if(!i)throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");const s=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(r.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:s,lexerErrors:r.errors,lexerReport:r.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{const i=!this.isRecording()&&e!==void 0;if(i){const a={$type:e};this.stack.push(a),e===Ds&&(a.value="")}let s;try{s=t(r)}catch(a){s=void 0}return s===void 0&&i&&(s=this.construct()),s}}extractHiddenTokens(e){const t=this.lexerResult.hidden;if(!t.length)return[];const r=e.startOffset;for(let i=0;i<t.length;i++)if(t[i].startOffset>r)return t.splice(0,i);return t.splice(0,t.length)}consume(e,t,r){const i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){const s=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(s);const a=this.nodeBuilder.buildLeafNode(i,r),{assignment:o,isCrossRef:l}=this.getAssignment(r),c=this.current;if(o){const u=ft(r)?i.image:this.converter.convert(i.image,a);this.assign(o.operator,o.feature,u,a,l)}else if(qi(c)){let u=i.image;ft(r)||(u=this.converter.convert(u,a).toString()),c.value+=u}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,r,i,s){let a;!this.isRecording()&&!r&&(a=this.nodeBuilder.buildCompositeNode(i));const o=this.wrapper.wrapSubrule(e,t,s);!this.isRecording()&&a&&a.length>0&&this.performSubruleAssignment(o,i,a)}performSubruleAssignment(e,t,r){const{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,r,s);else if(!i){const a=this.current;if(qi(a))a.value+=e.toString();else if(typeof e=="object"&&e){const l=this.assignWithoutOverride(e,a);this.stack.pop(),this.stack.push(l)}}}action(e,t){if(!this.isRecording()){let r=this.current;if(t.feature&&t.operator){r=this.construct(),this.nodeBuilder.removeNode(r.$cstNode),this.nodeBuilder.buildCompositeNode(t).content.push(r.$cstNode);const s={$type:e};this.stack.push(s),this.assign(t.operator,t.feature,r,r.$cstNode,!1)}else r.$type=e}}construct(){if(this.isRecording())return;const e=this.current;return wd(e),this.nodeBuilder.construct(e),this.stack.pop(),qi(e)?this.converter.convert(e.value,e.$cstNode):(_d(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=vi(e,dt);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?Ys(t.terminal):!1})}return this.assignmentMap.get(e)}assign(e,t,r,i,s){const a=this.current;let o;switch(s&&typeof r=="string"?o=this.linker.buildReference(a,t,i,r):o=r,e){case"=":{a[t]=o;break}case"?=":{a[t]=!0;break}case"+=":Array.isArray(a[t])||(a[t]=[]),a[t].push(o)}}assignWithoutOverride(e,t){for(const[i,s]of Object.entries(t)){const a=e[i];a===void 0?e[i]=s:Array.isArray(a)&&Array.isArray(s)&&(s.push(...a),e[i]=s)}const r=e.$cstNode;return r&&(r.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}}class Qp{buildMismatchTokenMessage(e){return Ct.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return Ct.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return Ct.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return Ct.buildEarlyExitMessage(e)}}class jc extends Qp{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class Zp extends Hc{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const r=this.wrapper.DEFINE_RULE(Kc(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,r),e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,i,s){this.before(i),this.wrapper.wrapSubrule(e,t,s),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}const em={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new jc};class tm extends lp{constructor(e,t){const r=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},em),{lookaheadStrategy:r?new oa({maxLookahead:t.maxLookahead}):new Np({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}function zc(n,e,t){return nm({parser:e,tokens:t,ruleNames:new Map},n),e}function nm(n,e){const t=jl(e,!1),r=ee(e.rules).filter(_e).filter(i=>t.has(i));for(const i of r){const s=Object.assign(Object.assign({},n),{consume:1,optional:1,subrule:1,many:1,or:1});n.parser.rule(i,mt(s,i.definition))}}function mt(n,e,t=!1){let r;if(ft(e))r=cm(n,e);else if(Ri(e))r=rm(n,e);else if(dt(e))r=mt(n,e.terminal);else if(Ys(e))r=qc(n,e);else if(ht(e))r=im(n,e);else if(Gl(e))r=am(n,e);else if(Bl(e))r=om(n,e);else if(Xs(e))r=lm(n,e);else if(kd(e)){const i=n.consume++;r=()=>n.parser.consume(i,rt,e)}else throw new Pl(e.$cstNode,`Unexpected element type: ${e.$type}`);return Yc(n,t?void 0:ui(e),r,e.cardinality)}function rm(n,e){const t=ea(e);return()=>n.parser.action(t,e)}function im(n,e){const t=e.rule.ref;if(_e(t)){const r=n.subrule++,i=t.fragment,s=e.arguments.length>0?sm(t,e.arguments):()=>({});return a=>n.parser.subrule(r,Xc(n,t),i,e,s(a))}else if(Tt(t)){const r=n.consume++,i=Fs(n,t.name);return()=>n.parser.consume(r,i,e)}else if(t)Zn();else throw new Pl(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}function sm(n,e){const t=e.map(r=>ze(r.value));return r=>{const i={};for(let s=0;s<t.length;s++){const a=n.parameters[s],o=t[s];i[a.name]=o(r)}return i}}function ze(n){if(yd(n)){const e=ze(n.left),t=ze(n.right);return r=>e(r)||t(r)}else if(gd(n)){const e=ze(n.left),t=ze(n.right);return r=>e(r)&&t(r)}else if(Td(n)){const e=ze(n.value);return t=>!e(t)}else if(Rd(n)){const e=n.parameter.ref.name;return t=>t!==void 0&&t[e]===!0}else if(md(n)){const e=!!n.true;return()=>e}Zn()}function am(n,e){if(e.elements.length===1)return mt(n,e.elements[0]);{const t=[];for(const i of e.elements){const s={ALT:mt(n,i,!0)},a=ui(i);a&&(s.GATE=ze(a)),t.push(s)}const r=n.or++;return i=>n.parser.alternatives(r,t.map(s=>{const a={ALT:()=>s.ALT(i)},o=s.GATE;return o&&(a.GATE=()=>o(i)),a}))}}function om(n,e){if(e.elements.length===1)return mt(n,e.elements[0]);const t=[];for(const o of e.elements){const l={ALT:mt(n,o,!0)},c=ui(o);c&&(l.GATE=ze(c)),t.push(l)}const r=n.or++,i=(o,l)=>{const c=l.getRuleStack().join("-");return`uGroup_${o}_${c}`},s=o=>n.parser.alternatives(r,t.map((l,c)=>{const u={ALT:()=>!0},d=n.parser;u.ALT=()=>{if(l.ALT(o),!d.isRecording()){const f=i(r,d);d.unorderedGroups.get(f)||d.unorderedGroups.set(f,[]);const m=d.unorderedGroups.get(f);typeof(m==null?void 0:m[c])=="undefined"&&(m[c]=!0)}};const h=l.GATE;return h?u.GATE=()=>h(o):u.GATE=()=>{const f=d.unorderedGroups.get(i(r,d));return!(f!=null&&f[c])},u})),a=Yc(n,ui(e),s,"*");return o=>{a(o),n.parser.isRecording()||n.parser.unorderedGroups.delete(i(r,n.parser))}}function lm(n,e){const t=e.elements.map(r=>mt(n,r));return r=>t.forEach(i=>i(r))}function ui(n){if(Xs(n))return n.guardCondition}function qc(n,e,t=e.terminal){if(t)if(ht(t)&&_e(t.rule.ref)){const r=t.rule.ref,i=n.subrule++;return s=>n.parser.subrule(i,Xc(n,r),!1,e,s)}else if(ht(t)&&Tt(t.rule.ref)){const r=n.consume++,i=Fs(n,t.rule.ref.name);return()=>n.parser.consume(r,i,e)}else if(ft(t)){const r=n.consume++,i=Fs(n,t.value);return()=>n.parser.consume(r,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const r=Yl(e.type.ref),i=r==null?void 0:r.terminal;if(!i)throw new Error("Could not find name assignment for type: "+ea(e.type.ref));return qc(n,e,i)}}function cm(n,e){const t=n.consume++,r=n.tokens[e.value];if(!r)throw new Error("Could not find token for keyword: "+e.value);return()=>n.parser.consume(t,r,e)}function Yc(n,e,t,r){const i=e&&ze(e);if(!r)if(i){const s=n.or++;return a=>n.parser.alternatives(s,[{ALT:()=>t(a),GATE:()=>i(a)},{ALT:eo(),GATE:()=>!i(a)}])}else return t;if(r==="*"){const s=n.many++;return a=>n.parser.many(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else if(r==="+"){const s=n.many++;if(i){const a=n.or++;return o=>n.parser.alternatives(a,[{ALT:()=>n.parser.atLeastOne(s,{DEF:()=>t(o)}),GATE:()=>i(o)},{ALT:eo(),GATE:()=>!i(o)}])}else return a=>n.parser.atLeastOne(s,{DEF:()=>t(a)})}else if(r==="?"){const s=n.optional++;return a=>n.parser.optional(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else Zn()}function Xc(n,e){const t=um(n,e),r=n.parser.getRule(t);if(!r)throw new Error(`Rule "${t}" not found."`);return r}function um(n,e){if(_e(e))return e.name;if(n.ruleNames.has(e))return n.ruleNames.get(e);{let t=e,r=t.$container,i=e.$type;for(;!_e(r);)(Xs(r)||Gl(r)||Bl(r))&&(i=r.elements.indexOf(t).toString()+":"+i),t=r,r=r.$container;return i=r.name+":"+i,n.ruleNames.set(e,i),i}}function Fs(n,e){const t=n.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}function dm(n){const e=n.Grammar,t=n.parser.Lexer,r=new Zp(n);return zc(e,r,t.definition),r.finalize(),r}function fm(n){const e=hm(n);return e.finalize(),e}function hm(n){const e=n.Grammar,t=n.parser.Lexer,r=new Jp(n);return zc(e,r,t.definition)}class Jc{constructor(){this.diagnostics=[]}buildTokens(e,t){const r=ee(jl(e,!1)),i=this.buildTerminalTokens(r),s=this.buildKeywordTokens(r,i,t);return i.forEach(a=>{const o=a.PATTERN;typeof o=="object"&&o&&"test"in o&&fs(o)?s.unshift(a):s.push(a)}),s}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(Tt).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){const t=ta(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:r};return typeof r=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=fs(t)?fe.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(r,i)=>(t.lastIndex=i,t.exec(r))}buildKeywordTokens(e,t,r){return e.filter(_e).flatMap(i=>er(i).filter(ft)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!(r!=null&&r.caseInsensitive)))}buildKeywordToken(e,t,r){const i=this.buildKeywordPattern(e,r),s={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,t)};return typeof i=="function"&&(s.LINE_BREAKS=!0),s}buildKeywordPattern(e,t){return t?new RegExp(Ud(e.value)):e.value}findLongerAlt(e,t){return t.reduce((r,i)=>{const s=i==null?void 0:i.PATTERN;return s!=null&&s.source&&Bd("^"+s.source+"$",e.value)&&r.push(i),r},[])}}class Qc{convert(e,t){let r=t.grammarSource;if(Ys(r)&&(r=Hd(r)),ht(r)){const i=r.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,r){var i;switch(e.name.toUpperCase()){case"INT":return He.convertInt(t);case"STRING":return He.convertString(t);case"ID":return He.convertID(t)}switch((i=Qd(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return He.convertNumber(t);case"boolean":return He.convertBoolean(t);case"bigint":return He.convertBigint(t);case"date":return He.convertDate(t);default:return t}}}var He;(function(n){function e(c){let u="";for(let d=1;d<c.length-1;d++){const h=c.charAt(d);if(h==="\\"){const f=c.charAt(++d);u+=t(f)}else u+=h}return u}n.convertString=e;function t(c){switch(c){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return c}}function r(c){return c.charAt(0)==="^"?c.substring(1):c}n.convertID=r;function i(c){return parseInt(c)}n.convertInt=i;function s(c){return BigInt(c)}n.convertBigint=s;function a(c){return new Date(c)}n.convertDate=a;function o(c){return Number(c)}n.convertNumber=o;function l(c){return c.toLowerCase()==="true"}n.convertBoolean=l})(He||(He={}));var Mn={},wi={};Object.defineProperty(wi,"__esModule",{value:!0});let Gs;function Us(){if(Gs===void 0)throw new Error("No runtime abstraction layer installed");return Gs}(function(n){function e(t){if(t===void 0)throw new Error("No runtime abstraction layer provided");Gs=t}n.install=e})(Us||(Us={}));wi.default=Us;var ae={};Object.defineProperty(ae,"__esModule",{value:!0});ae.stringArray=ae.array=ae.func=ae.error=ae.number=ae.string=ae.boolean=void 0;function pm(n){return n===!0||n===!1}ae.boolean=pm;function Zc(n){return typeof n=="string"||n instanceof String}ae.string=Zc;function mm(n){return typeof n=="number"||n instanceof Number}ae.number=mm;function gm(n){return n instanceof Error}ae.error=gm;function ym(n){return typeof n=="function"}ae.func=ym;function eu(n){return Array.isArray(n)}ae.array=eu;function Tm(n){return eu(n)&&n.every(e=>Zc(e))}ae.stringArray=Tm;var Ft={};Object.defineProperty(Ft,"__esModule",{value:!0});var tu=Ft.Emitter=Ft.Event=void 0;const Rm=wi;var ol;(function(n){const e={dispose(){}};n.None=function(){return e}})(ol||(Ft.Event=ol={}));class vm{add(e,t=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(r)&&r.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(!this._callbacks)return;let r=!1;for(let i=0,s=this._callbacks.length;i<s;i++)if(this._callbacks[i]===e)if(this._contexts[i]===t){this._callbacks.splice(i,1),this._contexts.splice(i,1);return}else r=!0;if(r)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];const t=[],r=this._callbacks.slice(0),i=this._contexts.slice(0);for(let s=0,a=r.length;s<a;s++)try{t.push(r[s].apply(i[s],e))}catch(o){(0,Rm.default)().console.error(o)}return t}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class _i{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,r)=>{this._callbacks||(this._callbacks=new vm),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t);const i={dispose:()=>{this._callbacks&&(this._callbacks.remove(e,t),i.dispose=_i._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(r)&&r.push(i),i}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}tu=Ft.Emitter=_i;_i._noop=function(){};var W;Object.defineProperty(Mn,"__esModule",{value:!0});var ma=Mn.CancellationTokenSource=W=Mn.CancellationToken=void 0;const Am=wi,Em=ae,Bs=Ft;var di;(function(n){n.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Bs.Event.None}),n.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Bs.Event.None});function e(t){const r=t;return r&&(r===n.None||r===n.Cancelled||Em.boolean(r.isCancellationRequested)&&!!r.onCancellationRequested)}n.is=e})(di||(W=Mn.CancellationToken=di={}));const km=Object.freeze(function(n,e){const t=(0,Am.default)().timer.setTimeout(n.bind(e),0);return{dispose(){t.dispose()}}});class ll{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?km:(this._emitter||(this._emitter=new Bs.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class xm{get token(){return this._token||(this._token=new ll),this._token}cancel(){this._token?this._token.cancel():this._token=di.Cancelled}dispose(){this._token?this._token instanceof ll&&this._token.dispose():this._token=di.None}}ma=Mn.CancellationTokenSource=xm;function Sm(){return new Promise(n=>{typeof setImmediate=="undefined"?setTimeout(n,0):setImmediate(n)})}let Dr=0,Im=10;function $m(){return Dr=performance.now(),new ma}const fi=Symbol("OperationCancelled");function Li(n){return n===fi}function Ee(n){return w(this,null,function*(){if(n===W.None)return;const e=performance.now();if(e-Dr>=Im&&(Dr=e,yield Sm(),Dr=performance.now()),n.isCancellationRequested)throw fi})}class ga{constructor(){this.promise=new Promise((e,t)=>{this.resolve=r=>(e(r),this),this.reject=r=>(t(r),this)})}}class Dn{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(const r of e)if(Dn.isIncremental(r)){const i=ru(r.range),s=this.offsetAt(i.start),a=this.offsetAt(i.end);this._content=this._content.substring(0,s)+r.text+this._content.substring(a,this._content.length);const o=Math.max(i.start.line,0),l=Math.max(i.end.line,0);let c=this._lineOffsets;const u=cl(r.text,!1,s);if(l-o===u.length)for(let h=0,f=u.length;h<f;h++)c[h+o+1]=u[h];else u.length<1e4?c.splice(o+1,l-o,...u):this._lineOffsets=c=c.slice(0,o+1).concat(u,c.slice(l+1));const d=r.text.length-(a-s);if(d!==0)for(let h=o+1+u.length,f=c.length;h<f;h++)c[h]=c[h]+d}else if(Dn.isFull(r))this._content=r.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=cl(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let r=0,i=t.length;if(i===0)return{line:0,character:e};for(;r<i;){const a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}const s=r-1;return e=this.ensureBeforeEOL(e,t[s]),{line:s,character:e-t[s]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const r=t[e.line];if(e.character<=0)return r;const i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(r+e.character,i);return this.ensureBeforeEOL(s,r)}ensureBeforeEOL(e,t){for(;e>t&&nu(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}}var Vs;(function(n){function e(i,s,a,o){return new Dn(i,s,a,o)}n.create=e;function t(i,s,a){if(i instanceof Dn)return i.update(s,a),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}n.update=t;function r(i,s){const a=i.getText(),o=Ws(s.map(Cm),(u,d)=>{const h=u.range.start.line-d.range.start.line;return h===0?u.range.start.character-d.range.start.character:h});let l=0;const c=[];for(const u of o){const d=i.offsetAt(u.range.start);if(d<l)throw new Error("Overlapping edit");d>l&&c.push(a.substring(l,d)),u.newText.length&&c.push(u.newText),l=i.offsetAt(u.range.end)}return c.push(a.substr(l)),c.join("")}n.applyEdits=r})(Vs||(Vs={}));function Ws(n,e){if(n.length<=1)return n;const t=n.length/2|0,r=n.slice(0,t),i=n.slice(t);Ws(r,e),Ws(i,e);let s=0,a=0,o=0;for(;s<r.length&&a<i.length;)e(r[s],i[a])<=0?n[o++]=r[s++]:n[o++]=i[a++];for(;s<r.length;)n[o++]=r[s++];for(;a<i.length;)n[o++]=i[a++];return n}function cl(n,e,t=0){const r=e?[t]:[];for(let i=0;i<n.length;i++){const s=n.charCodeAt(i);nu(s)&&(s===13&&i+1<n.length&&n.charCodeAt(i+1)===10&&i++,r.push(t+i+1))}return r}function nu(n){return n===13||n===10}function ru(n){const e=n.start,t=n.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:n}function Cm(n){const e=ru(n.range);return e!==n.range?{newText:n.newText,range:e}:n}var iu;(()=>{var n={470:i=>{function s(l){if(typeof l!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(l))}function a(l,c){for(var u,d="",h=0,f=-1,m=0,g=0;g<=l.length;++g){if(g<l.length)u=l.charCodeAt(g);else{if(u===47)break;u=47}if(u===47){if(!(f===g-1||m===1))if(f!==g-1&&m===2){if(d.length<2||h!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var v=d.lastIndexOf("/");if(v!==d.length-1){v===-1?(d="",h=0):h=(d=d.slice(0,v)).length-1-d.lastIndexOf("/"),f=g,m=0;continue}}else if(d.length===2||d.length===1){d="",h=0,f=g,m=0;continue}}c&&(d.length>0?d+="/..":d="..",h=2)}else d.length>0?d+="/"+l.slice(f+1,g):d=l.slice(f+1,g),h=g-f-1;f=g,m=0}else u===46&&m!==-1?++m:m=-1}return d}var o={resolve:function(){for(var l,c="",u=!1,d=arguments.length-1;d>=-1&&!u;d--){var h;d>=0?h=arguments[d]:(l===void 0&&(l=process.cwd()),h=l),s(h),h.length!==0&&(c=h+"/"+c,u=h.charCodeAt(0)===47)}return c=a(c,!u),u?c.length>0?"/"+c:"/":c.length>0?c:"."},normalize:function(l){if(s(l),l.length===0)return".";var c=l.charCodeAt(0)===47,u=l.charCodeAt(l.length-1)===47;return(l=a(l,!c)).length!==0||c||(l="."),l.length>0&&u&&(l+="/"),c?"/"+l:l},isAbsolute:function(l){return s(l),l.length>0&&l.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var l,c=0;c<arguments.length;++c){var u=arguments[c];s(u),u.length>0&&(l===void 0?l=u:l+="/"+u)}return l===void 0?".":o.normalize(l)},relative:function(l,c){if(s(l),s(c),l===c||(l=o.resolve(l))===(c=o.resolve(c)))return"";for(var u=1;u<l.length&&l.charCodeAt(u)===47;++u);for(var d=l.length,h=d-u,f=1;f<c.length&&c.charCodeAt(f)===47;++f);for(var m=c.length-f,g=h<m?h:m,v=-1,y=0;y<=g;++y){if(y===g){if(m>g){if(c.charCodeAt(f+y)===47)return c.slice(f+y+1);if(y===0)return c.slice(f+y)}else h>g&&(l.charCodeAt(u+y)===47?v=y:y===0&&(v=0));break}var A=l.charCodeAt(u+y);if(A!==c.charCodeAt(f+y))break;A===47&&(v=y)}var R="";for(y=u+v+1;y<=d;++y)y!==d&&l.charCodeAt(y)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+c.slice(f+v):(f+=v,c.charCodeAt(f)===47&&++f,c.slice(f))},_makeLong:function(l){return l},dirname:function(l){if(s(l),l.length===0)return".";for(var c=l.charCodeAt(0),u=c===47,d=-1,h=!0,f=l.length-1;f>=1;--f)if((c=l.charCodeAt(f))===47){if(!h){d=f;break}}else h=!1;return d===-1?u?"/":".":u&&d===1?"//":l.slice(0,d)},basename:function(l,c){if(c!==void 0&&typeof c!="string")throw new TypeError('"ext" argument must be a string');s(l);var u,d=0,h=-1,f=!0;if(c!==void 0&&c.length>0&&c.length<=l.length){if(c.length===l.length&&c===l)return"";var m=c.length-1,g=-1;for(u=l.length-1;u>=0;--u){var v=l.charCodeAt(u);if(v===47){if(!f){d=u+1;break}}else g===-1&&(f=!1,g=u+1),m>=0&&(v===c.charCodeAt(m)?--m==-1&&(h=u):(m=-1,h=g))}return d===h?h=g:h===-1&&(h=l.length),l.slice(d,h)}for(u=l.length-1;u>=0;--u)if(l.charCodeAt(u)===47){if(!f){d=u+1;break}}else h===-1&&(f=!1,h=u+1);return h===-1?"":l.slice(d,h)},extname:function(l){s(l);for(var c=-1,u=0,d=-1,h=!0,f=0,m=l.length-1;m>=0;--m){var g=l.charCodeAt(m);if(g!==47)d===-1&&(h=!1,d=m+1),g===46?c===-1?c=m:f!==1&&(f=1):c!==-1&&(f=-1);else if(!h){u=m+1;break}}return c===-1||d===-1||f===0||f===1&&c===d-1&&c===u+1?"":l.slice(c,d)},format:function(l){if(l===null||typeof l!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof l);return function(c,u){var d=u.dir||u.root,h=u.base||(u.name||"")+(u.ext||"");return d?d===u.root?d+h:d+"/"+h:h}(0,l)},parse:function(l){s(l);var c={root:"",dir:"",base:"",ext:"",name:""};if(l.length===0)return c;var u,d=l.charCodeAt(0),h=d===47;h?(c.root="/",u=1):u=0;for(var f=-1,m=0,g=-1,v=!0,y=l.length-1,A=0;y>=u;--y)if((d=l.charCodeAt(y))!==47)g===-1&&(v=!1,g=y+1),d===46?f===-1?f=y:A!==1&&(A=1):f!==-1&&(A=-1);else if(!v){m=y+1;break}return f===-1||g===-1||A===0||A===1&&f===g-1&&f===m+1?g!==-1&&(c.base=c.name=m===0&&h?l.slice(1,g):l.slice(m,g)):(m===0&&h?(c.name=l.slice(1,f),c.base=l.slice(1,g)):(c.name=l.slice(m,f),c.base=l.slice(m,g)),c.ext=l.slice(f,g)),m>0?c.dir=l.slice(0,m-1):h&&(c.dir="/"),c},sep:"/",delimiter:":",win32:null,posix:null};o.posix=o,i.exports=o}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var a=e[i]={exports:{}};return n[i](a,a.exports,t),a.exports}t.d=(i,s)=>{for(var a in s)t.o(s,a)&&!t.o(i,a)&&Object.defineProperty(i,a,{enumerable:!0,get:s[a]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var r={};(()=>{let i;t.r(r),t.d(r,{URI:()=>h,Utils:()=>Ce}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);const s=/^\w[\w\d+.-]*$/,a=/^\//,o=/^\/\//;function l(x,T){if(!x.scheme&&T)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${x.authority}", path: "${x.path}", query: "${x.query}", fragment: "${x.fragment}"}`);if(x.scheme&&!s.test(x.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(x.path){if(x.authority){if(!a.test(x.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(x.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const c="",u="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class h{constructor(T,E,I,b,O,L=!1){Ze(this,"scheme");Ze(this,"authority");Ze(this,"path");Ze(this,"query");Ze(this,"fragment");typeof T=="object"?(this.scheme=T.scheme||c,this.authority=T.authority||c,this.path=T.path||c,this.query=T.query||c,this.fragment=T.fragment||c):(this.scheme=function(Re,Y){return Re||Y?Re:"file"}(T,L),this.authority=E||c,this.path=function(Re,Y){switch(Re){case"https":case"http":case"file":Y?Y[0]!==u&&(Y=u+Y):Y=u}return Y}(this.scheme,I||c),this.query=b||c,this.fragment=O||c,l(this,L))}static isUri(T){return T instanceof h||!!T&&typeof T.authority=="string"&&typeof T.fragment=="string"&&typeof T.path=="string"&&typeof T.query=="string"&&typeof T.scheme=="string"&&typeof T.fsPath=="string"&&typeof T.with=="function"&&typeof T.toString=="function"}get fsPath(){return A(this,!1)}with(T){if(!T)return this;let{scheme:E,authority:I,path:b,query:O,fragment:L}=T;return E===void 0?E=this.scheme:E===null&&(E=c),I===void 0?I=this.authority:I===null&&(I=c),b===void 0?b=this.path:b===null&&(b=c),O===void 0?O=this.query:O===null&&(O=c),L===void 0?L=this.fragment:L===null&&(L=c),E===this.scheme&&I===this.authority&&b===this.path&&O===this.query&&L===this.fragment?this:new m(E,I,b,O,L)}static parse(T,E=!1){const I=d.exec(T);return I?new m(I[2]||c,ie(I[4]||c),ie(I[5]||c),ie(I[7]||c),ie(I[9]||c),E):new m(c,c,c,c,c)}static file(T){let E=c;if(i&&(T=T.replace(/\\/g,u)),T[0]===u&&T[1]===u){const I=T.indexOf(u,2);I===-1?(E=T.substring(2),T=u):(E=T.substring(2,I),T=T.substring(I)||u)}return new m("file",E,T,c,c)}static from(T){const E=new m(T.scheme,T.authority,T.path,T.query,T.fragment);return l(E,!0),E}toString(T=!1){return R(this,T)}toJSON(){return this}static revive(T){if(T){if(T instanceof h)return T;{const E=new m(T);return E._formatted=T.external,E._fsPath=T._sep===f?T.fsPath:null,E}}return T}}const f=i?1:void 0;class m extends h{constructor(){super(...arguments);Ze(this,"_formatted",null);Ze(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=A(this,!1)),this._fsPath}toString(E=!1){return E?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){const E={$mid:1};return this._fsPath&&(E.fsPath=this._fsPath,E._sep=f),this._formatted&&(E.external=this._formatted),this.path&&(E.path=this.path),this.scheme&&(E.scheme=this.scheme),this.authority&&(E.authority=this.authority),this.query&&(E.query=this.query),this.fragment&&(E.fragment=this.fragment),E}}const g={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function v(x,T,E){let I,b=-1;for(let O=0;O<x.length;O++){const L=x.charCodeAt(O);if(L>=97&&L<=122||L>=65&&L<=90||L>=48&&L<=57||L===45||L===46||L===95||L===126||T&&L===47||E&&L===91||E&&L===93||E&&L===58)b!==-1&&(I+=encodeURIComponent(x.substring(b,O)),b=-1),I!==void 0&&(I+=x.charAt(O));else{I===void 0&&(I=x.substr(0,O));const Re=g[L];Re!==void 0?(b!==-1&&(I+=encodeURIComponent(x.substring(b,O)),b=-1),I+=Re):b===-1&&(b=O)}}return b!==-1&&(I+=encodeURIComponent(x.substring(b))),I!==void 0?I:x}function y(x){let T;for(let E=0;E<x.length;E++){const I=x.charCodeAt(E);I===35||I===63?(T===void 0&&(T=x.substr(0,E)),T+=g[I]):T!==void 0&&(T+=x[E])}return T!==void 0?T:x}function A(x,T){let E;return E=x.authority&&x.path.length>1&&x.scheme==="file"?`//${x.authority}${x.path}`:x.path.charCodeAt(0)===47&&(x.path.charCodeAt(1)>=65&&x.path.charCodeAt(1)<=90||x.path.charCodeAt(1)>=97&&x.path.charCodeAt(1)<=122)&&x.path.charCodeAt(2)===58?T?x.path.substr(1):x.path[1].toLowerCase()+x.path.substr(2):x.path,i&&(E=E.replace(/\//g,"\\")),E}function R(x,T){const E=T?y:v;let I="",{scheme:b,authority:O,path:L,query:Re,fragment:Y}=x;if(b&&(I+=b,I+=":"),(O||b==="file")&&(I+=u,I+=u),O){let K=O.indexOf("@");if(K!==-1){const lt=O.substr(0,K);O=O.substr(K+1),K=lt.lastIndexOf(":"),K===-1?I+=E(lt,!1,!1):(I+=E(lt.substr(0,K),!1,!1),I+=":",I+=E(lt.substr(K+1),!1,!0)),I+="@"}O=O.toLowerCase(),K=O.lastIndexOf(":"),K===-1?I+=E(O,!1,!0):(I+=E(O.substr(0,K),!1,!0),I+=O.substr(K))}if(L){if(L.length>=3&&L.charCodeAt(0)===47&&L.charCodeAt(2)===58){const K=L.charCodeAt(1);K>=65&&K<=90&&(L=`/${String.fromCharCode(K+32)}:${L.substr(3)}`)}else if(L.length>=2&&L.charCodeAt(1)===58){const K=L.charCodeAt(0);K>=65&&K<=90&&(L=`${String.fromCharCode(K+32)}:${L.substr(2)}`)}I+=E(L,!0,!1)}return Re&&(I+="?",I+=E(Re,!1,!1)),Y&&(I+="#",I+=T?Y:v(Y,!1,!1)),I}function $(x){try{return decodeURIComponent(x)}catch(T){return x.length>3?x.substr(0,3)+$(x.substr(3)):x}}const G=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ie(x){return x.match(G)?x.replace(G,T=>$(T)):x}var Le=t(470);const Te=Le.posix||Le,Ge="/";var Ce;(function(x){x.joinPath=function(T,...E){return T.with({path:Te.join(T.path,...E)})},x.resolvePath=function(T,...E){let I=T.path,b=!1;I[0]!==Ge&&(I=Ge+I,b=!0);let O=Te.resolve(I,...E);return b&&O[0]===Ge&&!T.authority&&(O=O.substring(1)),T.with({path:O})},x.dirname=function(T){if(T.path.length===0||T.path===Ge)return T;let E=Te.dirname(T.path);return E.length===1&&E.charCodeAt(0)===46&&(E=""),T.with({path:E})},x.basename=function(T){return Te.basename(T.path)},x.extname=function(T){return Te.extname(T.path)}})(Ce||(Ce={}))})(),iu=r})();const{URI:gt,Utils:qt}=iu;var st;(function(n){n.basename=qt.basename,n.dirname=qt.dirname,n.extname=qt.extname,n.joinPath=qt.joinPath,n.resolvePath=qt.resolvePath;function e(i,s){return(i==null?void 0:i.toString())===(s==null?void 0:s.toString())}n.equals=e;function t(i,s){const a=typeof i=="string"?i:i.path,o=typeof s=="string"?s:s.path,l=a.split("/").filter(f=>f.length>0),c=o.split("/").filter(f=>f.length>0);let u=0;for(;u<l.length&&l[u]===c[u];u++);const d="../".repeat(l.length-u),h=c.slice(u).join("/");return d+h}n.relative=t;function r(i){return gt.parse(i.toString()).toString()}n.normalize=r})(st||(st={}));var B;(function(n){n[n.Changed=0]="Changed",n[n.Parsed=1]="Parsed",n[n.IndexedContent=2]="IndexedContent",n[n.ComputedScopes=3]="ComputedScopes",n[n.Linked=4]="Linked",n[n.IndexedReferences=5]="IndexedReferences",n[n.Validated=6]="Validated"})(B||(B={}));class Nm{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}fromUri(r){return w(this,arguments,function*(e,t=W.None){const i=yield this.fileSystemProvider.readFile(e);return this.createAsync(e,i,t)})}fromTextDocument(e,t,r){return t=t!=null?t:gt.parse(e.uri),W.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromString(e,t,r){return W.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,r){if(typeof t=="string"){const i=this.parse(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else if("$model"in t){const i={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{const i=this.parse(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}createAsync(e,t,r){return w(this,null,function*(){if(typeof t=="string"){const i=yield this.parseAsync(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else{const i=yield this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}})}createLangiumDocument(e,t,r,i){let s;if(r)s={parseResult:e,uri:t,state:B.Parsed,references:[],textDocument:r};else{const a=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:B.Parsed,references:[],get textDocument(){return a()}}}return e.value.$document=s,s}update(e,t){return w(this,null,function*(){var r,i;const s=(r=e.parseResult.value.$cstNode)===null||r===void 0?void 0:r.root.fullText,a=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),o=a?a.getText():yield this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{const l=this.createTextDocumentGetter(e.uri,o);Object.defineProperty(e,"textDocument",{get:l})}return s!==o&&(e.parseResult=yield this.parseAsync(e.uri,o,t),e.parseResult.value.$document=e),e.state=B.Parsed,e})}parse(e,t,r){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,r)}parseAsync(e,t,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){const r=this.serviceRegistry;let i;return()=>i!=null?i:i=Vs.create(e.toString(),r.getServices(e).LanguageMetaData.languageId,0,t!=null?t:"")}}class wm{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return ee(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}getOrCreateDocument(e,t){return w(this,null,function*(){let r=this.getDocument(e);return r||(r=yield this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r),r)})}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(i=>(this.addDocument(i),i));{const i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(this.serviceRegistry.getServices(e).references.Linker.unlink(r),r.state=B.Changed,r.precomputedScopes=void 0,r.diagnostics=void 0),r}deleteDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=B.Changed,this.documentMap.delete(t)),r}}const Yi=Symbol("ref_resolving");class _m{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}link(r){return w(this,arguments,function*(e,t=W.None){for(const i of Nt(e.parseResult.value))yield Ee(t),Wl(i).forEach(s=>this.doLink(s,e))})}doLink(e,t){var r;const i=e.reference;if(i._ref===void 0){i._ref=Yi;try{const s=this.getCandidate(e);if(wr(s))i._ref=s;else if(i._nodeDescription=s,this.langiumDocuments().hasDocument(s.documentUri)){const a=this.loadAstNode(s);i._ref=a!=null?a:this.createLinkingError(e,s)}else i._ref=void 0}catch(s){const a=(r=s.message)!==null&&r!==void 0?r:String(s);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${a}`})}t.references.push(i)}}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const r=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return r!=null?r:this.createLinkingError(e)}buildReference(e,t,r,i){const s=this,a={$refNode:r,$refText:i,get ref(){var o;if(oe(this._ref))return this._ref;if(sd(this._nodeDescription)){const l=s.loadAstNode(this._nodeDescription);this._ref=l!=null?l:s.createLinkingError({reference:a,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){this._ref=Yi;const l=us(e).$document,c=s.getLinkedNode({reference:a,container:e,property:t});if(c.error&&l&&l.state<B.ComputedScopes)return this._ref=void 0;this._ref=(o=c.node)!==null&&o!==void 0?o:c.error,this._nodeDescription=c.descr,l==null||l.references.push(this)}else if(this._ref===Yi)throw new Error(`Cyclic reference resolution detected: ${s.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${i}')`);return oe(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return wr(this._ref)?this._ref:void 0}};return a}getLinkedNode(e){var t;try{const r=this.getCandidate(e);if(wr(r))return{error:r};const i=this.loadAstNode(r);return i?{node:i,descr:r}:{descr:r,error:this.createLinkingError(e,r)}}catch(r){const i=(t=r.message)!==null&&t!==void 0?t:String(r);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){const r=us(e.container).$document;r&&r.state<B.ComputedScopes;const i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}}function Lm(n){return typeof n.name=="string"}class Om{getName(e){if(Lm(e))return e.name}getNameNode(e){return ql(e.$cstNode,"name")}}class bm{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=Xd(e),r=e.astNode;if(t&&r){const i=r[t.feature];if(Be(i))return i.ref;if(Array.isArray(i)){for(const s of i)if(Be(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(r){const i=this.nameProvider.getNameNode(r);if(i&&(i===e||ld(e,i)))return r}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(t!=null&&t.$cstNode){const r=this.nameProvider.getNameNode(t);return r!=null?r:t.$cstNode}}findReferences(e,t){const r=[];if(t.includeDeclaration){const s=this.getReferenceToSelf(e);s&&r.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>st.equals(s.sourceUri,t.documentUri))),r.push(...i),ee(r)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const r=tt(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:i,targetUri:r.uri,targetPath:i,segment:zr(t),local:!0}}}}class hi{constructor(e){if(this.map=new Map,e)for(const[t,r]of e)this.add(t,r)}get size(){return os.sum(ee(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{const r=this.map.get(e);if(r){const i=r.indexOf(t);if(i>=0)return r.length===1?this.map.delete(e):r.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{const r=this.map.get(e);return r?r.indexOf(t)>=0:!1}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(i=>e(i,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return ee(this.map.entries()).flatMap(([e,t])=>t.map(r=>[e,r]))}keys(){return ee(this.map.keys())}values(){return ee(this.map.values()).flat()}entriesGroupedByKey(){return ee(this.map.entries())}}class ul{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return t!==void 0?(this.map.delete(e),this.inverse.delete(t),!0):!1}}class Pm{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}computeExports(r){return w(this,arguments,function*(e,t=W.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)})}computeExportsForNode(s,a){return w(this,arguments,function*(e,t,r=Js,i=W.None){const o=[];this.exportNode(e,o,t);for(const l of r(e))yield Ee(i),this.exportNode(l,o,t);return o})}exportNode(e,t,r){const i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,r))}computeLocalScopes(r){return w(this,arguments,function*(e,t=W.None){const i=e.parseResult.value,s=new hi;for(const a of er(i))yield Ee(t),this.processNode(a,e,s);return s})}processNode(e,t,r){const i=e.$container;if(i){const s=this.nameProvider.getName(e);s&&r.add(i,this.descriptions.createDescription(e,s,t))}}}class dl{constructor(e,t,r){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const t=this.caseInsensitive?this.elements.find(r=>r.name.toLowerCase()===e.toLowerCase()):this.elements.find(r=>r.name===e);if(t)return t;if(this.outerScope)return this.outerScope.getElement(e)}}class Mm{constructor(e,t,r){var i;this.elements=new Map,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0?i:!1;for(const s of e){const a=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(a,s)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(t);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=ee(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class su{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class Dm extends su{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const r=t();return this.cache.set(e,r),r}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class Fm extends su{constructor(e){super(),this.cache=new Map,this.converter=e!=null?e:t=>t}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();const i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(r){const s=r();return i.set(t,s),s}else return}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class Gm extends Dm{constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((r,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class Um{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new Gm(e.shared)}getScope(e){const t=[],r=this.reflection.getReferenceType(e),i=tt(e.container).precomputedScopes;if(i){let a=e.container;do{const o=i.get(a);o.length>0&&t.push(ee(o).filter(l=>this.reflection.isSubtype(l.type,r))),a=a.$container}while(a)}let s=this.getGlobalScope(r,e);for(let a=t.length-1;a>=0;a--)s=this.createScope(t[a],s);return s}createScope(e,t,r){return new dl(ee(e),t,r)}createScopeForNodes(e,t,r){const i=ee(e).map(s=>{const a=this.nameProvider.getName(s);if(a)return this.descriptions.createDescription(s,a)}).nonNullable();return new dl(i,t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new Mm(this.indexManager.allElements(e)))}}function Bm(n){return typeof n.$comment=="string"}function fl(n){return typeof n=="object"&&!!n&&("$ref"in n||"$error"in n)}class Vm{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){const r=t!=null?t:{},i=t==null?void 0:t.replacer,s=(o,l)=>this.replacer(o,l,r),a=i?(o,l)=>i(o,l,s):s;try{return this.currentDocument=tt(e),JSON.stringify(e,a,t==null?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t){const r=t!=null?t:{},i=JSON.parse(e);return this.linkNode(i,i,r),i}replacer(e,t,{refText:r,sourceText:i,textRegions:s,comments:a,uriConverter:o}){var l,c,u,d;if(!this.ignoreProperties.has(e))if(Be(t)){const h=t.ref,f=r?t.$refText:void 0;if(h){const m=tt(h);let g="";this.currentDocument&&this.currentDocument!==m&&(o?g=o(m.uri,t):g=m.uri.toString());const v=this.astNodeLocator.getAstNodePath(h);return{$ref:`${g}#${v}`,$refText:f}}else return{$error:(c=(l=t.error)===null||l===void 0?void 0:l.message)!==null&&c!==void 0?c:"Could not resolve reference",$refText:f}}else if(oe(t)){let h;if(s&&(h=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&(h!=null&&h.$textRegion)&&(h.$textRegion.documentURI=(u=this.currentDocument)===null||u===void 0?void 0:u.uri.toString())),i&&!e&&(h!=null||(h=Object.assign({},t)),h.$sourceText=(d=t.$cstNode)===null||d===void 0?void 0:d.text),a){h!=null||(h=Object.assign({},t));const f=this.commentProvider.getComment(t);f&&(h.$comment=f.replace(/\r/g,""))}return h!=null?h:t}else return t}addAstNodeRegionWithAssignmentsTo(e){const t=r=>({offset:r.offset,end:r.end,length:r.length,range:r.range});if(e.$cstNode){const r=e.$textRegion=t(e.$cstNode),i=r.assignments={};return Object.keys(e).filter(s=>!s.startsWith("$")).forEach(s=>{const a=zd(e.$cstNode,s).map(t);a.length!==0&&(i[s]=a)}),e}}linkNode(e,t,r,i,s,a){for(const[l,c]of Object.entries(e))if(Array.isArray(c))for(let u=0;u<c.length;u++){const d=c[u];fl(d)?c[u]=this.reviveReference(e,l,t,d,r):oe(d)&&this.linkNode(d,t,r,e,l,u)}else fl(c)?e[l]=this.reviveReference(e,l,t,c,r):oe(c)&&this.linkNode(c,t,r,e,l);const o=e;o.$container=i,o.$containerProperty=s,o.$containerIndex=a}reviveReference(e,t,r,i,s){let a=i.$refText,o=i.$error;if(i.$ref){const l=this.getRefNode(r,i.$ref,s.uriConverter);if(oe(l))return a||(a=this.nameProvider.getName(l)),{$refText:a!=null?a:"",ref:l};o=l}if(o){const l={$refText:a!=null?a:""};return l.error={container:e,property:t,message:o,reference:l},l}else return}getRefNode(e,t,r){try{const i=t.indexOf("#");if(i===0){const l=this.astNodeLocator.getAstNode(e,t.substring(1));return l||"Could not resolve path: "+t}if(i<0){const l=r?r(t):gt.parse(t),c=this.langiumDocuments.getDocument(l);return c?c.parseResult.value:"Could not find document for URI: "+t}const s=r?r(t.substring(0,i)):gt.parse(t.substring(0,i)),a=this.langiumDocuments.getDocument(s);if(!a)return"Could not find document for URI: "+t;if(i===t.length-1)return a.parseResult.value;const o=this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(i+1));return o||"Could not resolve URI: "+t}catch(i){return String(i)}}}class Wm{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e==null?void 0:e.workspace.TextDocuments}register(e){const t=e.LanguageMetaData;for(const r of t.fileExtensions)this.fileExtensionMap.has(r),this.fileExtensionMap.set(r,e);this.languageIdMap.set(t.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var t,r;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const i=(r=(t=this.textDocuments)===null||t===void 0?void 0:t.get(e))===null||r===void 0?void 0:r.languageId;if(i!==void 0){const o=this.languageIdMap.get(i);if(o)return o}const s=st.extname(e),a=this.fileExtensionMap.get(s);if(!a)throw i?new Error(`The service registry contains no services for the extension '${s}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${s}'.`);return a}hasServices(e){try{return this.getServices(e),!0}catch(t){return!1}}get all(){return Array.from(this.languageIdMap.values())}}function $n(n){return{code:n}}var pi;(function(n){n.all=["fast","slow","built-in"]})(pi||(pi={}));class Km{constructor(e){this.entries=new hi,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if(r==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[i,s]of Object.entries(e)){const a=s;if(Array.isArray(a))for(const o of a){const l={check:this.wrapValidationException(o,t),category:r};this.addEntry(i,l)}else if(typeof a=="function"){const o={check:this.wrapValidationException(a,t),category:r};this.addEntry(i,o)}else Zn()}}wrapValidationException(e,t){return(r,i,s)=>w(this,null,function*(){yield this.handleException(()=>e.call(t,r,i,s),"An error occurred during validation",i,r)})}handleException(e,t,r,i){return w(this,null,function*(){try{yield e()}catch(s){if(Li(s))throw s;s instanceof Error&&s.stack;const a=s instanceof Error?s.message:String(s);r("error",`${t}: ${a}`,{node:i})}})}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(const r of this.reflection.getAllSubTypes(e))this.entries.add(r,t)}getChecks(e,t){let r=ee(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(i=>t.includes(i.category))),r.map(i=>i.check)}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,r){return(i,s,a,o)=>w(this,null,function*(){yield this.handleException(()=>e.call(r,i,s,a,o),t,s,i)})}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class Hm{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}validateDocument(i){return w(this,arguments,function*(e,t={},r=W.None){const s=e.parseResult,a=[];if(yield Ee(r),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(s,a,t),t.stopAfterLexingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===be.LexingError})||(this.processParsingErrors(s,a,t),t.stopAfterParsingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===be.ParsingError}))||(this.processLinkingErrors(e,a,t),t.stopAfterLinkingErrors&&a.some(o=>{var l;return((l=o.data)===null||l===void 0?void 0:l.code)===be.LinkingError}))))return a;try{a.push(...yield this.validateAst(s.value,t,r))}catch(o){if(Li(o))throw o}return yield Ee(r),a})}processLexingErrors(e,t,r){var i,s,a;const o=[...e.lexerErrors,...(s=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&s!==void 0?s:[]];for(const l of o){const c=(a=l.severity)!==null&&a!==void 0?a:"error",u={severity:Xi(c),range:{start:{line:l.line-1,character:l.column-1},end:{line:l.line-1,character:l.column+l.length-1}},message:l.message,data:zm(c),source:this.getSource()};t.push(u)}}processParsingErrors(e,t,r){for(const i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){const a=i.previousToken;if(isNaN(a.startOffset)){const o={line:0,character:0};s={start:o,end:o}}else{const o={line:a.endLine-1,character:a.endColumn};s={start:o,end:o}}}}else s=cs(i.token);if(s){const a={severity:Xi("error"),range:s,message:i.message,data:$n(be.ParsingError),source:this.getSource()};t.push(a)}}}processLinkingErrors(e,t,r){for(const i of e.references){const s=i.error;if(s){const a={node:s.container,property:s.property,index:s.index,data:{code:be.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,a))}}}validateAst(i,s){return w(this,arguments,function*(e,t,r=W.None){const a=[],o=(l,c,u)=>{a.push(this.toDiagnostic(l,c,u))};return yield this.validateAstBefore(e,t,o,r),yield this.validateAstNodes(e,t,o,r),yield this.validateAstAfter(e,t,o,r),a})}validateAstBefore(s,a,o){return w(this,arguments,function*(e,t,r,i=W.None){var l;const c=this.validationRegistry.checksBefore;for(const u of c)yield Ee(i),yield u(e,r,(l=t.categories)!==null&&l!==void 0?l:[],i)})}validateAstNodes(s,a,o){return w(this,arguments,function*(e,t,r,i=W.None){yield Promise.all(Nt(e).map(l=>w(this,null,function*(){yield Ee(i);const c=this.validationRegistry.getChecks(l.$type,t.categories);for(const u of c)yield u(l,r,i)})))})}validateAstAfter(s,a,o){return w(this,arguments,function*(e,t,r,i=W.None){var l;const c=this.validationRegistry.checksAfter;for(const u of c)yield Ee(i),yield u(e,r,(l=t.categories)!==null&&l!==void 0?l:[],i)})}toDiagnostic(e,t,r){return{message:t,range:jm(r),severity:Xi(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function jm(n){if(n.range)return n.range;let e;return typeof n.property=="string"?e=ql(n.node.$cstNode,n.property,n.index):typeof n.keyword=="string"&&(e=qd(n.node.$cstNode,n.keyword,n.index)),e!=null||(e=n.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}function Xi(n){switch(n){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+n)}}function zm(n){switch(n){case"error":return $n(be.LexingError);case"warning":return $n(be.LexingWarning);case"info":return $n(be.LexingInfo);case"hint":return $n(be.LexingHint);default:throw new Error("Invalid diagnostic severity: "+n)}}var be;(function(n){n.LexingError="lexing-error",n.LexingWarning="lexing-warning",n.LexingInfo="lexing-info",n.LexingHint="lexing-hint",n.ParsingError="parsing-error",n.LinkingError="linking-error"})(be||(be={}));class qm{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r){const i=r!=null?r:tt(e);t!=null||(t=this.nameProvider.getName(e));const s=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${s} has no name.`);let a;const o=()=>{var l;return a!=null?a:a=zr((l=this.nameProvider.getNameNode(e))!==null&&l!==void 0?l:e.$cstNode)};return{node:e,name:t,get nameSegment(){return o()},selectionSegment:zr(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}}class Ym{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}createDescriptions(r){return w(this,arguments,function*(e,t=W.None){const i=[],s=e.parseResult.value;for(const a of Nt(s))yield Ee(t),Wl(a).filter(o=>!wr(o)).forEach(o=>{const l=this.createDescription(o);l&&i.push(l)});return i})}createDescription(e){const t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;const i=tt(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:zr(r),local:st.equals(t.documentUri,i)}}}class Xm{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return t+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((i,s)=>{if(!i||s.length===0)return i;const a=s.indexOf(this.indexSeparator);if(a>0){const o=s.substring(0,a),l=parseInt(s.substring(a+1)),c=i[o];return c==null?void 0:c[l]}return i[s]},e)}}class Jm{constructor(e){this._ready=new ga,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new tu,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=(r=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&r!==void 0?r:!1}initialized(e){return w(this,null,function*(){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map(r=>this.toSectionName(r.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),r=yield e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,r[s])})}}this._ready.resolve()})}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{const r=e.settings[t];this.updateSectionConfiguration(t,r),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:r})})}updateSectionConfiguration(e,t){this.settings[e]=t}getConfiguration(e,t){return w(this,null,function*(){yield this.ready;const r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]})}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}var Ln;(function(n){function e(t){return{dispose:()=>w(this,null,function*(){return yield t()})}}n.create=e})(Ln||(Ln={}));class Qm{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new hi,this.documentPhaseListeners=new hi,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=B.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}build(i){return w(this,arguments,function*(e,t={},r=W.None){var s,a;for(const o of e){const l=o.uri.toString();if(o.state===B.Validated){if(typeof t.validation=="boolean"&&t.validation)o.state=B.IndexedReferences,o.diagnostics=void 0,this.buildState.delete(l);else if(typeof t.validation=="object"){const c=this.buildState.get(l),u=(s=c==null?void 0:c.result)===null||s===void 0?void 0:s.validationChecks;if(u){const h=((a=t.validation.categories)!==null&&a!==void 0?a:pi.all).filter(f=>!u.includes(f));h.length>0&&(this.buildState.set(l,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:h})},result:c.result}),o.state=B.IndexedReferences)}}}else this.buildState.delete(l)}this.currentState=B.Changed,yield this.emitUpdate(e.map(o=>o.uri),[]),yield this.buildDocuments(e,t,r)})}update(i,s){return w(this,arguments,function*(e,t,r=W.None){this.currentState=B.Changed;for(const l of t)this.langiumDocuments.deleteDocument(l),this.buildState.delete(l.toString()),this.indexManager.remove(l);for(const l of e){if(!this.langiumDocuments.invalidateDocument(l)){const u=this.langiumDocumentFactory.fromModel({$type:"INVALID"},l);u.state=B.Changed,this.langiumDocuments.addDocument(u)}this.buildState.delete(l.toString())}const a=ee(e).concat(t).map(l=>l.toString()).toSet();this.langiumDocuments.all.filter(l=>!a.has(l.uri.toString())&&this.shouldRelink(l,a)).forEach(l=>{this.serviceRegistry.getServices(l.uri).references.Linker.unlink(l),l.state=Math.min(l.state,B.ComputedScopes),l.diagnostics=void 0}),yield this.emitUpdate(e,t),yield Ee(r);const o=this.sortDocuments(this.langiumDocuments.all.filter(l=>{var c;return l.state<B.Linked||!(!((c=this.buildState.get(l.uri.toString()))===null||c===void 0)&&c.completed)}).toArray());yield this.buildDocuments(o,this.updateBuildOptions,r)})}emitUpdate(e,t){return w(this,null,function*(){yield Promise.all(this.updateListeners.map(r=>r(e,t)))})}sortDocuments(e){let t=0,r=e.length-1;for(;t<r;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;r>=0&&!this.hasTextDocument(e[r]);)r--;t<r&&([e[t],e[r]]=[e[r],e[t]])}return e}hasTextDocument(e){var t;return!!(!((t=this.textDocuments)===null||t===void 0)&&t.get(e.uri))}shouldRelink(e,t){return e.references.some(r=>r.error!==void 0)?!0:this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),Ln.create(()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}buildDocuments(e,t,r){return w(this,null,function*(){this.prepareBuild(e,t),yield this.runCancelable(e,B.Parsed,r,s=>this.langiumDocumentFactory.update(s,r)),yield this.runCancelable(e,B.IndexedContent,r,s=>this.indexManager.updateContent(s,r)),yield this.runCancelable(e,B.ComputedScopes,r,s=>w(this,null,function*(){const a=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=yield a.computeLocalScopes(s,r)})),yield this.runCancelable(e,B.Linked,r,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,r)),yield this.runCancelable(e,B.IndexedReferences,r,s=>this.indexManager.updateReferences(s,r));const i=e.filter(s=>this.shouldValidate(s));yield this.runCancelable(i,B.Validated,r,s=>this.validate(s,r));for(const s of e){const a=this.buildState.get(s.uri.toString());a&&(a.completed=!0)}})}prepareBuild(e,t){for(const r of e){const i=r.uri.toString(),s=this.buildState.get(i);(!s||s.completed)&&this.buildState.set(i,{completed:!1,options:t,result:s==null?void 0:s.result})}}runCancelable(e,t,r,i){return w(this,null,function*(){const s=e.filter(o=>o.state<t);for(const o of s)yield Ee(r),yield i(o),o.state=t,yield this.notifyDocumentPhase(o,t,r);const a=e.filter(o=>o.state===t);yield this.notifyBuildPhase(a,t,r),this.currentState=t})}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),Ln.create(()=>{this.buildPhaseListeners.delete(e,t)})}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),Ln.create(()=>{this.documentPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let i;if(t&&"path"in t?i=t:r=t,r!=null||(r=W.None),i){const s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(fi):new Promise((s,a)=>{const o=this.onBuildPhase(e,()=>{if(o.dispose(),l.dispose(),i){const c=this.langiumDocuments.getDocument(i);s(c==null?void 0:c.uri)}else s(void 0)}),l=r.onCancellationRequested(()=>{o.dispose(),l.dispose(),a(fi)})})}notifyDocumentPhase(e,t,r){return w(this,null,function*(){const s=this.documentPhaseListeners.get(t).slice();for(const a of s)try{yield a(e,r)}catch(o){if(!Li(o))throw o}})}notifyBuildPhase(e,t,r){return w(this,null,function*(){if(e.length===0)return;const s=this.buildPhaseListeners.get(t).slice();for(const a of s)yield Ee(r),yield a(e,r)})}shouldValidate(e){return!!this.getBuildOptions(e).validation}validate(e,t){return w(this,null,function*(){var r,i;const s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,o=typeof a=="object"?a:void 0,l=yield s.validateDocument(e,o,t);e.diagnostics?e.diagnostics.push(...l):e.diagnostics=l;const c=this.buildState.get(e.uri.toString());if(c){(r=c.result)!==null&&r!==void 0||(c.result={});const u=(i=o==null?void 0:o.categories)!==null&&i!==void 0?i:pi.all;c.result.validationChecks?c.result.validationChecks.push(...u):c.result.validationChecks=[...u]}})}getBuildOptions(e){var t,r;return(r=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&r!==void 0?r:{}}}class Zm{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new Fm,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const r=tt(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(a=>{st.equals(a.targetUri,r)&&a.targetPath===t&&i.push(a)})}),ee(i)}allElements(e,t){let r=ee(this.symbolIndex.keys());return t&&(r=r.filter(i=>!t||t.has(i))),r.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var r;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(o=>this.astReflection.isSubtype(o.type,t))}):(r=this.symbolIndex.get(e))!==null&&r!==void 0?r:[]}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}updateContent(r){return w(this,arguments,function*(e,t=W.None){const s=yield this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,t),a=e.uri.toString();this.symbolIndex.set(a,s),this.symbolByTypeIndex.clear(a)})}updateReferences(r){return w(this,arguments,function*(e,t=W.None){const s=yield this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),s)})}isAffected(e,t){const r=this.referenceIndex.get(e.uri.toString());return r?r.some(i=>!i.local&&t.has(i.targetUri.toString())):!1}}class eg{constructor(e){this.initialBuildOptions={},this._ready=new ga,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var r;return this.initializeWorkspace((r=this.folders)!==null&&r!==void 0?r:[],t)})}initializeWorkspace(r){return w(this,arguments,function*(e,t=W.None){const i=yield this.performStartup(e);yield Ee(t),yield this.documentBuilder.build(i,this.initialBuildOptions,t)})}performStartup(e){return w(this,null,function*(){const t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),r=[],i=s=>{r.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)};return yield this.loadAdditionalDocuments(e,i),yield Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(s=>w(this,null,function*(){return this.traverseFolder(...s,t,i)}))),this._ready.resolve(),r})}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return gt.parse(e.uri)}traverseFolder(e,t,r,i){return w(this,null,function*(){const s=yield this.fileSystemProvider.readDirectory(t);yield Promise.all(s.map(a=>w(this,null,function*(){if(this.includeEntry(e,a,r)){if(a.isDirectory)yield this.traverseFolder(e,a.uri,r,i);else if(a.isFile){const o=yield this.langiumDocuments.getOrCreateDocument(a.uri);i(o)}}})))})}includeEntry(e,t,r){const i=st.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){const s=st.extname(t.uri);return r.includes(s)}return!1}}class tg{buildUnexpectedCharactersMessage(e,t,r,i,s){return ms.buildUnexpectedCharactersMessage(e,t,r,i,s)}buildUnableToPopLexerModeMessage(e){return ms.buildUnableToPopLexerModeMessage(e)}}const ng={mode:"full"};class rg{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;const t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const r=hl(t)?Object.values(t):t,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new fe(r,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=ng){var r,i,s;const a=this.chevrotainLexer.tokenize(e);return{tokens:a.tokens,errors:a.errors,hidden:(r=a.groups.hidden)!==null&&r!==void 0?r:[],report:(s=(i=this.tokenBuilder).flushLexingReport)===null||s===void 0?void 0:s.call(i,e)}}toTokenTypeDictionary(e){if(hl(e))return e;const t=au(e)?Object.values(e.modes).flat():e,r={};return t.forEach(i=>r[i.name]=i),r}}function ig(n){return Array.isArray(n)&&(n.length===0||"name"in n[0])}function au(n){return n&&"modes"in n&&"defaultMode"in n}function hl(n){return!ig(n)&&!au(n)}function sg(n,e,t){let r,i;typeof n=="string"?(i=e,r=t):(i=n.range.start,r=e),i||(i=M.create(0,0));const s=ou(n),a=ya(r),o=lg({lines:s,position:i,options:a});return hg({index:0,tokens:o,position:i})}function ag(n,e){const t=ya(e),r=ou(n);if(r.length===0)return!1;const i=r[0],s=r[r.length-1],a=t.start,o=t.end;return!!(a!=null&&a.exec(i))&&!!(o!=null&&o.exec(s))}function ou(n){let e="";return typeof n=="string"?e=n:e=n.text,e.split(Pd)}const pl=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,og=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function lg(n){var e,t,r;const i=[];let s=n.position.line,a=n.position.character;for(let o=0;o<n.lines.length;o++){const l=o===0,c=o===n.lines.length-1;let u=n.lines[o],d=0;if(l&&n.options.start){const f=(e=n.options.start)===null||e===void 0?void 0:e.exec(u);f&&(d=f.index+f[0].length)}else{const f=(t=n.options.line)===null||t===void 0?void 0:t.exec(u);f&&(d=f.index+f[0].length)}if(c){const f=(r=n.options.end)===null||r===void 0?void 0:r.exec(u);f&&(u=u.substring(0,f.index))}if(u=u.substring(0,fg(u)),Ks(u,d)>=u.length){if(i.length>0){const f=M.create(s,a);i.push({type:"break",content:"",range:P.create(f,f)})}}else{pl.lastIndex=d;const f=pl.exec(u);if(f){const m=f[0],g=f[1],v=M.create(s,a+d),y=M.create(s,a+d+m.length);i.push({type:"tag",content:g,range:P.create(v,y)}),d+=m.length,d=Ks(u,d)}if(d<u.length){const m=u.substring(d),g=Array.from(m.matchAll(og));i.push(...cg(g,m,s,a+d))}}s++,a=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}function cg(n,e,t,r){const i=[];if(n.length===0){const s=M.create(t,r),a=M.create(t,r+e.length);i.push({type:"text",content:e,range:P.create(s,a)})}else{let s=0;for(const o of n){const l=o.index,c=e.substring(s,l);c.length>0&&i.push({type:"text",content:e.substring(s,l),range:P.create(M.create(t,s+r),M.create(t,l+r))});let u=c.length+1;const d=o[1];if(i.push({type:"inline-tag",content:d,range:P.create(M.create(t,s+u+r),M.create(t,s+u+d.length+r))}),u+=d.length,o.length===4){u+=o[2].length;const h=o[3];i.push({type:"text",content:h,range:P.create(M.create(t,s+u+r),M.create(t,s+u+h.length+r))})}else i.push({type:"text",content:"",range:P.create(M.create(t,s+u+r),M.create(t,s+u+r))});s=l+o[0].length}const a=e.substring(s);a.length>0&&i.push({type:"text",content:a,range:P.create(M.create(t,s+r),M.create(t,s+r+a.length))})}return i}const ug=/\S/,dg=/\s*$/;function Ks(n,e){const t=n.substring(e).match(ug);return t?e+t.index:n.length}function fg(n){const e=n.match(dg);if(e&&typeof e.index=="number")return e.index}function hg(n){var e,t,r,i;const s=M.create(n.position.line,n.position.character);if(n.tokens.length===0)return new ml([],P.create(s,s));const a=[];for(;n.index<n.tokens.length;){const c=pg(n,a[a.length-1]);c&&a.push(c)}const o=(t=(e=a[0])===null||e===void 0?void 0:e.range.start)!==null&&t!==void 0?t:s,l=(i=(r=a[a.length-1])===null||r===void 0?void 0:r.range.end)!==null&&i!==void 0?i:s;return new ml(a,P.create(o,l))}function pg(n,e){const t=n.tokens[n.index];if(t.type==="tag")return cu(n,!1);if(t.type==="text"||t.type==="inline-tag")return lu(n);mg(t,e),n.index++}function mg(n,e){if(e){const t=new du("",n.range);"inlines"in e?e.inlines.push(t):e.content.inlines.push(t)}}function lu(n){let e=n.tokens[n.index];const t=e;let r=e;const i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(gg(n)),r=e,e=n.tokens[n.index];return new Hs(i,P.create(t.range.start,r.range.end))}function gg(n){return n.tokens[n.index].type==="inline-tag"?cu(n,!0):uu(n)}function cu(n,e){const t=n.tokens[n.index++],r=t.content.substring(1),i=n.tokens[n.index];if((i==null?void 0:i.type)==="text")if(e){const s=uu(n);return new Qi(r,new Hs([s],s.range),e,P.create(t.range.start,s.range.end))}else{const s=lu(n);return new Qi(r,s,e,P.create(t.range.start,s.range.end))}else{const s=t.range;return new Qi(r,new Hs([],s),e,s)}}function uu(n){const e=n.tokens[n.index++];return new du(e.content,e.range)}function ya(n){if(!n)return ya({start:"/**",end:"*/",line:"*"});const{start:e,end:t,line:r}=n;return{start:Ji(e,!0),end:Ji(t,!1),line:Ji(r,!0)}}function Ji(n,e){if(typeof n=="string"||typeof n=="object"){const t=typeof n=="string"?Ei(n):n.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}else return n}class ml{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const t of this.elements)if(e.length===0)e=t.toString();else{const r=t.toString();e+=gl(e)+r}return e.trim()}toMarkdown(e){let t="";for(const r of this.elements)if(t.length===0)t=r.toMarkdown(e);else{const i=r.toMarkdown(e);t+=gl(t)+i}return t.trim()}}class Qi{constructor(e,t,r,i){this.name=e,this.content=t,this.inline=r,this.range=i}toString(){let e=`@${this.name}`;const t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,r;return(r=(t=e==null?void 0:e.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&r!==void 0?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const s=yg(this.name,t,e!=null?e:{});if(typeof s=="string")return s}let r="";(e==null?void 0:e.tag)==="italic"||(e==null?void 0:e.tag)===void 0?r="*":(e==null?void 0:e.tag)==="bold"?r="**":(e==null?void 0:e.tag)==="bold-italic"&&(r="***");let i=`${r}@${this.name}${r}`;return this.content.inlines.length===1?i=`${i} — ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}}function yg(n,e,t){var r,i;if(n==="linkplain"||n==="linkcode"||n==="link"){const s=e.indexOf(" ");let a=e;if(s>0){const l=Ks(e,s);a=e.substring(l),e=e.substring(0,s)}return(n==="linkcode"||n==="link"&&t.link==="code")&&(a=`\`${a}\``),(i=(r=t.renderLink)===null||r===void 0?void 0:r.call(t,e,a))!==null&&i!==void 0?i:Tg(e,a)}}function Tg(n,e){try{return gt.parse(n,!0),`[${e}](${n})`}catch(t){return n}}class Hs{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const r=this.inlines[t],i=this.inlines[t+1];e+=r.toString(),i&&i.range.start.line>r.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){const i=this.inlines[r],s=this.inlines[r+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}}class du{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function gl(n){return n.endsWith(`
`)?`
`:`

`}class Rg{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&ag(t))return sg(t).toMarkdown({renderLink:(i,s)=>this.documentationLinkRenderer(e,i,s),renderTag:i=>this.documentationTagRenderer(e,i)})}documentationLinkRenderer(e,t,r){var i;const s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){const a=s.nameSegment.range.start.line+1,o=s.nameSegment.range.start.character+1,l=s.documentUri.with({fragment:`L${a},${o}`});return`[${r}](${l.toString()})`}else return}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const i=tt(e).precomputedScopes;if(!i)return;let s=e;do{const o=i.get(s).find(l=>l.name===t);if(o)return o;s=s.$container}while(s)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(i=>i.name===t)}}class vg{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return Bm(e)?e.$comment:(t=fd(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}}class Ag{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}}class Eg{constructor(){this.previousTokenSource=new ma,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=$m();return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r=W.None){const i=new ga,s={action:t,deferred:i,cancellationToken:r};return e.push(s),this.performNextOperation(),i.promise}performNextOperation(){return w(this,null,function*(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,yield Promise.all(e.map(s=>w(this,[s],function*({action:t,deferred:r,cancellationToken:i}){try{const a=yield Promise.resolve().then(()=>t(i));r.resolve(a)}catch(a){Li(a)?r.resolve(void 0):r.reject(a)}}))),this.done=!0,this.performNextOperation()})}cancelWrite(){this.previousTokenSource.cancel()}}class kg{constructor(e){this.grammarElementIdMap=new ul,this.tokenTypeIdMap=new ul,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(t=>Object.assign(Object.assign({},t),{message:t.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const t=new Map,r=new Map;for(const i of Nt(e))t.set(i,{});if(e.$cstNode)for(const i of ls(e.$cstNode))r.set(i,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)oe(o)?a.push(this.dehydrateAstNode(o,t)):Be(o)?a.push(this.dehydrateReference(o,t)):a.push(o)}else oe(s)?r[i]=this.dehydrateAstNode(s,t):Be(s)?r[i]=this.dehydrateReference(s,t):s!==void 0&&(r[i]=s);return r}dehydrateReference(e,t){const r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){const r=t.cstNodes.get(e);return bl(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),bn(e)?r.content=e.content.map(i=>this.dehydrateCstNode(i,t)):Ol(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){const t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){const t=new Map,r=new Map;for(const s of Nt(e))t.set(s,{});let i;if(e.$cstNode)for(const s of ls(e.$cstNode)){let a;"fullText"in s?(a=new Wc(s.fullText),i=a):"content"in s?a=new ha:"tokenType"in s&&(a=this.hydrateCstLeafNode(s)),a&&(r.set(s,a),a.root=i)}return{astNodes:t,cstNodes:r}}hydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)oe(o)?a.push(this.setParent(this.hydrateAstNode(o,t),r)):Be(o)?a.push(this.hydrateReference(o,r,i,t)):a.push(o)}else oe(s)?r[i]=this.setParent(this.hydrateAstNode(s,t),r):Be(s)?r[i]=this.hydrateReference(s,r,i,t):s!==void 0&&(r[i]=s);return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,i){return this.linker.buildReference(t,r,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){const i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),bn(i))for(const s of e.content){const a=this.hydrateCstNode(s,t,r++);i.content.push(a)}return i}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),r=e.offset,i=e.length,s=e.startLine,a=e.startColumn,o=e.endLine,l=e.endColumn,c=e.hidden;return new Ms(r,i,{start:{line:s,character:a},end:{line:o,character:l}},t,c)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(const t of Nt(this.grammar))pd(t)&&this.grammarElementIdMap.set(t,e++)}}function vt(n){return{documentation:{CommentProvider:e=>new vg(e),DocumentationProvider:e=>new Rg(e)},parser:{AsyncParser:e=>new Ag(e),GrammarConfig:e=>sf(e),LangiumParser:e=>fm(e),CompletionParser:e=>dm(e),ValueConverter:()=>new Qc,TokenBuilder:()=>new Jc,Lexer:e=>new rg(e),ParserErrorMessageProvider:()=>new jc,LexerErrorMessageProvider:()=>new tg},workspace:{AstNodeLocator:()=>new Xm,AstNodeDescriptionProvider:e=>new qm(e),ReferenceDescriptionProvider:e=>new Ym(e)},references:{Linker:e=>new _m(e),NameProvider:()=>new Om,ScopeProvider:e=>new Um(e),ScopeComputation:e=>new Pm(e),References:e=>new bm(e)},serializer:{Hydrator:e=>new kg(e),JsonSerializer:e=>new Vm(e)},validation:{DocumentValidator:e=>new Hm(e),ValidationRegistry:e=>new Km(e)},shared:()=>n.shared}}function At(n){return{ServiceRegistry:e=>new Wm(e),workspace:{LangiumDocuments:e=>new wm(e),LangiumDocumentFactory:e=>new Nm(e),DocumentBuilder:e=>new Qm(e),IndexManager:e=>new Zm(e),WorkspaceManager:e=>new eg(e),FileSystemProvider:e=>n.fileSystemProvider(e),WorkspaceLock:()=>new Eg,ConfigurationProvider:e=>new Jm(e)}}}var yl;(function(n){n.merge=(e,t)=>mi(mi({},e),t)})(yl||(yl={}));function me(n,e,t,r,i,s,a,o,l){const c=[n,e,t,r,i,s,a,o,l].reduce(mi,{});return fu(c)}const xg=Symbol("isProxy");function fu(n,e){const t=new Proxy({},{deleteProperty:()=>!1,set:()=>{throw new Error("Cannot set property on injected service container")},get:(r,i)=>i===xg?!0:Rl(r,i,n,e||t),getOwnPropertyDescriptor:(r,i)=>(Rl(r,i,n,e||t),Object.getOwnPropertyDescriptor(r,i)),has:(r,i)=>i in n,ownKeys:()=>[...Object.getOwnPropertyNames(n)]});return t}const Tl=Symbol();function Rl(n,e,t,r){if(e in n){if(n[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:n[e]});if(n[e]===Tl)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return n[e]}else if(e in t){const i=t[e];n[e]=Tl;try{n[e]=typeof i=="function"?i(r):fu(i,r)}catch(s){throw n[e]=s instanceof Error?s:void 0,s}return n[e]}else return}function mi(n,e){if(e){for(const[t,r]of Object.entries(e))if(r!==void 0){const i=n[t];i!==null&&r!==null&&typeof i=="object"&&typeof r=="object"?n[t]=mi(i,r):n[t]=r}}return n}class Sg{readFile(){throw new Error("No file system is available.")}readDirectory(){return w(this,null,function*(){return[]})}}const Et={fileSystemProvider:()=>new Sg},Ig={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},$g={AstReflection:()=>new Vl};function Cg(){const n=me(At(Et),$g),e=me(vt({shared:n}),Ig);return n.ServiceRegistry.register(e),e}function Kt(n){var e;const t=Cg(),r=t.serializer.JsonSerializer.deserialize(n);return t.shared.workspace.LangiumDocumentFactory.fromModel(r,gt.parse(`memory://${(e=r.name)!==null&&e!==void 0?e:"grammar"}.langium`)),r}var Ng=Object.defineProperty,k=(n,e)=>Ng(n,"name",{value:e,configurable:!0}),vl="Statement",Fr="Architecture";function wg(n){return Fe.isInstance(n,Fr)}k(wg,"isArchitecture");var vr="Axis",Cn="Branch";function _g(n){return Fe.isInstance(n,Cn)}k(_g,"isBranch");var Ar="Checkout",Er="CherryPicking",Nn="Commit";function Lg(n){return Fe.isInstance(n,Nn)}k(Lg,"isCommit");var Gr="Common";function Og(n){return Fe.isInstance(n,Gr)}k(Og,"isCommon");var Zi="Curve",es="Edge",ts="Entry",wn="GitGraph";function bg(n){return Fe.isInstance(n,wn)}k(bg,"isGitGraph");var ns="Group",Ur="Info";function Pg(n){return Fe.isInstance(n,Ur)}k(Pg,"isInfo");var rs="Junction",_n="Merge";function Mg(n){return Fe.isInstance(n,_n)}k(Mg,"isMerge");var is="Option",Br="Packet";function Dg(n){return Fe.isInstance(n,Br)}k(Dg,"isPacket");var Vr="PacketBlock";function Fg(n){return Fe.isInstance(n,Vr)}k(Fg,"isPacketBlock");var Wr="Pie";function Gg(n){return Fe.isInstance(n,Wr)}k(Gg,"isPie");var Kr="PieSection";function Ug(n){return Fe.isInstance(n,Kr)}k(Ug,"isPieSection");var ss="Radar",as="Service",kr="Direction",Fn,hu=(Fn=class extends Ll{getAllTypes(){return[Fr,vr,Cn,Ar,Er,Nn,Gr,Zi,kr,es,ts,wn,ns,Ur,rs,_n,is,Br,Vr,Wr,Kr,ss,as,vl]}computeIsSubtype(e,t){switch(e){case Cn:case Ar:case Er:case Nn:case _n:return this.isSubtype(vl,t);case kr:return this.isSubtype(wn,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Entry:axis":return vr;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Fr:return{name:Fr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case vr:return{name:vr,properties:[{name:"label"},{name:"name"}]};case Cn:return{name:Cn,properties:[{name:"name"},{name:"order"}]};case Ar:return{name:Ar,properties:[{name:"branch"}]};case Er:return{name:Er,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case Nn:return{name:Nn,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case Gr:return{name:Gr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Zi:return{name:Zi,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case es:return{name:es,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case ts:return{name:ts,properties:[{name:"axis"},{name:"value"}]};case wn:return{name:wn,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case ns:return{name:ns,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case Ur:return{name:Ur,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case rs:return{name:rs,properties:[{name:"id"},{name:"in"}]};case _n:return{name:_n,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case is:return{name:is,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Br:return{name:Br,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case Vr:return{name:Vr,properties:[{name:"end"},{name:"label"},{name:"start"}]};case Wr:return{name:Wr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case Kr:return{name:Kr,properties:[{name:"label"},{name:"value"}]};case ss:return{name:ss,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case as:return{name:as,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case kr:return{name:kr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},k(Fn,"MermaidAstReflection"),Fn),Fe=new hu,xr,Bg=k(()=>xr!=null?xr:xr=Kt('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'),"InfoGrammar"),Sr,Vg=k(()=>Sr!=null?Sr:Sr=Kt(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`),"PacketGrammar"),Ir,Wg=k(()=>Ir!=null?Ir:Ir=Kt('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'),"PieGrammar"),$r,Kg=k(()=>$r!=null?$r:$r=Kt('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}'),"ArchitectureGrammar"),Cr,Hg=k(()=>Cr!=null?Cr:Cr=Kt(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`),"GitGraphGrammar"),Nr,jg=k(()=>Nr!=null?Nr:Nr=Kt(`{"$type":"Grammar","isDeclared":true,"name":"Radar","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@12"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@12"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9\\\\-_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`),"RadarGrammar"),zg={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},qg={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Yg={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Xg={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Jg={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Qg={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Ht={AstReflection:k(()=>new hu,"AstReflection")},Zg={Grammar:k(()=>Bg(),"Grammar"),LanguageMetaData:k(()=>zg,"LanguageMetaData"),parser:{}},ey={Grammar:k(()=>Vg(),"Grammar"),LanguageMetaData:k(()=>qg,"LanguageMetaData"),parser:{}},ty={Grammar:k(()=>Wg(),"Grammar"),LanguageMetaData:k(()=>Yg,"LanguageMetaData"),parser:{}},ny={Grammar:k(()=>Kg(),"Grammar"),LanguageMetaData:k(()=>Xg,"LanguageMetaData"),parser:{}},ry={Grammar:k(()=>Hg(),"Grammar"),LanguageMetaData:k(()=>Jg,"LanguageMetaData"),parser:{}},iy={Grammar:k(()=>jg(),"Grammar"),LanguageMetaData:k(()=>Qg,"LanguageMetaData"),parser:{}},sy=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,ay=/accTitle[\t ]*:([^\n\r]*)/,oy=/title([\t ][^\n\r]*|)/,ly={ACC_DESCR:sy,ACC_TITLE:ay,TITLE:oy},Gn,Ta=(Gn=class extends Qc{runConverter(e,t,r){let i=this.runCommonConverter(e,t,r);return i===void 0&&(i=this.runCustomConverter(e,t,r)),i===void 0?super.runConverter(e,t,r):i}runCommonConverter(e,t,r){const i=ly[e.name];if(i===void 0)return;const s=i.exec(t);if(s!==null){if(s[1]!==void 0)return s[1].trim().replace(/[\t ]{2,}/gm," ");if(s[2]!==void 0)return s[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},k(Gn,"AbstractMermaidValueConverter"),Gn),Un,Oi=(Un=class extends Ta{runCustomConverter(e,t,r){}},k(Un,"CommonValueConverter"),Un),Bn,kt=(Bn=class extends Jc{constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,r){const i=super.buildKeywordTokens(e,t,r);return i.forEach(s=>{this.keywords.has(s.name)&&s.PATTERN!==void 0&&(s.PATTERN=new RegExp(s.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},k(Bn,"AbstractMermaidTokenBuilder"),Bn),Vn;Vn=class extends kt{},k(Vn,"CommonTokenBuilder");var Wn,cy=(Wn=class extends kt{constructor(){super(["gitGraph"])}},k(Wn,"GitGraphTokenBuilder"),Wn),uy={parser:{TokenBuilder:k(()=>new cy,"TokenBuilder"),ValueConverter:k(()=>new Oi,"ValueConverter")}};function dy(n=Et){const e=me(At(n),Ht),t=me(vt({shared:e}),ry,uy);return e.ServiceRegistry.register(t),{shared:e,GitGraph:t}}k(dy,"createGitGraphServices");var Kn,fy=(Kn=class extends kt{constructor(){super(["info","showInfo"])}},k(Kn,"InfoTokenBuilder"),Kn),hy={parser:{TokenBuilder:k(()=>new fy,"TokenBuilder"),ValueConverter:k(()=>new Oi,"ValueConverter")}};function py(n=Et){const e=me(At(n),Ht),t=me(vt({shared:e}),Zg,hy);return e.ServiceRegistry.register(t),{shared:e,Info:t}}k(py,"createInfoServices");var Hn,my=(Hn=class extends kt{constructor(){super(["packet-beta"])}},k(Hn,"PacketTokenBuilder"),Hn),gy={parser:{TokenBuilder:k(()=>new my,"TokenBuilder"),ValueConverter:k(()=>new Oi,"ValueConverter")}};function yy(n=Et){const e=me(At(n),Ht),t=me(vt({shared:e}),ey,gy);return e.ServiceRegistry.register(t),{shared:e,Packet:t}}k(yy,"createPacketServices");var jn,Ty=(jn=class extends kt{constructor(){super(["pie","showData"])}},k(jn,"PieTokenBuilder"),jn),zn,Ry=(zn=class extends Ta{runCustomConverter(e,t,r){if(e.name==="PIE_SECTION_LABEL")return t.replace(/"/g,"").trim()}},k(zn,"PieValueConverter"),zn),vy={parser:{TokenBuilder:k(()=>new Ty,"TokenBuilder"),ValueConverter:k(()=>new Ry,"ValueConverter")}};function Ay(n=Et){const e=me(At(n),Ht),t=me(vt({shared:e}),ty,vy);return e.ServiceRegistry.register(t),{shared:e,Pie:t}}k(Ay,"createPieServices");var qn,Ey=(qn=class extends kt{constructor(){super(["architecture"])}},k(qn,"ArchitectureTokenBuilder"),qn),Yn,ky=(Yn=class extends Ta{runCustomConverter(e,t,r){if(e.name==="ARCH_ICON")return t.replace(/[()]/g,"").trim();if(e.name==="ARCH_TEXT_ICON")return t.replace(/["()]/g,"");if(e.name==="ARCH_TITLE")return t.replace(/[[\]]/g,"").trim()}},k(Yn,"ArchitectureValueConverter"),Yn),xy={parser:{TokenBuilder:k(()=>new Ey,"TokenBuilder"),ValueConverter:k(()=>new ky,"ValueConverter")}};function Sy(n=Et){const e=me(At(n),Ht),t=me(vt({shared:e}),ny,xy);return e.ServiceRegistry.register(t),{shared:e,Architecture:t}}k(Sy,"createArchitectureServices");var Xn,Iy=(Xn=class extends kt{constructor(){super(["radar-beta"])}},k(Xn,"RadarTokenBuilder"),Xn),$y={parser:{TokenBuilder:k(()=>new Iy,"TokenBuilder"),ValueConverter:k(()=>new Oi,"ValueConverter")}};function Cy(n=Et){const e=me(At(n),Ht),t=me(vt({shared:e}),iy,$y);return e.ServiceRegistry.register(t),{shared:e,Radar:t}}k(Cy,"createRadarServices");var et={},Ny={info:k(()=>w(void 0,null,function*(){const{createInfoServices:n}=yield St(()=>import("./info-4N47QTOZ-fc134342.js"),["./info-4N47QTOZ-fc134342.js","./chart-vendor-e1d59b84.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js","./_baseUniq-5ee25ed9.js","./_basePickBy-a1ec2f81.js","./clone-92746810.js"],import.meta.url),e=n().Info.parser.LangiumParser;et.info=e}),"info"),packet:k(()=>w(void 0,null,function*(){const{createPacketServices:n}=yield St(()=>import("./packet-KVYON367-9603ba8c.js"),["./packet-KVYON367-9603ba8c.js","./chart-vendor-e1d59b84.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js","./_baseUniq-5ee25ed9.js","./_basePickBy-a1ec2f81.js","./clone-92746810.js"],import.meta.url),e=n().Packet.parser.LangiumParser;et.packet=e}),"packet"),pie:k(()=>w(void 0,null,function*(){const{createPieServices:n}=yield St(()=>import("./pie-R6RNRRYF-cd38cf52.js"),["./pie-R6RNRRYF-cd38cf52.js","./chart-vendor-e1d59b84.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js","./_baseUniq-5ee25ed9.js","./_basePickBy-a1ec2f81.js","./clone-92746810.js"],import.meta.url),e=n().Pie.parser.LangiumParser;et.pie=e}),"pie"),architecture:k(()=>w(void 0,null,function*(){const{createArchitectureServices:n}=yield St(()=>import("./architecture-4AB2E3PP-22315bac.js"),["./architecture-4AB2E3PP-22315bac.js","./chart-vendor-e1d59b84.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js","./_baseUniq-5ee25ed9.js","./_basePickBy-a1ec2f81.js","./clone-92746810.js"],import.meta.url),e=n().Architecture.parser.LangiumParser;et.architecture=e}),"architecture"),gitGraph:k(()=>w(void 0,null,function*(){const{createGitGraphServices:n}=yield St(()=>import("./gitGraph-O2Q2CXLX-3743c2f0.js"),["./gitGraph-O2Q2CXLX-3743c2f0.js","./chart-vendor-e1d59b84.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js","./_baseUniq-5ee25ed9.js","./_basePickBy-a1ec2f81.js","./clone-92746810.js"],import.meta.url),e=n().GitGraph.parser.LangiumParser;et.gitGraph=e}),"gitGraph"),radar:k(()=>w(void 0,null,function*(){const{createRadarServices:n}=yield St(()=>import("./radar-MK3ICKWK-88eb76a9.js"),["./radar-MK3ICKWK-88eb76a9.js","./chart-vendor-e1d59b84.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js","./_baseUniq-5ee25ed9.js","./_basePickBy-a1ec2f81.js","./clone-92746810.js"],import.meta.url),e=n().Radar.parser.LangiumParser;et.radar=e}),"radar")};function wy(n,e){return w(this,null,function*(){const t=Ny[n];if(!t)throw new Error(`Unknown diagram type: ${n}`);et[n]||(yield t());const i=et[n].parse(e);if(i.lexerErrors.length>0||i.parserErrors.length>0)throw new _y(i);return i.value})}k(wy,"parse");var Jn,_y=(Jn=class extends Error{constructor(e){const t=e.lexerErrors.map(i=>i.message).join(`
`),r=e.parserErrors.map(i=>i.message).join(`
`);super(`Parsing failed: ${t} ${r}`),this.result=e}},k(Jn,"MermaidParseError"),Jn);export{xy as A,uy as G,hy as I,gy as P,$y as R,yy as a,vy as b,py as c,Ay as d,Sy as e,dy as f,Cy as g,wy as p};
