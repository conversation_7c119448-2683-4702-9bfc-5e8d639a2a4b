
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,$ as l,P as u,r as p,a0 as t,b as r,Q as i,c as d,e as s,f as o,w as n,j as y,h as c,_ as f,g as m,Y as H,k as g}from"./index-BERX8Mlm.js";import{a as _}from"./config-BrbFL53_.js";const U=a({__name:"hupijiao",setup(a){const g=l({payHupiStatus:"",payHupiAppId:"",payHupiSecret:"",payHupiGatewayUrl:"",payHupiNotifyUrl:"",payHupiReturnUrl:""}),U=u((()=>{const e="1"===g.payHupiStatus;return{payHupiStatus:[{required:!0,trigger:"change",message:"请选择当前支付开启状态"}],payHupiSecret:[{required:e,trigger:"blur",message:"请填写支付秘钥"}],payHupiGatewayUrl:[{required:e,trigger:"blur",message:"请填写网关"}],payHupiAppId:[{required:e,trigger:"blur",message:"请填写Appid"}],payHupiNotifyUrl:[{required:e,trigger:"blur",message:"请填写支付通知地址"}],payHupiReturnUrl:[{required:e,trigger:"blur",message:"请填写支付回调地址"}]}})),b=p();async function x(){const e=await _.queryConfig({keys:["payHupiSecret","payHupiNotifyUrl","payHupiGatewayUrl","payHupiReturnUrl","payHupiAppId","payHupiStatus"]});Object.assign(g,e.data)}function v(){var e;null==(e=b.value)||e.validate((async e=>{if(e){try{await _.setConfig({settings:(a=g,Object.keys(a).map((e=>({configKey:e,configVal:a[e]}))))}),H.success("变更配置信息成功")}catch(l){}x()}else H.error("请填写完整信息");var a}))}return t((()=>g.payHupiStatus),(()=>{setTimeout((()=>{var e;null==(e=b.value)||e.validateField(["payHupiSecret","payHupiGatewayUrl","payHupiAppId","payHupiNotifyUrl","payHupiReturnUrl"])}),0)})),r((()=>{x()})),(a,l)=>{const u=f,p=y,t=e,r=i("el-switch"),H=i("el-form-item"),_=i("el-col"),x=i("el-row"),V=i("el-input"),S=i("el-form"),w=i("el-card");return s(),d("div",null,[o(t,null,{title:n((()=>l[6]||(l[6]=[m("div",{class:"flex items-center gap-4"},"虎皮椒支付设置",-1)]))),content:n((()=>l[7]||(l[7]=[m("div",{class:"text-sm/6"},[m("div",null,[m("a",{href:"https://www.xunhupay.com/",target:"_blank"},"虎皮椒支付"),c(" 为第三方支付，接入请购买微信渠道。 ")]),m("div",null,"支付通知地址为： https://您的域名/api/pay/notify。")],-1)]))),default:n((()=>[o(p,{outline:"",onClick:v},{default:n((()=>[o(u,{name:"i-ri:file-text-line"}),l[8]||(l[8]=c(" 保存设置 "))])),_:1})])),_:1}),o(w,{style:{margin:"20px"}},{default:n((()=>[o(S,{ref_key:"formRef",ref:b,rules:U.value,model:g,"label-width":"120px"},{default:n((()=>[o(x,null,{default:n((()=>[o(_,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(H,{label:"启用当前支付",prop:"payHupiAppId"},{default:n((()=>[o(r,{modelValue:g.payHupiStatus,"onUpdate:modelValue":l[0]||(l[0]=e=>g.payHupiStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(x,null,{default:n((()=>[o(_,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(H,{label:"支付AppId",prop:"payHupiAppId"},{default:n((()=>[o(V,{modelValue:g.payHupiAppId,"onUpdate:modelValue":l[1]||(l[1]=e=>g.payHupiAppId=e),placeholder:"请填写AppId",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(x,null,{default:n((()=>[o(_,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(H,{label:"支付网关地址",prop:"payHupiGatewayUrl"},{default:n((()=>[o(V,{modelValue:g.payHupiGatewayUrl,"onUpdate:modelValue":l[2]||(l[2]=e=>g.payHupiGatewayUrl=e),placeholder:"请填写支付网关地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(x,null,{default:n((()=>[o(_,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(H,{label:"Secret秘钥",prop:"payHupiSecret"},{default:n((()=>[o(V,{modelValue:g.payHupiSecret,"onUpdate:modelValue":l[3]||(l[3]=e=>g.payHupiSecret=e),placeholder:"请填写支付秘钥",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(x,null,{default:n((()=>[o(_,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(H,{label:"支付通知地址",prop:"payHupiSecret"},{default:n((()=>[o(V,{modelValue:g.payHupiNotifyUrl,"onUpdate:modelValue":l[4]||(l[4]=e=>g.payHupiNotifyUrl=e),placeholder:"请填写支付通知地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(x,null,{default:n((()=>[o(_,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(H,{label:"支付回调地址",prop:"payHupiSecret"},{default:n((()=>[o(V,{modelValue:g.payHupiReturnUrl,"onUpdate:modelValue":l[5]||(l[5]=e=>g.payHupiReturnUrl=e),placeholder:"请填写支付成功后的回跳地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof g&&g(U);export{U as default};
