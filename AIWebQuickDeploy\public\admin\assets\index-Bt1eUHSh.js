
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{ac as l,d as t,r,$ as o,b as s,Q as u,a1 as n,c as i,e as d,f as p,w as f,g as c,V as m,W as _,i as w,a2 as g,h,a5 as b,t as v,ae as y,Y as x,k as z}from"./index-BERX8Mlm.js";import{P as I,e as V,f as k,g as j}from"./index-gPQwgooA.js";import{u as C}from"./utcFormatTime-BtFjiA-p.js";const U=e=>l.get("order/queryAll",{params:e}),Y=e=>l.post("order/delete",e),A=()=>l.post("order/deleteNotPay"),P=t({__name:"index",setup(l){const t=r(!1),z=r(!1),P=r([]),D=r(),q=r(0),F=r(0),M=o({userId:"",platform:"",status:"",page:1,size:15}),S=r([]);async function N(){t.value=!0;try{const e=await U(M);t.value=!1;const{rows:a,count:l,total_price:r}=e.data;q.value=l,S.value=a,F.value=r}catch(e){t.value=!1}}async function Q(e){const a=await y.queryAllUser({size:30,username:e});P.value=a.data.rows}async function R(){z.value=!0;try{await A(),x.success("删除未支付订单完成!"),await N(),z.value=!1}catch(e){x.error("删除未支付订单失败!"),z.value=!1}}return s((()=>{N()})),(l,r)=>{const o=a,s=u("el-option"),y=u("el-select"),U=u("el-form-item"),A=u("el-button"),T=u("el-popconfirm"),W=u("el-form"),$=u("el-statistic"),B=e,E=u("el-table-column"),G=u("el-tag"),H=u("el-table"),J=u("el-pagination"),K=u("el-row"),L=n("loading");return d(),i("div",null,[p(o,null,{title:f((()=>r[6]||(r[6]=[c("div",{class:"flex items-center gap-4"},"订单列表",-1)]))),_:1}),p(B,{class:"flex items-start justify-between"},{default:f((()=>[p(W,{ref_key:"formRef",ref:D,inline:!0,model:M},{default:f((()=>[p(U,{label:"用户名称",prop:"userId"},{default:f((()=>[p(y,{modelValue:M.userId,"onUpdate:modelValue":r[0]||(r[0]=e=>M.userId=e),filterable:"",clearable:"",remote:"","reserve-keyword":"",placeholder:"用户姓名[模糊搜索]","remote-show-suffix":"","remote-method":Q,style:{width:"180px"}},{default:f((()=>[(d(!0),i(m,null,_(w(P),(e=>(d(),g(s,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(U,{label:"支付平台",prop:"platform"},{default:f((()=>[p(y,{modelValue:M.platform,"onUpdate:modelValue":r[1]||(r[1]=e=>M.platform=e),clearable:"",placeholder:"请选择支付平台","remote-show-suffix":"",style:{width:"160px"}},{default:f((()=>[(d(!0),i(m,null,_(w(I),(e=>(d(),g(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(U,{label:"支付状态",prop:"status"},{default:f((()=>[p(y,{modelValue:M.status,"onUpdate:modelValue":r[2]||(r[2]=e=>M.status=e),clearable:"",placeholder:"请选择支付状态","remote-show-suffix":"",style:{width:"160px"}},{default:f((()=>[(d(!0),i(m,null,_(w(V),(e=>(d(),g(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(U,null,{default:f((()=>[p(A,{type:"primary",onClick:N},{default:f((()=>r[7]||(r[7]=[h(" 查询 ")]))),_:1}),p(A,{onClick:r[3]||(r[3]=e=>{return null==(a=w(D))||a.resetFields(),void N();var a})},{default:f((()=>r[8]||(r[8]=[h(" 重置 ")]))),_:1}),p(T,{title:"确认删除所有未支付订单么?",onConfirm:R},{reference:f((()=>[p(A,{type:"danger"},{default:f((()=>r[9]||(r[9]=[h(" 删除所有未支付订单 ")]))),_:1})])),_:1})])),_:1})])),_:1},8,["model"]),p($,{title:"累计已支付订单金额",value:w(F)},null,8,["value"])])),_:1}),p(B,{style:{width:"100%"}},{default:f((()=>[b((d(),g(H,{border:"",data:w(S),style:{width:"100%"},size:"large","tooltip-options":{}},{default:f((()=>[p(E,{fixed:"",prop:"orderId",label:"订单ID",width:"315"}),p(E,{prop:"userInfo.username",label:"用户名称",width:"180"}),p(E,{prop:"userInfo.email",label:"用户邮箱",width:"200"}),p(E,{prop:"goodsInfo.name",label:"套餐名称",width:"140"}),p(E,{prop:"price",label:"商品单价",width:"110"}),p(E,{prop:"count",label:"购买数量",width:"90",align:"center"}),p(E,{prop:"total",label:"订单总价",width:"90",align:"center"}),p(E,{prop:"total",label:"支付平台",width:"90",align:"center"},{default:f((e=>[h(v(w(k)[e.row.payPlatform]),1)])),_:1}),p(E,{prop:"status",label:"支付状态",width:"90",align:"center"},{default:f((e=>[p(G,{type:1===e.row.status?"success":"warning"},{default:f((()=>[h(v(w(j)[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),p(E,{prop:"createdAt",label:"订单时间",width:"200",fixed:"right"},{default:f((e=>[h(v(w(C)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),p(E,{fixed:"right",label:"操作"},{default:f((e=>[p(T,{title:"确认删除此订单么、删除订单不可恢复?",width:"400","icon-color":"red",onConfirm:a=>async function(e){const{orderId:a}=e;await Y({orderId:a}),x.success("删除订单完成!"),N()}(e.row)},{reference:f((()=>[p(A,{link:"",type:"danger",size:"small",loading:w(z)},{default:f((()=>r[10]||(r[10]=[h(" 删除订单 ")]))),_:1},8,["loading"])])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[L,w(t)]]),p(K,{class:"mt-5 flex justify-end"},{default:f((()=>[p(J,{"current-page":M.page,"onUpdate:currentPage":r[4]||(r[4]=e=>M.page=e),"page-size":M.size,"onUpdate:pageSize":r[5]||(r[5]=e=>M.size=e),class:"mr-5","page-sizes":[15,30,50,100],layout:"total, sizes, prev, pager, next, jumper",total:w(q),onSizeChange:N,onCurrentChange:N},null,8,["current-page","page-size","total"])])),_:1})])),_:1})])}}});"function"==typeof z&&z(P);export{P as default};
