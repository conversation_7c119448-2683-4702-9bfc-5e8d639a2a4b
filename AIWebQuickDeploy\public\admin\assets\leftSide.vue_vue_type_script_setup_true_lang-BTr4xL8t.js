
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,a as s,c as a,e as o,T as t,a2 as r,i,f as l,_ as n}from"./index-BERX8Mlm.js";import c from"./index-BTtsYu0a.js";const m={class:"flex items-center"},d=e({name:"ToolbarLeftSide",__name:"leftSide",setup(e){const d=s();return(e,s)=>{const p=n;return o(),a("div",m,["mobile"===i(d).mode?(o(),a("div",{key:0,class:"flex-center cursor-pointer px-2 py-1 -rotate-z-180",onClick:s[0]||(s[0]=e=>i(d).toggleSidebarCollapse())},[l(p,{name:"toolbar-collapse"})])):t("",!0),i(d).settings.toolbar.breadcrumb?(o(),r(c,{key:1})):t("",!0)])}}});export{d as _};
