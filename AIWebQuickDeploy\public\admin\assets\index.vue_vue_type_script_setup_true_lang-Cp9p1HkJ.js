
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import e from"./HKbd-LjWkyhwy.js";import{d as a,r as t,P as l,u as n,a as s,az as i,aF as o,a0 as r,b as u,aG as c,aA as d,aH as v,aI as f,a2 as p,e as m,w as x,f as h,i as b,aJ as y,g,aK as k,R as _,aL as w,c as H,T,a5 as z,_ as I,a3 as R,a4 as S,ab as j,aM as F,aN as C,aO as K,V as M,W as N,a6 as D,t as E,h as J,aP as L}from"./index-BERX8Mlm.js";import P from"./index-CZaxHimt.js";import{_ as U}from"./item.vue_vue_type_script_setup_true_lang-aMXYc_KV.js";const V={class:"fixed inset-0"},A={class:"h-full flex items-end justify-center p-4 text-center lg-items-center"},G={class:"flex flex-col overflow-y-auto rounded-xl bg-white shadow-xl dark-bg-stone-8"},O={class:"flex items-center px-4 py-3","border-b":"~ solid stone-2 dark-stone-7"},W=["onKeydown"],q=["data-index","onClick","onMouseover"],B={class:"flex flex-1 flex-col gap-1 truncate px-4 py-3","border-l":"~ solid stone-2 dark-stone-7"},Q={class:"truncate text-base font-bold"},X={key:1,flex:"center col","py-6":"","text-stone-5":""},Y={key:0,class:"flex justify-between px-4 py-3","border-t":"~ solid stone-2 dark-stone-7"},Z={class:"flex gap-8"},$={class:"inline-flex items-center gap-1 text-xs"},ee={class:"inline-flex items-center gap-1 text-xs"},ae={key:0,class:"inline-flex items-center gap-1 text-xs"},te=a({name:"Search",__name:"index",setup(a){const te=t({enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0"}),le=l((()=>({enter:"ease-out duration-300",enterFrom:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95",enterTo:"opacity-100 translate-y-0 lg-scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 translate-y-0 lg-scale-100",leaveTo:"opacity-0 translate-y-4 lg-translate-y-0 lg-scale-95"}))),ne=n(),se=s(),ie=i(),oe=t(!1),re=t(""),ue=t([]),ce=t(-1),de=t(),ve=t(),fe=t([]);o((()=>{fe.value=[]}));const pe=l((()=>{let e=[];return e=ue.value.filter((e=>{let a=!1;return e.title&&("function"==typeof e.title?e.title().includes(re.value)&&(a=!0):e.title.includes(re.value)&&(a=!0)),e.path.includes(re.value)&&(a=!0),e.breadcrumb.some((e=>{var a;if("function"==typeof e.title){if(e.title().includes(re.value))return!0}else if(null==(a=e.title)?void 0:a.includes(re.value))return!0;return!1}))&&(a=!0),a})),e}));function me(){ue.value=[],ie.allMenus.forEach((e=>{xe(e.children)}))}function xe(e,a,t,l){e.forEach((e=>{var n,s,i,o,r,u,c;if(!1!==(null==(n=e.meta)?void 0:n.menu)){const n=v(l)||[];e.children&&function(e){var a;let t=!0;return(null==(a=e.children)?void 0:a.every((e=>{var a;return!1===(null==(a=e.meta)?void 0:a.menu)})))&&(t=!1),t}(e)?(n.push({title:null==(s=e.meta)?void 0:s.title}),xe(e.children,f(a,e.path),(null==(i=e.meta)?void 0:i.icon)??t,n)):(n.push({title:null==(o=e.meta)?void 0:o.title}),ue.value.push({path:f(a,e.path),icon:(null==(r=e.meta)?void 0:r.icon)??t,title:null==(u=e.meta)?void 0:u.title,link:null==(c=e.meta)?void 0:c.link,breadcrumb:n}))}}))}function he(){pe.value.length&&(ce.value-=1,ce.value<0&&(ce.value=pe.value.length-1),ge())}function be(){pe.value.length&&(ce.value+=1,ce.value>pe.value.length-1&&(ce.value=0),ge())}function ye(){var e;-1!==ce.value&&(null==(e=fe.value.find((e=>Number.parseInt(e.dataset.index)===ce.value)))||e.click())}function ge(){var e,a;if(ve.value){const t=ve.value.osInstance().elements().content;let l=0;if(-1!==ce.value){l=t.scrollTop;const n=(null==(e=fe.value.find((e=>Number.parseInt(e.dataset.index)===ce.value)))?void 0:e.offsetTop)??0,s=(null==(a=fe.value.find((e=>Number.parseInt(e.dataset.index)===ce.value)))?void 0:a.clientHeight)??0,i=t.scrollTop,o=t.clientHeight;n+s>i+o?l=n+s-o:n<=i&&(l=n)}t.scrollTo({top:l})}}return r((()=>oe.value),(e=>{e?(re.value="",ce.value=-1,d("up",he),d("down",be),d("enter",ye)):(d.unbind("up",he),d.unbind("down",be),d.unbind("enter",ye))})),r((()=>pe.value),(()=>{ce.value=-1,ge()})),u((()=>{c.on("global-search-toggle",(()=>{oe.value||me(),oe.value=!oe.value})),d("alt+s",(e=>{se.settings.toolbar.navSearch&&se.settings.navSearch.enableHotkeys&&(e.preventDefault(),me(),oe.value=!0)})),d("esc",(e=>{se.settings.toolbar.navSearch&&se.settings.navSearch.enableHotkeys&&(e.preventDefault(),oe.value=!1)})),me()})),(a,t)=>{const l=I,n=e;return m(),p(b(L),{as:"template",show:b(oe)},{default:x((()=>[h(b(y),{"initial-focus":b(de),class:"fixed inset-0 z-2000 flex",onClose:t[2]||(t[2]=e=>b(oe)&&b(c).emit("global-search-toggle"))},{default:x((()=>[h(b(k),_({as:"template"},b(te)),{default:x((()=>t[3]||(t[3]=[g("div",{class:"fixed inset-0 bg-stone-200/75 backdrop-blur-sm transition-opacity dark-bg-stone-8/75"},null,-1)]))),_:1},16),g("div",V,[g("div",A,[h(b(k),_({as:"template"},b(le)),{default:x((()=>[h(b(w),{class:"relative h-full max-h-4/5 w-full flex flex-col text-left lg-max-w-2xl"},{default:x((()=>[g("div",G,[g("div",O,[h(l,{name:"i-ep:search",size:18,class:"text-stone-5"}),z(g("input",{ref_key:"searchInputRef",ref:de,"onUpdate:modelValue":t[0]||(t[0]=e=>j(re)?re.value=e:null),placeholder:"搜索页面，支持标题、URL模糊查询",class:"w-full border-0 rounded-md bg-transparent px-3 text-base text-dark dark-text-white focus-outline-none placeholder-stone-4 dark-placeholder-stone-5",onKeydown:[t[1]||(t[1]=R((e=>b(c).emit("global-search-toggle")),["esc"])),R(S(he,["prevent"]),["up"]),R(S(be,["prevent"]),["down"]),R(S(ye,["prevent"]),["enter"])]},null,40,W),[[F,b(re)]])]),h(b(C),{class:"relative m-0 of-y-hidden"},{default:x((()=>[h(b(K),{ref_key:"searchResultRef",ref:ve,options:{scrollbars:{autoHide:"leave",autoHideDelay:300}},defer:"",class:"h-full"},{default:x((()=>[b(pe).length>0?(m(!0),H(M,{key:0},N(b(pe),((e,a)=>(m(),H("a",{ref_for:!0,ref_key:"searchResultItemRef",ref:fe,key:e.path,class:D(["flex cursor-pointer items-center",{"bg-stone-2/40 dark-bg-stone-7/40":a===b(ce)}]),"data-index":a,onClick:a=>{return t=e.path,(l=e.link)?window.open(l,"_blank"):ne.push(t),void(oe.value=!1);var t,l},onMouseover:e=>ce.value=a},[e.icon?(m(),p(l,{key:0,name:e.icon,size:20,class:D(["basis-16 transition",{"scale-120 text-ui-primary":a===b(ce)}])},null,8,["name","class"])):T("",!0),g("div",B,[g("div",Q,E(("function"==typeof e.title?e.title():e.title)??"[ 无标题 ]"),1),e.breadcrumb.length?(m(),p(P,{key:0,class:"truncate"},{default:x((()=>[(m(!0),H(M,null,N(e.breadcrumb,((e,a)=>(m(),p(U,{key:a,class:"text-xs"},{default:x((()=>[J(E(("function"==typeof e.title?e.title():e.title)??"[ 无标题 ]"),1)])),_:2},1024)))),128))])),_:2},1024)):T("",!0)])],42,q)))),128)):(m(),H("div",X,[h(l,{name:"i-tabler:mood-empty",size:40}),t[4]||(t[4]=g("p",{"m-2":"","text-base":""},"没有找到你想要的",-1))]))])),_:1},512)])),_:1}),"pc"===b(se).mode?(m(),H("div",Y,[g("div",Z,[g("div",$,[h(n,null,{default:x((()=>[h(l,{name:"i-ion:md-return-left",size:14})])),_:1}),t[5]||(t[5]=g("span",null,"访问",-1))]),g("div",ee,[h(n,null,{default:x((()=>[h(l,{name:"i-ant-design:caret-up-filled",size:14})])),_:1}),h(n,null,{default:x((()=>[h(l,{name:"i-ant-design:caret-down-filled",size:14})])),_:1}),t[6]||(t[6]=g("span",null,"切换",-1))])]),b(se).settings.navSearch.enableHotkeys?(m(),H("div",ae,[h(n,null,{default:x((()=>t[7]||(t[7]=[J(" ESC ")]))),_:1}),t[8]||(t[8]=g("span",null,"退出",-1))])):T("",!0)])):T("",!0)])])),_:1})])),_:1},16)])])])),_:1},8,["initial-focus"])])),_:1},8,["show"])}}});export{te as _};
