
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import e from"./HKbd-LjWkyhwy.js";import{d as s,a,c as t,e as r,i as n,aG as o,a2 as i,_ as c,f as l,g as p,T as g,w as d,h as m,t as x}from"./index-BERX8Mlm.js";const h={key:1,class:"group inline-flex cursor-pointer items-center gap-1 whitespace-nowrap rounded-2 bg-stone-1 px-2 py-1.5 text-dark ring-stone-3 ring-inset transition dark-bg-stone-9 dark-text-white hover-ring-1 dark-ring-stone-7"},k=s({name:"ToolbarRightSide",__name:"index",setup(s){const k=a();return(s,a)=>{const u=c,b=e;return r(),t("span",{class:"flex-center cursor-pointer px-2 py-1",onClick:a[0]||(a[0]=e=>n(o).emit("global-search-toggle"))},["mobile"===n(k).mode?(r(),i(u,{key:0,name:"i-ri:search-line"})):(r(),t("span",h,[l(u,{name:"i-ri:search-line"}),a[1]||(a[1]=p("span",{class:"text-sm text-stone-5 transition group-hover-text-dark dark-group-hover-text-white"},"搜索",-1)),n(k).settings.navSearch.enableHotkeys?(r(),i(b,{key:0,class:"ml-2"},{default:d((()=>[m(x("mac"===n(k).os?"⌥":"Alt")+" S",1)])),_:1})):g("",!0)]))])}}});export{k as _};
