
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import e from"./index-DRUzQ5v3.js";import t from"./index-CGed-Lii.js";import{d as s,a,P as r,r as o,b as i,ai as n,a0 as p,c as u,e as _,a6 as d,i as m,a2 as l,T as b,Z as c}from"./index-BERX8Mlm.js";import"./index-DhWfG07N.js";import"./useMainPage-Dbp8uSF1.js";import"./leftSide.vue_vue_type_script_setup_true_lang-BTr4xL8t.js";import"./index-BTtsYu0a.js";import"./index-CZaxHimt.js";import"./item.vue_vue_type_script_setup_true_lang-aMXYc_KV.js";import"./rightSide.vue_vue_type_script_setup_true_lang-ClZNHSw-.js";import"./HDropdownMenu.vue_vue_type_script_setup_true_lang-Wc6l-Ngn.js";import"./index.vue_vue_type_script_setup_true_lang-PUnUpH4H.js";import"./HDropdown-DFGm5c_S.js";import"./HTabList.vue_vue_type_script_setup_true_lang-BEyYCazB.js";import"./use-resolve-button-type-DnRVrBaM.js";import"./index.vue_vue_type_script_setup_true_lang-DN03WRps.js";import"./index.vue_vue_type_script_setup_true_lang-Do4XPH2t.js";import"./HKbd-LjWkyhwy.js";import"./index.vue_vue_type_script_setup_true_lang-DbfRBGyF.js";const v=c(s({name:"Topbar",__name:"index",setup(s){const c=a(),v=r((()=>!("head"===c.settings.menu.menuMode&&(!c.settings.toolbar.breadcrumb||"filesystem"===c.settings.app.routeBaseOn)))),g=o(0),y=o(!1),j=r((()=>(c.settings.tabbar.enable?Number.parseInt(getComputedStyle(document.documentElement||document.body).getPropertyValue("--g-tabbar-height")):0)+(v.value?Number.parseInt(getComputedStyle(document.documentElement||document.body).getPropertyValue("--g-toolbar-height")):0)));function x(){g.value=(document.documentElement||document.body).scrollTop}return i((()=>{window.addEventListener("scroll",x)})),n((()=>{window.removeEventListener("scroll",x)})),p(g,((e,t)=>{y.value="sticky"===c.settings.topbar.mode&&e>t&&e>j.value})),(s,a)=>(_(),u("div",{class:d(["topbar-container",{"has-tabbar":m(c).settings.tabbar.enable,"has-toolbar":m(v),[`topbar-${m(c).settings.topbar.mode}`]:!0,shadow:m(g),hide:m(y)}]),"data-fixed-calc-width":""},[m(c).settings.tabbar.enable?(_(),l(e,{key:0})):b("",!0),m(v)?(_(),l(t,{key:1})):b("",!0)],2))}}),[["__scopeId","data-v-012f7d01"]]);export{v as default};
