
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as t,r as a,P as o,b as s,Q as c,c as n,e as u,f as d,w as r,j as i,h as m,_ as f,g as p,i as _,Y as g,k as S}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";const y=l({__name:"tencent",setup(l){const S=t({tencentCosStatus:"",cosSecretId:"",cosSecretKey:"",cosBucket:"",cosRegion:"",tencentCosAcceleratedDomain:""}),y=a();async function V(){const e=await x.queryConfig({keys:["cosSecretKey","cosBucket","cosRegion","cosSecretId","tencentCosStatus","tencentCosAcceleratedDomain"]});Object.assign(S,e.data)}function b(){var e;null==(e=y.value)||e.validate((async e=>{if(e){try{await x.setConfig({settings:(l=S,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),g.success("变更配置信息成功")}catch(t){}V()}else g.error("请填写完整信息");var l}))}const C=o((()=>[{required:1===Number(S.tencentCosStatus),message:"开启配置后请填写此项",trigger:"change"}]));return s((()=>{V()})),(l,t)=>{const a=f,o=i,s=e,g=c("el-switch"),x=c("el-form-item"),V=c("el-col"),h=c("el-row"),v=c("el-input"),w=c("el-form"),k=c("el-card");return u(),n("div",null,[d(s,null,{title:r((()=>t[6]||(t[6]=[p("div",{class:"flex items-center gap-4"},"腾讯云COS参数设置",-1)]))),content:r((()=>t[7]||(t[7]=[p("div",{class:"text-sm/6"},[p("div",null,[m(" 需前往腾讯云申请对象存储服务，更多配置及申请详见"),p("a",{href:"https://console.cloud.tencent.com/cos",target:"_blank"},"腾讯云COS"),m(" 。如果同时开启多个存储服务，服务优先级：本地存储 > S3存储 > 腾讯云COS > 阿里云OSS。 ")])],-1)]))),default:r((()=>[d(o,{outline:"",onClick:b},{default:r((()=>[d(a,{name:"i-ri:file-text-line"}),t[8]||(t[8]=m(" 保存设置 "))])),_:1})])),_:1}),d(k,{style:{margin:"20px"}},{default:r((()=>[d(w,{ref_key:"formRef",ref:y,model:S,"label-width":"120px"},{default:r((()=>[d(h,null,{default:r((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[d(x,{label:"启用状态",prop:"tencentCosStatus"},{default:r((()=>[d(g,{modelValue:S.tencentCosStatus,"onUpdate:modelValue":t[0]||(t[0]=e=>S.tencentCosStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(h,null,{default:r((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[d(x,{label:"SecretId",prop:"cosSecretId",rules:_(C)},{default:r((()=>[d(v,{modelValue:S.cosSecretId,"onUpdate:modelValue":t[1]||(t[1]=e=>S.cosSecretId=e),placeholder:"请填写SecretId",type:"password","show-password":"",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),d(h,null,{default:r((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[d(x,{label:"SecretKey",prop:"cosSecretKey",rules:_(C)},{default:r((()=>[d(v,{modelValue:S.cosSecretKey,"onUpdate:modelValue":t[2]||(t[2]=e=>S.cosSecretKey=e),placeholder:"请填写SecretKey",type:"password","show-password":"",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),d(h,null,{default:r((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[d(x,{label:"存储桶名称",prop:"cosBucket",rules:_(C)},{default:r((()=>[d(v,{modelValue:S.cosBucket,"onUpdate:modelValue":t[3]||(t[3]=e=>S.cosBucket=e),placeholder:"请填写存储桶名称",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),d(h,null,{default:r((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[d(x,{label:"所属地域",prop:"cosRegion",rules:_(C)},{default:r((()=>[d(v,{modelValue:S.cosRegion,"onUpdate:modelValue":t[4]||(t[4]=e=>S.cosRegion=e),placeholder:"请填写所属地域(ap-guangzhou)",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),d(h,null,{default:r((()=>[d(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[d(x,{label:"全球加速域名",prop:"tencentCosAcceleratedDomain"},{default:r((()=>[d(v,{modelValue:S.tencentCosAcceleratedDomain,"onUpdate:modelValue":t[5]||(t[5]=e=>S.tencentCosAcceleratedDomain=e),placeholder:"如您是国外服务器可开启全球加速域名得到更快响应速度、同理也会更高计费！",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})])}}});"function"==typeof S&&S(y);export{y as default};
