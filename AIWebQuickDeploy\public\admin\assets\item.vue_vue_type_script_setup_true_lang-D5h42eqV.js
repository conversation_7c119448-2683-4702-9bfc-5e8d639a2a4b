
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./HTooltip.vue_vue_type_script_setup_true_lang-T8XkjmIi.js";import{d as t,ap as a,r as l,P as n,Q as s,c as i,e as o,a6 as r,i as u,f as p,w as m,a2 as f,aD as v,R as c,ba as b,g as x,T as d,am as y,_ as g,t as M}from"./index-BERX8Mlm.js";const h="rootMenu",w=t({__name:"item",props:{uniqueKey:{},item:{},level:{default:0},subMenu:{type:Boolean,default:!1},expand:{type:Boolean,default:!1}},setup(t,{expose:w}){const _=t,P=a(h),k=l(),C=n((()=>_.subMenu?P.subMenus[_.uniqueKey.at(-1)].active:P.activeIndex===_.uniqueKey.at(-1))),N=n((()=>C.value&&(!_.subMenu||P.isMenuPopup))),j=n((()=>P.isMenuPopup?"":`padding-left: ${20*(_.level??0)}px`));return w({ref:k}),(t,a)=>{const l=g,n=e,h=s("router-link");return o(),i("div",{ref_key:"itemRef",ref:k,class:r(["menu-item relative transition-all",{active:u(N)}])},[p(h,{custom:"",to:t.uniqueKey.at(-1)??""},{default:m((({href:e,navigate:a})=>{var s,g,h;return[p(n,{enable:u(P).isMenuPopup&&0===t.level&&!t.subMenu,text:("function"==typeof(null==(s=t.item.meta)?void 0:s.title)?null==(g=t.item.meta)?void 0:g.title():null==(h=t.item.meta)?void 0:h.title)??"",placement:"right",class:"h-full w-full"},{default:m((()=>{var n,s,p,g,h;return[(o(),f(v(t.subMenu?"div":"a"),c({...!t.subMenu&&{href:(null==(n=t.item.meta)?void 0:n.link)?t.item.meta.link:e,target:(null==(s=t.item.meta)?void 0:s.link)?"_blank":"_self",class:"no-underline"}},{class:["group menu-item-container h-full w-full flex cursor-pointer items-center justify-between gap-1 px-5 py-4 text-[var(--g-sub-sidebar-menu-color)] transition-all hover-bg-[var(--g-sub-sidebar-menu-hover-bg)] hover-text-[var(--g-sub-sidebar-menu-hover-color)]",{"text-[var(--g-sub-sidebar-menu-active-color)]! bg-[var(--g-sub-sidebar-menu-active-bg)]!":u(N),"px-3!":u(P).isMenuPopup&&0===t.level}],title:"function"==typeof(null==(p=t.item.meta)?void 0:p.title)?null==(g=t.item.meta)?void 0:g.title():null==(h=t.item.meta)?void 0:h.title},b({...!t.subMenu&&{click:a}})),{default:m((()=>{var e,a,n,s;return[x("div",{class:r(["inline-flex flex-1 items-center justify-center gap-[12px]",{"flex-col":u(P).isMenuPopup&&0===t.level&&"vertical"===u(P).props.mode,"gap-1!":u(P).isMenuPopup&&0===t.level&&u(P).props.showCollapseName,"w-full":u(P).isMenuPopup&&0===t.level&&u(P).props.showCollapseName&&"vertical"===u(P).props.mode}]),style:y(u(j))},[(null==(e=_.item.meta)?void 0:e.icon)?(o(),f(l,{key:0,name:_.item.meta.icon,size:20,class:"menu-item-container-icon transition-transform group-hover-scale-120",async:""},null,8,["name"])):d("",!0),!u(P).isMenuPopup||0!==t.level||u(P).props.showCollapseName?(o(),i("span",{key:1,class:r(["w-0 flex-1 truncate text-sm transition-height transition-opacity transition-width",{"opacity-0 w-0 h-0":u(P).isMenuPopup&&0===t.level&&!u(P).props.showCollapseName,"w-full text-center":u(P).isMenuPopup&&0===t.level&&u(P).props.showCollapseName}])},M("function"==typeof(null==(a=t.item.meta)?void 0:a.title)?null==(n=t.item.meta)?void 0:n.title():null==(s=t.item.meta)?void 0:s.title),3)):d("",!0)],6),!t.subMenu||u(P).isMenuPopup&&0===t.level?d("",!0):(o(),i("i",{key:0,class:r(["relative ml-1 w-[10px] after:absolute after:h-[1.5px] after:w-[6px] after:bg-current after:transition-transform-200 after:content-empty after:-translate-y-[1px] before:absolute before:h-[1.5px] before:w-[6px] before:bg-current before:transition-transform-200 before:content-empty before:-translate-y-[1px]",[t.expand?"before:-rotate-45 before:-translate-x-[2px] after:rotate-45 after:translate-x-[2px]":"before:rotate-45 before:-translate-x-[2px] after:-rotate-45 after:translate-x-[2px]",u(P).isMenuPopup&&0===t.level&&"opacity-0",u(P).isMenuPopup&&0!==t.level&&"-rotate-90 -top-[1.5px]"]])},null,2))]})),_:2},1040,["class","title"]))]})),_:2},1032,["enable","text"])]})),_:1},8,["to"])],2)}}});export{w as _,h as r};
