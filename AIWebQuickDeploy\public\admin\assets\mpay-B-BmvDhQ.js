
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as e,$ as l,P as p,r as y,a0 as t,b as r,Q as u,c as d,e as n,f as i,w as s,j as o,h as c,_ as f,g as m,V as M,W as g,t as _,Y as b,k as U}from"./index-BERX8Mlm.js";import{a as h}from"./config-BrbFL53_.js";const x=e({__name:"mpay",setup(e){const U=l({payMpayStatus:"",payMpayPid:"",payMpaySecret:"",payMpayNotifyUrl:"",payMpayReturnUrl:"",payMpayApiPayUrl:"",payMpayRedirect:"",payMpayChannel:[]}),x=p((()=>{const a="1"===U.payMpayStatus;return{payMpayStatus:[{required:!0,trigger:"change",message:"请选择当前支付开启状态"}],payMpaySecret:[{required:a,trigger:"blur",message:"请填写支付秘钥"}],payMpayPid:[{required:a,trigger:"blur",message:"请填写商户PID"}],payMpayNotifyUrl:[{required:a,trigger:"blur",message:"请填写支付通知地址"}],payMpayReturnUrl:[{required:a,trigger:"blur",message:"请填写支付回调地址"}],payMpayApiPayUrl:[{required:a,trigger:"blur",message:"请填写平台支付API请求地址"}],payMpayChannel:[{required:a,trigger:"change",message:"请选择至少一个支付渠道"}]}})),v=y(),V=[{label:"微信支付",value:"wxpay"},{label:"支付宝支付",value:"alipay"}];async function P(){const a=await h.queryConfig({keys:["payMpaySecret","payMpayNotifyUrl","payMpayReturnUrl","payMpayPid","payMpayStatus","payMpayApiPayUrl","payMpayRedirect","payMpayChannel"]}),e=a.data.payMpayChannel?JSON.parse(a.data.payMpayChannel):[];Object.assign(U,a.data,{payMpayChannel:e})}function S(){var a;null==(a=v.value)||a.validate((async a=>{if(a){try{await h.setConfig({settings:(e=U,Object.keys(e).map((a=>({configKey:a,configVal:C(a,e[a])}))))}),b.success("变更配置信息成功")}catch(l){}P()}else b.error("请填写完整信息");var e}))}function C(a,e){return["payMpayChannel"].includes(a)?e?e?JSON.stringify(e):void 0:[]:e}return t((()=>U.payMpayStatus),(()=>{setTimeout((()=>{var a;null==(a=v.value)||a.validateField(["payMpaySecret","payMpayPid","payMpayNotifyUrl","payMpayReturnUrl","payMpayApiPayUrl","payMpayChannel"])}),0)})),r((()=>{P()})),(e,l)=>{const p=f,y=o,t=a,r=u("el-switch"),b=u("el-form-item"),h=u("el-col"),P=u("el-row"),C=u("el-input"),R=u("el-divider"),k=u("el-checkbox"),q=u("el-checkbox-group"),A=u("el-form"),N=u("el-card");return n(),d("div",null,[i(t,null,{title:s((()=>l[7]||(l[7]=[m("div",{class:"flex items-center gap-4"},"码支付设置",-1)]))),content:s((()=>l[8]||(l[8]=[m("div",{class:"text-sm/6"},[m("div",null,"支付通知地址为： https://您的域名/api/pay/notify。")],-1)]))),default:s((()=>[i(y,{outline:"",onClick:S},{default:s((()=>[i(p,{name:"i-ri:file-text-line"}),l[9]||(l[9]=c(" 保存设置 "))])),_:1})])),_:1}),i(N,{style:{margin:"20px"}},{default:s((()=>[i(A,{ref_key:"formRef",ref:v,rules:x.value,model:U,"label-width":"120px"},{default:s((()=>[i(P,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(b,{label:"启用当前支付",prop:"payMpayPid"},{default:s((()=>[i(r,{modelValue:U.payMpayStatus,"onUpdate:modelValue":l[0]||(l[0]=a=>U.payMpayStatus=a),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(P,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(b,{label:"商户PID",prop:"payMpayPid"},{default:s((()=>[i(C,{modelValue:U.payMpayPid,"onUpdate:modelValue":l[1]||(l[1]=a=>U.payMpayPid=a),placeholder:"请填写商户PID",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(P,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(b,{label:"商户秘钥",prop:"payMpaySecret"},{default:s((()=>[i(C,{modelValue:U.payMpaySecret,"onUpdate:modelValue":l[2]||(l[2]=a=>U.payMpaySecret=a),placeholder:"请填写商户秘钥",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(P,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(b,{label:"支付通知地址",prop:"payMpaySecret"},{default:s((()=>[i(C,{modelValue:U.payMpayNotifyUrl,"onUpdate:modelValue":l[3]||(l[3]=a=>U.payMpayNotifyUrl=a),placeholder:"请填写支付通知地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(P,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(b,{label:"支付回调地址",prop:"payMpaySecret"},{default:s((()=>[i(C,{modelValue:U.payMpayReturnUrl,"onUpdate:modelValue":l[4]||(l[4]=a=>U.payMpayReturnUrl=a),placeholder:"请填写支付成功后的回跳地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(R),i(P,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(b,{label:"支付请求地址",prop:"payMpayApiPayUrl"},{default:s((()=>[i(C,{modelValue:U.payMpayApiPayUrl,"onUpdate:modelValue":l[5]||(l[5]=a=>U.payMpayApiPayUrl=a),placeholder:"请填写平台支付请求地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(R),i(P,null,{default:s((()=>[i(h,{xs:24,md:24,lg:24,xl:24},{default:s((()=>[i(b,{label:"开启支付渠道",prop:"payMpayChannel"},{default:s((()=>[i(q,{modelValue:U.payMpayChannel,"onUpdate:modelValue":l[6]||(l[6]=a=>U.payMpayChannel=a),size:"small"},{default:s((()=>[(n(),d(M,null,g(V,(a=>i(k,{key:a.value,border:"",label:a.value},{default:s((()=>[c(_(a.label),1)])),_:2},1032,["label"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof U&&U(x);export{x as default};
