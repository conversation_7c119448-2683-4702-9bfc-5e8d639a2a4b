
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as t,a as e,c as s,T as a,i,e as n,g as r,f as o,_ as g,t as p,V as c,Z as y}from"./index-BERX8Mlm.js";const h={key:0,class:"copyright"},l={key:0},m=["href"],b={key:1},d={key:2,href:"https://beian.miit.gov.cn/",target:"_blank",rel:"noopener"},k=y(t({name:"Copyright",__name:"index",setup(t){const y=e();return(t,e)=>{const k=g;return i(y).settings.copyright.enable?(n(),s("footer",h,[e[0]||(e[0]=r("span",null,"Copyright",-1)),o(k,{name:"i-ri:copyright-line",size:18}),i(y).settings.copyright.dates?(n(),s("span",l,p(i(y).settings.copyright.dates),1)):a("",!0),i(y).settings.copyright.company?(n(),s(c,{key:1},[i(y).settings.copyright.website?(n(),s("a",{key:0,href:i(y).settings.copyright.website,target:"_blank",rel:"noopener"},p(i(y).settings.copyright.company),9,m)):(n(),s("span",b,p(i(y).settings.copyright.company),1))],64)):a("",!0),i(y).settings.copyright.beian?(n(),s("a",d,p(i(y).settings.copyright.beian),1)):a("",!0)])):a("",!0)}}}),[["__scopeId","data-v-c2a73981"]]);export{k as default};
