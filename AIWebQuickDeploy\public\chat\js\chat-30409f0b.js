var br=Object.defineProperty,wr=Object.defineProperties;var xr=Object.getOwnPropertyDescriptors;var Vs=Object.getOwnPropertySymbols;var po=Object.prototype.hasOwnProperty,go=Object.prototype.propertyIsEnumerable;var Ia=(e,t,n)=>t in e?br(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,me=(e,t)=>{for(var n in t||(t={}))po.call(t,n)&&Ia(e,n,t[n]);if(Vs)for(var n of Vs(t))go.call(t,n)&&Ia(e,n,t[n]);return e},mt=(e,t)=>wr(e,xr(t));var ho=(e,t)=>{var n={};for(var s in e)po.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(e!=null&&Vs)for(var s of Vs(e))t.indexOf(s)<0&&go.call(e,s)&&(n[s]=e[s]);return n};var et=(e,t,n)=>(Ia(e,typeof t!="symbol"?t+"":t,n),n),Sa=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)};var m=(e,t,n)=>(Sa(e,t,"read from private field"),n?n.call(e):t.get(e)),_e=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},xe=(e,t,n,s)=>(Sa(e,t,"write to private field"),s?s.call(e,n):t.set(e,n),n);var Ys=(e,t,n,s)=>({set _(a){xe(e,t,a,n)},get _(){return m(e,t,s)}}),ce=(e,t,n)=>(Sa(e,t,"access private method"),n);var H=(e,t,n)=>new Promise((s,a)=>{var o=l=>{try{c(n.next(l))}catch(u){a(u)}},i=l=>{try{c(n.throw(l))}catch(u){a(u)}},c=l=>l.done?s(l.value):Promise.resolve(l.value).then(o,i);c((n=n.apply(e,t)).next())});import{s as si,g as ns,p as Os,u as ft,a as It,_ as an,m as zt,b as it,t as oe,D as hn,f as kr,c as Ar,d as Cr,e as ai,h as Ir,i as Sr,j as _r,k as Tr,l as Lr,n as Mr,o as Er,q as Rr,r as oi,v as zr,w as Qt,x as Dr,y as jr,z as Pr,A as $r,B as Ur,C as Nr}from"./index-ac663bb2.js";import{D as ii,q as je,d as S,w as ke,x as g,E as r,G as R,H as I,v as d,I as kt,J as ne,K as sn,j as k,L as Xe,M as At,u as y,N as zs,O as Ne,l as X,e as Ge,P as ss,Q as pe,R as Ct,T as Hn,S as Fr,p as mo,z as vo,F as Pe,s as Ds,U as He,V as Gr,W as Br,i as Ue,X as Tt,t as ri,n as at,Y as Wn,Z as li,_ as ci,m as yo,$ as Or,a0 as Wr,y as da,a1 as va,a2 as ui,a3 as Hr,o as js,a4 as di}from"./vue-vendor-d751b0f5.js";import{C as mn,U as fi,E as qa,P as Vr,W as Yr,I as Zr,L as Xr,A as us,a as Qr,M as qr,D as Ls,b as Ms,c as Jr,d as Kr,e as Ja,f as el,V as ta,g as tl,R as pi,S as nl,h as sl,O as al,F as ol,i as il,j as rl,k as ll,T as gi,l as hi,m as cl,n as ul,o as dl,p as fl,q as pl,B as gl,r as hl,s as ml,t as mi,u as bo,v as vl,w as yl,x as bl,y as wl,z as xl,G as wo,H as kl,J as Al,K as Cl}from"./ui-vendor-70145f70.js";import{e as Il,f as Sl,h as _l,g as Ka}from"./utils-vendor-c35799af.js";import{bu as Tl,bv as Zs,bw as Ll}from"./chart-vendor-e1d59b84.js";import{M as vi,H as Xs}from"./editor-vendor-e2dea24d.js";function Ml(){return typeof process!="undefined"&&(process==null?void 0:process.type)==="renderer"?"electron":typeof wx!="undefined"?"wechat":typeof window!="undefined"&&window.matchMedia("(display-mode: standalone)").matches?"webApp":/(Android|webOS|iPhone|iPad|iPod|BlackBerry|Windows Phone)/i.test(navigator.userAgent)?"mobile":"webBrowser"}const yi="appSetting";function El(){return{siderCollapsed:!1,theme:"light",language:"zh-CN",env:Ml()}}function Rl(){const e=si.get(yi);return me(me({},El()),e)}function zl(e){si.set(yi,e)}const bs=ii("app-store",{state:()=>Rl(),actions:{setSiderCollapsed(e){this.siderCollapsed=e,this.recordState()},setTheme(e){localStorage.theme=e,this.theme=e,window.theme=e,this.recordState(),e==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},setLanguage(e){this.language!==e&&(this.language=e,this.recordState())},recordState(){zl(this.$state)},setEnv(){const e=/micromessenger/i.test(navigator.userAgent),t=navigator.userAgent.includes("Electron"),n=/(iPhone|iPad|iPod|Android|webOS|BlackBerry|Windows Phone)/i.test(navigator.userAgent);e?this.env="wechat":t?this.env="electron":n?this.env="mobile":!e&&!t&&(this.env="web")}}});function Dl(){return ns({url:"/app/queryCats"})}function bi(){return ns({url:"/app/list"})}function jl(){return ns({url:"/app/mineApps"})}function wi(e){return Os({url:"/app/collect",data:e})}function Ws(e){return ns({url:"/app/queryOneCat",data:e})}const xi=ii("app-cat-store",{state:()=>({catId:0,mineApps:[]}),actions:{setCatId(e){this.catId=e},queryMineApps(){return H(this,null,function*(){var t;const e=yield jl();this.mineApps=((t=e==null?void 0:e.data)==null?void 0:t.rows)||[]})}}});const Pl={key:0,class:"fixed inset-0 z-[10001] px-2 flex items-center justify-center bg-black bg-opacity-50"},$l={class:"bg-white dark:bg-gray-900 p-4 rounded-lg shadow-lg w-full max-w-3xl max-h-[80vh] flex flex-col relative"},Ul={class:"flex justify-end mt-3"},Nl=["disabled"],Fl={key:0},Gl={key:1},Bl=je({__name:"BadWordsDialog",props:{visible:{type:Boolean}},setup(e){const t=e;ft();const n=It(),s=S(15),a=S(!1);function o(){const c=setInterval(()=>{s.value>0?s.value-=1:(a.value=!0,clearInterval(c))},1e3)}function i(){a.value&&(n.UpdateBadWordsDialog(!1),s.value=15,a.value=!1)}return ke(()=>t.visible,c=>{c&&o()},{immediate:!0}),t.visible&&o(),(c,l)=>t.visible?(d(),g("div",Pl,[r("div",$l,[l[0]||(l[0]=r("div",{class:"flex justify-between items-center mb-3"},[r("span",{class:"text-xl"},"合理合规须知")],-1)),l[1]||(l[1]=r("div",{class:"flex-1 overflow-y-auto custom-scrollbar"},[r("p",null,"请合理合规使用，请勿咨询敏感信息或使用敏感词生成图片。"),r("p",null," 多次触发平台风控，将记录【账号/IP】等信息并禁止使用，保留向有关部门提交相关记录的权利。 ")],-1)),r("div",Ul,[r("button",{disabled:!a.value,onClick:i,class:"px-4 py-2 shadow-sm bg-primary-600 text-white rounded-md hover:bg-primary-500 disabled:bg-gray-400"},[a.value?(d(),g("span",Fl,"已知晓")):(d(),g("span",Gl,"请等待 "+R(s.value)+" 秒",1))],8,Nl)])])])):I("",!0)}}),Ol="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAELklEQVRYR+2YW2wUZRTH//9vtlCoF9IoIklT3PqgPGi326hoetuaGEhIr9SgCYkkgt2WGOQVCca+GavWdr0GjD4YhG3RB3hply1LQA1tEQIxEXapGI2pEkys9LIzx2ylYWfY6e5sF0oi+7hzzvl+3/9855xvhrjNf7zN+XAHcL4Z+n8o6JWTeYt++W25S596AIZy6TB+n3yo+Nchlk8vmIIVowdXU9c3Q1gDSilBlQwjgBAYFGDvdF58/4milqvZwDpOcXWsb5Uh8hmBqkwXFMhlCN8aX5LXNbRy/T+Z+iXsHAFWRXs3QGQPyLucLDJrK5DgUXdTsxPfjAEro8E3Ce50EtxsKxPTwCPH3U2jTmJkBJgWTnAMxDeGMEoa0xQ+LJQnCD4HYFkCyAC3RdwN3U7gMkpxRTTYrMD91sCJIgCxV5R6O1Jcfy7VwonqLoj9/CqB2kF341qncGkBvRe+ureAWpRgoalCBecMFzcdK24YymZRJz5zprgq1tsJwXYL3CVZGvdGHmwZc7JQtra2gE+f712ep2QUYP714DJhaJrXLqXZQszlZwtYdSHoB9ljVk/ePVrSZFL0ZkAlxzQBVseCT8WhZhRThtFB8plk9Zi/qCi8cv0fNxvKFrDy4oF11NXXIFy2EII4iBcG3Y03VLZT8OqRd5aFPduvOEpxRayvXolxAKB2g6NgEhobBlc1HHYKY7WvHf5wtVAPgegIlbbZ9seUZ7AyFnwewi9pGoUyDmhrB931kfnC1ZwOeKlLP8GZJi6QLSFP2yep4toXSbT3ZQAfX3O6omt8Nhd9r/aHQAUMOQywYBZo5uZD2ThQ2rbPCjlnH6yI9rUryE5DU75ctJaake46Be4DuDjF8dFBNA94/AdtiySVxIlpMlTS8td801o70vMigM9huTda2lhcKHVHPO2HZv/P6LIwX7hk/+qzPSvUJGMkrg8AQYTkroRdXMlE+HH/twsG6BsOdJHYZlaO/lBZ6weOiiSXqs3Gqj0TeAxx+T75DIpgwjC0onD51pQD4JaluPrkR/cpFT9DcoVp84LOgTL/DjtBbglgou+puHwB8lEznPxJw1XSX77VtgizBvQNBw4RMqB7xt4Lc3c8lQKJaQHoO4R8ydz0/7MWoCXk8c85MrMC9J3qaafw/WtQlwXST+F3BnAeYB4obgJ1BJIuG+YtiKAjVOZ/Pd1ZdwzoG+4uBtSPpjaRbhXLcwF3hzytb2TilgVgT5BkYybBrTYC+Rvg5nRpdTRJrIs8+VPXPQXj2i4ItxC4O2NQQUQnN4U9rRcz9nH64p4ceM2lziX5Y4s3KHCdUHwE77ecMkMEp6BwhIa2Z6DslZRvfulgHafYLuCas58WLp2aLCFUga70qxOFU6dPFL2W1feYeaU43Y5z/TxnCuYabMEuC043ckdBp4pZ7f8FE5psOI1g6fwAAAAASUVORK5CYII=";const Wl={props:{canvasWidth:{type:Number,default:310},canvasHeight:{type:Number,default:160},show:{type:Boolean,default:!1},puzzleScale:{type:Number,default:1},sliderSize:{type:Number,default:40},range:{type:Number,default:10},imgs:{type:Array},successText:{type:String,default:"验证通过！"},failText:{type:String,default:"验证失败，请重试"},sliderText:{type:String,default:"拖动滑块完成拼图"}},data(){return{mouseDown:!1,startWidth:50,startX:0,newX:0,pinX:0,pinY:0,loading:!1,isCanSlide:!1,error:!1,infoBoxShow:!1,infoText:"",infoBoxFail:!1,timer1:null,closeDown:!1,isSuccess:!1,imgIndex:-1,isSubmting:!1,resetSvg:Ol}},mounted(){document.body.appendChild(this.$el),document.addEventListener("mousemove",this.onRangeMouseMove,!1),document.addEventListener("mouseup",this.onRangeMouseUp,!1),document.addEventListener("touchmove",this.onRangeMouseMove,{passive:!1}),document.addEventListener("touchend",this.onRangeMouseUp,!1),this.show&&(document.body.classList.add("vue-puzzle-overflow"),this.reset())},beforeDestroy(){clearTimeout(this.timer1),document.body.removeChild(this.$el),document.removeEventListener("mousemove",this.onRangeMouseMove,!1),document.removeEventListener("mouseup",this.onRangeMouseUp,!1),document.removeEventListener("touchmove",this.onRangeMouseMove,{passive:!1}),document.removeEventListener("touchend",this.onRangeMouseUp,!1)},watch:{show(e){e?(document.body.classList.add("vue-puzzle-overflow"),this.reset()):(this.isSubmting=!1,this.isSuccess=!1,this.infoBoxShow=!1,document.body.classList.remove("vue-puzzle-overflow"))}},computed:{styleWidth(){const e=this.startWidth+this.newX-this.startX;return e<this.sliderBaseSize?this.sliderBaseSize:e>this.canvasWidth?this.canvasWidth:e},puzzleBaseSize(){return Math.round(Math.max(Math.min(this.puzzleScale,2),.2)*52.5+6)},sliderBaseSize(){return Math.max(Math.min(Math.round(this.sliderSize),Math.round(this.canvasWidth*.5)),10)}},methods:{onClose(){!this.mouseDown&&!this.isSubmting&&(clearTimeout(this.timer1),this.$emit("close"))},onCloseMouseDown(){this.closeDown=!0},onCloseMouseUp(){this.closeDown&&this.onClose(),this.closeDown=!1},onRangeMouseDown(e){this.isCanSlide&&(this.mouseDown=!0,this.startWidth=this.$refs["range-slider"].clientWidth,this.newX=e.clientX||e.changedTouches[0].clientX,this.startX=e.clientX||e.changedTouches[0].clientX)},onRangeMouseMove(e){this.mouseDown&&(e.preventDefault(),this.newX=e.clientX||e.changedTouches[0].clientX)},onRangeMouseUp(){this.mouseDown&&(this.mouseDown=!1,this.submit())},init(e){if(this.loading&&!e)return;this.loading=!0,this.isCanSlide=!1;const t=this.$refs.canvas1,n=this.$refs.canvas2,s=this.$refs.canvas3,a=t.getContext("2d"),o=n.getContext("2d"),i=s.getContext("2d"),c=navigator.userAgent.indexOf("Firefox")>=0&&navigator.userAgent.indexOf("Windows")>=0,l=document.createElement("img");if(a.fillStyle="rgba(255,255,255,1)",i.fillStyle="rgba(255,255,255,1)",a.clearRect(0,0,this.canvasWidth,this.canvasHeight),o.clearRect(0,0,this.canvasWidth,this.canvasHeight),this.pinX=this.getRandom(this.puzzleBaseSize,this.canvasWidth-this.puzzleBaseSize-20),this.pinY=this.getRandom(20,this.canvasHeight-this.puzzleBaseSize-20),l.crossOrigin="anonymous",l.onload=()=>{const[u,f,p,b]=this.makeImgSize(l);a.save(),this.paintBrick(a),a.closePath(),c?(a.clip(),a.save(),a.shadowOffsetX=0,a.shadowOffsetY=0,a.shadowColor="#000",a.shadowBlur=3,a.fill(),a.restore()):(a.shadowOffsetX=0,a.shadowOffsetY=0,a.shadowColor="#000",a.shadowBlur=3,a.fill(),a.clip()),a.drawImage(l,u,f,p,b),i.fillRect(0,0,this.canvasWidth,this.canvasHeight),i.drawImage(l,u,f,p,b),a.globalCompositeOperation="source-atop",this.paintBrick(a),a.arc(this.pinX+Math.ceil(this.puzzleBaseSize/2),this.pinY+Math.ceil(this.puzzleBaseSize/2),this.puzzleBaseSize*1.2,0,Math.PI*2,!0),a.closePath(),a.shadowColor="rgba(255, 255, 255, .8)",a.shadowOffsetX=-1,a.shadowOffsetY=-1,a.shadowBlur=Math.min(Math.ceil(8*this.puzzleScale),12),a.fillStyle="#ffffaa",a.fill();const h=a.getImageData(this.pinX-3,this.pinY-20,this.pinX+this.puzzleBaseSize+5,this.pinY+this.puzzleBaseSize+5);o.putImageData(h,0,this.pinY-20),a.restore(),a.clearRect(0,0,this.canvasWidth,this.canvasHeight),a.save(),this.paintBrick(a),a.globalAlpha=.8,a.fillStyle="#ffffff",a.fill(),a.restore(),a.save(),a.globalCompositeOperation="source-atop",this.paintBrick(a),a.arc(this.pinX+Math.ceil(this.puzzleBaseSize/2),this.pinY+Math.ceil(this.puzzleBaseSize/2),this.puzzleBaseSize*1.2,0,Math.PI*2,!0),a.shadowColor="#000",a.shadowOffsetX=2,a.shadowOffsetY=2,a.shadowBlur=16,a.fill(),a.restore(),a.save(),a.globalCompositeOperation="destination-over",a.drawImage(l,u,f,p,b),a.restore(),this.loading=!1,this.isCanSlide=!0},l.onerror=()=>{this.init(!0)},!e&&this.imgs&&this.imgs.length){let u=this.getRandom(0,this.imgs.length-1);u===this.imgIndex&&(u===this.imgs.length-1?u=0:u++),this.imgIndex=u,l.src=this.imgs[u]}else l.src=this.makeImgWithCanvas()},getRandom(e,t){return Math.ceil(Math.random()*(t-e)+e)},makeImgSize(e){const t=e.width/e.height,n=this.canvasWidth/this.canvasHeight;let s=0,a=0,o=0,i=0;return t>n?(i=this.canvasHeight,o=t*i,a=0,s=(this.canvasWidth-o)/2):(o=this.canvasWidth,i=o/t,s=0,a=(this.canvasHeight-i)/2),[s,a,o,i]},paintBrick(e){const t=Math.ceil(15*this.puzzleScale);e.beginPath(),e.moveTo(this.pinX,this.pinY),e.lineTo(this.pinX+t,this.pinY),e.arcTo(this.pinX+t,this.pinY-t/2,this.pinX+t+t/2,this.pinY-t/2,t/2),e.arcTo(this.pinX+t+t,this.pinY-t/2,this.pinX+t+t,this.pinY,t/2),e.lineTo(this.pinX+t+t+t,this.pinY),e.lineTo(this.pinX+t+t+t,this.pinY+t),e.arcTo(this.pinX+t+t+t+t/2,this.pinY+t,this.pinX+t+t+t+t/2,this.pinY+t+t/2,t/2),e.arcTo(this.pinX+t+t+t+t/2,this.pinY+t+t,this.pinX+t+t+t,this.pinY+t+t,t/2),e.lineTo(this.pinX+t+t+t,this.pinY+t+t+t),e.lineTo(this.pinX,this.pinY+t+t+t),e.lineTo(this.pinX,this.pinY+t+t),e.arcTo(this.pinX+t/2,this.pinY+t+t,this.pinX+t/2,this.pinY+t+t/2,t/2),e.arcTo(this.pinX+t/2,this.pinY+t,this.pinX,this.pinY+t,t/2),e.lineTo(this.pinX,this.pinY)},makeImgWithCanvas(){const e=document.createElement("canvas"),t=e.getContext("2d");e.width=this.canvasWidth,e.height=this.canvasHeight,t.fillStyle=`rgb(${this.getRandom(100,255)},${this.getRandom(100,255)},${this.getRandom(100,255)})`,t.fillRect(0,0,this.canvasWidth,this.canvasHeight);for(let n=0;n<12;n++)if(t.fillStyle=`rgb(${this.getRandom(100,255)},${this.getRandom(100,255)},${this.getRandom(100,255)})`,t.strokeStyle=`rgb(${this.getRandom(100,255)},${this.getRandom(100,255)},${this.getRandom(100,255)})`,this.getRandom(0,2)>1)t.save(),t.rotate(this.getRandom(-90,90)*Math.PI/180),t.fillRect(this.getRandom(-20,e.width-20),this.getRandom(-20,e.height-20),this.getRandom(10,e.width/2+10),this.getRandom(10,e.height/2+10)),t.restore();else{t.beginPath();const s=this.getRandom(-Math.PI,Math.PI);t.arc(this.getRandom(0,e.width),this.getRandom(0,e.height),this.getRandom(10,e.height/2+10),s,s+Math.PI*1.5),t.closePath(),t.fill()}return e.toDataURL("image/png")},submit(){this.isSubmting=!0;const e=Math.abs(this.pinX-(this.styleWidth-this.sliderBaseSize)+(this.puzzleBaseSize-this.sliderBaseSize)*((this.styleWidth-this.sliderBaseSize)/(this.canvasWidth-this.sliderBaseSize))-3);e<this.range?(this.infoText=this.successText,this.infoBoxFail=!1,this.infoBoxShow=!0,this.isCanSlide=!1,this.isSuccess=!0,clearTimeout(this.timer1),this.timer1=setTimeout(()=>{this.isSubmting=!1,this.$emit("success",e)},800)):(this.infoText=this.failText,this.infoBoxFail=!0,this.infoBoxShow=!0,this.isCanSlide=!1,this.$emit("fail",e),clearTimeout(this.timer1),this.timer1=setTimeout(()=>{this.isSubmting=!1,this.reset()},800))},resetState(){this.infoBoxFail=!1,this.infoBoxShow=!1,this.isCanSlide=!1,this.isSuccess=!1,this.startWidth=this.sliderBaseSize,this.startX=0,this.newX=0},reset(){this.isSubmting||(this.resetState(),this.init())}}},Hl=["width","height"],Vl=["width","height"],Yl=["width","height"],Zl=["src"],Xl={class:"auth-control_"},Ql={class:"range-text"};function ql(e,t,n,s,a,o){return d(),g("div",{class:ne(["vue-puzzle-vcode",{show_:n.show}]),onMousedown:t[5]||(t[5]=(...i)=>o.onCloseMouseDown&&o.onCloseMouseDown(...i)),onMouseup:t[6]||(t[6]=(...i)=>o.onCloseMouseUp&&o.onCloseMouseUp(...i)),onTouchstart:t[7]||(t[7]=(...i)=>o.onCloseMouseDown&&o.onCloseMouseDown(...i)),onTouchend:t[8]||(t[8]=(...i)=>o.onCloseMouseUp&&o.onCloseMouseUp(...i))},[r("div",{class:"vue-auth-box_ rounded-lg bg-white dark:bg-gray-800",onMousedown:t[3]||(t[3]=sn(()=>{},["stop"])),onTouchstart:t[4]||(t[4]=sn(()=>{},["stop"]))},[r("div",{class:"auth-body_",style:kt(`height: ${n.canvasHeight}px`)},[r("canvas",{ref:"canvas1",width:n.canvasWidth,height:n.canvasHeight,style:kt(`width:${n.canvasWidth}px;height:${n.canvasHeight}px`)},null,12,Hl),r("canvas",{ref:"canvas3",class:ne(["auth-canvas3_",{show:a.isSuccess}]),width:n.canvasWidth,height:n.canvasHeight,style:kt(`width:${n.canvasWidth}px;height:${n.canvasHeight}px`)},null,14,Vl),r("canvas",{width:o.puzzleBaseSize,class:"auth-canvas2_",height:n.canvasHeight,ref:"canvas2",style:kt(`width:${o.puzzleBaseSize}px;height:${n.canvasHeight}px;transform:translateX(${o.styleWidth-o.sliderBaseSize-(o.puzzleBaseSize-o.sliderBaseSize)*((o.styleWidth-o.sliderBaseSize)/(n.canvasWidth-o.sliderBaseSize))}px)`)},null,12,Yl),r("div",{class:ne(["loading-box_",{hide_:!a.loading}])},t[9]||(t[9]=[r("div",{class:"loading-gif_"},[r("span"),r("span"),r("span"),r("span"),r("span")],-1)]),2),r("div",{class:ne(["info-box_",{show:a.infoBoxShow},{fail:a.infoBoxFail}])},R(a.infoText),3),r("div",{class:ne(["flash_",{show:a.isSuccess}]),style:kt(`transform: translateX(${a.isSuccess?`${n.canvasWidth+n.canvasHeight*.578}px`:`-${n.canvasHeight*.578}px`}) skew(-30deg, 0);`)},null,6),r("img",{class:"reset_",onClick:t[0]||(t[0]=(...i)=>o.reset&&o.reset(...i)),src:a.resetSvg},null,8,Zl)],4),r("div",Xl,[r("div",{class:"range-box bg-gray-100 dark:bg-gray-700",style:kt(`height:${o.sliderBaseSize}px`)},[r("div",Ql,R(n.sliderText),1),r("div",{class:"range-slider",ref:"range-slider",style:kt(`width:${o.styleWidth}px`)},[r("div",{class:ne(["range-btn",{isDown:a.mouseDown}]),style:kt(`width:${o.sliderBaseSize}px`),onMousedown:t[1]||(t[1]=i=>o.onRangeMouseDown(i)),onTouchstart:t[2]||(t[2]=i=>o.onRangeMouseDown(i))},t[10]||(t[10]=[r("div",null,null,-1),r("div",null,null,-1),r("div",null,null,-1)]),38)],4)],4)])],32)],34)}const Da=an(Wl,[["render",ql]]),Jl={class:"flex flex-col gap-2"},Kl={for:"username",class:"block text-sm/6 font-medium text-gray-900 dark:text-gray-300"},ec=["placeholder"],tc={class:"mt-6 relative"},nc={class:"flex flex-col gap-2"},sc={for:"password",class:"block text-sm/6 font-medium text-gray-900 dark:text-gray-300"},ac=["placeholder"],oc={key:0,class:"mt-5"},ic={class:"flex items-center"},rc={class:"ml-2 text-sm text-gray-600 dark:text-gray-400"},lc=["disabled"],cc={key:0,class:"inline-block mr-2"},uc={class:"flex flex-col gap-2"},dc={for:"contact",class:"block text-sm/6 font-medium text-gray-900 dark:text-gray-300"},fc=["placeholder"],pc={class:"mt-6"},gc={class:"flex flex-col gap-2"},hc={class:"relative px-1"},mc={class:"flex relative"},vc=["placeholder"],yc=["disabled"],bc={key:0,class:"inline-block mr-1"},wc={class:"rounded-lg"},xc={key:0,class:"mt-5"},kc={class:"flex items-center"},Ac={class:"ml-2 text-sm text-gray-600 dark:text-gray-400"},Cc=["disabled"],Ic={key:0,class:"inline-block mr-2"},Sc=je({__name:"Email",props:{loginMode:{}},setup(e){const t=e,n=S(null),s=zt(),a=S(!1),o=ft(),i=S(0),{isMobile:c}=it(),l=S(!1),u=It(),f=k(()=>o.globalConfig),p=S({contact:"",captchaId:null,code:""}),b=S({username:"",password:""}),h=()=>{let Y=!1;return t.loginMode==="password"?(b.value.username.trim()?(b.value.username.length<2||b.value.username.length>30)&&(Y=!0):Y=!0,b.value.password.trim()?(b.value.password.length<6||b.value.password.length>30)&&(Y=!0):Y=!0):t.loginMode==="captcha"&&(p.value.contact.trim()||(Y=!0),p.value.captchaId||(Y=!0)),!Y},x=()=>p.value.contact.trim()!=="",w=k(()=>Number(o.globalConfig.phoneLoginStatus)===1),v=k(()=>Number(o.globalConfig.emailLoginStatus)===1),C=S(!0);function A(){C.value=!0,u.updateSettingsDialog(!0,hn.AGREEMENT)}const L=k(()=>v.value&&w.value?oe("login.emailPhone"):v.value?oe("login.email"):w.value?oe("login.phone"):""),$=k(()=>v.value&&w.value?oe("login.enterEmailOrPhone"):v.value?oe("login.enterEmail"):w.value?oe("login.enterPhone"):"");function E(){i.value>0&&setTimeout(()=>{i.value--,E()},1e3)}function D(){return H(this,null,function*(){if(l.value=!1,x())try{const{contact:Y}=p.value,P={contact:Y};let se;se=yield kr(P);const{success:G}=se;G&&(s.success(se.data),i.value=60,E())}catch(Y){}})}function O(Y){if(Y.preventDefault(),C.value===!1&&f.value.isAutoOpenAgreement==="1")return s.error(`请阅读并同意《${f.value.agreementTitle}》`);h()&&T()}function T(){return H(this,null,function*(){try{a.value=!0;const Y=t.loginMode==="password"?{username:b.value.username,password:b.value.password}:{username:p.value.contact,captchaId:p.value.captchaId},P=yield Ar(Y);a.value=!1;const{success:se}=P;if(!se)return;s.success(oe("login.loginSuccess")),o.setToken(P.data),o.getUserInfo(),o.setLoginDialog(!1)}catch(Y){a.value=!1,s.error(Y.message)}})}return(Y,P)=>(d(),g("div",{class:ne(["w-full h-full flex flex-col justify-between",y(c)?"px-5 ":"px-10 "])},[Y.loginMode==="password"?(d(),g("form",{key:0,ref_key:"formRef",ref:n,class:"flex flex-col flex-1 justify-between",onSubmit:O},[r("div",null,[r("div",Jl,[r("label",Kl,R(L.value),1),r("div",null,[Xe(r("input",{id:"username",type:"text","onUpdate:modelValue":P[0]||(P[0]=se=>b.value.username=se),placeholder:$.value,class:"input input-lg w-full"},null,8,ec),[[At,b.value.username]])])]),r("div",tc,[r("div",nc,[r("label",sc,R(y(oe)("login.password")),1),r("div",null,[Xe(r("input",{id:"password",type:"password","onUpdate:modelValue":P[1]||(P[1]=se=>b.value.password=se),placeholder:y(oe)("login.enterYourPassword"),class:"input input-lg w-full"},null,8,ac),[[At,b.value.password]])])])]),f.value.isAutoOpenAgreement==="1"?(d(),g("div",oc,[r("div",ic,[Xe(r("input",{id:"agreement-password","onUpdate:modelValue":P[2]||(P[2]=se=>C.value=se),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 dark:border-gray-700 dark:bg-gray-800"},null,512),[[zs,C.value]]),r("p",rc,[P[9]||(P[9]=Ne(" 登录即代表同意 ")),r("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300",onClick:A},"《"+R(f.value.agreementTitle)+"》",1)])])])):I("",!0)]),r("div",null,[r("button",{type:"submit",class:"btn btn-primary btn-lg w-full rounded-full disabled:opacity-50 disabled:cursor-not-allowed",disabled:a.value||!b.value.username.trim()||!b.value.password},[a.value?(d(),g("span",cc,P[10]||(P[10]=[r("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"},null,-1)]))):I("",!0),Ne(" "+R(y(oe)("login.loginAccount")),1)],8,lc)])],544)):I("",!0),Y.loginMode==="captcha"?(d(),g("form",{key:1,ref_key:"formRef",ref:n,class:"flex flex-col flex-1 justify-between",onSubmit:O},[r("div",null,[r("div",uc,[r("label",dc,R(L.value),1),r("div",null,[Xe(r("input",{id:"contact",type:"text","onUpdate:modelValue":P[3]||(P[3]=se=>p.value.contact=se),placeholder:y(oe)("login.enterContact")+L.value,class:"input input-lg w-full"},null,8,fc),[[At,p.value.contact]])])]),r("div",pc,[r("div",gc,[P[12]||(P[12]=r("label",{for:"captchaId",class:"block text-sm/6 font-medium text-gray-900 dark:text-gray-300"},"验证码",-1)),r("div",hc,[r("div",mc,[Xe(r("input",{id:"captchaId",type:"text","onUpdate:modelValue":P[4]||(P[4]=se=>p.value.captchaId=se),placeholder:y(oe)("login.enterCode"),class:"input input-lg w-full pr-32"},null,8,vc),[[At,p.value.captchaId]]),r("button",{type:"button",class:"btn-captcha px-4",disabled:a.value||i.value>0||!p.value.contact.trim(),onClick:P[5]||(P[5]=se=>l.value=!0)},[a.value&&i.value===0?(d(),g("span",bc,P[11]||(P[11]=[r("div",{class:"animate-spin rounded-full h-3 w-3 border-b-2 border-current"},null,-1)]))):I("",!0),Ne(" "+R(i.value>0?`${i.value}秒`:y(oe)("login.sendVerificationCode")),1)],8,yc)])])])]),r("div",wc,[X(Da,{show:l.value,onSuccess:P[6]||(P[6]=se=>D()),onClose:P[7]||(P[7]=se=>l.value=!1),class:"z-[10000]"},null,8,["show"])]),f.value.isAutoOpenAgreement==="1"?(d(),g("div",xc,[r("div",kc,[Xe(r("input",{id:"agreement-captcha","onUpdate:modelValue":P[8]||(P[8]=se=>C.value=se),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 dark:border-gray-700 dark:bg-gray-800"},null,512),[[zs,C.value]]),r("p",Ac,[P[13]||(P[13]=Ne(" 登录即代表同意 ")),r("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300",onClick:A},"《"+R(f.value.agreementTitle)+"》",1)])])])):I("",!0)]),r("div",null,[r("button",{type:"submit",class:"btn btn-primary btn-lg w-full rounded-full disabled:opacity-50 disabled:cursor-not-allowed",disabled:a.value||!p.value.contact.trim()||!p.value.captchaId},[a.value?(d(),g("span",Ic,P[14]||(P[14]=[r("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"},null,-1)]))):I("",!0),P[15]||(P[15]=Ne(" 验证码登录 "))],8,Cc)])],544)):I("",!0)],2))}}),_c={class:"flex flex-col items-center flex-1"},Tc={class:"relative w-[200px] h-[200px] mb-6 mt-auto"},Lc=["src"],Mc={key:1,class:"w-full h-full rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse"},Ec={key:2,class:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"},Rc={key:0,class:"flex items-center mt-2"},zc=je({__name:"Wechat",setup(e){const t=S(),n=S(),s=S(0),a=S(""),o=S(""),i=S(!1),c=S(!0),l=zt(),u=ft(),{isMobile:f}=it(),p=S(!0),b=It();function h(){p.value=!0,b.updateSettingsDialog(!0,hn.AGREEMENT)}const x=k(()=>u.globalConfig);function w($){return new Promise((E,D)=>{const O=new Image;O.onload=()=>E(O),O.onerror=D,O.src=$})}function v(){return H(this,null,function*(){const E=yield Cr({});E.success&&(o.value=E.data,A())})}function C(){return H(this,null,function*(){if(!o.value)return;const $=yield Ir({sceneStr:o.value});$.data&&(clearInterval(t.value),l.success(oe("login.loginSuccess")),u.setToken($.data),u.getUserInfo(),u.setLoginDialog(!1))})}function A(){return H(this,null,function*(){c.value=!0;const $=yield ai({sceneStr:o.value});$.success&&(i.value=!0,yield w($.data),a.value=$.data,c.value=!1,s.value=Date.now(),t.value=setInterval(()=>{if(Date.now()-s.value>6e4){clearInterval(t.value);return}C()},1e3))})}function L(){v()}return Ge(()=>{L(),n.value!==null&&clearInterval(n.value),n.value=setInterval(L,6e4)}),ss(()=>{t.value!==null&&clearInterval(t.value),n.value!==null&&clearInterval(n.value)}),($,E)=>(d(),g("div",{class:ne(["w-full h-full flex flex-col justify-between",y(f)?"px-5 ":"px-10 "])},[r("div",_c,[r("div",Tc,[a.value&&(p.value||x.value.isAutoOpenAgreement!=="1")?(d(),g("img",{key:0,class:"w-full h-full select-none shadow-sm rounded-lg object-cover border border-gray-100 dark:border-gray-700",src:a.value,alt:"微信登录二维码"},null,8,Lc)):(d(),g("div",Mc)),c.value?(d(),g("div",Ec,E[1]||(E[1]=[r("div",{class:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-600 dark:border-primary-400"},null,-1)]))):I("",!0)]),E[3]||(E[3]=r("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-2"},"请使用微信扫描二维码登录",-1)),x.value.isAutoOpenAgreement==="1"?(d(),g("div",Rc,[Xe(r("input",{"onUpdate:modelValue":E[0]||(E[0]=D=>p.value=D),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600 dark:border-gray-700 dark:bg-gray-800"},null,512),[[zs,p.value]]),r("p",{class:"ml-2 text-sm text-gray-600 dark:text-gray-400"},[E[2]||(E[2]=Ne(" 扫码登录即代表同意 ")),r("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300",onClick:h},"《用户协议及隐私协议》")])])):I("",!0)]),E[4]||(E[4]=r("div",{class:"h-6"},null,-1))],2))}}),Dc={key:0,class:"fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-50"},jc={class:"flex-1 flex flex-col items-center justify-center"},Pc={class:"tab-group tab-group-default dark:bg-gray-800"},$c={class:"w-full flex-1 flex flex-col overflow-hidden"},Uc=je({__name:"Login",props:{visible:{type:Boolean}},setup(e){const t=ft(),{isMobile:n}=it(),s=S("wechat"),a=k(()=>Number(t.globalConfig.emailLoginStatus)===1),o=k(()=>Number(t.globalConfig.wechatRegisterStatus)===1),i=k(()=>Number(t.globalConfig.phoneLoginStatus)===1);Ge(()=>{c()});function c(){o.value?s.value="wechat":a.value||i.value?s.value="captcha":s.value="password"}function l(u){s.value=u}return(u,f)=>u.visible?(d(),g("div",Dc,[r("div",{class:ne(["bg-white py-12 rounded-xl shadow-lg w-full h-[32rem] flex flex-col dark:bg-gray-900 dark:text-gray-300 relative",{"w-[98vw] px-4":y(n),"max-w-xl px-8":!y(n)}])},[r("button",{onClick:f[0]||(f[0]=p=>y(t).setLoginDialog(!1)),class:"btn-icon btn-sm absolute top-4 right-4 z-30 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"},[X(y(mn),{theme:"outline",size:"18"})]),r("div",jc,[r("div",{class:ne(["w-full flex justify-center mb-10",{"px-5":y(n),"px-10":!y(n)}])},[r("div",Pc,[o.value?(d(),g("button",{key:0,onClick:f[1]||(f[1]=p=>l("wechat")),class:ne(["tab tab-lg",{"tab-active":s.value==="wechat","px-0":y(n)}])}," 微信登录 ",2)):I("",!0),a.value||i.value?(d(),g("button",{key:1,onClick:f[2]||(f[2]=p=>l("captcha")),class:ne(["tab tab-lg",{"tab-active":s.value==="captcha","px-0":y(n)}])}," 验证码登录 ",2)):I("",!0),r("button",{onClick:f[3]||(f[3]=p=>l("password")),class:ne(["tab tab-lg",{"tab-active":s.value==="password","px-0":y(n)}])}," 密码登录 ",2)])],2),r("div",$c,[s.value==="wechat"?(d(),pe(zc,{key:0,onChangeLoginType:l})):(d(),pe(Sc,{key:1,"login-mode":s.value==="password"?"password":"captcha",onChangeLoginType:l},null,8,["login-mode"]))])])],2)])):I("",!0)}}),Nc={key:0,class:"fixed inset-0 z-[9999]"},Fc={class:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"},Gc={class:"p-4 border-b border-neutral-200 dark:border-neutral-700"},Bc={class:"text-lg font-medium text-neutral-900 dark:text-neutral-100"},Oc={class:"p-4 text-neutral-700 dark:text-neutral-300"},Wc={class:"flex justify-end gap-2 px-4 py-3"},Hc=je({__name:"Confirm",setup(e,{expose:t}){const n=S(!1),s=S(null),a=S({title:"",content:"",positiveText:"确认",negativeText:"取消"}),o=l=>(a.value=me(me({},a.value),l),n.value=!0,new Promise(u=>{s.value=u})),i=()=>H(this,null,function*(){try{a.value.onPositiveClick&&(yield a.value.onPositiveClick()),n.value=!1,s.value(!0)}catch(l){s.value(!1)}}),c=()=>{n.value=!1,s.value(!1)};return t({showDialog:o}),(l,u)=>(d(),pe(Fr,{to:"body",disabled:!n.value},[X(Hn,{"enter-active-class":"transition duration-300 ease-out","enter-from-class":"transform opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition duration-200 ease-in","leave-from-class":"opacity-100","leave-to-class":"transform opacity-0"},{default:Ct(()=>[n.value?(d(),g("div",Nc,[r("div",{class:"absolute inset-0 bg-black/50",onClick:c}),r("div",Fc,[r("div",{class:"w-[400px] bg-white dark:bg-[#24272e] rounded-lg shadow-lg overflow-hidden",onClick:u[0]||(u[0]=sn(()=>{},["stop"]))},[r("div",Gc,[r("h3",Bc,R(a.value.title),1)]),r("div",Oc,R(a.value.content),1),r("div",Wc,[r("button",{class:"px-4 py-2 text-sm rounded-md border border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-300 transition-colors",onClick:c},R(a.value.negativeText),1),r("button",{class:"px-4 py-2 text-sm rounded-md bg-primary-500 hover:bg-primary-600 text-white transition-colors",onClick:i},R(a.value.positiveText),1)])])])])):I("",!0)]),_:1})],8,["disabled"]))}});function ya(){var s;const e=document.createElement("div"),t=X(Hc);mo(t,e),document.body.appendChild(e);const n=(s=t.component)==null?void 0:s.exposed;return{warning:a=>H(this,null,function*(){try{return(yield n.showDialog(a))?Promise.resolve():Promise.reject()}finally{mo(null,e),e.remove()}})}}const Vc=""+new URL("../images/avatar-e985e5ec.png",import.meta.url).href,Yc={class:"w-full"},Zc={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4 flex flex-col space-y-4"},Xc={class:"flex items-center"},Qc={class:"avatar avatar-lg avatar-bordered avatar-primary"},qc=["src"],Jc={class:"flex items-center"},Kc={key:0,class:"flex items-center"},eu={class:"text-gray-900 dark:text-gray-200"},tu={class:"group relative ml-2"},nu={key:1,class:"flex items-center space-x-2"},su={key:0,class:"flex items-center"},au={class:"text-gray-900 dark:text-gray-200"},ou={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4 flex flex-col space-y-4"},iu={class:"grid grid-cols-1 gap-4"},ru={key:0,class:"flex items-center justify-between"},lu={class:"flex items-center"},cu={class:"text-gray-900 dark:text-gray-200 flex items-center"},uu={class:"text-xs text-gray-500"},du={key:1,class:"text-green-600 text-sm"},fu={key:1,class:"flex items-center justify-between"},pu={class:"flex items-center"},gu={class:"text-gray-900 dark:text-gray-200 flex items-center"},hu={class:"text-xs text-gray-500"},mu=["disabled"],vu={key:2,class:"flex items-center justify-between"},yu={class:"flex items-center"},bu={class:"text-gray-900 dark:text-gray-200 flex items-center"},wu={class:"text-xs text-gray-500"},xu={key:1,class:"text-green-600 text-sm"},ku={class:"flex items-center justify-between"},Au={class:"flex items-center"},Cu={class:"text-gray-900 dark:text-gray-200 flex items-center"},Iu={key:1,class:"max-h-[70vh] overflow-y-auto custom-scrollbar p-2"},Su={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4"},_u={class:"flex items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700"},Tu={class:"flex flex-col space-y-1"},Lu={key:0,class:"text-xs text-red-500 mt-1"},Mu={class:"flex flex-col space-y-1"},Eu={key:0,class:"text-xs text-red-500 mt-1"},Ru={class:"pt-2"},zu=["disabled"],Du={key:0},ju={key:1},Pu={key:2,class:"max-h-[70vh] overflow-y-auto custom-scrollbar p-2"},$u={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4"},Uu={class:"flex items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700"},Nu={class:"space-y-4"},Fu={class:"flex space-x-2"},Gu={class:"flex-1"},Bu={class:"flex items-end"},Ou=["disabled"],Wu={key:0,class:"flex items-center"},Hu={class:"ml-1 text-center text-sm text-gray-500 dark:text-gray-400"},Vu={key:3,class:"max-h-[70vh] overflow-y-auto custom-scrollbar p-2"},Yu={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4"},Zu={class:"flex items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700"},Xu={class:"space-y-4"},Qu={key:0,class:"flex items-center"},qu={class:"ml-1 text-center text-sm text-gray-500 dark:text-gray-400"},Ju={key:4,class:"max-h-[70vh] overflow-y-auto custom-scrollbar p-2"},Ku={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4"},ed={class:"flex items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700"},td={key:0,class:"text-center py-6"},nd={key:1,class:"px-4 pt-2 pb-6"},sd={class:"text-center my-6"},ad={class:"text-gray-700 dark:text-gray-300"},od={class:"text-red-500 font-medium"},id={class:"flex justify-center my-8"},rd={class:"relative w-[200px] h-[200px]"},ld=["src"],cd={key:1,class:"w-full h-full rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse"},ud={key:2,class:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"},dd=je({__name:"AccountManagement",props:{visible:{type:Boolean}},setup(e){const t=e,n=ft(),s=S(!1),a=zt(),o=S(!1),{isMobile:i}=it(),c=k(()=>n.userInfo||{}),l=k(()=>Number(n.globalConfig.emailLoginStatus)===1),u=k(()=>Number(n.globalConfig.wechatRegisterStatus)===1),f=k(()=>Number(n.globalConfig.phoneLoginStatus)===1),p=k(()=>Number(n.globalConfig.openIdentity)===1);k(()=>Number(n.globalConfig.oldWechatMigrationStatus)===1);const b=k(()=>n.isLogin),h=vo({newPassword:"",confirmPassword:""}),x=S(""),w=S(""),v=S(!1);function C(){const{newPassword:N,confirmPassword:U}=h;if(x.value="",w.value="",!N){x.value="请输入新密码";return}if(N.length<8||N.length>20){x.value="密码长度必须在8-20个字符之间";return}if(!/[a-zA-Z]/.test(N)){x.value="密码必须包含字母";return}if(!/\d/.test(N)){x.value="密码必须包含数字";return}if(!U){w.value="请再次输入新密码";return}if(N!==U){w.value="两次输入的密码不一致";return}A()}function A(){return H(this,null,function*(){try{s.value=!0,v.value=!0;const N=yield Rr({password:h.newPassword});s.value=!1,v.value=!1,N.success?(a.success("密码修改成功"),h.newPassword="",h.confirmPassword="",he()):a.error(N.message||"密码修改失败")}catch(N){s.value=!1,v.value=!1,a.error("密码修改失败")}})}function L(){h.newPassword="",h.confirmPassword="",x.value="",w.value=""}function $(){return b.value?!0:(a.warning("请登录后使用账户管理"),z.updateSettingsDialog(!1),n.setLoginDialog(!0),!1)}function E(){return H(this,null,function*(){if($())try{s.value=!0;const N=new Promise((U,ye)=>setTimeout(()=>ye(new Error("获取用户信息超时")),1e4));yield Promise.race([n.getUserInfo(),N]),s.value=!1}catch(N){s.value=!1,zn()}})}const D=k(()=>c.value.avatar||Vc),O=k(()=>c.value.email||""),T=k(()=>c.value.nickname||""),Y=k(()=>c.value.phone||""),P=k(()=>c.value.realName||""),se=k(()=>!O.value||!l.value?!1:!["@aiweb.com","@cooper.com","@default.com"].some(U=>O.value.endsWith(U))),G=S(!1),F=S("");function J(){F.value=T.value,G.value=!0}function V(){G.value=!1}function te(){return H(this,null,function*(){try{s.value=!0;const N=yield Sr({nickname:F.value});if(s.value=!1,!N.success){a.error(N.message||"更新失败");return}a.success("个人信息更新成功"),G.value=!1,yield n.getUserInfo()}catch(N){s.value=!1,a.error("更新失败")}})}const j=S(!1),z=It(),Z=k(()=>n.globalConfig),ee=S(!1),Q=S(!1),le=S({name:"",idCard:""}),Ae=S({phone:"",code:""}),ue=S(!0),we=S(!1),Re=S(0),xt=S(null),rt=S(null),nt=S(60),lt=S(!1),Ve=S(!1),ct=S(""),Be=S(""),Dt=S(!1),ae=S(null),_=k(()=>c.value&&Number(c.value.isBindWx)===1),W=S("main");function re(){W.value="phone"}function ge(){W.value="identity"}function q(){Sn(),W.value="wx",W.value==="wx"&&!_.value&&In()}function he(){W.value==="wx"&&Sn(),W.value="main"}function ve(){W.value="password"}function Ce(){ue.value=!0,z.updateSettingsDialog(!0,hn.AGREEMENT)}function $e(){if(ue.value===!1&&Z.value.isAutoOpenAgreement==="1")return a.error(`请阅读并同意《${Z.value.agreementTitle}》`);ee.value=!0}function Qe(){ee.value=!1,_r(le.value).then(N=>{N.code===200?(a.success("身份认证成功"),E()):a.error(N.message||"认证失败")})}function Ye(){return H(this,null,function*(){if(!Ae.value.phone)return a.error("请输入手机号");if(!(Re.value>0))try{we.value=!0;const N={phone:Ae.value.phone},U=yield Tr(N);U.success?(a.success(U.data||"验证码已发送"),Re.value=60,Fe()):a.error(U.message||"发送验证码失败")}catch(N){a.error("发送验证码失败，请稍后重试")}finally{we.value=!1}})}const Fe=()=>{lt.value||(lt.value=!0,nt.value=60,rt.value=window.setInterval(()=>{nt.value--,nt.value<=0&&(clearInterval(rt.value),rt.value=null,lt.value=!1)},1e3))};function pt(){if(ue.value===!1&&Z.value.isAutoOpenAgreement==="1")return a.error(`请阅读并同意《${Z.value.agreementTitle}》`);if(!Ae.value.phone||!Ae.value.code)return a.error("请填写完整信息");Q.value=!0}function Nt(){return H(this,null,function*(){Q.value=!1;try{s.value=!0;const N=yield Lr({phone:Ae.value.phone,code:Ae.value.code,username:"",password:""});N.success?(a.success("手机认证成功"),Ae.value.phone="",Ae.value.code="",yield E(),he()):a.error(N.message||"手机认证失败")}catch(N){a.error("手机认证失败，请稍后重试")}finally{s.value=!1}})}function In(){return H(this,null,function*(){try{const N=yield Mr();N.success&&(Be.value=N.data,Vn(),Fe())}catch(N){}})}function Vn(){return H(this,null,function*(){try{const N=yield ai({sceneStr:Be.value});N.success&&(Ve.value=!0,ct.value=N.data,ae.value!==null&&clearInterval(ae.value),ae.value=window.setInterval(()=>{ws()},1e3))}catch(N){}})}function ws(){return H(this,null,function*(){if(Be.value)try{const N=yield Er({sceneStr:Be.value});if(N.data){ae.value!==null&&(clearInterval(ae.value),ae.value=null);const{status:U,msg:ye}=N.data;U&&(a.success(ye),yield n.getUserInfo(),Dt.value=!1)}}catch(N){}})}function Sn(){ae.value!==null&&(clearInterval(ae.value),ae.value=null),ct.value="",Be.value="",Ve.value=!1}function xs(){return H(this,null,function*(){try{a.warning("暂不支持解绑微信，请联系管理员")}catch(N){}})}const Yn=S(null);function qt(){Yn.value!==null&&(clearInterval(Yn.value),Yn.value=null)}ss(()=>{s.value=!1,xt.value!==null&&(clearInterval(xt.value),xt.value=null),L(),ee.value=!1,Q.value=!1,G.value=!1,j.value=!1,o.value=!1,Sn(),qt()});function zn(){G.value=!1,j.value=!1,o.value=!1,Dt.value=!1,W.value="main"}ke(()=>t.visible,N=>{N?$()&&E():(zn(),s.value=!1,Sn())}),ke(b,N=>{t.visible&&!N&&(a.warning("账户已登出，请重新登录后查看"),z.updateSettingsDialog(!1),n.setLoginDialog(!0))}),Ge(()=>{$()&&E()});function vn(N){return!N||N.length<7?N:N.replace(/(\d{3})\d*(\d{4})/,"$1****$2")}const Zn=vo({status:k(()=>Y.value?"bound":"unbound"),phone:k(()=>vn(Y.value))}),B=k(()=>P.value?"verified":"unverified");return(N,U)=>(d(),g(Pe,null,[r("div",Yc,[W.value==="main"?(d(),g("div",{key:0,class:ne(["overflow-y-auto custom-scrollbar p-1",{"max-h-[70vh]":!y(i)}])},[r("div",Zc,[U[15]||(U[15]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 pb-2 border-b border-gray-200 dark:border-gray-700"}," 个人信息 ",-1)),r("div",Xc,[U[11]||(U[11]=r("div",{class:"w-20 text-gray-500 dark:text-gray-400"},"头像",-1)),r("div",Qc,[D.value?(d(),g("img",{key:0,src:D.value,class:"w-full h-full object-cover",alt:"用户头像"},null,8,qc)):I("",!0),D.value?I("",!0):(d(),pe(y(fi),{key:1,theme:"outline",size:"20",class:"text-white","aria-hidden":"true"}))])]),r("div",Jc,[U[13]||(U[13]=r("div",{class:"w-20 text-gray-500 dark:text-gray-400"},"昵称",-1)),G.value?(d(),g("div",nu,[Xe(r("input",{"onUpdate:modelValue":U[0]||(U[0]=ye=>F.value=ye),class:"input input-md w-full",placeholder:"请输入昵称"},null,512),[[At,F.value]]),r("button",{onClick:te,class:"btn btn-primary btn-md"},"保存"),r("button",{onClick:V,class:"btn btn-secondary btn-md"},"取消")])):(d(),g("div",Kc,[r("div",eu,R(T.value||"未设置"),1),r("div",tu,[r("button",{onClick:J,class:"btn-icon btn-sm","aria-label":"编辑昵称"},[X(y(qa),{theme:"outline",size:"16",class:"text-gray-500 hover:text-primary-600"})]),U[12]||(U[12]=r("div",{class:"tooltip tooltip-right"},"编辑",-1))])]))]),se.value?(d(),g("div",su,[U[14]||(U[14]=r("div",{class:"w-20 text-gray-500 dark:text-gray-400"},"邮箱",-1)),r("div",au,R(O.value),1)])):I("",!0)]),r("div",ou,[U[21]||(U[21]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 pb-2 border-b border-gray-200 dark:border-gray-700"}," 账户安全 ",-1)),r("div",iu,[f.value?(d(),g("div",ru,[r("div",lu,[r("div",null,[r("div",cu,[U[16]||(U[16]=Ne(" 手机绑定 ")),X(y(Vr),{theme:"outline",size:"16",class:"text-gray-500 ml-1"})]),r("div",uu,R(Zn.status==="bound"?"已绑定":"未绑定")+" "+R(Zn.phone||""),1)])]),Zn.status!=="bound"?(d(),g("button",{key:0,onClick:re,class:"btn btn-secondary btn-sm"}," 绑定 ")):(d(),g("span",du,"已绑定"))])):I("",!0),u.value?(d(),g("div",fu,[r("div",pu,[r("div",null,[r("div",gu,[U[17]||(U[17]=Ne(" 微信绑定 ")),X(y(Yr),{theme:"outline",size:"16",class:"text-gray-500 ml-1"})]),r("div",hu,R(_.value?"已绑定微信":"未绑定微信"),1)])]),r("button",{onClick:q,class:"btn btn-secondary btn-sm",disabled:_.value},R(_.value?"已绑定":"绑定"),9,mu)])):I("",!0),p.value?(d(),g("div",vu,[r("div",yu,[r("div",null,[r("div",bu,[U[18]||(U[18]=Ne(" 实名认证 ")),X(y(Zr),{theme:"outline",size:"16",class:"text-gray-500 ml-1"})]),r("div",wu,R(B.value==="verified"?"已认证":"未认证"),1)])]),B.value!=="verified"?(d(),g("button",{key:0,onClick:ge,class:"btn btn-secondary btn-sm"}," 认证 ")):(d(),g("span",xu,"已完成"))])):I("",!0),r("div",ku,[r("div",Au,[r("div",null,[r("div",Cu,[U[19]||(U[19]=Ne(" 账户密码 ")),X(y(Xr),{theme:"outline",size:"16",class:"text-gray-500 ml-1"})]),U[20]||(U[20]=r("div",{class:"text-xs text-gray-500"},"定期修改密码可以保护账户安全",-1))])]),r("button",{onClick:ve,class:"btn btn-secondary btn-sm"},"修改")])])])],2)):W.value==="password"?(d(),g("div",Iu,[r("div",Su,[r("div",_u,[r("button",{onClick:he,class:"text-gray-500 hover:text-gray-700 mr-2"},[X(y(us),{size:"18"})]),U[22]||(U[22]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100"},"修改密码",-1))]),r("form",{onSubmit:sn(C,["prevent"]),class:"flex flex-col space-y-4"},[r("div",Tu,[U[23]||(U[23]=r("label",{for:"new-password",class:"text-sm text-gray-700 dark:text-gray-300"},"新密码",-1)),Xe(r("input",{id:"new-password","onUpdate:modelValue":U[1]||(U[1]=ye=>h.newPassword=ye),type:"password",placeholder:"请输入新密码",class:"input input-md w-full",required:""},null,512),[[At,h.newPassword]]),U[24]||(U[24]=r("div",{class:"text-xs text-gray-500"},"密码长度8-20个字符，必须包含字母和数字",-1)),x.value?(d(),g("div",Lu,R(x.value),1)):I("",!0)]),r("div",Mu,[U[25]||(U[25]=r("label",{for:"confirm-password",class:"text-sm text-gray-700 dark:text-gray-300"},"确认新密码",-1)),Xe(r("input",{id:"confirm-password","onUpdate:modelValue":U[2]||(U[2]=ye=>h.confirmPassword=ye),type:"password",placeholder:"请再次输入新密码",class:"input input-md w-full",required:""},null,512),[[At,h.confirmPassword]]),w.value?(d(),g("div",Eu,R(w.value),1)):I("",!0)]),r("div",Ru,[r("button",{type:"submit",class:"btn btn-primary btn-md w-full",disabled:v.value},[v.value?(d(),g("span",Du,"提交中...")):(d(),g("span",ju,"确认修改"))],8,zu)])],32)])])):W.value==="phone"?(d(),g("div",Pu,[r("div",$u,[r("div",Uu,[r("button",{onClick:he,class:"text-gray-500 hover:text-gray-700 mr-2"},[X(y(us),{size:"18"})]),U[26]||(U[26]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100"},"绑定手机号",-1))]),r("div",Nu,[r("div",null,[U[27]||(U[27]=r("label",{class:"block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"}," 手机号码 ",-1)),Xe(r("input",{"onUpdate:modelValue":U[3]||(U[3]=ye=>Ae.value.phone=ye),class:"input input-md w-full",placeholder:"请输入手机号码"},null,512),[[At,Ae.value.phone]])]),r("div",Fu,[r("div",Gu,[U[28]||(U[28]=r("label",{class:"block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"}," 验证码 ",-1)),Xe(r("input",{"onUpdate:modelValue":U[4]||(U[4]=ye=>Ae.value.code=ye),class:"input input-md w-full",placeholder:"请输入验证码"},null,512),[[At,Ae.value.code]])]),r("div",Bu,[r("button",{onClick:Ye,class:"btn btn-secondary btn-sm w-full",disabled:we.value||Re.value>0},R(Re.value>0?`${Re.value}s后重新获取`:"获取验证码"),9,Ou)])]),Z.value.isAutoOpenAgreement==="1"?(d(),g("div",Wu,[Xe(r("input",{"onUpdate:modelValue":U[5]||(U[5]=ye=>ue.value=ye),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600"},null,512),[[zs,ue.value]]),r("p",Hu,[U[29]||(U[29]=Ne(" 已阅读并同意 ")),r("a",{href:"#",class:"font-semibold leading-6 text-primary-600 hover:text-primary-500 dark:text-primary-500 dark:hover:text-primary-600",onClick:Ce},"《"+R(Z.value.agreementTitle)+"》",1)])])):I("",!0),r("div",null,[r("button",{onClick:pt,class:"btn btn-primary btn-md w-full"}," 提交认证 ")])])])])):W.value==="identity"?(d(),g("div",Vu,[r("div",Yu,[r("div",Zu,[r("button",{onClick:he,class:"text-gray-500 hover:text-gray-700 mr-2"},[X(y(us),{size:"18"})]),U[30]||(U[30]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100"},"实名认证",-1))]),r("div",Xu,[r("div",null,[U[31]||(U[31]=r("label",{class:"block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"}," 真实姓名 ",-1)),Xe(r("input",{"onUpdate:modelValue":U[6]||(U[6]=ye=>le.value.name=ye),class:"input input-md w-full",placeholder:"请输入真实姓名"},null,512),[[At,le.value.name]])]),r("div",null,[U[32]||(U[32]=r("label",{class:"block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300"}," 身份证号 ",-1)),Xe(r("input",{"onUpdate:modelValue":U[7]||(U[7]=ye=>le.value.idCard=ye),class:"input input-md w-full",placeholder:"请输入身份证号"},null,512),[[At,le.value.idCard]])]),Z.value.isAutoOpenAgreement==="1"?(d(),g("div",Qu,[Xe(r("input",{"onUpdate:modelValue":U[8]||(U[8]=ye=>ue.value=ye),type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600"},null,512),[[zs,ue.value]]),r("p",qu,[U[33]||(U[33]=Ne(" 已阅读并同意 ")),r("a",{href:"#",class:"font-semibold leading-6 text-primary-600 hover:text-primary-500 dark:text-primary-500 dark:hover:text-primary-600",onClick:Ce},"《"+R(Z.value.agreementTitle)+"》",1)])])):I("",!0),r("div",null,[r("button",{onClick:$e,class:"btn btn-primary btn-md w-full"}," 提交认证 ")])])])])):W.value==="wx"?(d(),g("div",Ju,[r("div",Ku,[r("div",ed,[r("button",{onClick:he,class:"text-gray-500 hover:text-gray-700 mr-2"},[X(y(us),{size:"18"})]),U[34]||(U[34]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100"},"绑定微信",-1))]),_.value?(d(),g("div",td,[U[35]||(U[35]=r("div",{class:"text-green-500 text-lg mb-4"},"✓ 已成功绑定微信",-1)),r("button",{onClick:xs,class:"btn btn-secondary btn-sm"},"解除绑定")])):(d(),g("div",nd,[r("div",sd,[r("p",ad,[U[36]||(U[36]=Ne(" 请在 ")),r("span",od,R(nt.value>0?`00:${nt.value<10?"0"+nt.value:nt.value}`:"00:00"),1),U[37]||(U[37]=Ne(" 时间内完成绑定 "))])]),r("div",id,[r("div",rd,[ct.value&&(ue.value||Z.value.isAutoOpenAgreement!=="1")?(d(),g("img",{key:0,class:"w-full h-full select-none shadow-sm rounded-lg object-cover border border-gray-100 dark:border-gray-700",src:ct.value,alt:"微信绑定二维码"},null,8,ld)):(d(),g("div",cd)),ct.value?I("",!0):(d(),g("div",ud,U[38]||(U[38]=[r("div",{class:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-600 dark:border-primary-400"},null,-1)])))])]),U[39]||(U[39]=r("div",{class:"text-center text-gray-700 dark:text-gray-300 text-base"}," 打开微信扫码绑定账户 ",-1))]))])])):I("",!0)]),X(Da,{show:ee.value,onSuccess:Qe,onClose:U[9]||(U[9]=ye=>ee.value=!1)},null,8,["show"]),X(Da,{show:Q.value,onSuccess:Nt,onClose:U[10]||(U[10]=ye=>Q.value=!1)},null,8,["show"])],64))}});const ki=an(dd,[["__scopeId","data-v-dd92b63a"]]);function fd(e){return Os({url:"/crami/useCrami",data:e})}function pd(e){return ns({url:"/crami/queryAllPackage",data:e})}function ja(e){return Os({url:"/order/buy",data:e})}function gd(e){return ns({url:"/order/queryByOrderId",data:e})}function hd(){return Os({url:"/signin/sign"})}function md(){return ns({url:"/signin/signinLog"})}const xo=""+new URL("../images/alipay-eab94eb8.png",import.meta.url).href,ko=""+new URL("../images/wxpay-2d8f84af.png",import.meta.url).href;function vd(e,t){const n=Il(e),s=Ds("");return ke(n,a=>H(this,null,function*(){n.value&&Sl&&(s.value=yield _l.toDataURL(a,t))}),{immediate:!0}),s}const yd=["src"],bd=je({__name:"index",props:{value:{default:""},size:{default:160},color:{default:"#000"},backgroundColor:{default:"#FFF"},bordered:{type:Boolean,default:!0},borderColor:{default:"#0505050f"},scale:{default:8},errorLevel:{default:"H"}},setup(e){const t=e,n=vd(t.value,{errorCorrectionLevel:t.errorLevel,type:"image/png",quality:1,margin:3,scale:t.scale,color:{dark:t.color,light:t.backgroundColor}});return(s,a)=>(d(),g("div",{class:ne(["m-qrcode",{bordered:s.bordered}]),style:kt(`width: ${s.size}px; height: ${s.size}px; border-color: ${s.borderColor};`)},[r("img",{src:y(n),class:"u-qrcode",alt:"QRCode"},null,8,yd)],6))}});const _a=an(bd,[["__scopeId","data-v-d2539722"]]),wd={class:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4"},xd={class:"flex items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700"},kd={class:"text-base font-semibold text-gray-900 dark:text-gray-100"},Ad={class:"p-2"},Cd={class:"whitespace-nowrap font-bold"},Id={class:"ml-1 text-xl font-bold tracking-tight"},Sd={class:"mt-2 flex"},_d={class:"whitespace-nowrap font-bold"},Td={class:"ml-2"},Ld={class:"flex items-center justify-center my-3 relative"},Md={key:0,class:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"},Ed={key:1,class:"w-[240px] h-[240px] rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse"},Rd=["src"],zd=["src"],Dd={class:"mb-10 mt-5 text-base"},jd=["disabled"],Pd=["src"],$d=["src"],Ud={key:0,class:"flex items-center justify-center text-lg"},Nd={class:"inline-block w-16 text-primary-500 text-center"},Fd={class:"mt-6 space-y-6"},Gd=["id","value"],Bd=["for"],Od=["src"],Wd=1e3,Hd=je({__name:"MemberPayment",props:{visible:{type:Boolean}},emits:["back-to-main","payment-success"],setup(e,{emit:t}){const n=e,s=t,{isMobile:a}=it(),o=ft(),i=oi(),c=zt(),l=S(!0),u=S("wxpay"),f=k(()=>{var Q;const z=window.navigator.userAgent.toLowerCase(),Z=z.match(/MicroMessenger/i)&&((Q=z==null?void 0:z.match(/MicroMessenger/i))==null?void 0:Q[0])==="micromessenger",ee=!z.includes("windows")&&!z.includes("macintosh");return Z&&ee}),p=k(()=>{const{payHupiStatus:z,payEpayStatus:Z,payMpayStatus:ee,payWechatStatus:Q,payLtzfStatus:le,payDuluPayStatus:Ae}=o.globalConfig;return Number(Q)===1?"wechat":Number(Z)===1?"epay":Number(ee)===1?"mpay":Number(z)===1?"hupi":Number(le)===1?"ltzf":Number(Ae)===1?"dulu":null}),b=k(()=>{const{payEpayChannel:z,payMpayChannel:Z,payDuluPayChannel:ee}=o.globalConfig;return p.value==="mpay"?Z?JSON.parse(Z):[]:p.value==="epay"?z?JSON.parse(z):[]:p.value==="wechat"?["wxpay"]:p.value==="hupi"?["wxpay"]:p.value==="ltzf"?["wxpay"]:p.value==="dulu"?ee?JSON.parse(ee):[]:[]}),h=k(()=>u.value==="wxpay"?oe("pay.wechat"):oe("pay.alipay")),x=S(null),w=S(60);S(!1);const v=k(()=>{const{payEpayApiPayUrl:z,payDuluPayRedirect:Z}=o.globalConfig;return p.value==="epay"&&z.includes("submit")||p.value==="mpay"||p.value==="dulu"&&Z==="1"});function C(){w.value=300,x.value||(x.value=setInterval(()=>{w.value--,w.value<=0&&A()},1e3))}function A(){x.value&&(clearInterval(x.value),x.value=null),l.value=!1,c.warning(oe("pay.paymentTimeExpired")),G()}ke(u,()=>{F(),x.value&&(clearInterval(x.value),x.value=null),C()});const L=S("");let $;const E=k(()=>[{label:oe("pay.wechatPay"),value:"wxpay",icon:ko,payChannel:"wxpay"},{label:oe("pay.alipayPay"),value:"alipay",icon:xo,payChannel:"alipay"}].filter(z=>b.value.includes(z.payChannel))),D=()=>H(this,null,function*(){if(!L.value)return;const z=yield gd({orderId:L.value}),{success:Z,data:ee}=z;if(Z){const{status:Q}=ee;Q===1&&(j(),c.success(oe("pay.paymentSuccess")),l.value=!1,o.getUserInfo(),s("payment-success"))}}),O=k(()=>i==null?void 0:i.orderInfo),T=S(""),Y=S(!0),P=S(!0),se=S("");function G(){V(),s("back-to-main")}function F(){return H(this,null,function*(){!v.value&&(Y.value=!0),v.value&&(P.value=!0);let z=null;z=u.value,p.value==="wechat"&&(z=f.value?"jsapi":"native");try{const Z=yield ja({goodsId:O.value.pkgInfo.id,payType:z}),{data:ee,success:Q}=Z;if(!Q)return;const{url_qrcode:le,orderId:Ae,redirectUrl:ue}=ee;se.value=ue,L.value=Ae,T.value=le,Y.value=!1,P.value=!1}catch(Z){G(),Y.value=!1,P.value=!1}})}function J(){window.open(se.value)}function V(){j(),x.value&&(clearInterval(x.value),x.value=null),T.value="",L.value="",l.value=!1}function te(){return H(this,null,function*(){yield F(),$||($=setInterval(()=>{D()},Wd)),C()})}function j(){$&&(clearInterval($),$=null)}return ke(()=>n.visible,(z,Z)=>{z&&!Z?(l.value=!0,te()):!z&&Z&&V()}),Ge(()=>{n.visible&&te()}),ss(()=>{V()}),(z,Z)=>{var ee,Q;return d(),g("div",{class:ne(["overflow-y-auto custom-scrollbar p-2",{"max-h-[70vh]":!y(a)}])},[r("div",wd,[r("div",xd,[r("button",{onClick:G,class:"btn-icon btn-md mr-2"},[X(y(us),{size:"18"})]),r("div",kd,R(y(oe)("pay.productPayment")),1)]),r("div",Ad,[r("div",null,[r("span",Cd,R(y(oe)("pay.amountDue")),1),r("span",Id,R(`￥${(ee=O.value.pkgInfo)==null?void 0:ee.price}`),1)]),r("div",Sd,[r("span",_d,R(y(oe)("pay.packageName")),1),r("span",Td,R((Q=O.value.pkgInfo)==null?void 0:Q.name),1)]),r("div",{class:ne(["flex justify-center",[y(a)?"flex-col":"flex-row",v.value?"flex-row-reverse":""]])},[r("div",null,[r("div",Ld,[Y.value&&!v.value?(d(),g("div",Md,Z[1]||(Z[1]=[r("div",{class:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-600 dark:border-primary-400"},null,-1)]))):I("",!0),Y.value?(d(),g("div",Ed)):I("",!0),p.value==="epay"&&!Y.value&&!P.value&&!v.value?(d(),pe(_a,{key:2,value:T.value,size:240},null,8,["value"])):I("",!0),p.value==="dulu"&&!Y.value&&!P.value&&!v.value?(d(),pe(_a,{key:3,value:T.value,size:240},null,8,["value"])):I("",!0),u.value==="wxpay"&&!Y.value&&!v.value?(d(),g("img",{key:4,src:y(ko),class:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 bg-[#fff]"},null,8,Rd)):I("",!0),u.value==="alipay"&&!Y.value&&!v.value?(d(),g("img",{key:5,src:y(xo),class:"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 bg-[#fff]"},null,8,zd)):I("",!0),p.value==="wechat"&&!Y.value?(d(),pe(_a,{key:6,value:T.value,size:240},null,8,["value"])):I("",!0),v.value?(d(),g("div",{key:7,class:ne(["flex flex-col",[v.value&&y(a)?"ml-0":"ml-20"]])},[r("span",Dd,R(y(oe)("pay.siteAdminEnabledRedirect")),1),v.value?(d(),g("button",{key:0,type:"button",class:"btn btn-primary btn-md",disabled:P.value,onClick:J},R(y(oe)("pay.clickToPay")),9,jd)):I("",!0)],2)):I("",!0),p.value==="hupi"&&!P.value?(d(),g("iframe",{key:8,class:"w-[280px] h-[280px] scale-90",src:T.value,frameborder:"0"},null,8,Pd)):I("",!0),p.value==="ltzf"&&!P.value?(d(),g("img",{key:9,src:T.value,class:"w-[280px] h-[280px] scale-90",alt:"QRCode"},null,8,$d)):I("",!0)]),v.value?I("",!0):(d(),g("span",Ud,R(y(oe)("pay.open"))+" "+R(h.value)+" "+R(y(oe)("pay.scanToPay")),1))]),r("div",{class:ne(["flex flex-col",[y(a)?"w-full ":" ml-10 w-[200] "]])},[r("div",{class:ne(["flex items-center justify-center mt-6 w-full font-bold text-sm",[y(a)?"mb-2":"mb-10"]]),style:{"white-space":"nowrap"}},[r("span",null,R(y(oe)("pay.completePaymentWithin")),1),r("span",Nd,R(w.value)+"秒 ",1),r("span",null,R(y(oe)("pay.timeToCompletePayment")),1)],2),r("div",Fd,[(d(!0),g(Pe,null,He(E.value,le=>(d(),g("div",{key:le.value,class:"flex items-center"},[Xe(r("input",{type:"radio",id:le.value,name:"payment-method",value:le.value,"onUpdate:modelValue":Z[0]||(Z[0]=Ae=>u.value=Ae),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"},null,8,Gd),[[Gr,u.value]]),r("label",{for:le.value,class:"ml-3 block text-sm font-medium leading-6 text-gray-900 dark:text-gray-300"},[r("img",{class:"h-4 object-contain mr-2 inline-block",src:le.icon,alt:""},null,8,Od),Ne(" "+R(le.label),1)],8,Bd)]))),128))])],2)],2)])])],2)}}});const Vd=an(Hd,[["__scopeId","data-v-9057daaf"]]),Yd={key:0},Zd={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4 flex flex-col space-y-4"},Xd={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Qd=["onClick"],qd={class:"relative"},Jd={class:"text-lg font-semibold leading-8 dark:text-white"},Kd={key:0,class:"flex justify-between items-end mt-4"},ef={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},tf={class:"font-bold dark:text-white"},nf={key:1,class:"flex justify-between items-end mt-2"},sf={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},af={class:"font-bold dark:text-white"},of={key:2,class:"flex justify-between items-end mt-2"},rf={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},lf={class:"font-bold dark:text-white"},cf={class:"mt-4 flex items-baseline gap-x-1"},uf={class:"text-3xl font-bold tracking-tight text-gray-900 dark:text-white"},df={class:"mt-6"},ff=["onClick"],pf={key:3,class:"mt-4 space-y-2 text-sm leading-6 text-gray-600 dark:text-gray-400"},gf={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},hf={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col space-y-4 h-full"},mf={class:"bg-gray-50 mb-4 p-3 rounded-lg border border-gray-200 dark:border-gray-700 dark:bg-gray-700"},vf={key:0},yf={class:"mx-2 text-primary-500"},bf={class:"dark:text-gray-300"},wf={key:1},xf={class:"mx-2 text-primary-500"},kf={class:"dark:text-gray-300"},Af={key:2},Cf={class:"mx-2 text-primary-500"},If={class:"dark:text-gray-300"},Sf={class:"dark:text-gray-300"},_f={class:"text-red-500 mx-1"},Tf={class:"flex-grow"},Lf={class:"mt-2 grid grid-cols-7 text-sm"},Mf=["datetime"],Ef={class:"mt-4 pt-2 border-t border-gray-200 dark:border-gray-700"},Rf=["disabled"],zf={key:0},Df={key:1},jf={key:2},Pf={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 flex flex-col space-y-4 h-full"},$f={class:"space-y-3"},Uf={key:0,class:"flex items-center p-2 border border-gray-200 dark:border-gray-700 rounded-lg"},Nf={class:"text-gray-500 dark:text-gray-400 w-28"},Ff={class:"text-lg font-bold text-gray-900 dark:text-gray-100"},Gf={key:0,class:"text-sm text-gray-500 dark:text-gray-400 ml-1"},Bf={key:1,class:"flex items-center p-2 border border-gray-200 dark:border-gray-700 rounded-lg"},Of={class:"text-gray-500 dark:text-gray-400 w-28"},Wf={class:"text-lg font-bold text-gray-900 dark:text-gray-100"},Hf={key:0,class:"text-sm text-gray-500 dark:text-gray-400 ml-1"},Vf={key:2,class:"flex items-center p-2 border border-gray-200 dark:border-gray-700 rounded-lg"},Yf={class:"text-gray-500 dark:text-gray-400 w-28"},Zf={class:"text-lg font-bold text-gray-900 dark:text-gray-100"},Xf={key:0,class:"text-sm text-gray-500 dark:text-gray-400 ml-1"},Qf={class:"flex items-center p-2 border border-gray-200 dark:border-gray-700 rounded-lg"},qf={key:0,class:"flex-grow mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},Jf={class:"flex items-center space-x-2"},Kf=["placeholder"],ep=["disabled"],tp=je({__name:"MemberCenter",props:{visible:{type:Boolean}},setup(e){const t=e,n=ft(),s=It(),a=S(!0),o=S([]),i=zt(),c=S(!1),l=k(()=>n.globalConfig.model3Name||oe("goods.basicModelQuota")),{isMobile:u}=it(),f=k(()=>n.globalConfig.model4Name||oe("goods.advancedModelQuota")),p=k(()=>n.globalConfig.drawMjName||oe("goods.drawingQuota")),b=k(()=>Number(n.globalConfig.isHideModel3Point)===1),h=k(()=>Number(n.globalConfig.isHideModel4Point)===1),x=k(()=>Number(n.globalConfig.isHideDrawMjPoint)===1),w=k(()=>{var _;const ae=window.navigator.userAgent.toLowerCase();return ae.match(/MicroMessenger/i)&&((_=ae==null?void 0:ae.match(/MicroMessenger/i))==null?void 0:_[0])==="micromessenger"}),v=k(()=>{const{payHupiStatus:ae,payEpayStatus:_,payMpayStatus:W,payWechatStatus:re,payLtzfStatus:ge,payDuluPayStatus:q}=n.globalConfig;return Number(re)===1?"wechat":Number(W)===1?"mpay":Number(ae)===1?"hupi":Number(_)===1?"epay":Number(ge)===1?"ltzf":Number(q)===1?"dulu":null}),C=k(()=>{const{payEpayChannel:ae,payMpayChannel:_}=n.globalConfig;return v.value==="mpay"?_?JSON.parse(_):[]:v.value==="epay"?ae?JSON.parse(ae):[]:v.value==="wechat"?["wxpay"]:v.value==="hupi"?["hupi"]:v.value==="dulu"?["dulu"]:v.value==="ltzf"?["wxpay"]:[]});Ge(()=>{t.visible&&Dt()&&(P(),w.value&&O())});const A=S("main"),L=S(null);function $(ae){L.value=ae,s.updateOrderInfo({pkgInfo:ae}),A.value="payment"}function E(){A.value="main",L.value=null}function D(){i.success(oe("goods.purchaseSuccess")),A.value="main",L.value=null,n.getUserInfo(),setTimeout(()=>{s.updateSettingsDialog(!1)},2e3)}ss(()=>{o.value=[],a.value=!0,A.value="main",L.value=null});function O(){return H(this,null,function*(){const ae=window.location.href.replace(/#.*$/,""),_=yield zr({url:ae}),{appId:W,nonceStr:re,timestamp:ge,signature:q}=_.data;W&&(wx.config({debug:!1,appId:W,timestamp:ge,nonceStr:re,signature:q,jsApiList:["chooseWXPay"]}),wx.ready(()=>{}),wx.error(()=>{}))})}function T(ae){const{appId:_,timeStamp:W,nonceStr:re,package:ge,signType:q,paySign:he}=ae;WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:_,timeStamp:W,nonceStr:re,package:ge,signType:q,paySign:he},ve=>{ve.err_msg==="get_brand_wxpay_request:ok"?(i.success(oe("goods.purchaseSuccess")),setTimeout(()=>{n.getUserInfo()},500)):i.success(oe("goods.paymentNotSuccessful"))})}function Y(ae){return H(this,null,function*(){if(c.value)return;function _(){const W=window.navigator.userAgent.toLowerCase(),re=W.indexOf("micromessenger")!==-1,ge=W.indexOf("windows")===-1&&W.indexOf("macintosh")===-1;return re&&ge}if(_()&&v.value==="wechat"&&Number(n.globalConfig.payWechatStatus)===1){if(typeof WeixinJSBridge=="undefined"){const W=()=>{(()=>H(this,null,function*(){const ge=yield ja({goodsId:ae.id,payType:"jsapi"}),{success:q,data:he}=ge;q&&T(he)}))()};document.addEventListener&&document.addEventListener("WeixinJSBridgeReady",W,!1)}else{const W=yield ja({goodsId:ae.id,payType:"jsapi"}),{success:re,data:ge}=W;re&&T(ge)}return}s.updateOrderInfo({pkgInfo:ae})})}function P(){return H(this,null,function*(){if(Dt()){a.value=!0;try{o.value=[],yield n.getUserInfo();const ae=yield pd({status:1,size:30});o.value=ae.data.rows,yield rt(),a.value=!1}catch(ae){a.value=!1}}})}const se=S(""),G=ae=>{se.value=ae.name,te.value=!1};function F(ae){if(!C.value.length){i.warning(oe("goods.paymentNotEnabled"));return}if(w.value&&v.value==="wechat"&&Number(n.globalConfig.payWechatStatus)===1){Y(ae);return}$(ae)}function J(ae){return ae.split(`
`)}const V=S(""),te=S(!1);function j(){return H(this,null,function*(){if(!V.value.trim()){i.info(oe("usercenter.pleaseEnterCardDetails"));return}try{a.value=!0,yield fd({code:V.value}),i.success(oe("usercenter.cardRedeemSuccess")),n.getUserInfo(),a.value=!1,V.value=""}catch(ae){a.value=!1,V.value=""}})}const z=S(!0);Br(),S(!1);const Z=S([]),ee=S(!1),Q=new Date().toISOString().split("T")[0],le=k(()=>Z.value.map(ae=>{var _;return mt(me({},ae),{day:(_=ae.signInDate.split("-").pop())==null?void 0:_.replace(/^0/,""),isToday:ae.signInDate===Q})})),Ae=k(()=>n.userInfo.consecutiveDays||0),ue=k(()=>{var ae;return Number((ae=n.globalConfig)==null?void 0:ae.signInModel3Count)||0}),we=k(()=>{var ae;return Number((ae=n.globalConfig)==null?void 0:ae.signInModel4Count)||0}),Re=k(()=>{var ae;return Number((ae=n.globalConfig)==null?void 0:ae.signInMjDrawToken)||0}),xt=k(()=>Z.value.some(ae=>ae.signInDate===Q&&ae.isSigned));function rt(){return H(this,null,function*(){try{const ae=yield md();ae.success&&(Z.value=ae.data||[])}catch(ae){}})}function nt(){return H(this,null,function*(){try{ee.value=!0,(yield hd()).success&&(i.success("签到成功！"),yield rt(),n.getUserInfo()),ee.value=!1}catch(ae){ee.value=!1}})}function lt(ae,_){return new Date(ae,_,1).getDay()}const Ve=k(()=>n.userBalance),ct=k(()=>Ve.value.isMember||!1),Be=k(()=>n.isLogin);function Dt(){return Be.value?!0:(i.warning("请先登录后使用会员中心"),s.updateSettingsDialog(!1),n.setLoginDialog(!0),!1)}return ke(Be,ae=>{t.visible&&!ae&&(i.warning("账户已登出，请重新登录后查看"),s.updateSettingsDialog(!1),n.setLoginDialog(!0))}),ke(()=>t.visible,ae=>{ae&&Dt()&&(P(),w.value&&O())}),(ae,_)=>{var W,re,ge;return d(),g("div",{class:ne(["overflow-y-auto custom-scrollbar p-1",{"max-h-[70vh]":!y(u)}])},[A.value==="main"?(d(),g("div",Yd,[r("div",Zd,[_[2]||(_[2]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 pb-2 border-b border-gray-200 dark:border-gray-700"}," 套餐列表 ",-1)),r("div",Xd,[(d(!0),g(Pe,null,He(o.value,(q,he)=>(d(),g("div",{key:he,class:ne([q.name==se.value?"ring-2 ring-primary-500 shadow-md":"ring-1 ring-gray-200 dark:ring-gray-700","rounded-lg p-6 hover:shadow-md bg-white dark:bg-gray-750"]),onClick:ve=>G(q)},[r("div",qd,[r("b",Jd,R(q.name),1)]),b.value?I("",!0):(d(),g("div",Kd,[r("span",ef,R(l.value),1),r("span",tf,R(q.model3Count>99999?"无限额度":q.model3Count),1)])),h.value?I("",!0):(d(),g("div",nf,[r("span",sf,R(f.value),1),r("span",af,R(q.model4Count>99999?"无限额度":q.model4Count),1)])),x.value?I("",!0):(d(),g("div",of,[r("span",rf,R(p.value),1),r("span",lf,R(q.drawMjCount>99999?"无限额度":q.drawMjCount),1)])),r("div",cf,[r("span",uf,R(`￥${q.price}`),1)]),r("div",df,[r("button",{onClick:sn(ve=>F(q),["stop"]),class:"btn btn-primary btn-md w-full"}," 购买套餐 ",8,ff)]),q.des?(d(),g("ul",pf,[(d(!0),g(Pe,null,He(J(q.des),(ve,Ce)=>(d(),g("li",{key:Ce,class:"flex gap-x-2"},[_[1]||(_[1]=r("svg",{class:"h-5 w-5 flex-none text-primary-600 dark:text-primary-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[r("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z","clip-rule":"evenodd"})],-1)),Ne(" "+R(ve),1)]))),128))])):I("",!0)],10,Qd))),128))])]),r("div",gf,[r("div",hf,[_[7]||(_[7]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 pb-2 border-b border-gray-200 dark:border-gray-700"}," 签到奖励 ",-1)),r("div",mf,[_[5]||(_[5]=r("span",{class:"dark:text-gray-300"},"签到赠送：",-1)),ue.value>0&&!b.value?(d(),g("span",vf,[r("b",yf,R(ue.value),1),r("span",bf,R(l.value),1)])):I("",!0),we.value>0&&!h.value?(d(),g("span",wf,[r("b",xf,R(we.value),1),r("span",kf,R(f.value),1)])):I("",!0),Re.value>0&&!x.value?(d(),g("span",Af,[r("b",Cf,R(Re.value),1),r("span",If,R(p.value),1)])):I("",!0),r("span",Sf,[_[3]||(_[3]=Ne("（已连续签到")),r("b",_f,R(Ae.value),1),_[4]||(_[4]=Ne("天）"))])]),r("div",Tf,[_[6]||(_[6]=r("div",{class:"grid grid-cols-7 text-center text-xs leading-6 text-gray-500 dark:text-gray-400"},[r("div",null,"日"),r("div",null,"一"),r("div",null,"二"),r("div",null,"三"),r("div",null,"四"),r("div",null,"五"),r("div",null,"六")],-1)),r("div",Lf,[(d(!0),g(Pe,null,He(lt(new Date().getFullYear(),new Date().getMonth()),q=>(d(),g("div",{key:"empty-"+q,class:"py-2"}))),128)),(d(!0),g(Pe,null,He(le.value,q=>(d(),g("div",{key:q.signInDate,class:"py-2"},[r("button",{type:"button",class:ne([q.isToday?"bg-primary-600 text-white":q.isSigned?"text-primary-600 dark:text-primary-400":"text-gray-900 dark:text-gray-100","hover:bg-gray-200 dark:hover:bg-gray-700 mx-auto flex h-8 w-8 items-center justify-center rounded-full"])},[r("time",{datetime:q.signInDate},R(q.day),9,Mf)],2)]))),128))])]),r("div",Ef,[r("button",{onClick:nt,disabled:xt.value||ee.value,class:"btn btn-primary btn-md w-full"},[ee.value?(d(),g("span",zf,"签到中...")):xt.value?(d(),g("span",Df,"已签到")):(d(),g("span",jf,"签到"))],8,Rf)])]),r("div",Pf,[_[10]||(_[10]=r("div",{class:"text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 pb-2 border-b border-gray-200 dark:border-gray-700"}," 额度信息 ",-1)),r("div",$f,[b.value?I("",!0):(d(),g("div",Uf,[r("div",Nf,R(l.value),1),r("div",Ff,[Ne(R(Ve.value.sumModel3Count>999999?"无限额度":(W=Ve.value.sumModel3Count)!=null?W:0)+" ",1),Ve.value.sumModel3Count<=999999?(d(),g("span",Gf,R(y(oe)("usercenter.points")),1)):I("",!0)])])),h.value?I("",!0):(d(),g("div",Bf,[r("div",Of,R(f.value),1),r("div",Wf,[Ne(R(Ve.value.sumModel4Count>99999?"无限额度":(re=Ve.value.sumModel4Count)!=null?re:0)+" ",1),Ve.value.sumModel4Count<=99999?(d(),g("span",Hf,R(y(oe)("usercenter.points")),1)):I("",!0)])])),x.value?I("",!0):(d(),g("div",Vf,[r("div",Yf,R(p.value),1),r("div",Zf,[Ne(R(Ve.value.sumDrawMjCount>99999?"无限额度":(ge=Ve.value.sumDrawMjCount)!=null?ge:0)+" ",1),Ve.value.sumDrawMjCount<=99999?(d(),g("span",Xf,R(y(oe)("usercenter.points")),1)):I("",!0)])])),r("div",Qf,[_[8]||(_[8]=r("div",{class:"text-gray-500 dark:text-gray-400 w-28"},"会员状态",-1)),r("div",{class:ne(["text-lg font-bold",ct.value?"text-red-500":"text-gray-500 dark:text-gray-400"])},R(Ve.value.expirationTime?`${Ve.value.expirationTime} 到期`:"非会员"),3)])]),z.value?(d(),g("div",qf,[_[9]||(_[9]=r("div",{class:"text-base font-medium text-gray-900 dark:text-gray-100 mb-3"},"卡密兑换",-1)),r("div",Jf,[Xe(r("input",{"onUpdate:modelValue":_[0]||(_[0]=q=>V.value=q),placeholder:y(oe)("usercenter.enterCardDetails"),class:"input input-md w-full",type:"text"},null,8,Kf),[[At,V.value]]),r("button",{disabled:a.value||!V.value,onClick:j,class:"btn btn-primary btn-md w-24"}," 兑换 ",8,ep)])])):I("",!0)])])])):A.value==="payment"?(d(),pe(Vd,{key:1,visible:A.value==="payment",onBackToMain:E,onPaymentSuccess:D},null,8,["visible"])):I("",!0)],2)}}});const Ai=an(tp,[["__scopeId","data-v-052f727d"]]),np=e=>{const t=typeof e;return t!=="function"&&t!=="object"||e===null},sp=e=>{const t=e.flags===""?void 0:e.flags;return new RegExp(e.source,t)},_s=(e,t=new WeakMap)=>{if(e===null||np(e))return e;if(t.has(e))return t.get(e);if(e instanceof RegExp)return sp(e);if(e instanceof Date)return new Date(e.getTime());if(e instanceof Function)return e;if(e instanceof Map){const s=new Map;return t.set(e,s),e.forEach((a,o)=>{s.set(o,_s(a,t))}),s}if(e instanceof Set){const s=new Set;t.set(e,s);for(const a of e)s.add(_s(a,t));return s}if(Array.isArray(e)){const s=[];return t.set(e,s),e.forEach(a=>{s.push(_s(a,t))}),s}const n={};t.set(e,n);for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(n[s]=_s(e[s],t));return n},Ci=(e,t=200)=>{let n=0;return(...s)=>new Promise(a=>{n&&(clearTimeout(n),a("cancel")),n=window.setTimeout(()=>{e.apply(void 0,s),n=0,a("done")},t)})},Pa=()=>`${Date.now().toString(36)}${Math.random().toString(36).substring(2)}`,Ao=e=>e!==null&&typeof e=="object"&&!Array.isArray(e),Ii=(e,t,n={})=>{const{excludeKeys:s}=n;for(const a in t)s&&s(a)?e[a]=t[a]:Ao(t[a])&&Ao(e[a])?e[a]=Ii(e[a],t[a],n):e[a]=t[a];return e},de="md-editor",ap="https://at.alicdn.com/t/c/font_2605852_cmafimm6hot.js",op="https://at.alicdn.com/t/c/font_2605852_cmafimm6hot.css",Ke="https://unpkg.com",ip=`${Ke}/@highlightjs/cdn-assets@11.10.0/highlight.min.js`,Co={main:`${Ke}/prettier@3.3.3/standalone.js`,markdown:`${Ke}/prettier@3.3.3/plugins/markdown.js`},rp={css:`${Ke}/cropperjs@1.6.2/dist/cropper.min.css`,js:`${Ke}/cropperjs@1.6.2/dist/cropper.min.js`},lp=`${Ke}/screenfull@5.2.0/dist/screenfull.js`,cp=`${Ke}/mermaid@11.3.0/dist/mermaid.min.js`,up={js:`${Ke}/katex@0.16.11/dist/katex.min.js`,css:`${Ke}/katex@0.16.11/dist/katex.min.css`},$a={a11y:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/a11y-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/a11y-dark.min.css`},atom:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/atom-one-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/atom-one-dark.min.css`},github:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/github.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/github-dark.min.css`},gradient:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/gradient-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/gradient-dark.min.css`},kimbie:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/kimbie-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/kimbie-dark.min.css`},paraiso:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/paraiso-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/paraiso-dark.min.css`},qtcreator:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/qtcreator-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/qtcreator-dark.min.css`},stackoverflow:{light:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/stackoverflow-light.min.css`,dark:`${Ke}/@highlightjs/cdn-assets@11.10.0/styles/stackoverflow-dark.min.css`}},dp=["bold","underline","italic","strikeThrough","-","title","sub","sup","quote","unorderedList","orderedList","task","-","codeRow","code","link","image","table","mermaid","katex","-","revoke","next","save","=","prettier","pageFullscreen","fullscreen","preview","previewOnly","htmlPreview","catalog","github"],fp=["markdownTotal","=","scrollSwitch"],Io={"zh-CN":{toolbarTips:{bold:"加粗",underline:"下划线",italic:"斜体",strikeThrough:"删除线",title:"标题",sub:"下标",sup:"上标",quote:"引用",unorderedList:"无序列表",orderedList:"有序列表",task:"任务列表",codeRow:"行内代码",code:"块级代码",link:"链接",image:"图片",table:"表格",mermaid:"mermaid图",katex:"katex公式",revoke:"后退",next:"前进",save:"保存",prettier:"美化",pageFullscreen:"浏览器全屏",fullscreen:"屏幕全屏",preview:"预览",previewOnly:"仅预览",htmlPreview:"html代码预览",catalog:"目录",github:"源码地址"},titleItem:{h1:"一级标题",h2:"二级标题",h3:"三级标题",h4:"四级标题",h5:"五级标题",h6:"六级标题"},imgTitleItem:{link:"添加链接",upload:"上传图片",clip2upload:"裁剪上传"},linkModalTips:{linkTitle:"添加链接",imageTitle:"添加图片",descLabel:"链接描述：",descLabelPlaceHolder:"请输入描述...",urlLabel:"链接地址：",urlLabelPlaceHolder:"请输入链接...",buttonOK:"确定"},clipModalTips:{title:"裁剪图片上传",buttonUpload:"上传"},copyCode:{text:"复制代码",successTips:"已复制！",failTips:"复制失败！"},mermaid:{flow:"流程图",sequence:"时序图",gantt:"甘特图",class:"类图",state:"状态图",pie:"饼图",relationship:"关系图",journey:"旅程图"},katex:{inline:"行内公式",block:"块级公式"},footer:{markdownTotal:"字数",scrollAuto:"同步滚动"}},"en-US":{toolbarTips:{bold:"bold",underline:"underline",italic:"italic",strikeThrough:"strikeThrough",title:"title",sub:"subscript",sup:"superscript",quote:"quote",unorderedList:"unordered list",orderedList:"ordered list",task:"task list",codeRow:"inline code",code:"block-level code",link:"link",image:"image",table:"table",mermaid:"mermaid",katex:"formula",revoke:"revoke",next:"undo revoke",save:"save",prettier:"prettier",pageFullscreen:"fullscreen in page",fullscreen:"fullscreen",preview:"preview",previewOnly:"preview only",htmlPreview:"html preview",catalog:"catalog",github:"source code"},titleItem:{h1:"Lv1 Heading",h2:"Lv2 Heading",h3:"Lv3 Heading",h4:"Lv4 Heading",h5:"Lv5 Heading",h6:"Lv6 Heading"},imgTitleItem:{link:"Add Image Link",upload:"Upload Images",clip2upload:"Clip Upload"},linkModalTips:{linkTitle:"Add Link",imageTitle:"Add Image",descLabel:"Desc:",descLabelPlaceHolder:"Enter a description...",urlLabel:"Link:",urlLabelPlaceHolder:"Enter a link...",buttonOK:"OK"},clipModalTips:{title:"Crop Image",buttonUpload:"Upload"},copyCode:{text:"Copy",successTips:"Copied!",failTips:"Copy failed!"},mermaid:{flow:"flow",sequence:"sequence",gantt:"gantt",class:"class",state:"state",pie:"pie",relationship:"relationship",journey:"journey"},katex:{inline:"inline",block:"block"},footer:{markdownTotal:"Character Count",scrollAuto:"Scroll Auto"}}},Xt={editorExtensions:{highlight:{js:ip,css:$a},prettier:{standaloneJs:Co.main,parserMarkdownJs:Co.markdown},cropper:me({},rp),iconfont:ap,iconfontClass:op,screenfull:{js:lp},mermaid:{js:cp},katex:me({},up)},editorExtensionsAttrs:{},editorConfig:{languageUserDefined:{},mermaidTemplate:{},renderDelay:500,zIndex:2e4},codeMirrorExtensions:(e,t)=>t,markdownItConfig:()=>{},markdownItPlugins:e=>e,iconfontType:"svg",mermaidConfig:e=>e,katexConfig:e=>e},pp=({instance:e,ctx:t,props:n={}},s="default")=>{const a=(e==null?void 0:e.$slots[s])||(t==null?void 0:t.slots[s]);return(a?a(e):"")||n[s]};var gp=Object.defineProperty,hp=(e,t,n)=>t in e?gp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,mp=(e,t,n)=>hp(e,typeof t!="symbol"?t+"":t,n);class vp{constructor(){mp(this,"pools",{})}remove(t,n,s){const o=this.pools[t]&&this.pools[t][n];o&&(this.pools[t][n]=o.filter(i=>i!==s))}clear(t){this.pools[t]={}}on(t,n){return this.pools[t]||(this.pools[t]={}),this.pools[t][n.name]||(this.pools[t][n.name]=[]),this.pools[t][n.name].push(n.callback),this.pools[t][n.name].includes(n.callback)}emit(t,n,...s){this.pools[t]||(this.pools[t]={});const o=this.pools[t][n];o&&o.forEach(i=>{try{i(...s)}catch(c){}})}}const Mn=new vp,yp="buildFinished",Ta="catalogChanged",bp="pushCatalog",Si="rerender",wp="taskStateChanged",Rn=(e,t,n="")=>{var s;const a=document.getElementById(t.id);if(a)n!==""&&(Reflect.get(window,n)?(s=t.onload)==null||s.call(a,new Event("load")):t.onload&&a.addEventListener("load",t.onload));else{const o=me({},t);o.onload=null;const i=kp(e,o);t.onload&&i.addEventListener("load",t.onload),document.head.appendChild(i)}},xp=(e,t)=>{const n=document.getElementById(t.id);n==null||n.remove(),Rn(e,t)},kp=(e,t)=>{const n=document.createElement(e);return Object.keys(t).forEach(s=>{t[s]!==void 0&&(n[s]=t[s])}),n},So=(()=>{const e=n=>{if(!n)return;const s=n.firstChild;let a=1,o=0,i=0,c=!1,l,u,f,p=1;const b=()=>{s.style.transform=`translate(${o}px, ${i}px) scale(${a})`};n.addEventListener("touchstart",h=>{h.touches.length===1?(c=!0,l=h.touches[0].clientX-o,u=h.touches[0].clientY-i):h.touches.length===2&&(f=Math.hypot(h.touches[0].clientX-h.touches[1].clientX,h.touches[0].clientY-h.touches[1].clientY),p=a)},{passive:!1}),n.addEventListener("touchmove",h=>{if(h.preventDefault(),c&&h.touches.length===1)o=h.touches[0].clientX-l,i=h.touches[0].clientY-u,b();else if(h.touches.length===2){const w=Math.hypot(h.touches[0].clientX-h.touches[1].clientX,h.touches[0].clientY-h.touches[1].clientY)/f,v=a;a=p*(1+(w-1));const C=(h.touches[0].clientX+h.touches[1].clientX)/2,A=(h.touches[0].clientY+h.touches[1].clientY)/2,L=s.getBoundingClientRect(),$=(C-L.left)/v,E=(A-L.top)/v;o-=$*(a-v),i-=E*(a-v),b()}},{passive:!1}),n.addEventListener("touchend",()=>{c=!1}),n.addEventListener("wheel",h=>{h.preventDefault();const x=.02,w=a;h.deltaY<0?a+=x:a=Math.max(.1,a-x);const v=s.getBoundingClientRect(),C=h.clientX-v.left,A=h.clientY-v.top;o-=C/w*(a-w),i-=A/w*(a-w),b()},{passive:!1}),n.addEventListener("mousedown",h=>{c=!0,l=h.clientX-o,u=h.clientY-i}),n.addEventListener("mousemove",h=>{c&&(o=h.clientX-l,i=h.clientY-u,b())}),n.addEventListener("mouseup",()=>{c=!1}),n.addEventListener("mouseleave",()=>{c=!1})};return n=>{n.forEach(s=>{e(s)})}})(),Ap=(e,t)=>{if(!e)return e;const n=t.split(`
`),s=['<span rn-wrapper aria-hidden="true">'];return n.forEach(()=>{s.push("<span></span>")}),s.push("</span>"),`<span class="${de}-code-block">${e}</span>${s.join("")}`},Cp=(()=>{let e=0;return t=>t+ ++e})();/*! medium-zoom 1.1.0 | MIT License | https://github.com/francoischalifour/medium-zoom */var qn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s])}return e},Qs=function(t){return t.tagName==="IMG"},Ip=function(t){return NodeList.prototype.isPrototypeOf(t)},na=function(t){return t&&t.nodeType===1},_o=function(t){var n=t.currentSrc||t.src;return n.substr(-4).toLowerCase()===".svg"},To=function(t){try{return Array.isArray(t)?t.filter(Qs):Ip(t)?[].slice.call(t).filter(Qs):na(t)?[t].filter(Qs):typeof t=="string"?[].slice.call(document.querySelectorAll(t)).filter(Qs):[]}catch(n){throw new TypeError(`The provided selector is invalid.
Expects a CSS selector, a Node element, a NodeList or an array.
See: https://github.com/francoischalifour/medium-zoom`)}},Sp=function(t){var n=document.createElement("div");return n.classList.add("medium-zoom-overlay"),n.style.background=t,n},_p=function(t){var n=t.getBoundingClientRect(),s=n.top,a=n.left,o=n.width,i=n.height,c=t.cloneNode(),l=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,u=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;return c.removeAttribute("id"),c.style.position="absolute",c.style.top=s+l+"px",c.style.left=a+u+"px",c.style.width=o+"px",c.style.height=i+"px",c.style.transform="",c},ls=function(t,n){var s=qn({bubbles:!1,cancelable:!1,detail:void 0},n);if(typeof window.CustomEvent=="function")return new CustomEvent(t,s);var a=document.createEvent("CustomEvent");return a.initCustomEvent(t,s.bubbles,s.cancelable,s.detail),a},Tp=function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=window.Promise||function(G){function F(){}G(F,F)},a=function(G){var F=G.target;if(F===Y){x();return}L.indexOf(F)!==-1&&w({target:F})},o=function(){if(!(E||!T.original)){var G=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(D-G)>O.scrollOffset&&setTimeout(x,150)}},i=function(G){var F=G.key||G.keyCode;(F==="Escape"||F==="Esc"||F===27)&&x()},c=function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},F=G;if(G.background&&(Y.style.background=G.background),G.container&&G.container instanceof Object&&(F.container=qn({},O.container,G.container)),G.template){var J=na(G.template)?G.template:document.querySelector(G.template);F.template=J}return O=qn({},O,F),L.forEach(function(V){V.dispatchEvent(ls("medium-zoom:update",{detail:{zoom:P}}))}),P},l=function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return e(qn({},O,G))},u=function(){for(var G=arguments.length,F=Array(G),J=0;J<G;J++)F[J]=arguments[J];var V=F.reduce(function(te,j){return[].concat(te,To(j))},[]);return V.filter(function(te){return L.indexOf(te)===-1}).forEach(function(te){L.push(te),te.classList.add("medium-zoom-image")}),$.forEach(function(te){var j=te.type,z=te.listener,Z=te.options;V.forEach(function(ee){ee.addEventListener(j,z,Z)})}),P},f=function(){for(var G=arguments.length,F=Array(G),J=0;J<G;J++)F[J]=arguments[J];T.zoomed&&x();var V=F.length>0?F.reduce(function(te,j){return[].concat(te,To(j))},[]):L;return V.forEach(function(te){te.classList.remove("medium-zoom-image"),te.dispatchEvent(ls("medium-zoom:detach",{detail:{zoom:P}}))}),L=L.filter(function(te){return V.indexOf(te)===-1}),P},p=function(G,F){var J=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return L.forEach(function(V){V.addEventListener("medium-zoom:"+G,F,J)}),$.push({type:"medium-zoom:"+G,listener:F,options:J}),P},b=function(G,F){var J=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return L.forEach(function(V){V.removeEventListener("medium-zoom:"+G,F,J)}),$=$.filter(function(V){return!(V.type==="medium-zoom:"+G&&V.listener.toString()===F.toString())}),P},h=function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},F=G.target,J=function(){var te={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},j=void 0,z=void 0;if(O.container)if(O.container instanceof Object)te=qn({},te,O.container),j=te.width-te.left-te.right-O.margin*2,z=te.height-te.top-te.bottom-O.margin*2;else{var Z=na(O.container)?O.container:document.querySelector(O.container),ee=Z.getBoundingClientRect(),Q=ee.width,le=ee.height,Ae=ee.left,ue=ee.top;te=qn({},te,{width:Q,height:le,left:Ae,top:ue})}j=j||te.width-O.margin*2,z=z||te.height-O.margin*2;var we=T.zoomedHd||T.original,Re=_o(we)?j:we.naturalWidth||j,xt=_o(we)?z:we.naturalHeight||z,rt=we.getBoundingClientRect(),nt=rt.top,lt=rt.left,Ve=rt.width,ct=rt.height,Be=Math.min(Math.max(Ve,Re),j)/Ve,Dt=Math.min(Math.max(ct,xt),z)/ct,ae=Math.min(Be,Dt),_=(-lt+(j-Ve)/2+O.margin+te.left)/ae,W=(-nt+(z-ct)/2+O.margin+te.top)/ae,re="scale("+ae+") translate3d("+_+"px, "+W+"px, 0)";T.zoomed.style.transform=re,T.zoomedHd&&(T.zoomedHd.style.transform=re)};return new s(function(V){if(F&&L.indexOf(F)===-1){V(P);return}var te=function Q(){E=!1,T.zoomed.removeEventListener("transitionend",Q),T.original.dispatchEvent(ls("medium-zoom:opened",{detail:{zoom:P}})),V(P)};if(T.zoomed){V(P);return}if(F)T.original=F;else if(L.length>0){var j=L;T.original=j[0]}else{V(P);return}if(T.original.dispatchEvent(ls("medium-zoom:open",{detail:{zoom:P}})),D=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,E=!0,T.zoomed=_p(T.original),document.body.appendChild(Y),O.template){var z=na(O.template)?O.template:document.querySelector(O.template);T.template=document.createElement("div"),T.template.appendChild(z.content.cloneNode(!0)),document.body.appendChild(T.template)}if(T.original.parentElement&&T.original.parentElement.tagName==="PICTURE"&&T.original.currentSrc&&(T.zoomed.src=T.original.currentSrc),document.body.appendChild(T.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),T.original.classList.add("medium-zoom-image--hidden"),T.zoomed.classList.add("medium-zoom-image--opened"),T.zoomed.addEventListener("click",x),T.zoomed.addEventListener("transitionend",te),T.original.getAttribute("data-zoom-src")){T.zoomedHd=T.zoomed.cloneNode(),T.zoomedHd.removeAttribute("srcset"),T.zoomedHd.removeAttribute("sizes"),T.zoomedHd.removeAttribute("loading"),T.zoomedHd.src=T.zoomed.getAttribute("data-zoom-src"),T.zoomedHd.onerror=function(){clearInterval(Z),T.zoomedHd=null,J()};var Z=setInterval(function(){T.zoomedHd.complete&&(clearInterval(Z),T.zoomedHd.classList.add("medium-zoom-image--opened"),T.zoomedHd.addEventListener("click",x),document.body.appendChild(T.zoomedHd),J())},10)}else if(T.original.hasAttribute("srcset")){T.zoomedHd=T.zoomed.cloneNode(),T.zoomedHd.removeAttribute("sizes"),T.zoomedHd.removeAttribute("loading");var ee=T.zoomedHd.addEventListener("load",function(){T.zoomedHd.removeEventListener("load",ee),T.zoomedHd.classList.add("medium-zoom-image--opened"),T.zoomedHd.addEventListener("click",x),document.body.appendChild(T.zoomedHd),J()})}else J()})},x=function(){return new s(function(G){if(E||!T.original){G(P);return}var F=function J(){T.original.classList.remove("medium-zoom-image--hidden"),document.body.removeChild(T.zoomed),T.zoomedHd&&document.body.removeChild(T.zoomedHd),document.body.removeChild(Y),T.zoomed.classList.remove("medium-zoom-image--opened"),T.template&&document.body.removeChild(T.template),E=!1,T.zoomed.removeEventListener("transitionend",J),T.original.dispatchEvent(ls("medium-zoom:closed",{detail:{zoom:P}})),T.original=null,T.zoomed=null,T.zoomedHd=null,T.template=null,G(P)};E=!0,document.body.classList.remove("medium-zoom--opened"),T.zoomed.style.transform="",T.zoomedHd&&(T.zoomedHd.style.transform=""),T.template&&(T.template.style.transition="opacity 150ms",T.template.style.opacity=0),T.original.dispatchEvent(ls("medium-zoom:close",{detail:{zoom:P}})),T.zoomed.addEventListener("transitionend",F)})},w=function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},F=G.target;return T.original?x():h({target:F})},v=function(){return O},C=function(){return L},A=function(){return T.original},L=[],$=[],E=!1,D=0,O=n,T={original:null,zoomed:null,zoomedHd:null,template:null};Object.prototype.toString.call(t)==="[object Object]"?O=t:(t||typeof t=="string")&&u(t),O=qn({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},O);var Y=Sp(O.background);document.addEventListener("click",a),document.addEventListener("keyup",i),document.addEventListener("scroll",o),window.addEventListener("resize",x);var P={open:h,close:x,toggle:w,update:c,clone:l,attach:u,detach:f,on:p,off:b,getOptions:v,getImages:C,getZoomedImage:A};return P};function Lp(e,t){t===void 0&&(t={});var n=t.insertAt;if(!(!e||typeof document=="undefined")){var s=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",n==="top"&&s.firstChild?s.insertBefore(a,s.firstChild):s.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}}var Mp=".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)!important}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}";Lp(Mp);const Ep=Tp;var Rp=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],s=0;s<e.rangeCount;s++)n.push(e.getRangeAt(s));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||n.forEach(function(a){e.addRange(a)}),t&&t.focus()}},zp=Rp,Lo={"text/plain":"Text","text/html":"Url",default:"Text"},Dp="Copy to clipboard: #{key}, Enter";function jp(e){var t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}function Pp(e,t){var n,s,a,o,i,c,l=!1;t||(t={}),n=t.debug||!1;try{a=zp(),o=document.createRange(),i=document.getSelection(),c=document.createElement("span"),c.textContent=e,c.ariaHidden="true",c.style.all="unset",c.style.position="fixed",c.style.top=0,c.style.clip="rect(0, 0, 0, 0)",c.style.whiteSpace="pre",c.style.webkitUserSelect="text",c.style.MozUserSelect="text",c.style.msUserSelect="text",c.style.userSelect="text",c.addEventListener("copy",function(f){if(f.stopPropagation(),t.format)if(f.preventDefault(),typeof f.clipboardData=="undefined"){window.clipboardData.clearData();var p=Lo[t.format]||Lo.default;window.clipboardData.setData(p,e)}else f.clipboardData.clearData(),f.clipboardData.setData(t.format,e);t.onCopy&&(f.preventDefault(),t.onCopy(f.clipboardData))}),document.body.appendChild(c),o.selectNodeContents(c),i.addRange(o);var u=document.execCommand("copy");if(!u)throw new Error("copy command was unsuccessful");l=!0}catch(f){try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),l=!0}catch(p){s=jp("message"in t?t.message:Dp),window.prompt(s,e)}}finally{i&&(typeof i.removeRange=="function"?i.removeRange(o):i.removeAllRanges()),c&&document.body.removeChild(c),a()}return l}var $p=Pp;const Up=Ka($p),Mo=new Set([!0,!1,"alt","title"]);function _i(e,t){return(Array.isArray(e)?e:[]).filter(([n])=>n!==t)}function Ti(e,t){e&&e.attrs&&(e.attrs=_i(e.attrs,t))}function Np(e,t){if(!Mo.has(e))throw new TypeError(`figcaption must be one of: ${[...Mo]}.`);if(e==="alt")return t.content;const n=t.attrs.find(([s])=>s==="title");return Array.isArray(n)&&n[1]?(Ti(t,"title"),n[1]):void 0}function Fp(e,t){t=t||{},e.core.ruler.before("linkify","image_figures",function(n){let s=1;for(let a=1,o=n.tokens.length;a<o-1;++a){const i=n.tokens[a];if(i.type!=="inline"||!i.children||i.children.length!==1&&i.children.length!==3||i.children.length===1&&i.children[0].type!=="image")continue;if(i.children.length===3){const[u,f,p]=i.children;if(u.type!=="link_open"||f.type!=="image"||p.type!=="link_close")continue}if(a!==0&&n.tokens[a-1].type!=="paragraph_open"||a!==o-1&&n.tokens[a+1].type!=="paragraph_close")continue;const c=n.tokens[a-1];let l;if(c.type="figure_open",c.tag="figure",n.tokens[a+1].type="figure_close",n.tokens[a+1].tag="figure",t.dataType&&n.tokens[a-1].attrPush(["data-type","image"]),t.link&&i.children.length===1){[l]=i.children;const u=new n.Token("link_open","a",1);u.attrPush(["href",l.attrGet("src")]),i.children.unshift(u),i.children.push(new n.Token("link_close","a",-1))}if(l=i.children.length===1?i.children[0]:i.children[1],t.figcaption){const u=Np(t.figcaption,l);if(u){const[f]=e.parseInline(u,n.env);i.children.push(new n.Token("figcaption_open","figcaption",1)),i.children.push(...f.children),i.children.push(new n.Token("figcaption_close","figcaption",-1)),l.attrs&&(l.attrs=_i(l.attrs,"title"))}}if(t.copyAttrs&&l.attrs){const u=t.copyAttrs===!0?"":t.copyAttrs;c.attrs=l.attrs.filter(([f])=>f.match(u)).map(f=>Array.from(f))}if(t.tabindex&&(n.tokens[a-1].attrPush(["tabindex",s]),s++),t.lazy&&(l.attrs.some(([u])=>u==="loading")||l.attrs.push(["loading","lazy"])),t.async&&(l.attrs.some(([u])=>u==="decoding")||l.attrs.push(["decoding","async"])),t.classes&&typeof t.classes=="string"){let u=!1;for(let f=0,p=l.attrs.length;f<p&&!u;f++){const b=l.attrs[f];b[0]==="class"&&(b[1]=`${b[1]} ${t.classes}`,u=!0)}u||l.attrs.push(["class",t.classes])}if(t.removeSrc){const u=l.attrs.find(([f])=>f==="src");l.attrs.push(["data-src",u[1]]),Ti(l,"src")}}})}const Gp=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Bp(e,t){const n=e.posMax,s=e.pos;if(e.src.charCodeAt(s)!==126||t||s+2>=n)return!1;e.pos=s+1;let a=!1;for(;e.pos<n;){if(e.src.charCodeAt(e.pos)===126){a=!0;break}e.md.inline.skipToken(e)}if(!a||s+1===e.pos)return e.pos=s,!1;const o=e.src.slice(s+1,e.pos);if(o.match(/(^|[^\\])(\\\\)*\s/))return e.pos=s,!1;e.posMax=e.pos,e.pos=s+1;const i=e.push("sub_open","sub",1);i.markup="~";const c=e.push("text","",0);c.content=o.replace(Gp,"$1");const l=e.push("sub_close","sub",-1);return l.markup="~",e.pos=e.posMax+1,e.posMax=n,!0}function Op(e){e.inline.ruler.after("emphasis","sub",Bp)}const Wp=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Hp(e,t){const n=e.posMax,s=e.pos;if(e.src.charCodeAt(s)!==94||t||s+2>=n)return!1;e.pos=s+1;let a=!1;for(;e.pos<n;){if(e.src.charCodeAt(e.pos)===94){a=!0;break}e.md.inline.skipToken(e)}if(!a||s+1===e.pos)return e.pos=s,!1;const o=e.src.slice(s+1,e.pos);if(o.match(/(^|[^\\])(\\\\)*\s/))return e.pos=s,!1;e.posMax=e.pos,e.pos=s+1;const i=e.push("sup_open","sup",1);i.markup="^";const c=e.push("text","",0);c.content=o.replace(Wp,"$1");const l=e.push("sup_close","sup",-1);return l.markup="^",e.pos=e.posMax+1,e.posMax=n,!0}function Vp(e){e.inline.ruler.after("emphasis","sup",Hp)}const cs=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Li=new Set,Ua=typeof process=="object"&&process?process:{},Mi=(e,t,n,s)=>{typeof Ua.emitWarning=="function"&&Ua.emitWarning(e,t,n,s)};let fa=globalThis.AbortController,Eo=globalThis.AbortSignal;var ni;if(typeof fa=="undefined"){Eo=class{constructor(){et(this,"onabort");et(this,"_onabort",[]);et(this,"reason");et(this,"aborted",!1)}addEventListener(s,a){this._onabort.push(a)}},fa=class{constructor(){et(this,"signal",new Eo);t()}abort(s){var a,o;if(!this.signal.aborted){this.signal.reason=s,this.signal.aborted=!0;for(const i of this.signal._onabort)i(s);(o=(a=this.signal).onabort)==null||o.call(a,s)}}};let e=((ni=Ua.env)==null?void 0:ni.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{e&&(e=!1,Mi("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const Yp=e=>!Li.has(e),Un=e=>e&&e===Math.floor(e)&&e>0&&isFinite(e),Ei=e=>Un(e)?e<=Math.pow(2,8)?Uint8Array:e<=Math.pow(2,16)?Uint16Array:e<=Math.pow(2,32)?Uint32Array:e<=Number.MAX_SAFE_INTEGER?sa:null:null;class sa extends Array{constructor(t){super(t),this.fill(0)}}var fs;const Jn=class Jn{constructor(t,n){et(this,"heap");et(this,"length");if(!m(Jn,fs))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(t),this.length=0}static create(t){const n=Ei(t);if(!n)return[];xe(Jn,fs,!0);const s=new Jn(t,n);return xe(Jn,fs,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};fs=new WeakMap,_e(Jn,fs,!1);let Na=Jn;var rn,Ht,ln,cn,ps,gs,vt,un,dt,Je,Te,Et,Vt,_t,bt,dn,wt,fn,pn,Yt,gn,On,Rt,$s,Ga,Kn,En,Us,Zt,ma,Ri,es,hs,Ns,kn,Nn,An,Fn,Fs,Ba,n1,ms,aa,vs,oa,We,qe,Gs,Oa,ts,Ts,Cn,Gn,Bs,Wa;const io=class io{constructor(t){_e(this,$s);_e(this,ma);_e(this,kn);_e(this,An);_e(this,Fs);_e(this,ms);_e(this,vs);_e(this,We);_e(this,Gs);_e(this,ts);_e(this,Cn);_e(this,Bs);_e(this,rn,void 0);_e(this,Ht,void 0);_e(this,ln,void 0);_e(this,cn,void 0);_e(this,ps,void 0);_e(this,gs,void 0);et(this,"ttl");et(this,"ttlResolution");et(this,"ttlAutopurge");et(this,"updateAgeOnGet");et(this,"updateAgeOnHas");et(this,"allowStale");et(this,"noDisposeOnSet");et(this,"noUpdateTTL");et(this,"maxEntrySize");et(this,"sizeCalculation");et(this,"noDeleteOnFetchRejection");et(this,"noDeleteOnStaleGet");et(this,"allowStaleOnFetchAbort");et(this,"allowStaleOnFetchRejection");et(this,"ignoreFetchAbort");_e(this,vt,void 0);_e(this,un,void 0);_e(this,dt,void 0);_e(this,Je,void 0);_e(this,Te,void 0);_e(this,Et,void 0);_e(this,Vt,void 0);_e(this,_t,void 0);_e(this,bt,void 0);_e(this,dn,void 0);_e(this,wt,void 0);_e(this,fn,void 0);_e(this,pn,void 0);_e(this,Yt,void 0);_e(this,gn,void 0);_e(this,On,void 0);_e(this,Rt,void 0);_e(this,Kn,()=>{});_e(this,En,()=>{});_e(this,Us,()=>{});_e(this,Zt,()=>!1);_e(this,es,t=>{});_e(this,hs,(t,n,s)=>{});_e(this,Ns,(t,n,s,a)=>{if(s||a)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});et(this,n1,"LRUCache");const{max:n=0,ttl:s,ttlResolution:a=1,ttlAutopurge:o,updateAgeOnGet:i,updateAgeOnHas:c,allowStale:l,dispose:u,disposeAfter:f,noDisposeOnSet:p,noUpdateTTL:b,maxSize:h=0,maxEntrySize:x=0,sizeCalculation:w,fetchMethod:v,memoMethod:C,noDeleteOnFetchRejection:A,noDeleteOnStaleGet:L,allowStaleOnFetchRejection:$,allowStaleOnFetchAbort:E,ignoreFetchAbort:D}=t;if(n!==0&&!Un(n))throw new TypeError("max option must be a nonnegative integer");const O=n?Ei(n):Array;if(!O)throw new Error("invalid max value: "+n);if(xe(this,rn,n),xe(this,Ht,h),this.maxEntrySize=x||m(this,Ht),this.sizeCalculation=w,this.sizeCalculation){if(!m(this,Ht)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(C!==void 0&&typeof C!="function")throw new TypeError("memoMethod must be a function if defined");if(xe(this,gs,C),v!==void 0&&typeof v!="function")throw new TypeError("fetchMethod must be a function if specified");if(xe(this,ps,v),xe(this,On,!!v),xe(this,dt,new Map),xe(this,Je,new Array(n).fill(void 0)),xe(this,Te,new Array(n).fill(void 0)),xe(this,Et,new O(n)),xe(this,Vt,new O(n)),xe(this,_t,0),xe(this,bt,0),xe(this,dn,Na.create(n)),xe(this,vt,0),xe(this,un,0),typeof u=="function"&&xe(this,ln,u),typeof f=="function"?(xe(this,cn,f),xe(this,wt,[])):(xe(this,cn,void 0),xe(this,wt,void 0)),xe(this,gn,!!m(this,ln)),xe(this,Rt,!!m(this,cn)),this.noDisposeOnSet=!!p,this.noUpdateTTL=!!b,this.noDeleteOnFetchRejection=!!A,this.allowStaleOnFetchRejection=!!$,this.allowStaleOnFetchAbort=!!E,this.ignoreFetchAbort=!!D,this.maxEntrySize!==0){if(m(this,Ht)!==0&&!Un(m(this,Ht)))throw new TypeError("maxSize must be a positive integer if specified");if(!Un(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");ce(this,ma,Ri).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!L,this.updateAgeOnGet=!!i,this.updateAgeOnHas=!!c,this.ttlResolution=Un(a)||a===0?a:1,this.ttlAutopurge=!!o,this.ttl=s||0,this.ttl){if(!Un(this.ttl))throw new TypeError("ttl must be a positive integer if specified");ce(this,$s,Ga).call(this)}if(m(this,rn)===0&&this.ttl===0&&m(this,Ht)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!m(this,rn)&&!m(this,Ht)){const T="LRU_CACHE_UNBOUNDED";Yp(T)&&(Li.add(T),Mi("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",T,io))}}static unsafeExposeInternals(t){return{starts:m(t,pn),ttls:m(t,Yt),sizes:m(t,fn),keyMap:m(t,dt),keyList:m(t,Je),valList:m(t,Te),next:m(t,Et),prev:m(t,Vt),get head(){return m(t,_t)},get tail(){return m(t,bt)},free:m(t,dn),isBackgroundFetch:n=>{var s;return ce(s=t,We,qe).call(s,n)},backgroundFetch:(n,s,a,o)=>{var i;return ce(i=t,vs,oa).call(i,n,s,a,o)},moveToTail:n=>{var s;return ce(s=t,ts,Ts).call(s,n)},indexes:n=>{var s;return ce(s=t,kn,Nn).call(s,n)},rindexes:n=>{var s;return ce(s=t,An,Fn).call(s,n)},isStale:n=>{var s;return m(s=t,Zt).call(s,n)}}}get max(){return m(this,rn)}get maxSize(){return m(this,Ht)}get calculatedSize(){return m(this,un)}get size(){return m(this,vt)}get fetchMethod(){return m(this,ps)}get memoMethod(){return m(this,gs)}get dispose(){return m(this,ln)}get disposeAfter(){return m(this,cn)}getRemainingTTL(t){return m(this,dt).has(t)?1/0:0}*entries(){for(const t of ce(this,kn,Nn).call(this))m(this,Te)[t]!==void 0&&m(this,Je)[t]!==void 0&&!ce(this,We,qe).call(this,m(this,Te)[t])&&(yield[m(this,Je)[t],m(this,Te)[t]])}*rentries(){for(const t of ce(this,An,Fn).call(this))m(this,Te)[t]!==void 0&&m(this,Je)[t]!==void 0&&!ce(this,We,qe).call(this,m(this,Te)[t])&&(yield[m(this,Je)[t],m(this,Te)[t]])}*keys(){for(const t of ce(this,kn,Nn).call(this)){const n=m(this,Je)[t];n!==void 0&&!ce(this,We,qe).call(this,m(this,Te)[t])&&(yield n)}}*rkeys(){for(const t of ce(this,An,Fn).call(this)){const n=m(this,Je)[t];n!==void 0&&!ce(this,We,qe).call(this,m(this,Te)[t])&&(yield n)}}*values(){for(const t of ce(this,kn,Nn).call(this))m(this,Te)[t]!==void 0&&!ce(this,We,qe).call(this,m(this,Te)[t])&&(yield m(this,Te)[t])}*rvalues(){for(const t of ce(this,An,Fn).call(this))m(this,Te)[t]!==void 0&&!ce(this,We,qe).call(this,m(this,Te)[t])&&(yield m(this,Te)[t])}[Symbol.iterator](){return this.entries()}find(t,n={}){for(const s of ce(this,kn,Nn).call(this)){const a=m(this,Te)[s],o=ce(this,We,qe).call(this,a)?a.__staleWhileFetching:a;if(o!==void 0&&t(o,m(this,Je)[s],this))return this.get(m(this,Je)[s],n)}}forEach(t,n=this){for(const s of ce(this,kn,Nn).call(this)){const a=m(this,Te)[s],o=ce(this,We,qe).call(this,a)?a.__staleWhileFetching:a;o!==void 0&&t.call(n,o,m(this,Je)[s],this)}}rforEach(t,n=this){for(const s of ce(this,An,Fn).call(this)){const a=m(this,Te)[s],o=ce(this,We,qe).call(this,a)?a.__staleWhileFetching:a;o!==void 0&&t.call(n,o,m(this,Je)[s],this)}}purgeStale(){let t=!1;for(const n of ce(this,An,Fn).call(this,{allowStale:!0}))m(this,Zt).call(this,n)&&(ce(this,Cn,Gn).call(this,m(this,Je)[n],"expire"),t=!0);return t}info(t){const n=m(this,dt).get(t);if(n===void 0)return;const s=m(this,Te)[n],a=ce(this,We,qe).call(this,s)?s.__staleWhileFetching:s;if(a===void 0)return;const o={value:a};if(m(this,Yt)&&m(this,pn)){const i=m(this,Yt)[n],c=m(this,pn)[n];if(i&&c){const l=i-(cs.now()-c);o.ttl=l,o.start=Date.now()}}return m(this,fn)&&(o.size=m(this,fn)[n]),o}dump(){const t=[];for(const n of ce(this,kn,Nn).call(this,{allowStale:!0})){const s=m(this,Je)[n],a=m(this,Te)[n],o=ce(this,We,qe).call(this,a)?a.__staleWhileFetching:a;if(o===void 0||s===void 0)continue;const i={value:o};if(m(this,Yt)&&m(this,pn)){i.ttl=m(this,Yt)[n];const c=cs.now()-m(this,pn)[n];i.start=Math.floor(Date.now()-c)}m(this,fn)&&(i.size=m(this,fn)[n]),t.unshift([s,i])}return t}load(t){this.clear();for(const[n,s]of t){if(s.start){const a=Date.now()-s.start;s.start=cs.now()-a}this.set(n,s.value,s)}}set(t,n,s={}){var b,h,x,w,v;if(n===void 0)return this.delete(t),this;const{ttl:a=this.ttl,start:o,noDisposeOnSet:i=this.noDisposeOnSet,sizeCalculation:c=this.sizeCalculation,status:l}=s;let{noUpdateTTL:u=this.noUpdateTTL}=s;const f=m(this,Ns).call(this,t,n,s.size||0,c);if(this.maxEntrySize&&f>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),ce(this,Cn,Gn).call(this,t,"set"),this;let p=m(this,vt)===0?void 0:m(this,dt).get(t);if(p===void 0)p=m(this,vt)===0?m(this,bt):m(this,dn).length!==0?m(this,dn).pop():m(this,vt)===m(this,rn)?ce(this,ms,aa).call(this,!1):m(this,vt),m(this,Je)[p]=t,m(this,Te)[p]=n,m(this,dt).set(t,p),m(this,Et)[m(this,bt)]=p,m(this,Vt)[p]=m(this,bt),xe(this,bt,p),Ys(this,vt)._++,m(this,hs).call(this,p,f,l),l&&(l.set="add"),u=!1;else{ce(this,ts,Ts).call(this,p);const C=m(this,Te)[p];if(n!==C){if(m(this,On)&&ce(this,We,qe).call(this,C)){C.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:A}=C;A!==void 0&&!i&&(m(this,gn)&&((b=m(this,ln))==null||b.call(this,A,t,"set")),m(this,Rt)&&((h=m(this,wt))==null||h.push([A,t,"set"])))}else i||(m(this,gn)&&((x=m(this,ln))==null||x.call(this,C,t,"set")),m(this,Rt)&&((w=m(this,wt))==null||w.push([C,t,"set"])));if(m(this,es).call(this,p),m(this,hs).call(this,p,f,l),m(this,Te)[p]=n,l){l.set="replace";const A=C&&ce(this,We,qe).call(this,C)?C.__staleWhileFetching:C;A!==void 0&&(l.oldValue=A)}}else l&&(l.set="update")}if(a!==0&&!m(this,Yt)&&ce(this,$s,Ga).call(this),m(this,Yt)&&(u||m(this,Us).call(this,p,a,o),l&&m(this,En).call(this,l,p)),!i&&m(this,Rt)&&m(this,wt)){const C=m(this,wt);let A;for(;A=C==null?void 0:C.shift();)(v=m(this,cn))==null||v.call(this,...A)}return this}pop(){var t;try{for(;m(this,vt);){const n=m(this,Te)[m(this,_t)];if(ce(this,ms,aa).call(this,!0),ce(this,We,qe).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(m(this,Rt)&&m(this,wt)){const n=m(this,wt);let s;for(;s=n==null?void 0:n.shift();)(t=m(this,cn))==null||t.call(this,...s)}}}has(t,n={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:a}=n,o=m(this,dt).get(t);if(o!==void 0){const i=m(this,Te)[o];if(ce(this,We,qe).call(this,i)&&i.__staleWhileFetching===void 0)return!1;if(m(this,Zt).call(this,o))a&&(a.has="stale",m(this,En).call(this,a,o));else return s&&m(this,Kn).call(this,o),a&&(a.has="hit",m(this,En).call(this,a,o)),!0}else a&&(a.has="miss");return!1}peek(t,n={}){const{allowStale:s=this.allowStale}=n,a=m(this,dt).get(t);if(a===void 0||!s&&m(this,Zt).call(this,a))return;const o=m(this,Te)[a];return ce(this,We,qe).call(this,o)?o.__staleWhileFetching:o}fetch(s){return H(this,arguments,function*(t,n={}){const{allowStale:a=this.allowStale,updateAgeOnGet:o=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:c=this.ttl,noDisposeOnSet:l=this.noDisposeOnSet,size:u=0,sizeCalculation:f=this.sizeCalculation,noUpdateTTL:p=this.noUpdateTTL,noDeleteOnFetchRejection:b=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:h=this.allowStaleOnFetchRejection,ignoreFetchAbort:x=this.ignoreFetchAbort,allowStaleOnFetchAbort:w=this.allowStaleOnFetchAbort,context:v,forceRefresh:C=!1,status:A,signal:L}=n;if(!m(this,On))return A&&(A.fetch="get"),this.get(t,{allowStale:a,updateAgeOnGet:o,noDeleteOnStaleGet:i,status:A});const $={allowStale:a,updateAgeOnGet:o,noDeleteOnStaleGet:i,ttl:c,noDisposeOnSet:l,size:u,sizeCalculation:f,noUpdateTTL:p,noDeleteOnFetchRejection:b,allowStaleOnFetchRejection:h,allowStaleOnFetchAbort:w,ignoreFetchAbort:x,status:A,signal:L};let E=m(this,dt).get(t);if(E===void 0){A&&(A.fetch="miss");const D=ce(this,vs,oa).call(this,t,E,$,v);return D.__returned=D}else{const D=m(this,Te)[E];if(ce(this,We,qe).call(this,D)){const se=a&&D.__staleWhileFetching!==void 0;return A&&(A.fetch="inflight",se&&(A.returnedStale=!0)),se?D.__staleWhileFetching:D.__returned=D}const O=m(this,Zt).call(this,E);if(!C&&!O)return A&&(A.fetch="hit"),ce(this,ts,Ts).call(this,E),o&&m(this,Kn).call(this,E),A&&m(this,En).call(this,A,E),D;const T=ce(this,vs,oa).call(this,t,E,$,v),P=T.__staleWhileFetching!==void 0&&a;return A&&(A.fetch=O?"stale":"refresh",P&&O&&(A.returnedStale=!0)),P?T.__staleWhileFetching:T.__returned=T}})}forceFetch(s){return H(this,arguments,function*(t,n={}){const a=yield this.fetch(t,n);if(a===void 0)throw new Error("fetch() returned undefined");return a})}memo(t,n={}){const s=m(this,gs);if(!s)throw new Error("no memoMethod provided to constructor");const u=n,{context:a,forceRefresh:o}=u,i=ho(u,["context","forceRefresh"]),c=this.get(t,i);if(!o&&c!==void 0)return c;const l=s(t,c,{options:i,context:a});return this.set(t,l,i),l}get(t,n={}){const{allowStale:s=this.allowStale,updateAgeOnGet:a=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,status:i}=n,c=m(this,dt).get(t);if(c!==void 0){const l=m(this,Te)[c],u=ce(this,We,qe).call(this,l);return i&&m(this,En).call(this,i,c),m(this,Zt).call(this,c)?(i&&(i.get="stale"),u?(i&&s&&l.__staleWhileFetching!==void 0&&(i.returnedStale=!0),s?l.__staleWhileFetching:void 0):(o||ce(this,Cn,Gn).call(this,t,"expire"),i&&s&&(i.returnedStale=!0),s?l:void 0)):(i&&(i.get="hit"),u?l.__staleWhileFetching:(ce(this,ts,Ts).call(this,c),a&&m(this,Kn).call(this,c),l))}else i&&(i.get="miss")}delete(t){return ce(this,Cn,Gn).call(this,t,"delete")}clear(){return ce(this,Bs,Wa).call(this,"delete")}};n1=Symbol.toStringTag,rn=new WeakMap,Ht=new WeakMap,ln=new WeakMap,cn=new WeakMap,ps=new WeakMap,gs=new WeakMap,vt=new WeakMap,un=new WeakMap,dt=new WeakMap,Je=new WeakMap,Te=new WeakMap,Et=new WeakMap,Vt=new WeakMap,_t=new WeakMap,bt=new WeakMap,dn=new WeakMap,wt=new WeakMap,fn=new WeakMap,pn=new WeakMap,Yt=new WeakMap,gn=new WeakMap,On=new WeakMap,Rt=new WeakMap,$s=new WeakSet,Ga=function(){const t=new sa(m(this,rn)),n=new sa(m(this,rn));xe(this,Yt,t),xe(this,pn,n),xe(this,Us,(o,i,c=cs.now())=>{if(n[o]=i!==0?c:0,t[o]=i,i!==0&&this.ttlAutopurge){const l=setTimeout(()=>{m(this,Zt).call(this,o)&&ce(this,Cn,Gn).call(this,m(this,Je)[o],"expire")},i+1);l.unref&&l.unref()}}),xe(this,Kn,o=>{n[o]=t[o]!==0?cs.now():0}),xe(this,En,(o,i)=>{if(t[i]){const c=t[i],l=n[i];if(!c||!l)return;o.ttl=c,o.start=l,o.now=s||a();const u=o.now-l;o.remainingTTL=c-u}});let s=0;const a=()=>{const o=cs.now();if(this.ttlResolution>0){s=o;const i=setTimeout(()=>s=0,this.ttlResolution);i.unref&&i.unref()}return o};this.getRemainingTTL=o=>{const i=m(this,dt).get(o);if(i===void 0)return 0;const c=t[i],l=n[i];if(!c||!l)return 1/0;const u=(s||a())-l;return c-u},xe(this,Zt,o=>{const i=n[o],c=t[o];return!!c&&!!i&&(s||a())-i>c})},Kn=new WeakMap,En=new WeakMap,Us=new WeakMap,Zt=new WeakMap,ma=new WeakSet,Ri=function(){const t=new sa(m(this,rn));xe(this,un,0),xe(this,fn,t),xe(this,es,n=>{xe(this,un,m(this,un)-t[n]),t[n]=0}),xe(this,Ns,(n,s,a,o)=>{if(ce(this,We,qe).call(this,s))return 0;if(!Un(a))if(o){if(typeof o!="function")throw new TypeError("sizeCalculation must be a function");if(a=o(s,n),!Un(a))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return a}),xe(this,hs,(n,s,a)=>{if(t[n]=s,m(this,Ht)){const o=m(this,Ht)-t[n];for(;m(this,un)>o;)ce(this,ms,aa).call(this,!0)}xe(this,un,m(this,un)+t[n]),a&&(a.entrySize=s,a.totalCalculatedSize=m(this,un))})},es=new WeakMap,hs=new WeakMap,Ns=new WeakMap,kn=new WeakSet,Nn=function*({allowStale:t=this.allowStale}={}){if(m(this,vt))for(let n=m(this,bt);!(!ce(this,Fs,Ba).call(this,n)||((t||!m(this,Zt).call(this,n))&&(yield n),n===m(this,_t)));)n=m(this,Vt)[n]},An=new WeakSet,Fn=function*({allowStale:t=this.allowStale}={}){if(m(this,vt))for(let n=m(this,_t);!(!ce(this,Fs,Ba).call(this,n)||((t||!m(this,Zt).call(this,n))&&(yield n),n===m(this,bt)));)n=m(this,Et)[n]},Fs=new WeakSet,Ba=function(t){return t!==void 0&&m(this,dt).get(m(this,Je)[t])===t},ms=new WeakSet,aa=function(t){var o,i;const n=m(this,_t),s=m(this,Je)[n],a=m(this,Te)[n];return m(this,On)&&ce(this,We,qe).call(this,a)?a.__abortController.abort(new Error("evicted")):(m(this,gn)||m(this,Rt))&&(m(this,gn)&&((o=m(this,ln))==null||o.call(this,a,s,"evict")),m(this,Rt)&&((i=m(this,wt))==null||i.push([a,s,"evict"]))),m(this,es).call(this,n),t&&(m(this,Je)[n]=void 0,m(this,Te)[n]=void 0,m(this,dn).push(n)),m(this,vt)===1?(xe(this,_t,xe(this,bt,0)),m(this,dn).length=0):xe(this,_t,m(this,Et)[n]),m(this,dt).delete(s),Ys(this,vt)._--,n},vs=new WeakSet,oa=function(t,n,s,a){const o=n===void 0?void 0:m(this,Te)[n];if(ce(this,We,qe).call(this,o))return o;const i=new fa,{signal:c}=s;c==null||c.addEventListener("abort",()=>i.abort(c.reason),{signal:i.signal});const l={signal:i.signal,options:s,context:a},u=(w,v=!1)=>{const{aborted:C}=i.signal,A=s.ignoreFetchAbort&&w!==void 0;if(s.status&&(C&&!v?(s.status.fetchAborted=!0,s.status.fetchError=i.signal.reason,A&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),C&&!A&&!v)return p(i.signal.reason);const L=h;return m(this,Te)[n]===h&&(w===void 0?L.__staleWhileFetching?m(this,Te)[n]=L.__staleWhileFetching:ce(this,Cn,Gn).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,w,l.options))),w},f=w=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=w),p(w)),p=w=>{const{aborted:v}=i.signal,C=v&&s.allowStaleOnFetchAbort,A=C||s.allowStaleOnFetchRejection,L=A||s.noDeleteOnFetchRejection,$=h;if(m(this,Te)[n]===h&&(!L||$.__staleWhileFetching===void 0?ce(this,Cn,Gn).call(this,t,"fetch"):C||(m(this,Te)[n]=$.__staleWhileFetching)),A)return s.status&&$.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),$.__staleWhileFetching;if($.__returned===$)throw w},b=(w,v)=>{var A;const C=(A=m(this,ps))==null?void 0:A.call(this,t,o,l);C&&C instanceof Promise&&C.then(L=>w(L===void 0?void 0:L),v),i.signal.addEventListener("abort",()=>{(!s.ignoreFetchAbort||s.allowStaleOnFetchAbort)&&(w(void 0),s.allowStaleOnFetchAbort&&(w=L=>u(L,!0)))})};s.status&&(s.status.fetchDispatched=!0);const h=new Promise(b).then(u,f),x=Object.assign(h,{__abortController:i,__staleWhileFetching:o,__returned:void 0});return n===void 0?(this.set(t,x,mt(me({},l.options),{status:void 0})),n=m(this,dt).get(t)):m(this,Te)[n]=x,x},We=new WeakSet,qe=function(t){if(!m(this,On))return!1;const n=t;return!!n&&n instanceof Promise&&n.hasOwnProperty("__staleWhileFetching")&&n.__abortController instanceof fa},Gs=new WeakSet,Oa=function(t,n){m(this,Vt)[n]=t,m(this,Et)[t]=n},ts=new WeakSet,Ts=function(t){t!==m(this,bt)&&(t===m(this,_t)?xe(this,_t,m(this,Et)[t]):ce(this,Gs,Oa).call(this,m(this,Vt)[t],m(this,Et)[t]),ce(this,Gs,Oa).call(this,m(this,bt),t),xe(this,bt,t))},Cn=new WeakSet,Gn=function(t,n){var a,o,i,c;let s=!1;if(m(this,vt)!==0){const l=m(this,dt).get(t);if(l!==void 0)if(s=!0,m(this,vt)===1)ce(this,Bs,Wa).call(this,n);else{m(this,es).call(this,l);const u=m(this,Te)[l];if(ce(this,We,qe).call(this,u)?u.__abortController.abort(new Error("deleted")):(m(this,gn)||m(this,Rt))&&(m(this,gn)&&((a=m(this,ln))==null||a.call(this,u,t,n)),m(this,Rt)&&((o=m(this,wt))==null||o.push([u,t,n]))),m(this,dt).delete(t),m(this,Je)[l]=void 0,m(this,Te)[l]=void 0,l===m(this,bt))xe(this,bt,m(this,Vt)[l]);else if(l===m(this,_t))xe(this,_t,m(this,Et)[l]);else{const f=m(this,Vt)[l];m(this,Et)[f]=m(this,Et)[l];const p=m(this,Et)[l];m(this,Vt)[p]=m(this,Vt)[l]}Ys(this,vt)._--,m(this,dn).push(l)}}if(m(this,Rt)&&((i=m(this,wt))!=null&&i.length)){const l=m(this,wt);let u;for(;u=l==null?void 0:l.shift();)(c=m(this,cn))==null||c.call(this,...u)}return s},Bs=new WeakSet,Wa=function(t){var n,s,a;for(const o of ce(this,An,Fn).call(this,{allowStale:!0})){const i=m(this,Te)[o];if(ce(this,We,qe).call(this,i))i.__abortController.abort(new Error("deleted"));else{const c=m(this,Je)[o];m(this,gn)&&((n=m(this,ln))==null||n.call(this,i,c,t)),m(this,Rt)&&((s=m(this,wt))==null||s.push([i,c,t]))}}if(m(this,dt).clear(),m(this,Te).fill(void 0),m(this,Je).fill(void 0),m(this,Yt)&&m(this,pn)&&(m(this,Yt).fill(0),m(this,pn).fill(0)),m(this,fn)&&m(this,fn).fill(0),xe(this,_t,0),xe(this,bt,0),m(this,dn).length=0,xe(this,un,0),xe(this,vt,0),m(this,Rt)&&m(this,wt)){const o=m(this,wt);let i;for(;i=o==null?void 0:o.shift();)(a=m(this,cn))==null||a.call(this,...i)}};let Fa=io;function Zp(e,t){for(var n=0;n<t.length;n++){const s=t[n];if(typeof s!="string"&&!Array.isArray(s)){for(const a in s)if(a!=="default"&&!(a in e)){const o=Object.getOwnPropertyDescriptor(s,a);o&&Object.defineProperty(e,a,o.get?o:{enumerable:!0,get:()=>s[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}const Xp=(e,t)=>{const n=Ue("editorId"),{noImgZoomIn:s}=e,a=Ci(()=>{const o=document.querySelectorAll(`#${n}-preview img:not(.not-zoom):not(.medium-zoom-image)`);o.length!==0&&Ep(o,{background:"#00000073"})});Ge(()=>{!s&&e.setting.preview&&a()}),ke([t,ri(e.setting,"preview")],()=>{!s&&e.setting.preview&&a()})},Qp=(e,t,n)=>{const s=Ue("editorId"),a=Ue("rootRef"),o=Ue("usedLanguageText"),i=()=>{a.value.querySelectorAll(`#${s} .${de}-preview .${de}-code`).forEach(u=>{let f=-1;const p=u.querySelector(`.${de}-copy-button`);p&&(p.onclick=b=>{b.preventDefault(),clearTimeout(f);const x=(u.querySelector("input:checked + pre code")||u.querySelector("pre code")).textContent,w=Up(e.formatCopiedText(x)),{text:v,successTips:C,failTips:A}=o.value.copyCode,L=w?C:A;p.dataset.isIcon?p.dataset.tips=L:p.innerHTML=L,f=window.setTimeout(()=>{p.dataset.isIcon?p.dataset.tips=v:p.innerHTML=v},1500)})})},c=()=>{at(i)},l=u=>{u&&at(i)};ke([t,n],c),ke(()=>e.setting.preview,l),ke(()=>e.setting.htmlPreview,l),ke(()=>o.value,i),Ge(i)},qp=e=>{const t=Ue("highlight"),n=Ds(Xt.editorExtensions.highlight.instance);return Ge(()=>{e.noHighlight||n.value||(Rn("link",mt(me({},t.value.css),{rel:"stylesheet",id:`${de}-hlCss`})),Rn("script",mt(me({},t.value.js),{id:`${de}-hljs`,onload(){n.value=window.hljs}}),"hljs"))}),ke(()=>t.value.css,()=>{e.noHighlight||Xt.editorExtensions.highlight.instance||xp("link",mt(me({},t.value.css),{rel:"stylesheet",id:`${de}-hlCss`}))}),n},ia=new Fa({max:1e3,ttl:6e5}),Jp=e=>{const t=Ue("theme"),n=Ue("rootRef"),{editorExtensions:s,editorExtensionsAttrs:a,mermaidConfig:o}=Xt,i=Ds(s.mermaid.instance),c=Ds(-1),l=()=>{const f=i.value;!e.noMermaid&&f&&(f.initialize(o({startOnLoad:!1,theme:t.value==="dark"?"dark":"default"})),c.value=c.value+1)};return ke(()=>t.value,()=>{ia.clear(),l()}),Ge(()=>{var f,p;if(e.noMermaid||i.value)return;const b=s.mermaid.js;/\.mjs/.test(b)?(Rn("link",mt(me({},(f=a.mermaid)==null?void 0:f.js),{rel:"modulepreload",href:b,id:`${de}-mermaid-m`})),Tl(()=>import(b),[],import.meta.url).then(h=>{i.value=h.default,l()})):Rn("script",mt(me({},(p=a.mermaid)==null?void 0:p.js),{src:b,id:`${de}-mermaid`,onload(){i.value=window.mermaid,l()}}),"mermaid")}),{mermaidRef:i,reRenderRef:c,replaceMermaid:()=>H(void 0,null,function*(){if(!e.noMermaid&&i.value){const f=n.value.querySelectorAll(`div.${de}-mermaid`),p=document.createElement("div"),b=document.body.offsetWidth>1366?document.body.offsetWidth:1366,h=document.body.offsetHeight>768?document.body.offsetHeight:768;p.style.width=b+"px",p.style.height=h+"px",p.style.position="fixed",p.style.zIndex="-10000",p.style.top="-10000";let x=f.length;x>0&&document.body.appendChild(p),yield Promise.allSettled(Array.from(f).map(w=>(C=>H(void 0,null,function*(){var A;let L=ia.get(C.innerText);if(!L){const $=Pa();let E={svg:""};try{E=yield i.value.render($,C.innerText,p),L=yield e.sanitizeMermaid(E.svg);const D=document.createElement("p");D.className=`${de}-mermaid`,D.setAttribute("data-processed",""),D.innerHTML=L,(A=D.children[0])==null||A.removeAttribute("height"),ia.set(C.innerText,D.innerHTML),C.dataset.line!==void 0&&(D.dataset.line=C.dataset.line),C.replaceWith(D)}catch(D){}--x===0&&p.remove()}}))(w)))}})}},Kp=e=>{const t=Ds(Xt.editorExtensions.katex.instance);return Ge(()=>{if(e.noKatex||t.value)return;const{editorExtensions:n}=Xt;Rn("script",{src:n.katex.js,id:`${de}-katex`,onload(){t.value=window.katex}},"katex"),Rn("link",{rel:"stylesheet",href:n.katex.css,id:`${de}-katexCss`})}),t},eg=(e,t)=>{const n=e.renderer.rules.fence.bind(e.renderer.rules);e.renderer.rules.fence=(s,a,o,i,c)=>{const l=s[a],u=l.content.trim();if(l.info==="mermaid"){let f;s[a].map&&s[a].level===0&&(f=s[a].map[0],s[a].attrSet("data-line",String(f)));const p=ia.get(u);return p?`<p class="${de}-mermaid" ${f!==void 0?"data-line="+f:""} data-processed>${p}</p>`:`<div class="${de}-mermaid" ${f!==void 0?"data-line="+f:""} data-mermaid-theme=${t.themeRef.value}>${u}</div>`}return n(s,a,o,i,c)}},pa=(e,t)=>{const n=e.attrs?e.attrs.slice():[];return t.forEach(s=>{const a=e.attrIndex(s[0]);a<0?n.push(s):(n[a]=n[a].slice(),n[a][1]+=` ${s[1]}`)}),n},tg=(e,t)=>{const n=[{open:"$",close:"$"},{open:"\\(",close:"\\)"}];let s,a,o;for(const i of n)if(e.src.startsWith(i.open,e.pos)){const c=e.pos+i.open.length;for(s=c;(s=e.src.indexOf(i.close,s))!==-1;){for(o=s-1;e.src[o]==="\\";)o-=1;if((s-o)%2==1)break;s+=i.close.length}return s===-1?(t||(e.pending+=i.open),e.pos=c,!0):s-c===0?(t||(e.pending+=i.open+i.close),e.pos=c+i.close.length,!0):(t||(a=e.push("math_inline","math",0),a.markup=i.open,a.content=e.src.slice(c,s)),e.pos=s+i.close.length,!0)}return!1},ng=(e,t,n,s)=>{const a=[{open:"$$",close:"$$"},{open:"\\[",close:"\\]"}];let o,i,c,l,u=!1,f=e.bMarks[t]+e.tShift[t],p=e.eMarks[t];for(const b of a){if(f+b.open.length>p||e.src.slice(f,f+b.open.length)!==b.open)continue;if(f+=b.open.length,o=e.src.slice(f,p),s)return!0;for(o.trim().slice(-b.close.length)===b.close&&(o=o.trim().slice(0,-b.close.length),u=!0),c=t;!u&&(c++,!(c>=n||(f=e.bMarks[c]+e.tShift[c],p=e.eMarks[c],f<p&&e.tShift[c]<e.blkIndent)));)e.src.slice(f,p).trim().slice(-b.close.length)===b.close&&(l=e.src.slice(0,p).lastIndexOf(b.close),i=e.src.slice(f,l),u=!0);e.line=c+1;const h=e.push("math_block","math",0);return h.block=!0,h.content=(o&&o.trim()?o+`
`:"")+e.getLines(t+1,c,e.tShift[t],!0)+(i&&i.trim()?i:""),h.map=[t,e.line],h.markup=b.open,!0}return!1},sg=(e,{katexRef:t})=>{const n=(a,o,i,c,l)=>{const u=a[o],f={attrs:pa(u,[["class",`${de}-katex-inline`]])};if(t.value){const p=t.value.renderToString(u.content,Xt.katexConfig({throwOnError:!1}));return`<span ${l.renderAttrs(f)} data-processed>${p}</span>`}else return`<span ${l.renderAttrs(f)}>${u.content}</span>`},s=(a,o,i,c,l)=>{const u=a[o],f={attrs:pa(u,[["class",`${de}-katex-block`]])};if(t.value){const p=t.value.renderToString(u.content,Xt.katexConfig({throwOnError:!1,displayMode:!0}));return`<p ${l.renderAttrs(f)} data-processed>${p}</p>`}else return`<p ${l.renderAttrs(f)}>${u.content}</p>`};e.inline.ruler.before("escape","math_inline",tg),e.block.ruler.after("blockquote","math_block",ng,{alt:["paragraph","reference","blockquote","list"]}),e.renderer.rules.math_inline=n,e.renderer.rules.math_block=s},ag=(e,t)=>{t=t||{};const n=3,s=t.marker||"!",a=s.charCodeAt(0),o=s.length;let i="",c="";const l=(f,p,b,h,x)=>{const w=f[p];return w.type==="admonition_open"?f[p].attrPush(["class",`${de}-admonition ${de}-admonition-${w.info}`]):w.type==="admonition_title_open"&&f[p].attrPush(["class",`${de}-admonition-title`]),x.renderToken(f,p,b)},u=f=>{const p=f.trim().split(" ",2);c="",i=p[0],p.length>1&&(c=f.substring(i.length+2))};e.block.ruler.before("code","admonition",(f,p,b,h)=>{let x,w,v,C=!1,A=f.bMarks[p]+f.tShift[p],L=f.eMarks[p];if(a!==f.src.charCodeAt(A))return!1;for(x=A+1;x<=L&&s[(x-A)%o]===f.src[x];x++);const $=Math.floor((x-A)/o);if($!==n)return!1;x-=(x-A)%o;const E=f.src.slice(A,x),D=f.src.slice(x,L);if(u(D),h)return!0;for(w=p;w++,!(w>=b||(A=f.bMarks[w]+f.tShift[w],L=f.eMarks[w],A<L&&f.sCount[w]<f.blkIndent));)if(a===f.src.charCodeAt(A)&&!(f.sCount[w]-f.blkIndent>=4)){for(x=A+1;x<=L&&s[(x-A)%o]===f.src[x];x++);if(!(Math.floor((x-A)/o)<$)&&(x-=(x-A)%o,x=f.skipSpaces(x),!(x<L))){C=!0;break}}const O=f.parentType,T=f.lineMax;return f.parentType="root",f.lineMax=w,v=f.push("admonition_open","div",1),v.markup=E,v.block=!0,v.info=i,v.map=[p,w],c&&(v=f.push("admonition_title_open","p",1),v.markup=E+" "+i,v.map=[p,w],v=f.push("inline","",0),v.content=c,v.map=[p,f.line-1],v.children=[],v=f.push("admonition_title_close","p",-1),v.markup=E+" "+i),f.md.block.tokenize(f,p+1,w),v=f.push("admonition_close","div",-1),v.markup=f.src.slice(A,x),v.block=!0,f.parentType=O,f.lineMax=T,f.line=w+(C?1:0),!0},{alt:["paragraph","reference","blockquote","list"]}),e.renderer.rules.admonition_open=l,e.renderer.rules.admonition_title_open=l,e.renderer.rules.admonition_title_close=l,e.renderer.rules.admonition_close=l},og=(e,t)=>{e.renderer.rules.heading_open=(n,s)=>{var a;const o=n[s],i=((a=n[s+1].children)==null?void 0:a.reduce((l,u)=>l+(["text","code_inline","math_inline"].includes(u.type)&&u.content||""),""))||"",c=o.markup.length;return t.headsRef.value.push({text:i,level:c}),o.map&&o.level===0&&o.attrSet("id",t.mdHeadingId(i,c,t.headsRef.value.length)),e.renderer.renderToken(n,s,t)},e.renderer.rules.heading_close=(n,s,a,o,i)=>i.renderToken(n,s,a)},ig=(e,t)=>{if(typeof t[e]=="string")return t[e];const n=`<i class="${de}-iconfont ${de}-icon-${e}"></i>`;switch(Xt.iconfontType){case"svg":return`<svg class="${de}-icon" aria-hidden="true"><use xlink:href="#${de}-icon-${e}"></use></svg>`;default:return n}},rg=ig,lg=(e,t)=>{const n=e.renderer.rules.fence,s=e.utils.unescapeAll,a=/\[(\w*)(?::([\w ]*))?\]/,o=/::(open|close)/,i=p=>p.info?s(p.info).trim():"",c=p=>{const b=i(p),[h=null,x=""]=(a.exec(b)||[]).slice(1);return[h,x]},l=p=>{const b=i(p);return b?b.split(/(\s+)/g)[0]:""},u=p=>{const b=p.info.match(o)||[],h=b[1]==="open"||b[1]!=="close"&&t.codeFoldable&&p.content.trim().split(`
`).length<t.autoFoldThreshold,x=b[1]||t.codeFoldable?"details":"div",w=b[1]||t.codeFoldable?"summary":"div";return{open:h,tagContainer:x,tagHeader:w}},f=(p,b,h,x,w)=>{var v;if(p[b].hidden)return"";const C=(v=t.usedLanguageTextRef.value)==null?void 0:v.copyCode.text,A=t.customIconRef.value.copy||C,L=!!t.customIconRef.value.copy,$=`<span class="${de}-collapse-tips">${rg("collapse-tips",t.customIconRef.value)}</span>`,[E]=c(p[b]);if(E===null){const{open:z,tagContainer:Z,tagHeader:ee}=u(p[b]),Q=[["class",`${de}-code`]];z&&Q.push(["open",""]);const le={attrs:pa(p[b],Q)};p[b].info=p[b].info.replace(o,"");const Ae=n(p,b,h,x,w);return`
        <${Z} ${w.renderAttrs(le)}>
          <${ee} class="${de}-code-head">
            <div class="${de}-code-flag"><span></span><span></span><span></span></div>
            <div class="${de}-code-action">
              <span class="${de}-code-lang">${p[b].info.trim()}</span>
              <span class="${de}-copy-button" data-tips="${C}"${L?" data-is-icon=true":""}>${A}</span>
              ${Z==="details"?$:""}
            </div>
          </${ee}>
          ${Ae}
        </${Z}>
      `}let D,O,T,Y,P="",se="",G="";const{open:F,tagContainer:J,tagHeader:V}=u(p[b]),te=[["class",`${de}-code`]];F&&te.push(["open",""]);const j={attrs:pa(p[b],te)};for(let z=b;z<p.length&&(D=p[z],[O,T]=c(D),O===E);z++){D.info=D.info.replace(a,"").replace(o,""),D.hidden=!0;const Z=`${de}-codetab-${t.editorId}-${b}-${z-b}`;Y=z-b>0?"":"checked",P+=`
        <li>
          <input
            type="radio"
            id="label-${de}-codetab-label-1-${t.editorId}-${b}-${z-b}"
            name="${de}-codetab-label-${t.editorId}-${b}"
            class="${Z}"
            ${Y}
          >
          <label
            for="label-${de}-codetab-label-1-${t.editorId}-${b}-${z-b}"
            onclick="this.getRootNode().querySelectorAll('.${Z}').forEach(e => e.click())"
          >
            ${T||l(D)}
          </label>
        </li>`,se+=`
        <div role="tabpanel">
          <input
            type="radio"
            name="${de}-codetab-pre-${t.editorId}-${b}"
            class="${Z}"
            ${Y}
            role="presentation">
          ${n(p,z,h,x,w)}
        </div>`,G+=`
        <input
          type="radio"
          name="${de}-codetab-lang-${t.editorId}-${b}"
          class="${Z}"
          ${Y}
          role="presentation">
        <span class=${de}-code-lang role="note">${l(D)}</span>`}return`
      <${J} ${w.renderAttrs(j)}>
        <${V} class="${de}-code-head">
          <div class="${de}-code-flag">
            <ul class="${de}-codetab-label" role="tablist">${P}</ul>
          </div>
          <div class="${de}-code-action">
            <span class="${de}-codetab-lang">${G}</span>
            <span class="${de}-copy-button" data-tips="${C}"${L?" data-is-icon=true":""}>${A}</span>
            ${J==="details"?$:""}
          </div>
        </${V}>
        ${se}
      </${J}>
    `};e.renderer.rules.fence=f,e.renderer.rules.code_block=f};function cg(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ha={exports:{}},tt={},Va={exports:{}},as={};function zi(){var e={};return e["align-content"]=!1,e["align-items"]=!1,e["align-self"]=!1,e["alignment-adjust"]=!1,e["alignment-baseline"]=!1,e.all=!1,e["anchor-point"]=!1,e.animation=!1,e["animation-delay"]=!1,e["animation-direction"]=!1,e["animation-duration"]=!1,e["animation-fill-mode"]=!1,e["animation-iteration-count"]=!1,e["animation-name"]=!1,e["animation-play-state"]=!1,e["animation-timing-function"]=!1,e.azimuth=!1,e["backface-visibility"]=!1,e.background=!0,e["background-attachment"]=!0,e["background-clip"]=!0,e["background-color"]=!0,e["background-image"]=!0,e["background-origin"]=!0,e["background-position"]=!0,e["background-repeat"]=!0,e["background-size"]=!0,e["baseline-shift"]=!1,e.binding=!1,e.bleed=!1,e["bookmark-label"]=!1,e["bookmark-level"]=!1,e["bookmark-state"]=!1,e.border=!0,e["border-bottom"]=!0,e["border-bottom-color"]=!0,e["border-bottom-left-radius"]=!0,e["border-bottom-right-radius"]=!0,e["border-bottom-style"]=!0,e["border-bottom-width"]=!0,e["border-collapse"]=!0,e["border-color"]=!0,e["border-image"]=!0,e["border-image-outset"]=!0,e["border-image-repeat"]=!0,e["border-image-slice"]=!0,e["border-image-source"]=!0,e["border-image-width"]=!0,e["border-left"]=!0,e["border-left-color"]=!0,e["border-left-style"]=!0,e["border-left-width"]=!0,e["border-radius"]=!0,e["border-right"]=!0,e["border-right-color"]=!0,e["border-right-style"]=!0,e["border-right-width"]=!0,e["border-spacing"]=!0,e["border-style"]=!0,e["border-top"]=!0,e["border-top-color"]=!0,e["border-top-left-radius"]=!0,e["border-top-right-radius"]=!0,e["border-top-style"]=!0,e["border-top-width"]=!0,e["border-width"]=!0,e.bottom=!1,e["box-decoration-break"]=!0,e["box-shadow"]=!0,e["box-sizing"]=!0,e["box-snap"]=!0,e["box-suppress"]=!0,e["break-after"]=!0,e["break-before"]=!0,e["break-inside"]=!0,e["caption-side"]=!1,e.chains=!1,e.clear=!0,e.clip=!1,e["clip-path"]=!1,e["clip-rule"]=!1,e.color=!0,e["color-interpolation-filters"]=!0,e["column-count"]=!1,e["column-fill"]=!1,e["column-gap"]=!1,e["column-rule"]=!1,e["column-rule-color"]=!1,e["column-rule-style"]=!1,e["column-rule-width"]=!1,e["column-span"]=!1,e["column-width"]=!1,e.columns=!1,e.contain=!1,e.content=!1,e["counter-increment"]=!1,e["counter-reset"]=!1,e["counter-set"]=!1,e.crop=!1,e.cue=!1,e["cue-after"]=!1,e["cue-before"]=!1,e.cursor=!1,e.direction=!1,e.display=!0,e["display-inside"]=!0,e["display-list"]=!0,e["display-outside"]=!0,e["dominant-baseline"]=!1,e.elevation=!1,e["empty-cells"]=!1,e.filter=!1,e.flex=!1,e["flex-basis"]=!1,e["flex-direction"]=!1,e["flex-flow"]=!1,e["flex-grow"]=!1,e["flex-shrink"]=!1,e["flex-wrap"]=!1,e.float=!1,e["float-offset"]=!1,e["flood-color"]=!1,e["flood-opacity"]=!1,e["flow-from"]=!1,e["flow-into"]=!1,e.font=!0,e["font-family"]=!0,e["font-feature-settings"]=!0,e["font-kerning"]=!0,e["font-language-override"]=!0,e["font-size"]=!0,e["font-size-adjust"]=!0,e["font-stretch"]=!0,e["font-style"]=!0,e["font-synthesis"]=!0,e["font-variant"]=!0,e["font-variant-alternates"]=!0,e["font-variant-caps"]=!0,e["font-variant-east-asian"]=!0,e["font-variant-ligatures"]=!0,e["font-variant-numeric"]=!0,e["font-variant-position"]=!0,e["font-weight"]=!0,e.grid=!1,e["grid-area"]=!1,e["grid-auto-columns"]=!1,e["grid-auto-flow"]=!1,e["grid-auto-rows"]=!1,e["grid-column"]=!1,e["grid-column-end"]=!1,e["grid-column-start"]=!1,e["grid-row"]=!1,e["grid-row-end"]=!1,e["grid-row-start"]=!1,e["grid-template"]=!1,e["grid-template-areas"]=!1,e["grid-template-columns"]=!1,e["grid-template-rows"]=!1,e["hanging-punctuation"]=!1,e.height=!0,e.hyphens=!1,e.icon=!1,e["image-orientation"]=!1,e["image-resolution"]=!1,e["ime-mode"]=!1,e["initial-letters"]=!1,e["inline-box-align"]=!1,e["justify-content"]=!1,e["justify-items"]=!1,e["justify-self"]=!1,e.left=!1,e["letter-spacing"]=!0,e["lighting-color"]=!0,e["line-box-contain"]=!1,e["line-break"]=!1,e["line-grid"]=!1,e["line-height"]=!1,e["line-snap"]=!1,e["line-stacking"]=!1,e["line-stacking-ruby"]=!1,e["line-stacking-shift"]=!1,e["line-stacking-strategy"]=!1,e["list-style"]=!0,e["list-style-image"]=!0,e["list-style-position"]=!0,e["list-style-type"]=!0,e.margin=!0,e["margin-bottom"]=!0,e["margin-left"]=!0,e["margin-right"]=!0,e["margin-top"]=!0,e["marker-offset"]=!1,e["marker-side"]=!1,e.marks=!1,e.mask=!1,e["mask-box"]=!1,e["mask-box-outset"]=!1,e["mask-box-repeat"]=!1,e["mask-box-slice"]=!1,e["mask-box-source"]=!1,e["mask-box-width"]=!1,e["mask-clip"]=!1,e["mask-image"]=!1,e["mask-origin"]=!1,e["mask-position"]=!1,e["mask-repeat"]=!1,e["mask-size"]=!1,e["mask-source-type"]=!1,e["mask-type"]=!1,e["max-height"]=!0,e["max-lines"]=!1,e["max-width"]=!0,e["min-height"]=!0,e["min-width"]=!0,e["move-to"]=!1,e["nav-down"]=!1,e["nav-index"]=!1,e["nav-left"]=!1,e["nav-right"]=!1,e["nav-up"]=!1,e["object-fit"]=!1,e["object-position"]=!1,e.opacity=!1,e.order=!1,e.orphans=!1,e.outline=!1,e["outline-color"]=!1,e["outline-offset"]=!1,e["outline-style"]=!1,e["outline-width"]=!1,e.overflow=!1,e["overflow-wrap"]=!1,e["overflow-x"]=!1,e["overflow-y"]=!1,e.padding=!0,e["padding-bottom"]=!0,e["padding-left"]=!0,e["padding-right"]=!0,e["padding-top"]=!0,e.page=!1,e["page-break-after"]=!1,e["page-break-before"]=!1,e["page-break-inside"]=!1,e["page-policy"]=!1,e.pause=!1,e["pause-after"]=!1,e["pause-before"]=!1,e.perspective=!1,e["perspective-origin"]=!1,e.pitch=!1,e["pitch-range"]=!1,e["play-during"]=!1,e.position=!1,e["presentation-level"]=!1,e.quotes=!1,e["region-fragment"]=!1,e.resize=!1,e.rest=!1,e["rest-after"]=!1,e["rest-before"]=!1,e.richness=!1,e.right=!1,e.rotation=!1,e["rotation-point"]=!1,e["ruby-align"]=!1,e["ruby-merge"]=!1,e["ruby-position"]=!1,e["shape-image-threshold"]=!1,e["shape-outside"]=!1,e["shape-margin"]=!1,e.size=!1,e.speak=!1,e["speak-as"]=!1,e["speak-header"]=!1,e["speak-numeral"]=!1,e["speak-punctuation"]=!1,e["speech-rate"]=!1,e.stress=!1,e["string-set"]=!1,e["tab-size"]=!1,e["table-layout"]=!1,e["text-align"]=!0,e["text-align-last"]=!0,e["text-combine-upright"]=!0,e["text-decoration"]=!0,e["text-decoration-color"]=!0,e["text-decoration-line"]=!0,e["text-decoration-skip"]=!0,e["text-decoration-style"]=!0,e["text-emphasis"]=!0,e["text-emphasis-color"]=!0,e["text-emphasis-position"]=!0,e["text-emphasis-style"]=!0,e["text-height"]=!0,e["text-indent"]=!0,e["text-justify"]=!0,e["text-orientation"]=!0,e["text-overflow"]=!0,e["text-shadow"]=!0,e["text-space-collapse"]=!0,e["text-transform"]=!0,e["text-underline-position"]=!0,e["text-wrap"]=!0,e.top=!1,e.transform=!1,e["transform-origin"]=!1,e["transform-style"]=!1,e.transition=!1,e["transition-delay"]=!1,e["transition-duration"]=!1,e["transition-property"]=!1,e["transition-timing-function"]=!1,e["unicode-bidi"]=!1,e["vertical-align"]=!1,e.visibility=!1,e["voice-balance"]=!1,e["voice-duration"]=!1,e["voice-family"]=!1,e["voice-pitch"]=!1,e["voice-range"]=!1,e["voice-rate"]=!1,e["voice-stress"]=!1,e["voice-volume"]=!1,e.volume=!1,e["white-space"]=!1,e.widows=!1,e.width=!0,e["will-change"]=!1,e["word-break"]=!0,e["word-spacing"]=!0,e["word-wrap"]=!0,e["wrap-flow"]=!1,e["wrap-through"]=!1,e["writing-mode"]=!1,e["z-index"]=!1,e}function ug(e,t,n){}function dg(e,t,n){}var fg=/javascript\s*\:/img;function pg(e,t){return fg.test(t)?"":t}as.whiteList=zi();as.getDefaultWhiteList=zi;as.onAttr=ug;as.onIgnoreAttr=dg;as.safeAttrValue=pg;var gg={indexOf:function(e,t){var n,s;if(Array.prototype.indexOf)return e.indexOf(t);for(n=0,s=e.length;n<s;n++)if(e[n]===t)return n;return-1},forEach:function(e,t,n){var s,a;if(Array.prototype.forEach)return e.forEach(t,n);for(s=0,a=e.length;s<a;s++)t.call(n,e[s],s,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(e){return String.prototype.trimRight?e.trimRight():e.replace(/(\s*$)/g,"")}},Cs=gg;function hg(e,t){e=Cs.trimRight(e),e[e.length-1]!==";"&&(e+=";");var n=e.length,s=!1,a=0,o=0,i="";function c(){if(!s){var f=Cs.trim(e.slice(a,o)),p=f.indexOf(":");if(p!==-1){var b=Cs.trim(f.slice(0,p)),h=Cs.trim(f.slice(p+1));if(b){var x=t(a,i.length,b,h,f);x&&(i+=x+"; ")}}}a=o+1}for(;o<n;o++){var l=e[o];if(l==="/"&&e[o+1]==="*"){var u=e.indexOf("*/",o+2);if(u===-1)break;o=u+1,a=o+1,s=!1}else l==="("?s=!0:l===")"?s=!1:l===";"?s||c():l===`
`&&c()}return Cs.trim(i)}var mg=hg,qs=as,vg=mg;function Ro(e){return e==null}function yg(e){var t={};for(var n in e)t[n]=e[n];return t}function Di(e){e=yg(e||{}),e.whiteList=e.whiteList||qs.whiteList,e.onAttr=e.onAttr||qs.onAttr,e.onIgnoreAttr=e.onIgnoreAttr||qs.onIgnoreAttr,e.safeAttrValue=e.safeAttrValue||qs.safeAttrValue,this.options=e}Di.prototype.process=function(e){if(e=e||"",e=e.toString(),!e)return"";var t=this,n=t.options,s=n.whiteList,a=n.onAttr,o=n.onIgnoreAttr,i=n.safeAttrValue,c=vg(e,function(l,u,f,p,b){var h=s[f],x=!1;if(h===!0?x=h:typeof h=="function"?x=h(p):h instanceof RegExp&&(x=h.test(p)),x!==!0&&(x=!1),p=i(f,p),!!p){var w={position:u,sourcePosition:l,source:b,isWhite:x};if(x){var v=a(f,p,w);return Ro(v)?f+":"+p:v}else{var v=o(f,p,w);if(!Ro(v))return v}}});return c};var bg=Di;(function(e,t){var n=as,s=bg;function a(i,c){var l=new s(c);return l.process(i)}t=e.exports=a,t.FilterCSS=s;for(var o in n)t[o]=n[o];typeof window!="undefined"&&(window.filterCSS=e.exports)})(Va,Va.exports);var eo=Va.exports,to={indexOf:function(e,t){var n,s;if(Array.prototype.indexOf)return e.indexOf(t);for(n=0,s=e.length;n<s;n++)if(e[n]===t)return n;return-1},forEach:function(e,t,n){var s,a;if(Array.prototype.forEach)return e.forEach(t,n);for(s=0,a=e.length;s<a;s++)t.call(n,e[s],s,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(e){var t=/\s|\n|\t/,n=t.exec(e);return n?n.index:-1}},wg=eo.FilterCSS,xg=eo.getDefaultWhiteList,ga=to;function ji(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var Pi=new wg;function kg(e,t,n){}function Ag(e,t,n){}function Cg(e,t,n){}function Ig(e,t,n){}function $i(e){return e.replace(_g,"&lt;").replace(Tg,"&gt;")}function Sg(e,t,n,s){if(n=Oi(n),t==="href"||t==="src"){if(n=ga.trim(n),n==="#")return"#";if(!(n.substr(0,7)==="http://"||n.substr(0,8)==="https://"||n.substr(0,7)==="mailto:"||n.substr(0,4)==="tel:"||n.substr(0,11)==="data:image/"||n.substr(0,6)==="ftp://"||n.substr(0,2)==="./"||n.substr(0,3)==="../"||n[0]==="#"||n[0]==="/"))return""}else if(t==="background"){if(Js.lastIndex=0,Js.test(n))return""}else if(t==="style"){if(zo.lastIndex=0,zo.test(n)||(Do.lastIndex=0,Do.test(n)&&(Js.lastIndex=0,Js.test(n))))return"";s!==!1&&(s=s||Pi,n=s.process(n))}return n=Wi(n),n}var _g=/</g,Tg=/>/g,Lg=/"/g,Mg=/&quot;/g,Eg=/&#([a-zA-Z0-9]*);?/gim,Rg=/&colon;?/gim,zg=/&newline;?/gim,Js=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,zo=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,Do=/u\s*r\s*l\s*\(.*/gi;function Ui(e){return e.replace(Lg,"&quot;")}function Ni(e){return e.replace(Mg,'"')}function Fi(e){return e.replace(Eg,function(n,s){return s[0]==="x"||s[0]==="X"?String.fromCharCode(parseInt(s.substr(1),16)):String.fromCharCode(parseInt(s,10))})}function Gi(e){return e.replace(Rg,":").replace(zg," ")}function Bi(e){for(var t="",n=0,s=e.length;n<s;n++)t+=e.charCodeAt(n)<32?" ":e.charAt(n);return ga.trim(t)}function Oi(e){return e=Ni(e),e=Fi(e),e=Gi(e),e=Bi(e),e}function Wi(e){return e=Ui(e),e=$i(e),e}function Dg(){return""}function jg(e,t){typeof t!="function"&&(t=function(){});var n=!Array.isArray(e);function s(i){return n?!0:ga.indexOf(e,i)!==-1}var a=[],o=!1;return{onIgnoreTag:function(i,c,l){if(s(i))if(l.isClosing){var u="[/removed]",f=l.position+u.length;return a.push([o!==!1?o:l.position,f]),o=!1,u}else return o||(o=l.position),"[removed]";else return t(i,c,l)},remove:function(i){var c="",l=0;return ga.forEach(a,function(u){c+=i.slice(l,u[0]),l=u[1]}),c+=i.slice(l),c}}}function Pg(e){for(var t="",n=0;n<e.length;){var s=e.indexOf("<!--",n);if(s===-1){t+=e.slice(n);break}t+=e.slice(n,s);var a=e.indexOf("-->",s);if(a===-1)break;n=a+3}return t}function $g(e){var t=e.split("");return t=t.filter(function(n){var s=n.charCodeAt(0);return s===127?!1:s<=31?s===10||s===13:!0}),t.join("")}tt.whiteList=ji();tt.getDefaultWhiteList=ji;tt.onTag=kg;tt.onIgnoreTag=Ag;tt.onTagAttr=Cg;tt.onIgnoreTagAttr=Ig;tt.safeAttrValue=Sg;tt.escapeHtml=$i;tt.escapeQuote=Ui;tt.unescapeQuote=Ni;tt.escapeHtmlEntities=Fi;tt.escapeDangerHtml5Entities=Gi;tt.clearNonPrintableCharacter=Bi;tt.friendlyAttrValue=Oi;tt.escapeAttrValue=Wi;tt.onIgnoreTagStripAll=Dg;tt.StripTagBody=jg;tt.stripCommentTag=Pg;tt.stripBlankChar=$g;tt.attributeWrapSign='"';tt.cssFilter=Pi;tt.getDefaultCSSWhiteList=xg;var ba={},Bn=to;function Ug(e){var t=Bn.spaceIndex(e),n;return t===-1?n=e.slice(1,-1):n=e.slice(1,t+1),n=Bn.trim(n).toLowerCase(),n.slice(0,1)==="/"&&(n=n.slice(1)),n.slice(-1)==="/"&&(n=n.slice(0,-1)),n}function Ng(e){return e.slice(0,2)==="</"}function Fg(e,t,n){var s="",a=0,o=!1,i=!1,c=0,l=e.length,u="",f="";e:for(c=0;c<l;c++){var p=e.charAt(c);if(o===!1){if(p==="<"){o=c;continue}}else if(i===!1){if(p==="<"){s+=n(e.slice(a,c)),o=c,a=c;continue}if(p===">"||c===l-1){s+=n(e.slice(a,o)),f=e.slice(o,c+1),u=Ug(f),s+=t(o,s.length,u,f,Ng(f)),a=c+1,o=!1;continue}if(p==='"'||p==="'")for(var b=1,h=e.charAt(c-b);h.trim()===""||h==="=";){if(h==="="){i=p;continue e}h=e.charAt(c-++b)}}else if(p===i){i=!1;continue}}return a<l&&(s+=n(e.substr(a))),s}var Gg=/[^a-zA-Z0-9\\_:.-]/gim;function Bg(e,t){var n=0,s=0,a=[],o=!1,i=e.length;function c(b,h){if(b=Bn.trim(b),b=b.replace(Gg,"").toLowerCase(),!(b.length<1)){var x=t(b,h||"");x&&a.push(x)}}for(var l=0;l<i;l++){var u=e.charAt(l),f,p;if(o===!1&&u==="="){o=e.slice(n,l),n=l+1,s=e.charAt(n)==='"'||e.charAt(n)==="'"?n:Wg(e,l+1);continue}if(o!==!1&&l===s){if(p=e.indexOf(u,l+1),p===-1)break;f=Bn.trim(e.slice(s+1,p)),c(o,f),o=!1,l=p,n=l+1;continue}if(/\s|\n|\t/.test(u))if(e=e.replace(/\s|\n|\t/g," "),o===!1)if(p=Og(e,l),p===-1){f=Bn.trim(e.slice(n,l)),c(f),o=!1,n=l+1;continue}else{l=p-1;continue}else if(p=Hg(e,l-1),p===-1){f=Bn.trim(e.slice(n,l)),f=jo(f),c(o,f),o=!1,n=l+1;continue}else continue}return n<e.length&&(o===!1?c(e.slice(n)):c(o,jo(Bn.trim(e.slice(n))))),Bn.trim(a.join(" "))}function Og(e,t){for(;t<e.length;t++){var n=e[t];if(n!==" ")return n==="="?t:-1}}function Wg(e,t){for(;t<e.length;t++){var n=e[t];if(n!==" ")return n==="'"||n==='"'?t:-1}}function Hg(e,t){for(;t>0;t--){var n=e[t];if(n!==" ")return n==="="?t:-1}}function Vg(e){return e[0]==='"'&&e[e.length-1]==='"'||e[0]==="'"&&e[e.length-1]==="'"}function jo(e){return Vg(e)?e.substr(1,e.length-2):e}ba.parseTag=Fg;ba.parseAttr=Bg;var Yg=eo.FilterCSS,nn=tt,Hi=ba,Zg=Hi.parseTag,Xg=Hi.parseAttr,ra=to;function Ks(e){return e==null}function Qg(e){var t=ra.spaceIndex(e);if(t===-1)return{html:"",closing:e[e.length-2]==="/"};e=ra.trim(e.slice(t+1,-1));var n=e[e.length-1]==="/";return n&&(e=ra.trim(e.slice(0,-1))),{html:e,closing:n}}function qg(e){var t={};for(var n in e)t[n]=e[n];return t}function Jg(e){var t={};for(var n in e)Array.isArray(e[n])?t[n.toLowerCase()]=e[n].map(function(s){return s.toLowerCase()}):t[n.toLowerCase()]=e[n];return t}function Vi(e){e=qg(e||{}),e.stripIgnoreTag&&(e.onIgnoreTag,e.onIgnoreTag=nn.onIgnoreTagStripAll),e.whiteList||e.allowList?e.whiteList=Jg(e.whiteList||e.allowList):e.whiteList=nn.whiteList,this.attributeWrapSign=e.singleQuotedAttributeValue===!0?"'":nn.attributeWrapSign,e.onTag=e.onTag||nn.onTag,e.onTagAttr=e.onTagAttr||nn.onTagAttr,e.onIgnoreTag=e.onIgnoreTag||nn.onIgnoreTag,e.onIgnoreTagAttr=e.onIgnoreTagAttr||nn.onIgnoreTagAttr,e.safeAttrValue=e.safeAttrValue||nn.safeAttrValue,e.escapeHtml=e.escapeHtml||nn.escapeHtml,this.options=e,e.css===!1?this.cssFilter=!1:(e.css=e.css||{},this.cssFilter=new Yg(e.css))}Vi.prototype.process=function(e){if(e=e||"",e=e.toString(),!e)return"";var t=this,n=t.options,s=n.whiteList,a=n.onTag,o=n.onIgnoreTag,i=n.onTagAttr,c=n.onIgnoreTagAttr,l=n.safeAttrValue,u=n.escapeHtml,f=t.attributeWrapSign,p=t.cssFilter;n.stripBlankChar&&(e=nn.stripBlankChar(e)),n.allowCommentTag||(e=nn.stripCommentTag(e));var b=!1;n.stripIgnoreTagBody&&(b=nn.StripTagBody(n.stripIgnoreTagBody,o),o=b.onIgnoreTag);var h=Zg(e,function(x,w,v,C,A){var L={sourcePosition:x,position:w,isClosing:A,isWhite:Object.prototype.hasOwnProperty.call(s,v)},$=a(v,C,L);if(!Ks($))return $;if(L.isWhite){if(L.isClosing)return"</"+v+">";var E=Qg(C),D=s[v],O=Xg(E.html,function(T,Y){var P=ra.indexOf(D,T)!==-1,se=i(v,T,Y,P);return Ks(se)?P?(Y=l(v,T,Y,p),Y?T+"="+f+Y+f:T):(se=c(v,T,Y,P),Ks(se)?void 0:se):se});return C="<"+v,O&&(C+=" "+O),E.closing&&(C+=" /"),C+=">",C}else return $=o(v,C,L),Ks($)?u(C):$},u);return b&&(h=b.remove(h)),h};var Kg=Vi;(function(e,t){var n=tt,s=ba,a=Kg;function o(c,l){var u=new a(l);return u.process(c)}t=e.exports=o,t.filterXSS=o,t.FilterXSS=a,function(){for(var c in n)t[c]=n[c];for(var l in s)t[l]=s[l]}(),typeof window!="undefined"&&(window.filterXSS=e.exports);function i(){return typeof self!="undefined"&&typeof DedicatedWorkerGlobalScope!="undefined"&&self instanceof DedicatedWorkerGlobalScope}i()&&(self.filterXSS=e.exports)})(Ha,Ha.exports);var Es=Ha.exports;const eh=cg(Es),th=Zp({__proto__:null,default:eh},[Es]),Po={img:["class"],input:["class","disabled","type","checked"],iframe:["class","width","height","src","title","border","frameborder","framespacing","allow","allowfullscreen"]},nh=(e,t)=>{const{extendedWhiteList:n={},xss:s={}}=t;let a;if(typeof s=="function")a=new Es.FilterXSS(s(th));else{const o=Es.getDefaultWhiteList();[...Object.keys(n),...Object.keys(Po)].forEach(c=>{const l=o[c]||[],u=Po[c]||[],f=n[c]||[];o[c]=[...new Set([...l,...u,...f])]}),a=new Es.FilterXSS(me({whiteList:o},s))}e.core.ruler.after("linkify","xss",o=>{for(let i=0;i<o.tokens.length;i++){const c=o.tokens[i];switch(c.type){case"html_block":{c.content=a.process(c.content);break}case"inline":{(c.children||[]).forEach(u=>{u.type==="html_inline"&&(u.content=a.process(u.content))});break}}}})},$o=(e,t,n)=>{const s=e.attrIndex(t),a=[t,n];s<0?e.attrPush(a):(e.attrs=e.attrs||[],e.attrs[s]=a)},sh=e=>e.type==="inline",ah=e=>e.type==="paragraph_open",oh=e=>e.type==="list_item_open",ih=e=>e.content.indexOf("[ ] ")===0||e.content.indexOf("[x] ")===0||e.content.indexOf("[X] ")===0,rh=(e,t)=>sh(e[t])&&ah(e[t-1])&&oh(e[t-2])&&ih(e[t]),lh=(e,t)=>{const n=e[t].level-1;for(let s=t-1;s>=0;s--)if(e[s].level===n)return s;return-1},ch=e=>{const t=new e("html_inline","",0);return t.content="<label>",t},uh=e=>{const t=new e("html_inline","",0);return t.content="</label>",t},dh=(e,t,n)=>{const s=new n("html_inline","",0);return s.content='<label class="task-list-item-label" for="'+t+'">'+e+"</label>",s.attrs=[{for:t}],s},fh=(e,t,n)=>{const s=new t("html_inline","",0),a=n.enabled?" ":' disabled="" ';return e.content.indexOf("[ ] ")===0?s.content='<input class="task-list-item-checkbox"'+a+'type="checkbox">':(e.content.indexOf("[x] ")===0||e.content.indexOf("[X] ")===0)&&(s.content='<input class="task-list-item-checkbox" checked=""'+a+'type="checkbox">'),s},ph=(e,t,n)=>{if(e.children=e.children||[],e.children.unshift(fh(e,t,n)),e.children[1].content=e.children[1].content.slice(3),e.content=e.content.slice(3),n.label)if(n.labelAfter){e.children.pop();const s="task-item-"+Math.ceil(Math.random()*(1e4*1e3)-1e3);e.children[0].content=e.children[0].content.slice(0,-1)+' id="'+s+'">',e.children.push(dh(e.content,s,t))}else e.children.unshift(ch(t)),e.children.push(uh(t))},gh=(e,t={})=>{e.core.ruler.after("inline","github-task-lists",n=>{const s=n.tokens;for(let a=2;a<s.length;a++)rh(s,a)&&(ph(s[a],n.Token,t),$o(s[a-2],"class","task-list-item"+(t.enabled?" enabled":" ")),$o(s[lh(s,a-2)],"class","contains-task-list"))})},hh=e=>{e.core.ruler.push("init-line-number",t=>(t.tokens.forEach(n=>{n.map&&(n.attrs||(n.attrs=[]),n.attrs.push(["data-line",n.map[0].toString()]))}),!0))},mh=(e,t)=>{const{editorConfig:n,markdownItConfig:s,markdownItPlugins:a}=Xt,o=Ue("editorId"),i=Ue("language"),c=Ue("usedLanguageText"),l=Ue("showCodeRowNumber"),u=Ue("theme"),f=Ue("customIcon"),p=Ue("rootRef"),b=S([]),h=qp(e),x=Kp(e),{reRenderRef:w,replaceMermaid:v}=Jp(e),C=vi({html:!0,breaks:!0,linkify:!0});s(C,{editorId:o});const A=[{type:"image",plugin:Fp,options:{figcaption:!0,classes:"md-zoom"}},{type:"admonition",plugin:ag,options:{}},{type:"taskList",plugin:gh,options:{}},{type:"heading",plugin:og,options:{mdHeadingId:e.mdHeadingId,headsRef:b}},{type:"code",plugin:lg,options:{editorId:o,usedLanguageTextRef:c,codeFoldable:e.codeFoldable,autoFoldThreshold:e.autoFoldThreshold,customIconRef:f}},{type:"xss",plugin:nh,options:{}},{type:"sub",plugin:Op,options:{}},{type:"sup",plugin:Vp,options:{}}];e.noKatex||A.push({type:"katex",plugin:sg,options:{katexRef:x}}),e.noMermaid||A.push({type:"mermaid",plugin:eg,options:{themeRef:u}}),a(A,{editorId:o}).forEach(Y=>{C.use(Y.plugin,Y.options)});const L=C.options.highlight;C.set({highlight:(Y,P,se)=>{if(L){const J=L(Y,P,se);if(J)return J}let G;!e.noHighlight&&h.value?h.value.getLanguage(P)?G=h.value.highlight(Y,{language:P,ignoreIllegals:!0}).value:G=h.value.highlightAuto(Y).value:G=C.utils.escapeHtml(Y);const F=l?Ap(G.replace(/^\n+|\n+$/g,""),Y.replace(/^\n+|\n+$/g,"")):`<span class="${de}-code-block">${G.replace(/^\n+|\n+$/g,"")}</span>`;return`<pre><code class="language-${P}" language=${P}>${F}</code></pre>`}}),hh(C);const $=S(`_article-key_${Pa()}`),E=S(e.sanitize(C.render(e.modelValue))),D=()=>{Mn.emit(o,yp,E.value),e.onHtmlChanged(E.value),e.onGetCatalog(b.value),Mn.emit(o,Ta,b.value),at(()=>{v().then(()=>{So(p.value.querySelectorAll(`#${o} .${de}-mermaid`))})})};Ge(D);const O=()=>{b.value=[],E.value=e.sanitize(C.render(e.modelValue)),D()},T=k(()=>(e.noKatex||x.value)&&(e.noHighlight||h.value));return ke([ri(e,"modelValue"),T,w,i],Ci(O,t?0:n.renderDelay)),ke(()=>e.setting.preview,()=>{e.setting.preview&&at(()=>{v().then(()=>{So(p.value.querySelectorAll(`#${o} .${de}-mermaid`))}),Mn.emit(o,Ta,b.value)})}),Ge(()=>{Mn.on(o,{name:bp,callback(){Mn.emit(o,Ta,b.value)}}),Mn.on(o,{name:Si,callback:()=>{$.value=`_article-key_${Pa()}`,O()}})}),{html:E,key:$}},Uo={checked:{regexp:/- \[x\]/,value:"- [ ]"},unChecked:{regexp:/- \[\s\]/,value:"- [x]"}},vh=(e,t)=>{const n=Ue("editorId"),s=Ue("rootRef");let a=()=>{};const o=()=>{if(!s.value)return!1;const i=s.value.querySelectorAll(".task-list-item.enabled"),c=l=>{var u;l.preventDefault();const f=l.target.checked?"unChecked":"checked",p=(u=l.target.parentElement)==null?void 0:u.dataset.line;if(!p)return;const b=Number(p),h=e.modelValue.split(`
`),x=h[Number(b)].replace(Uo[f].regexp,Uo[f].value);e.previewOnly?(h[Number(b)]=x,e.onChange(h.join(`
`))):Mn.emit(n,wp,b+1,x)};i.forEach(l=>{l.addEventListener("click",c)}),a=()=>{i.forEach(l=>{l.removeEventListener("click",c)})}};ss(()=>{a()}),ke([t],()=>{a(),at(o)},{immediate:!0})},Yi={modelValue:{type:String,default:""},onChange:{type:Function,default:()=>{}},setting:{type:Object,default:()=>({preview:!0})},onHtmlChanged:{type:Function,default:()=>{}},onGetCatalog:{type:Function,default:()=>{}},mdHeadingId:{type:Function,default:()=>""},noMermaid:{type:Boolean,default:!1},sanitize:{type:Function,default:e=>e},noKatex:{type:Boolean,default:!1},formatCopiedText:{type:Function,default:e=>e},noHighlight:{type:Boolean,default:!1},previewOnly:{type:Boolean,default:!1},noImgZoomIn:{type:Boolean},sanitizeMermaid:{type:Function},codeFoldable:{type:Boolean},autoFoldThreshold:{type:Number}};mt(me({},Yi),{updateModelValue:{type:Function,default:()=>{}},placeholder:{type:String,default:""},scrollAuto:{type:Boolean},autofocus:{type:Boolean},disabled:{type:Boolean},readonly:{type:Boolean},maxlength:{type:Number},autoDetectCode:{type:Boolean},onBlur:{type:Function,default:()=>{}},onFocus:{type:Function,default:()=>{}},noPrettier:{type:Boolean},completions:{type:Array},catalogVisible:{type:Boolean},theme:{type:String,default:"light"},onInput:{type:Function},onDrop:{type:Function,default:()=>{}},inputBoxWitdh:{type:String},onInputBoxWitdhChange:{type:Function},transformImgUrl:{type:Function,default:e=>e}});const yh=je({name:"ContentPreview",props:Yi,setup(e){const t=Ue("editorId"),n=Ue("previewTheme"),s=Ue("showCodeRowNumber"),{html:a,key:o}=mh(e,e.previewOnly);return Qp(e,a,o),Xp(e,a),vh(e,a),()=>X(Pe,null,[e.setting.preview&&X("div",{id:`${t}-preview-wrapper`,class:`${de}-preview-wrapper`,key:"content-preview-wrapper"},[X("div",{key:o.value,id:`${t}-preview`,class:[`${de}-preview`,`${n==null?void 0:n.value}-theme`,s&&`${de}-scrn`],innerHTML:a.value},null)]),!e.previewOnly&&e.setting.htmlPreview&&X("div",{id:`${t}-html-wrapper`,class:`${de}-preview-wrapper`,key:"html-preview-wrapper"},[X("div",{class:`${de}-html`},[a.value])])])}}),bh=(e,t)=>{const{editorId:n}=e,s=Xt.editorExtensions.highlight,a=Xt.editorExtensionsAttrs.highlight;Tt("editorId",n),Tt("rootRef",t),Tt("theme",k(()=>e.theme)),Tt("language",k(()=>e.language)),Tt("highlight",k(()=>{const{js:i}=s,c=me(me({},$a),s.css),{js:l,css:u={}}=a||{},f=e.codeStyleReverse&&e.codeStyleReverseList.includes(e.previewTheme)?"dark":e.theme,p=c[e.codeTheme]?c[e.codeTheme][f]:$a.atom[f],b=c[e.codeTheme]&&u[e.codeTheme]?u[e.codeTheme][f]:u.atom?u.atom[f]:{};return{js:me({src:i},l),css:me({href:p},b)}})),Tt("showCodeRowNumber",e.showCodeRowNumber);const o=k(()=>{const i=me(me({},Io),Xt.editorConfig.languageUserDefined);return Ii(_s(Io["en-US"]),i[e.language]||{})});Tt("usedLanguageText",o),Tt("previewTheme",k(()=>e.previewTheme)),Tt("customIcon",k(()=>e.customIcon))},wh=e=>{Ge(()=>{const{editorExtensions:t,editorExtensionsAttrs:n,iconfontType:s}=Xt;e.noIconfont||(s==="svg"?Rn("script",mt(me({},n.iconfont),{src:t.iconfont,id:`${de}-icon`})):Rn("link",mt(me({},n.iconfontClass),{rel:"stylesheet",href:t.iconfontClass,id:`${de}-icon-class`})))})},xh=e=>e,Zi={modelValue:{type:String,default:""},onChange:{type:Function,default:void 0},theme:{type:String,default:"light"},class:{type:String,default:""},language:{type:String,default:"zh-CN"},onHtmlChanged:{type:Function,default:void 0},onGetCatalog:{type:Function,default:void 0},editorId:{type:String,default:()=>Cp("md-editor-v3_")},showCodeRowNumber:{type:Boolean,default:!0},previewTheme:{type:String,default:"default"},style:{type:Object,default:()=>({})},mdHeadingId:{type:Function,default:xh},sanitize:{type:Function,default:e=>e},noMermaid:{type:Boolean,default:!1},noKatex:{type:Boolean,default:!1},codeTheme:{type:String,default:"atom"},noIconfont:{type:Boolean,default:void 0},formatCopiedText:{type:Function,default:e=>e},codeStyleReverse:{type:Boolean,default:!0},codeStyleReverseList:{type:Array,default:["default","mk-cute"]},noHighlight:{type:Boolean,default:!1},noImgZoomIn:{type:Boolean,default:!1},customIcon:{type:Object,default:{}},sanitizeMermaid:{type:Function,default:e=>Promise.resolve(e)},codeFoldable:{type:Boolean,default:!0},autoFoldThreshold:{type:Number,default:30}};mt(me({},Zi),{onSave:{type:Function,default:void 0},onUploadImg:{type:Function,default:void 0},pageFullscreen:{type:Boolean,default:!1},preview:{type:Boolean,default:!0},htmlPreview:{type:Boolean,default:!1},toolbars:{type:Array,default:dp},toolbarsExclude:{type:Array,default:[]},noPrettier:{type:Boolean,default:!1},tabWidth:{type:Number,default:2},tableShape:{type:Array,default:[6,4]},placeholder:{type:String,default:""},defToolbars:{type:[String,Object],default:void 0},onError:{type:Function,default:void 0},footers:{type:Array,default:fp},scrollAuto:{type:Boolean,default:!0},defFooters:{type:[String,Object],default:void 0},noUploadImg:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},readOnly:{type:Boolean,default:!1},maxLength:{type:Number,default:void 0},autoDetectCode:{type:Boolean,default:!1},onBlur:{type:Function,default:void 0},onFocus:{type:Function,default:void 0},completions:{type:Array,default:void 0},showToolbarName:{type:Boolean,default:!1},onInput:{type:Function,default:void 0},onDrop:{type:Function,default:void 0},inputBoxWitdh:{type:String,default:"50%"},onInputBoxWitdhChange:{type:Function,default:void 0},transformImgUrl:{type:Function,default:e=>e}});const Xi=["onHtmlChanged","onGetCatalog","onChange","update:modelValue"];[...Xi];const kh=(e,t)=>{const{editorId:n}=e,s={rerender(){Mn.emit(n,Si)}};t.expose(s)},Rs=je({name:"MdPreview",props:Zi,emits:Xi,setup(e,t){const{editorId:n,noKatex:s,noMermaid:a,noHighlight:o}=e,i=S();return bh(e,i),wh(e),kh(e,t),ss(()=>{Mn.clear(n)}),()=>X("div",{id:n,class:[de,e.class,e.theme==="dark"&&`${de}-dark`,`${de}-previewOnly`],style:e.style,ref:i},[X(yh,{modelValue:e.modelValue,onChange:c=>{e.onChange&&e.onChange(c),t.emit("onChange",c),t.emit("update:modelValue",c)},onHtmlChanged:c=>{e.onHtmlChanged?e.onHtmlChanged(c):t.emit("onHtmlChanged",c)},onGetCatalog:c=>{e.onGetCatalog?e.onGetCatalog(c):t.emit("onGetCatalog",c)},mdHeadingId:e.mdHeadingId,noMermaid:a,sanitize:e.sanitize,noKatex:s,formatCopiedText:e.formatCopiedText,noHighlight:o,noImgZoomIn:e.noImgZoomIn,previewOnly:!0,sanitizeMermaid:e.sanitizeMermaid,codeFoldable:e.codeFoldable,autoFoldThreshold:e.autoFoldThreshold},null)])}});Rs.install=e=>(e.component(Rs.name,Rs),e);const Ah={onClick:{type:Function,default:void 0}},La=je({name:"NormalFooterToolbar",props:Ah,emits:["onClick"],setup(e,t){return()=>{const n=pp({props:e,ctx:t});return X("div",{class:`${de}-footer-item`,onClick:s=>{e.onClick instanceof Function?e.onClick(s):t.emit("onClick",s)}},[n])}}});La.install=e=>(e.component(La.name,La),e);const Ch={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4 flex flex-col space-y-4"},Ih={class:"text-base font-semibold text-gray-900 dark:text-gray-100 pb-2 border-b border-gray-200 dark:border-gray-700"},Sh=je({__name:"NoticeDialog",props:{visible:{type:Boolean}},setup(e){const t=ft(),n=bs(),s=k(()=>n.theme==="dark"),{isMobile:a}=it(),{noticeInfo:o}=t.globalConfig,i=e,c=k(()=>t.globalConfig);function l(){t.getGlobalConfig().catch(u=>{})}return ke(()=>i.visible,u=>{u&&l()}),Ge(()=>{i.visible&&l()}),(u,f)=>(d(),g("div",{class:ne(["overflow-y-auto custom-scrollbar p-1",{"max-h-[70vh]":!y(a)}])},[r("div",Ch,[r("div",Ih,R(c.value.noticeTitle||"平台公告"),1),r("div",{class:ne(["overflow-y-auto",{"max-h-[calc(70vh-120px)]":!y(a)}])},[X(y(Rs),{editorId:"preview-only",modelValue:y(o),theme:s.value?"dark":"light",class:"dark:bg-gray-700 w-full"},null,8,["modelValue","theme"])],2)])],2))}});const Qi=an(Sh,[["__scopeId","data-v-d65d4e78"]]),_h={class:"p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4 flex flex-col space-y-4"},Th={class:"text-base font-semibold text-gray-900 dark:text-gray-100 pb-2 border-b border-gray-200 dark:border-gray-700"},Lh=je({__name:"UserAgreement",props:{visible:{type:Boolean}},setup(e){const t=ft(),n=bs(),s=k(()=>n.theme==="dark"),a=k(()=>t.globalConfig),{isMobile:o}=it();return(i,c)=>(d(),g("div",{class:ne(["overflow-y-auto custom-scrollbar p-1",{"max-h-[70vh]":!y(o)}])},[r("div",_h,[r("div",Th,R(a.value.agreementTitle||"用户协议"),1),r("div",{class:ne(["overflow-y-auto custom-scrollbar",{"max-h-[calc(70vh-160px)]":!y(o)}])},[X(y(Rs),{editorId:"preview-only",modelValue:a.value.agreementInfo,theme:s.value?"dark":"light",class:"dark:bg-gray-700 w-full"},null,8,["modelValue","theme"])],2)])],2))}});const qi=an(Lh,[["__scopeId","data-v-f4ba477e"]]),Mh={key:0,class:"fixed inset-0 z-[9000] flex items-center justify-center bg-gray-900 bg-opacity-50"},Eh={class:"w-full h-full bg-white dark:bg-gray-750 flex flex-col overflow-hidden"},Rh={class:"flex justify-between items-center mb-2 flex-shrink-0 px-4 pt-4 pb-2 border-b dark:border-gray-600"},zh={class:"flex items-center"},Dh={class:"text-xl font-semibold dark:text-white"},jh={class:"flex flex-col flex-grow overflow-y-auto px-3 pb-4"},Ph={key:0,class:"flex-grow py-2"},$h=["onClick"],Uh={class:"font-medium text-base"},Nh={key:1,class:"flex-grow py-2"},Fh={key:2,class:"mt-auto pt-4 pb-2 flex-shrink-0 px-4"},Gh=je({__name:"MobileSettingsDialog",props:{visible:{type:Boolean}},setup(e){const t=e,n=oi(),s=ft(),a=k(()=>s.globalConfig),o=k(()=>n.mobileInitialTab),i=k(()=>{const v=[{name:"账户管理",component:Wn(ki),id:"account"},{name:"会员中心",component:Wn(Ai),id:"member"},{name:"网站公告",component:Wn(Qi),id:"notice"}];return a.value.isAutoOpenAgreement==="1"&&v.push({name:"用户协议",component:Wn(qi),id:"agreement"}),v}),c=S("main"),l=S(-1),u=S(Date.now()),f=k(()=>l.value>=0?i.value[l.value].name:"设置");function p(v){v<0||v>=i.value.length||(l.value=v,c.value="tab",u.value=Date.now())}function b(v){const C=i.value.findIndex(A=>A.id===v);C!==-1&&p(C)}function h(){c.value="main",l.value=-1}function x(){n.updateMobileSettingsDialog(!1),setTimeout(()=>{h()},300)}ke([()=>t.visible,o],([v,C])=>{v&&C?b(C):v||setTimeout(()=>{h()},300)},{immediate:!0});function w(){ya().warning({title:"退出登录",content:"确定要退出登录吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:()=>{s.logOut(),x()}})}return(v,C)=>(d(),pe(Hn,{name:"modal-fade"},{default:Ct(()=>{var A;return[t.visible?(d(),g("div",Mh,[r("div",Eh,[r("div",Rh,[r("div",zh,[c.value!=="main"?(d(),g("button",{key:0,onClick:h,class:"mr-2 p-1 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"},[X(y(us),{size:"20"})])):I("",!0),r("span",Dh,R(c.value==="main"?"设置":f.value),1)]),r("button",{onClick:x,class:"p-1 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"},[X(y(mn),{size:"20"})])]),r("div",jh,[c.value==="main"?(d(),g("div",Ph,[(d(!0),g(Pe,null,He(i.value,(L,$)=>(d(),g("div",{key:`mobile-tab-${$}`,class:"mb-1 border-b dark:border-gray-600 last:border-b-0"},[r("div",{onClick:E=>p($),class:ne(["flex justify-between items-center px-4 py-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-150",{"text-gray-800 dark:text-gray-200":!0}])},[r("span",Uh,R(L.name),1),C[0]||(C[0]=r("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[r("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1))],8,$h)]))),128))])):(d(),g("div",Nh,[(d(),pe(li,null,[(A=i.value[l.value])!=null&&A.component?(d(),pe(ci(i.value[l.value].component),{key:`mobile-component-${l.value}-${u.value}`,visible:t.visible&&c.value!=="main"},null,8,["visible"])):I("",!0)],1024))])),c.value==="main"?(d(),g("div",Fh,[r("button",{onClick:w,class:"w-full flex items-center justify-center gap-3 px-4 py-3 rounded-lg cursor-pointer group font-medium text-sm text-red-500 dark:text-red-400 bg-red-50 dark:bg-red-900/30 hover:bg-red-100 dark:hover:bg-red-900/50 border border-red-200 dark:border-red-500/50 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-300 dark:focus:ring-red-600"}," 退出登录 ")])):I("",!0)])])])):I("",!0)]}),_:1}))}});const Bh=an(Gh,[["__scopeId","data-v-e88c7d30"]]),Oh={key:0,class:"fixed inset-0 z-[9000] flex items-center justify-center bg-gray-900 bg-opacity-50"},Wh={class:"flex justify-between items-center mb-2"},Hh={class:"flex flex-grow"},Vh={class:"w-1/5 bg-white dark:bg-gray-750 rounded-lg"},Yh=["onClick"],Zh={class:"w-4/5 bg-white dark:bg-gray-750 rounded-lg ml-4"},Xh={key:"loaded-content"},Qh={key:"loading-placeholder",class:"flex justify-center items-center h-full"},qh=je({__name:"SettingsDialog",props:{visible:{type:Boolean}},setup(e){const t=It(),{isMobile:n}=it(),s=e,a=ft(),o=k(()=>a.globalConfig),i=k(()=>{const h=[{name:"账户管理",component:Wn(ki)},{name:"会员中心",component:Wn(Ai)},{name:"网站公告",component:Wn(Qi)}];return o.value.isAutoOpenAgreement==="1"&&h.push({name:"用户协议",component:Wn(qi)}),h}),c=S(t.settingsActiveTab>=0&&t.settingsActiveTab<i.value.length?t.settingsActiveTab:0),l=S(Date.now()),u=S(!1);function f(h){h>=0&&h<i.value.length&&h!==c.value&&(u.value=!0,c.value=h,t.settingsActiveTab=h,at(()=>{l.value=Date.now(),setTimeout(()=>{u.value=!1},50)}))}ke(()=>t.settingsActiveTab,h=>{s.visible&&!n.value&&h>=0&&h<i.value.length&&h!==c.value&&f(h)}),ke(()=>s.visible,h=>{if(h&&!n.value){const x=t.settingsActiveTab>=0&&t.settingsActiveTab<i.value.length?t.settingsActiveTab:0;c.value!==x&&(c.value=x),l.value=Date.now(),u.value=!1}else h||(u.value=!1)},{immediate:!0});function p(){t.updateSettingsDialog(!1)}function b(){ya().warning({title:"退出登录",content:"确定要退出登录吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:()=>{a.logOut(),p()}})}return Ge(()=>{}),(h,x)=>(d(),pe(Hn,{name:"modal-fade"},{default:Ct(()=>[s.visible?(d(),g("div",Oh,[r("div",{class:ne(["bg-white dark:bg-gray-750 rounded-lg shadow-lg flex flex-col",y(n)?"w-full h-full":"h-[80vh] rounded-lg shadow-lg w-full max-w-5xl p-4 mx-2"])},[r("div",Wh,[x[0]||(x[0]=r("span",{class:"text-xl font-bold dark:text-white"},"设置",-1)),r("button",{onClick:p,class:"btn-icon btn-md"},[X(y(mn),{size:"20"})])]),r("div",Hh,[r("div",Vh,[(d(!0),g(Pe,null,He(i.value,(w,v)=>(d(),g("div",{key:v,onClick:C=>f(v),class:ne(["relative flex items-center gap-3 px-3 py-3 my-1 break-all rounded-lg cursor-pointer group dark:hover:bg-gray-700 font-medium text-sm",{"bg-gray-50 text-primary-600 dark:bg-gray-700 dark:text-primary-400":c.value===v,"text-gray-700 dark:text-gray-400":c.value!==v}])},R(w.name),11,Yh))),128)),r("div",{class:"mt-auto"},[r("div",{onClick:b,class:"relative flex items-center gap-3 px-3 py-3 my-1 break-all rounded-lg cursor-pointer group font-medium text-sm text-red-500 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700"}," 退出登录 ")])]),r("div",Zh,[X(Hn,{name:"fade",mode:"out-in"},{default:Ct(()=>[u.value?(d(),g("div",Qh,x[1]||(x[1]=[r("div",{class:"animate-pulse flex space-x-4"},[r("div",{class:"flex-1 space-y-4 py-1"},[r("div",{class:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"}),r("div",{class:"space-y-2"},[r("div",{class:"h-4 bg-gray-200 dark:bg-gray-700 rounded"}),r("div",{class:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"})])])],-1)]))):(d(),g("div",Xh,[(d(),pe(li,null,[i.value[c.value]?(d(),pe(ci(i.value[c.value].component),{key:l.value,visible:s.visible&&!u.value},null,8,["visible"])):I("",!0)],1024))]))]),_:1})])])],2)])):I("",!0)]),_:1}))}});const Jh=an(qh,[["__scopeId","data-v-4f8410fd"]]),Ji=""+new URL("../images/logo-89dd0dfe.png",import.meta.url).href,Ki=/^[a-z0-9]+(-[a-z0-9]+)*$/,wa=(e,t,n,s="")=>{const a=e.split(":");if(e.slice(0,1)==="@"){if(a.length<2||a.length>3)return null;s=a.shift().slice(1)}if(a.length>3||!a.length)return null;if(a.length>1){const c=a.pop(),l=a.pop(),u={provider:a.length>0?a[0]:s,prefix:l,name:c};return t&&!la(u)?null:u}const o=a[0],i=o.split("-");if(i.length>1){const c={provider:s,prefix:i.shift(),name:i.join("-")};return t&&!la(c)?null:c}if(n&&s===""){const c={provider:s,prefix:"",name:o};return t&&!la(c,n)?null:c}return null},la=(e,t)=>e?!!((t&&e.prefix===""||e.prefix)&&e.name):!1,er=Object.freeze({left:0,top:0,width:16,height:16}),ha=Object.freeze({rotate:0,vFlip:!1,hFlip:!1}),xa=Object.freeze(me(me({},er),ha)),Ya=Object.freeze(mt(me({},xa),{body:"",hidden:!1}));function Kh(e,t){const n={};!e.hFlip!=!t.hFlip&&(n.hFlip=!0),!e.vFlip!=!t.vFlip&&(n.vFlip=!0);const s=((e.rotate||0)+(t.rotate||0))%4;return s&&(n.rotate=s),n}function No(e,t){const n=Kh(e,t);for(const s in Ya)s in ha?s in e&&!(s in n)&&(n[s]=ha[s]):s in t?n[s]=t[s]:s in e&&(n[s]=e[s]);return n}function em(e,t){const n=e.icons,s=e.aliases||Object.create(null),a=Object.create(null);function o(i){if(n[i])return a[i]=[];if(!(i in a)){a[i]=null;const c=s[i]&&s[i].parent,l=c&&o(c);l&&(a[i]=[c].concat(l))}return a[i]}return Object.keys(n).concat(Object.keys(s)).forEach(o),a}function tm(e,t,n){const s=e.icons,a=e.aliases||Object.create(null);let o={};function i(c){o=No(s[c]||a[c],o)}return i(t),n.forEach(i),No(e,o)}function tr(e,t){const n=[];if(typeof e!="object"||typeof e.icons!="object")return n;e.not_found instanceof Array&&e.not_found.forEach(a=>{t(a,null),n.push(a)});const s=em(e);for(const a in s){const o=s[a];o&&(t(a,tm(e,a,o)),n.push(a))}return n}const nm=me({provider:"",aliases:{},not_found:{}},er);function Ma(e,t){for(const n in t)if(n in e&&typeof e[n]!=typeof t[n])return!1;return!0}function nr(e){if(typeof e!="object"||e===null)return null;const t=e;if(typeof t.prefix!="string"||!e.icons||typeof e.icons!="object"||!Ma(e,nm))return null;const n=t.icons;for(const a in n){const o=n[a];if(!a||typeof o.body!="string"||!Ma(o,Ya))return null}const s=t.aliases||Object.create(null);for(const a in s){const o=s[a],i=o.parent;if(!a||typeof i!="string"||!n[i]&&!s[i]||!Ma(o,Ya))return null}return t}const Fo=Object.create(null);function sm(e,t){return{provider:e,prefix:t,icons:Object.create(null),missing:new Set}}function ys(e,t){const n=Fo[e]||(Fo[e]=Object.create(null));return n[t]||(n[t]=sm(e,t))}function sr(e,t){return nr(t)?tr(t,(n,s)=>{s?e.icons[n]=s:e.missing.add(n)}):[]}function am(e,t,n){try{if(typeof n.body=="string")return e.icons[t]=me({},n),!0}catch(s){}return!1}let Ps=!1;function ar(e){return typeof e=="boolean"&&(Ps=e),Ps}function om(e){const t=typeof e=="string"?wa(e,!0,Ps):e;if(t){const n=ys(t.provider,t.prefix),s=t.name;return n.icons[s]||(n.missing.has(s)?null:void 0)}}function im(e,t){const n=wa(e,!0,Ps);if(!n)return!1;const s=ys(n.provider,n.prefix);return t?am(s,n.name,t):(s.missing.add(n.name),!0)}function rm(e,t){if(typeof e!="object")return!1;if(typeof t!="string"&&(t=e.provider||""),Ps&&!t&&!e.prefix){let a=!1;return nr(e)&&(e.prefix="",tr(e,(o,i)=>{im(o,i)&&(a=!0)})),a}const n=e.prefix;if(!la({provider:t,prefix:n,name:"a"}))return!1;const s=ys(t,n);return!!sr(s,e)}const or=Object.freeze({width:null,height:null}),ir=Object.freeze(me(me({},or),ha)),lm=/(-?[0-9.]*[0-9]+[0-9.]*)/g,cm=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function Go(e,t,n){if(t===1)return e;if(n=n||100,typeof e=="number")return Math.ceil(e*t*n)/n;if(typeof e!="string")return e;const s=e.split(lm);if(s===null||!s.length)return e;const a=[];let o=s.shift(),i=cm.test(o);for(;;){if(i){const c=parseFloat(o);isNaN(c)?a.push(o):a.push(Math.ceil(c*t*n)/n)}else a.push(o);if(o=s.shift(),o===void 0)return a.join("");i=!i}}function um(e,t="defs"){let n="";const s=e.indexOf("<"+t);for(;s>=0;){const a=e.indexOf(">",s),o=e.indexOf("</"+t);if(a===-1||o===-1)break;const i=e.indexOf(">",o);if(i===-1)break;n+=e.slice(a+1,o).trim(),e=e.slice(0,s).trim()+e.slice(i+1)}return{defs:n,content:e}}function dm(e,t){return e?"<defs>"+e+"</defs>"+t:t}function fm(e,t,n){const s=um(e);return dm(s.defs,t+s.content+n)}const pm=e=>e==="unset"||e==="undefined"||e==="none";function gm(e,t){const n=me(me({},xa),e),s=me(me({},ir),t),a={left:n.left,top:n.top,width:n.width,height:n.height};let o=n.body;[n,s].forEach(w=>{const v=[],C=w.hFlip,A=w.vFlip;let L=w.rotate;C?A?L+=2:(v.push("translate("+(a.width+a.left).toString()+" "+(0-a.top).toString()+")"),v.push("scale(-1 1)"),a.top=a.left=0):A&&(v.push("translate("+(0-a.left).toString()+" "+(a.height+a.top).toString()+")"),v.push("scale(1 -1)"),a.top=a.left=0);let $;switch(L<0&&(L-=Math.floor(L/4)*4),L=L%4,L){case 1:$=a.height/2+a.top,v.unshift("rotate(90 "+$.toString()+" "+$.toString()+")");break;case 2:v.unshift("rotate(180 "+(a.width/2+a.left).toString()+" "+(a.height/2+a.top).toString()+")");break;case 3:$=a.width/2+a.left,v.unshift("rotate(-90 "+$.toString()+" "+$.toString()+")");break}L%2===1&&(a.left!==a.top&&($=a.left,a.left=a.top,a.top=$),a.width!==a.height&&($=a.width,a.width=a.height,a.height=$)),v.length&&(o=fm(o,'<g transform="'+v.join(" ")+'">',"</g>"))});const i=s.width,c=s.height,l=a.width,u=a.height;let f,p;i===null?(p=c===null?"1em":c==="auto"?u:c,f=Go(p,l/u)):(f=i==="auto"?l:i,p=c===null?Go(f,u/l):c==="auto"?u:c);const b={},h=(w,v)=>{pm(v)||(b[w]=v.toString())};h("width",f),h("height",p);const x=[a.left,a.top,l,u];return b.viewBox=x.join(" "),{attributes:b,viewBox:x,body:o}}const hm=/\sid="(\S+)"/g,mm="IconifyId"+Date.now().toString(16)+(Math.random()*16777216|0).toString(16);let vm=0;function ym(e,t=mm){const n=[];let s;for(;s=hm.exec(e);)n.push(s[1]);if(!n.length)return e;const a="suffix"+(Math.random()*16777216|Date.now()).toString(16);return n.forEach(o=>{const i=typeof t=="function"?t(o):t+(vm++).toString(),c=o.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");e=e.replace(new RegExp('([#;"])('+c+')([")]|\\.[a-z])',"g"),"$1"+i+a+"$3")}),e=e.replace(new RegExp(a,"g"),""),e}const Za=Object.create(null);function bm(e,t){Za[e]=t}function Xa(e){return Za[e]||Za[""]}function no(e){let t;if(typeof e.resources=="string")t=[e.resources];else if(t=e.resources,!(t instanceof Array)||!t.length)return null;return{resources:t,path:e.path||"/",maxURL:e.maxURL||500,rotate:e.rotate||750,timeout:e.timeout||5e3,random:e.random===!0,index:e.index||0,dataAfterTimeout:e.dataAfterTimeout!==!1}}const so=Object.create(null),Is=["https://api.simplesvg.com","https://api.unisvg.com"],ca=[];for(;Is.length>0;)Is.length===1||Math.random()>.5?ca.push(Is.shift()):ca.push(Is.pop());so[""]=no({resources:["https://api.iconify.design"].concat(ca)});function wm(e,t){const n=no(t);return n===null?!1:(so[e]=n,!0)}function ao(e){return so[e]}const xm=()=>{let e;try{if(e=fetch,typeof e=="function")return e}catch(t){}};let Bo=xm();function km(e,t){const n=ao(e);if(!n)return 0;let s;if(!n.maxURL)s=0;else{let a=0;n.resources.forEach(i=>{a=Math.max(a,i.length)});const o=t+".json?icons=";s=n.maxURL-a-n.path.length-o.length}return s}function Am(e){return e===404}const Cm=(e,t,n)=>{const s=[],a=km(e,t),o="icons";let i={type:o,provider:e,prefix:t,icons:[]},c=0;return n.forEach((l,u)=>{c+=l.length+1,c>=a&&u>0&&(s.push(i),i={type:o,provider:e,prefix:t,icons:[]},c=l.length),i.icons.push(l)}),s.push(i),s};function Im(e){if(typeof e=="string"){const t=ao(e);if(t)return t.path}return"/"}const Sm=(e,t,n)=>{if(!Bo){n("abort",424);return}let s=Im(t.provider);switch(t.type){case"icons":{const o=t.prefix,c=t.icons.join(","),l=new URLSearchParams({icons:c});s+=o+".json?"+l.toString();break}case"custom":{const o=t.uri;s+=o.slice(0,1)==="/"?o.slice(1):o;break}default:n("abort",400);return}let a=503;Bo(e+s).then(o=>{const i=o.status;if(i!==200){setTimeout(()=>{n(Am(i)?"abort":"next",i)});return}return a=501,o.json()}).then(o=>{if(typeof o!="object"||o===null){setTimeout(()=>{o===404?n("abort",o):n("next",a)});return}setTimeout(()=>{n("success",o)})}).catch(()=>{n("next",a)})},_m={prepare:Cm,send:Sm};function Tm(e){const t={loaded:[],missing:[],pending:[]},n=Object.create(null);e.sort((a,o)=>a.provider!==o.provider?a.provider.localeCompare(o.provider):a.prefix!==o.prefix?a.prefix.localeCompare(o.prefix):a.name.localeCompare(o.name));let s={provider:"",prefix:"",name:""};return e.forEach(a=>{if(s.name===a.name&&s.prefix===a.prefix&&s.provider===a.provider)return;s=a;const o=a.provider,i=a.prefix,c=a.name,l=n[o]||(n[o]=Object.create(null)),u=l[i]||(l[i]=ys(o,i));let f;c in u.icons?f=t.loaded:i===""||u.missing.has(c)?f=t.missing:f=t.pending;const p={provider:o,prefix:i,name:c};f.push(p)}),t}function rr(e,t){e.forEach(n=>{const s=n.loaderCallbacks;s&&(n.loaderCallbacks=s.filter(a=>a.id!==t))})}function Lm(e){e.pendingCallbacksFlag||(e.pendingCallbacksFlag=!0,setTimeout(()=>{e.pendingCallbacksFlag=!1;const t=e.loaderCallbacks?e.loaderCallbacks.slice(0):[];if(!t.length)return;let n=!1;const s=e.provider,a=e.prefix;t.forEach(o=>{const i=o.icons,c=i.pending.length;i.pending=i.pending.filter(l=>{if(l.prefix!==a)return!0;const u=l.name;if(e.icons[u])i.loaded.push({provider:s,prefix:a,name:u});else if(e.missing.has(u))i.missing.push({provider:s,prefix:a,name:u});else return n=!0,!0;return!1}),i.pending.length!==c&&(n||rr([e],o.id),o.callback(i.loaded.slice(0),i.missing.slice(0),i.pending.slice(0),o.abort))})}))}let Mm=0;function Em(e,t,n){const s=Mm++,a=rr.bind(null,n,s);if(!t.pending.length)return a;const o={id:s,icons:t,callback:e,abort:a};return n.forEach(i=>{(i.loaderCallbacks||(i.loaderCallbacks=[])).push(o)}),a}function Rm(e,t=!0,n=!1){const s=[];return e.forEach(a=>{const o=typeof a=="string"?wa(a,t,n):a;o&&s.push(o)}),s}var zm={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Dm(e,t,n,s){const a=e.resources.length,o=e.random?Math.floor(Math.random()*a):e.index;let i;if(e.random){let D=e.resources.slice(0);for(i=[];D.length>1;){const O=Math.floor(Math.random()*D.length);i.push(D[O]),D=D.slice(0,O).concat(D.slice(O+1))}i=i.concat(D)}else i=e.resources.slice(o).concat(e.resources.slice(0,o));const c=Date.now();let l="pending",u=0,f,p=null,b=[],h=[];typeof s=="function"&&h.push(s);function x(){p&&(clearTimeout(p),p=null)}function w(){l==="pending"&&(l="aborted"),x(),b.forEach(D=>{D.status==="pending"&&(D.status="aborted")}),b=[]}function v(D,O){O&&(h=[]),typeof D=="function"&&h.push(D)}function C(){return{startTime:c,payload:t,status:l,queriesSent:u,queriesPending:b.length,subscribe:v,abort:w}}function A(){l="failed",h.forEach(D=>{D(void 0,f)})}function L(){b.forEach(D=>{D.status==="pending"&&(D.status="aborted")}),b=[]}function $(D,O,T){const Y=O!=="success";switch(b=b.filter(P=>P!==D),l){case"pending":break;case"failed":if(Y||!e.dataAfterTimeout)return;break;default:return}if(O==="abort"){f=T,A();return}if(Y){f=T,b.length||(i.length?E():A());return}if(x(),L(),!e.random){const P=e.resources.indexOf(D.resource);P!==-1&&P!==e.index&&(e.index=P)}l="completed",h.forEach(P=>{P(T)})}function E(){if(l!=="pending")return;x();const D=i.shift();if(D===void 0){if(b.length){p=setTimeout(()=>{x(),l==="pending"&&(L(),A())},e.timeout);return}A();return}const O={status:"pending",resource:D,callback:(T,Y)=>{$(O,T,Y)}};b.push(O),u++,p=setTimeout(E,e.rotate),n(D,t,O.callback)}return setTimeout(E),C}function lr(e){const t=me(me({},zm),e);let n=[];function s(){n=n.filter(c=>c().status==="pending")}function a(c,l,u){const f=Dm(t,c,l,(p,b)=>{s(),u&&u(p,b)});return n.push(f),f}function o(c){return n.find(l=>c(l))||null}return{query:a,find:o,setIndex:c=>{t.index=c},getIndex:()=>t.index,cleanup:s}}function Oo(){}const Ea=Object.create(null);function jm(e){if(!Ea[e]){const t=ao(e);if(!t)return;const n=lr(t),s={config:t,redundancy:n};Ea[e]=s}return Ea[e]}function Pm(e,t,n){let s,a;if(typeof e=="string"){const o=Xa(e);if(!o)return n(void 0,424),Oo;a=o.send;const i=jm(e);i&&(s=i.redundancy)}else{const o=no(e);if(o){s=lr(o);const i=e.resources?e.resources[0]:"",c=Xa(i);c&&(a=c.send)}}return!s||!a?(n(void 0,424),Oo):s.query(t,a,n)().abort}function Wo(){}function $m(e){e.iconsLoaderFlag||(e.iconsLoaderFlag=!0,setTimeout(()=>{e.iconsLoaderFlag=!1,Lm(e)}))}function Um(e){const t=[],n=[];return e.forEach(s=>{(s.match(Ki)?t:n).push(s)}),{valid:t,invalid:n}}function Ss(e,t,n){function s(){const a=e.pendingIcons;t.forEach(o=>{a&&a.delete(o),e.icons[o]||e.missing.add(o)})}if(n&&typeof n=="object")try{if(!sr(e,n).length){s();return}}catch(a){}s(),$m(e)}function Ho(e,t){e instanceof Promise?e.then(n=>{t(n)}).catch(()=>{t(null)}):t(e)}function Nm(e,t){e.iconsToLoad?e.iconsToLoad=e.iconsToLoad.concat(t).sort():e.iconsToLoad=t,e.iconsQueueFlag||(e.iconsQueueFlag=!0,setTimeout(()=>{e.iconsQueueFlag=!1;const{provider:n,prefix:s}=e,a=e.iconsToLoad;if(delete e.iconsToLoad,!a||!a.length)return;const o=e.loadIcon;if(e.loadIcons&&(a.length>1||!o)){Ho(e.loadIcons(a,s,n),f=>{Ss(e,a,f)});return}if(o){a.forEach(f=>{const p=o(f,s,n);Ho(p,b=>{const h=b?{prefix:s,icons:{[f]:b}}:null;Ss(e,[f],h)})});return}const{valid:i,invalid:c}=Um(a);if(c.length&&Ss(e,c,null),!i.length)return;const l=s.match(Ki)?Xa(n):null;if(!l){Ss(e,i,null);return}l.prepare(n,s,i).forEach(f=>{Pm(n,f,p=>{Ss(e,f.icons,p)})})}))}const Fm=(e,t)=>{const n=Rm(e,!0,ar()),s=Tm(n);if(!s.pending.length){let l=!0;return t&&setTimeout(()=>{l&&t(s.loaded,s.missing,s.pending,Wo)}),()=>{l=!1}}const a=Object.create(null),o=[];let i,c;return s.pending.forEach(l=>{const{provider:u,prefix:f}=l;if(f===c&&u===i)return;i=u,c=f,o.push(ys(u,f));const p=a[u]||(a[u]=Object.create(null));p[f]||(p[f]=[])}),s.pending.forEach(l=>{const{provider:u,prefix:f,name:p}=l,b=ys(u,f),h=b.pendingIcons||(b.pendingIcons=new Set);h.has(p)||(h.add(p),a[u][f].push(p))}),o.forEach(l=>{const u=a[l.provider][l.prefix];u.length&&Nm(l,u)}),t?Em(t,s,o):Wo};function Gm(e,t){const n=me({},e);for(const s in t){const a=t[s],o=typeof a;s in or?(a===null||a&&(o==="string"||o==="number"))&&(n[s]=a):o===typeof n[s]&&(n[s]=s==="rotate"?a%4:a)}return n}const Bm=/[\s,]+/;function Om(e,t){t.split(Bm).forEach(n=>{switch(n.trim()){case"horizontal":e.hFlip=!0;break;case"vertical":e.vFlip=!0;break}})}function Wm(e,t=0){const n=e.replace(/^-?[0-9.]*/,"");function s(a){for(;a<0;)a+=4;return a%4}if(n===""){const a=parseInt(e);return isNaN(a)?0:s(a)}else if(n!==e){let a=0;switch(n){case"%":a=25;break;case"deg":a=90}if(a){let o=parseFloat(e.slice(0,e.length-n.length));return isNaN(o)?0:(o=o/a,o%1===0?s(o):0)}}return t}function Hm(e,t){let n=e.indexOf("xlink:")===-1?"":' xmlns:xlink="http://www.w3.org/1999/xlink"';for(const s in t)n+=" "+s+'="'+t[s]+'"';return'<svg xmlns="http://www.w3.org/2000/svg"'+n+">"+e+"</svg>"}function Vm(e){return e.replace(/"/g,"'").replace(/%/g,"%25").replace(/#/g,"%23").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/\s+/g," ")}function Ym(e){return"data:image/svg+xml,"+Vm(e)}function Zm(e){return'url("'+Ym(e)+'")'}const Vo=mt(me({},ir),{inline:!1}),Xm={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img"},Qm={display:"inline-block"},Qa={backgroundColor:"currentColor"},cr={backgroundColor:"transparent"},Yo={Image:"var(--svg)",Repeat:"no-repeat",Size:"100% 100%"},Zo={webkitMask:Qa,mask:Qa,background:cr};for(const e in Zo){const t=Zo[e];for(const n in Yo)t[e+n]=Yo[n]}const ua={};["horizontal","vertical"].forEach(e=>{const t=e.slice(0,1)+"Flip";ua[e+"-flip"]=t,ua[e.slice(0,1)+"-flip"]=t,ua[e+"Flip"]=t});function Xo(e){return e+(e.match(/^[-0-9.]+$/)?"px":"")}const Qo=(e,t)=>{const n=Gm(Vo,t),s=me({},Xm),a=t.mode||"svg",o={},i=t.style,c=typeof i=="object"&&!(i instanceof Array)?i:{};for(let w in t){const v=t[w];if(v!==void 0)switch(w){case"icon":case"style":case"onLoad":case"mode":case"ssr":break;case"inline":case"hFlip":case"vFlip":n[w]=v===!0||v==="true"||v===1;break;case"flip":typeof v=="string"&&Om(n,v);break;case"color":o.color=v;break;case"rotate":typeof v=="string"?n[w]=Wm(v):typeof v=="number"&&(n[w]=v);break;case"ariaHidden":case"aria-hidden":v!==!0&&v!=="true"&&delete s["aria-hidden"];break;default:{const C=ua[w];C?(v===!0||v==="true"||v===1)&&(n[C]=!0):Vo[w]===void 0&&(s[w]=v)}}}const l=gm(e,n),u=l.attributes;if(n.inline&&(o.verticalAlign="-0.125em"),a==="svg"){s.style=me(me({},o),c),Object.assign(s,u);let w=0,v=t.id;return typeof v=="string"&&(v=v.replace(/-/g,"_")),s.innerHTML=ym(l.body,v?()=>v+"ID"+w++:"iconifyVue"),yo("svg",s)}const{body:f,width:p,height:b}=e,h=a==="mask"||(a==="bg"?!1:f.indexOf("currentColor")!==-1),x=Hm(f,mt(me({},u),{width:p+"",height:b+""}));return s.style=me(me(me(mt(me({},o),{"--svg":Zm(x),width:Xo(u.width),height:Xo(u.height)}),Qm),h?Qa:cr),c),yo("span",s)};ar(!0);bm("",_m);if(typeof document!="undefined"&&typeof window!="undefined"){const e=window;if(e.IconifyPreload!==void 0){const t=e.IconifyPreload,n="Invalid IconifyPreload syntax.";typeof t=="object"&&t!==null&&(t instanceof Array?t:[t]).forEach(s=>{try{typeof s!="object"||s===null||s instanceof Array||typeof s.icons!="object"||typeof s.prefix!="string"||rm(s)}catch(a){}})}if(e.IconifyProviders!==void 0){const t=e.IconifyProviders;if(typeof t=="object"&&t!==null)for(let n in t){const s="IconifyProviders["+n+"] is invalid.";try{const a=t[n];if(typeof a!="object"||!a||a.resources===void 0)continue;wm(n,a)}catch(a){}}}}const qm=mt(me({},xa),{body:""}),Jm=je({inheritAttrs:!1,data(){return{_name:"",_loadingIcon:null,iconMounted:!1,counter:0}},mounted(){this.iconMounted=!0},unmounted(){this.abortLoading()},methods:{abortLoading(){this._loadingIcon&&(this._loadingIcon.abort(),this._loadingIcon=null)},getIcon(e,t,n){if(typeof e=="object"&&e!==null&&typeof e.body=="string")return this._name="",this.abortLoading(),{data:e};let s;if(typeof e!="string"||(s=wa(e,!1,!0))===null)return this.abortLoading(),null;let a=om(s);if(!a)return(!this._loadingIcon||this._loadingIcon.name!==e)&&(this.abortLoading(),this._name="",a!==null&&(this._loadingIcon={name:e,abort:Fm([s],()=>{this.counter++})})),null;if(this.abortLoading(),this._name!==e&&(this._name=e,t&&t(e)),n){a=Object.assign({},a);const i=n(a.body,s.name,s.prefix,s.provider);typeof i=="string"&&(a.body=i)}const o=["iconify"];return s.prefix!==""&&o.push("iconify--"+s.prefix),s.provider!==""&&o.push("iconify--"+s.provider),{data:a,classes:o}}},render(){this.counter;const e=this.$attrs,t=this.iconMounted||e.ssr?this.getIcon(e.icon,e.onLoad,e.customise):null;if(!t)return Qo(qm,e);let n=e;return t.classes&&(n=mt(me({},e),{class:(typeof e.class=="string"?e.class+" ":"")+t.classes.join(" ")})),Qo(me(me({},xa),t.data),n)}}),ka=je({__name:"index",props:{icon:{}},setup(e){const t=Or(),n=k(()=>({class:t.class||"",style:t.style||"width: 1em, height: 1em"}));return(s,a)=>(d(),pe(y(Jm),Wr({icon:s.icon},n.value),null,16,["icon"]))}}),Km={class:"menu relative"},ev=["aria-expanded","disabled"],tv=["disabled"],nv=["aria-hidden"],oo=je({__name:"index",props:{modelValue:{type:Boolean,default:!1},trigger:{},position:{default:"bottom-left"},maxHeight:{default:"60vh"},minWidth:{default:"200px"},disabled:{type:Boolean,default:!1},menuClass:{default:""},triggerClass:{default:""},closeOnClickOutside:{type:Boolean,default:!0},closeOnEscape:{type:Boolean,default:!0},zIndex:{default:50}},emits:["update:modelValue","open","close"],setup(e,{expose:t,emit:n}){const s=e,a=n,o=k({get:()=>s.modelValue,set:A=>a("update:modelValue",A)}),i=S(),c=S(),l=S("bottom-left"),u=()=>{if(s.position!=="auto"||!c.value)return;const A=c.value.getBoundingClientRect(),L=window.innerHeight,T=`${A.top+A.height/2<L/2?"bottom":"top"}-right`;l.value=T},f=k(()=>s.position==="auto"?l.value:s.position||"bottom-left"),p=k(()=>{const A=f.value,L={"bottom-left":"menu-items-bottom","bottom-right":"menu-items-bottom menu-items-right-aligned","bottom-center":"menu-items-bottom menu-items-center","top-left":"menu-items-top","top-right":"menu-items-top menu-items-right-aligned","top-center":"menu-items-top menu-items-center"};return L[A]||L["bottom-left"]}),b=k(()=>["menu-items","custom-scrollbar",p.value,s.menuClass].filter(Boolean).join(" ")),h=()=>{s.disabled||(o.value=!o.value)};function x(){s.disabled||(o.value=!0)}function w(){o.value=!1}function v(A){if(!s.closeOnClickOutside||!o.value)return;const L=A.target,$=i.value,E=c.value;$&&E&&!$.contains(L)&&!E.contains(L)&&w()}function C(A){s.closeOnEscape&&A.key==="Escape"&&o.value&&w()}return ke(o,(A,L)=>{A&&!L?(s.position==="auto"&&at(()=>{u()}),a("open")):!A&&L&&a("close")}),Ge(()=>{s.closeOnClickOutside&&document.addEventListener("click",v),s.closeOnEscape&&window.addEventListener("keydown",C)}),ss(()=>{document.removeEventListener("click",v),window.removeEventListener("keydown",C)}),t({open:x,close:w,toggle:h}),(A,L)=>(d(),g("div",Km,[r("div",{ref_key:"triggerRef",ref:c,class:ne(["cursor-pointer",A.triggerClass,{"opacity-50 cursor-not-allowed":A.disabled}]),onClick:h,role:"button","aria-expanded":o.value,"aria-haspopup":!0,disabled:A.disabled},[da(A.$slots,"trigger",{isOpen:o.value,disabled:A.disabled},()=>[r("button",{type:"button",class:"menu-button flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-transparent hover:bg-gray-50 dark:hover:bg-gray-750 text-gray-600 dark:text-gray-400",disabled:A.disabled},[L[1]||(L[1]=r("span",null,"点击展开菜单",-1)),(d(),g("svg",{class:ne(["ml-2 w-4 h-4 transition-transform duration-200",{"rotate-180":o.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},L[0]||(L[0]=[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))],8,tv)])],10,ev),X(Hn,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:Ct(()=>[Xe(r("div",{ref_key:"menuRef",ref:i,class:ne(b.value),style:kt({maxHeight:A.maxHeight,minWidth:A.minWidth,zIndex:A.zIndex,overflowY:"auto"}),role:"menu","aria-hidden":!o.value},[da(A.$slots,"menu",{close:w,isOpen:o.value},()=>[L[2]||(L[2]=r("div",null,[r("div",{class:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300"},"请添加菜单内容")],-1))])],14,nv),[[va,o.value]])]),_:3})]))}});function sv(e,t){let n;return(...s)=>{const a=()=>{clearTimeout(n),e(...s)};clearTimeout(n),n=setTimeout(a,t)}}const av={key:0,class:"text-xs font-bold"},ov={class:"ml-1"},iv=["onClick"],rv={class:"flex items-center"},lv=["onUpdate:modelValue","onKeypress"],cv={key:1,class:"flex-1 truncate max-w-48"},uv={key:0,class:"absolute z-10 right-2 flex items-center h-full"},dv={class:"flex items-center justify-center w-6 h-6 transition-colors"},fv=["onClick"],pv=["onClick"],gv=["onClick"],qo=je({__name:"ListItem",props:{dataSources:{},title:{}},emits:["update","delete","sticky","select"],setup(e,{emit:t}){const n={mounted:v=>{at(()=>{v.focus()})}},s=S({}),a=e,o=t,i=a.dataSources,c=Qt();function l(v){return H(this,null,function*(){o("select",v)})}function u(v,C,A){A==null||A.stopPropagation(),v.isEdit=C}function f(v,C){return H(this,null,function*(){C==null||C.stopPropagation(),yield c.updateGroupInfo({isSticky:!v.isSticky,groupId:v.uuid})})}function p(v,C){return H(this,null,function*(){C==null||C.stopPropagation(),o("delete",v)})}const b=sv(p,600);function h(v){return H(this,null,function*(){const{uuid:C,title:A}=v;v.isEdit=!1,yield c.updateGroupInfo({groupId:C,title:A})})}function x(v,C){return H(this,null,function*(){C==null||C.stopPropagation(),C.key==="Enter"&&h(v)})}function w(v){return c.active===v}return(v,C)=>{var A;return d(),g(Pe,null,[a.title?(d(),g("p",av,[Ne(R(a.title)+" ",1),r("span",ov,"("+R((A=y(i))==null?void 0:A.length)+")",1)])):I("",!0),(d(!0),g(Pe,null,He(y(i),L=>(d(),g("div",{key:`${L.uuid}`},[r("div",{class:ne(["relative flex items-center gap-3 px-3 py-2 break-all rounded-lg cursor-pointer hover:bg-white group dark:hover:bg-gray-800 font-medium text-sm",w(L.uuid)?["bg-white","text-primary-600","dark:bg-gray-800","dark:text-white"]:["text-gray-700","dark:bg-gray-900","dark:text-gray-400"]]),onClick:$=>l(L)},[r("div",rv,[L.isEdit?Xe((d(),g("input",{key:0,"onUpdate:modelValue":$=>L.title=$,type:"text",class:"bg-transparent border border-gray-200 dark:border-gray-400 px-1 shadow-none flex-1 truncate",onKeypress:$=>x(L,$)},null,40,lv)),[[At,L.title],[n]]):(d(),g("span",cv,R(L.title),1)),L.isEdit?(d(),pe(y(Qr),{key:2,class:"ml-2",theme:"outline",size:"20","aria-hidden":"true",onClick:$=>h(L)},null,8,["onClick"])):I("",!0)]),w(L.uuid)?(d(),g("div",uv,[L.isEdit?I("",!0):(d(),pe(y(oo),{key:0,modelValue:s.value[L.uuid],"onUpdate:modelValue":$=>s.value[L.uuid]=$,position:"auto","min-width":"128px"},{trigger:Ct(()=>[r("div",dv,[X(y(qr),{size:"20","aria-hidden":"true"})])]),menu:Ct(({close:$})=>[r("div",null,[r("div",{class:"menu-item menu-item-md",onClick:()=>{u(L,!0),$()},role:"menuitem",tabindex:"0"},R(y(oe)("chat.rename")),9,fv),r("div",{class:"menu-item menu-item-md",onClick:()=>{f(L),$()},role:"menuitem",tabindex:"0"},R(L.isSticky?y(oe)("chat.unfavorite"):y(oe)("chat.favoriteConversations")),9,pv),r("div",{class:"menu-item menu-item-md",onClick:()=>{y(b)(L),$()},role:"menuitem",tabindex:"0"},R(y(oe)("chat.deleteConversation")),9,gv)])]),_:2},1032,["modelValue","onUpdate:modelValue"]))])):I("",!0)],10,iv)]))),128))],64)}}}),hv={class:"custom-scrollbar px-4 overflow-y-auto h-full"},mv={class:"flex flex-col gap-3 text-sm"},vv={key:0,class:"flex flex-col items-center mt-4 text-center text-neutral-300"},yv={class:"text-xs font-bold"},bv={key:0,class:"relative group"},wv={class:"tooltip tooltip-left"},xv={key:0},kv=["onClick"],Av={class:"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden"},Cv=["src"],Iv={key:1,class:"text-sm font-medium"},Sv={class:"flex-1 text-left"},_v={class:"relative opacity-0 group-hover:opacity-100 z-10"},Tv=["onClick"],Lv={class:"text-xs font-bold"},Mv={class:"ml-1"},Ev={key:0,class:"relative group"},Rv=je({__name:"List",setup(e){const{isMobile:t}=it(),n=bs(),s=Qt(),a=ft(),o=zt(),i=Ue("showAppConfigModal"),c=Ue("tryParseJson"),l=S(100),u=xi(),f=S(!1),p=S(!1),b=S(!0),h=k(()=>s.groupList),x=k(()=>s.groupKeyWord);ke(h,()=>l.value=l.value+1),ke(x,()=>l.value=l.value+1);const w=k(()=>s.isStreamIn!==void 0?s.isStreamIn:!1),v=k(()=>u.mineApps),C=k(()=>h.value.filter(G=>x.value?G.title.includes(x.value)&&G.isSticky:G.isSticky)),A=k(()=>h.value.filter(G=>x.value?G.title.includes(x.value)&&!G.isSticky:!G.isSticky));function L(G){return H(this,null,function*(){if(w.value){o.info("AI回复中，请稍后再试");return}const{uuid:F}=G;O(F)||(yield s.setActiveGroup(F),t.value&&n.setSiderCollapsed(!0))})}function $(G){return H(this,null,function*(){const F=v.value.find(J=>J.appId===G);if(F!=null&&F.prompt,c&&i&&(F!=null&&F.prompt)){const J=c(F.prompt);if(J){const V=mt(me({},F),{id:F.appId,name:F.appName,des:F.appDes});i(V,J);return}}yield s.addNewChatGroup(G)})}function E(G){return H(this,null,function*(){event==null||event.stopPropagation(),yield s.deleteGroup(G),yield s.queryMyGroup(),t.value&&n.setSiderCollapsed(!0)})}const D=It();function O(G){return s.active===G}function T(G){return H(this,null,function*(){try{const F=yield wi({appId:G});o.success(F.data),yield u.queryMineApps()}catch(F){}})}function Y(){return H(this,null,function*(){ya().warning({title:oe("chat.clearConversation"),content:oe("chat.clearAllNonFavoriteConversations"),positiveText:oe("common.confirm"),negativeText:oe("common.cancel"),onPositiveClick:()=>H(this,null,function*(){try{yield s.delAllGroup()}catch(F){}})})})}ke(()=>a.isLogin,(G,F)=>{G===!0&&(u.queryMineApps(),s.queryMyGroup())},{immediate:!0});const P=S(!1),se=S(!1);return(G,F)=>(d(),g("div",hv,[r("div",mv,[h.value.length?(d(),g(Pe,{key:1},[r("div",{class:"flex items-center justify-between mt-3 group",onMouseenter:F[1]||(F[1]=J=>se.value=!0),onMouseleave:F[2]||(F[2]=J=>se.value=!1)},[r("p",yv,R(y(oe)("chat.myApps")),1),se.value?(d(),g("div",bv,[r("button",{class:"flex items-center justify-center text-xs text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-opacity opacity-70 hover:opacity-100 mr-3",onClick:F[0]||(F[0]=J=>b.value=!b.value)},[b.value?(d(),pe(y(Ms),{key:1,theme:"outline",size:"16"})):(d(),pe(y(Ls),{key:0,theme:"outline",size:"16"}))]),r("div",wv,R(b.value?"折叠应用":"展开应用"),1)])):I("",!0)],32),b.value?(d(),g("div",xv,[(d(!0),g(Pe,null,He(p.value?v.value:v.value.slice(0,4),J=>(d(),g("div",{key:J.appId,onClick:V=>$(J.appId),class:"relative flex items-center gap-3 my-1 px-3 py-1 break-all rounded-lg cursor-pointer hover:bg-white group dark:hover:bg-gray-800 font-medium text-sm 'text-gray-700', 'dark:bg-gray-900', 'dark:text-gray-400'"},[r("div",Av,[J.coverImg?(d(),g("img",{key:0,src:J.coverImg,alt:"app cover",class:"w-full h-full object-cover"},null,8,Cv)):(d(),g("span",Iv,R(J.appName.charAt(0)),1))]),r("button",Sv,R(J.appName),1),r("div",_v,[r("button",{class:"p-1",onClick:sn(V=>T(J.appId),["stop"]),"aria-label":"取消收藏",title:"取消收藏"},[X(y(Jr),{theme:"filled",size:"18",class:"text-primary-600"})],8,Tv)])],8,kv))),128)),v.value.length>4?(d(),g("button",{key:0,class:"relative flex items-center gap-3 px-3 break-all rounded-lg cursor-pointer text-gray-900 dark:text-gray-400 text-xs font-bold",onClick:F[3]||(F[3]=J=>p.value=!p.value)},[Ne(R(p.value?y(oe)("chat.collapse"):y(oe)("chat.more"))+" ",1),p.value?(d(),pe(y(Ms),{key:1,theme:"outline",size:"20"})):(d(),pe(y(Ls),{key:0,theme:"outline",size:"20"}))])):I("",!0),r("div",{class:"relative flex items-center gap-3 px-3 py-1 break-all rounded-lg cursor-pointer hover:bg-white group dark:hover:bg-gray-800 font-medium text-sm 'text-gray-700', 'dark:bg-gray-900', 'dark:text-gray-400'",onClick:F[4]||(F[4]=()=>{y(D).updateShowAppListComponent(!0),y(t)&&y(n).setSiderCollapsed(!0)})},[X(y(Kr),{theme:"outline",size:"25",class:"ml-1 mr-1 text-sm my-1 text-gray-600"}),Ne(" "+R(y(oe)("chat.appSquare")),1)])])):I("",!0),C.value.length?(d(),pe(qo,{key:`stickyList-${f.value}-${C.value}`,title:y(oe)("chat.favorites"),"data-sources":f.value?C.value:C.value.slice(0,5),onSelect:L,onDelete:E},null,8,["title","data-sources"])):I("",!0),C.value.length>5?(d(),g("button",{key:2,class:"relative flex items-center gap-3 px-3 break-all rounded-lg cursor-pointer text-gray-900 dark:text-gray-400 text-xs font-bold",onClick:F[5]||(F[5]=J=>f.value=!f.value)},[Ne(R(f.value?y(oe)("chat.collapse"):y(oe)("chat.more"))+" ",1),f.value?(d(),pe(y(Ms),{key:1,theme:"outline",size:"20"})):(d(),pe(y(Ls),{key:0,theme:"outline",size:"20"}))])):I("",!0),r("div",{class:"flex items-center justify-between mt-3 mb-1 group",onMouseenter:F[6]||(F[6]=J=>P.value=!0),onMouseleave:F[7]||(F[7]=J=>P.value=!1)},[r("p",Lv,[Ne(R(y(oe)("chat.historyConversations"))+" ",1),r("span",Mv,"("+R(A.value.length)+")",1)]),A.value.length>0&&P.value?(d(),g("div",Ev,[r("button",{class:"flex items-center justify-center text-xs text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-opacity opacity-70 hover:opacity-100 mr-3",onClick:Y},[X(y(Ja),{theme:"outline",size:"16"})]),F[8]||(F[8]=r("div",{class:"tooltip tooltip-left"},"清空对话",-1))])):I("",!0)],32),A.value.length?(d(),pe(qo,{key:3e3+l.value,"data-sources":A.value,onSelect:L,onDelete:E},null,8,["data-sources"])):I("",!0)],64)):(d(),g("div",vv,[X(ka,{icon:"ri:inbox-line",class:"mb-2 text-3xl"}),r("span",null,R(y(oe)("common.noData")),1)]))])]))}}),zv={class:"flex flex-col h-full flex-1"},Dv={class:"flex bg-opacity w-full justify-between items-center px-4 dark:bg-gray-900 pt-3"},jv={class:"w-full py-1 text-primary-600 dark:text-gray-100 text-lg font-bold flex justify-between items-center"},Pv=["src"],$v={class:"mx-auto"},Uv={class:"relative group"},Nv={key:0,class:"tooltip tooltip-right"},Fv={class:"flex-1 min-h-0 overflow-hidden"},Gv={class:"p-4 pb-1 py-2 border-t-gray-100 dark:border-t-gray-800 flex items-center justify-between text-left w-full"},Bv={class:"avatar avatar-lg avatar-bordered avatar-primary"},Ov=["src"],Wv={key:0,class:"tooltip tooltip-top"},Hv=["aria-label"],Vv=je({__name:"index",setup(e){var Z;const t=It(),n=bs(),s=Qt(),a=ft(),o=S(null),i=S(null),c=S(null),l=k(()=>a.userBalance),u=k(()=>s.currentPlugin),f=S(0),p=S(0),b=S(0),h=S(0),x=S(0),w=S(0),{isMobile:v}=it(),C=k(()=>a.isLogin),A=k(()=>a.globalConfig.clientLogoPath||Ji),L=((Z=a.globalConfig)==null?void 0:Z.siteName)||"AIWeb",$=k(()=>a.globalConfig.model3Name||oe("chat.ordinaryPoints")),E=k(()=>a.globalConfig.model4Name)||oe("chat.advancedPoints"),D=k(()=>a.globalConfig.drawMjName)||oe("chat.drawingPoints"),O=k(()=>a.userInfo.avatar),T=k(()=>n.siderCollapsed),Y=k(()=>s.groupList.find(Q=>Q.uuid===s.active)),P=k(()=>{var ee;try{return JSON.parse(((ee=Y.value)==null?void 0:ee.config)||"{}")}catch(Q){return{}}}),se=k(()=>s==null?void 0:s.activeModelDeductType),G=k(()=>{var Ae;const ee=((Ae=u==null?void 0:u.value)==null?void 0:Ae.deductType)||P.value.deductType||se.value;let Q,le;switch(ee){case 1:Q=l.value.sumModel3Count||0,le=$.value;break;case 2:Q=l.value.sumModel4Count||0,le=E.value;break;case 3:Q=l.value.sumDrawMjCount||0,le=D.value;break;default:Q=0,le=oe("chat.points")}return Q>99999&&(Q="不限",le="次数"),{remainingPoints:Q,buttonText:le}});function F(){n.setSiderCollapsed(!T.value)}function J(){C.value?a.logOut():a.setLoginDialog(!0)}const V=k(()=>v.value?{position:"fixed",zIndex:50}:{}),te=k(()=>v.value?{paddingBottom:"env(safe-area-inset-bottom)"}:{});ke(v,ee=>{n.setSiderCollapsed(ee)},{immediate:!0,flush:"post"});function j(ee,Q,le=1e3,Ae){const ue=performance.now(),we=Q-ee,Re=xt=>{const rt=xt-ue;if(rt<le){const nt=ee+we*(rt/le);Ae(Math.round(nt)),requestAnimationFrame(Re)}else Ae(Q)};requestAnimationFrame(Re)}ke(()=>a.userBalance.useModel3Token,(ee,Q)=>{f.value=Q||0,p.value=ee||0,o.value=ee;const le=document.getElementById("model3-count");le&&j(Q||0,ee||0,1e3,Ae=>{le.textContent=Ae.toString()})},{immediate:!0,flush:"post"}),ke(()=>a.userBalance.useModel4Token,(ee,Q)=>{b.value=Q||0,h.value=ee||0,i.value=ee},{immediate:!0,flush:"post"}),ke(()=>a.userBalance.useDrawMjToken,(ee,Q)=>{x.value=Q||0,w.value=ee||0,c.value=ee},{immediate:!0,flush:"post"}),Ge(()=>{s.queryPlugins(),s.queryMyGroup()});function z(ee){v.value?(t.updateMobileSettingsDialog(!0,ee),n.setSiderCollapsed(!0)):t.updateSettingsDialog(!0,ee)}return(ee,Q)=>(d(),g("div",null,[r("div",{class:ne(["fixed top-0 left-0 z-40 h-full transition-all duration-500 ease-in-out",[(y(v),"w-[260px]"),T.value?"-translate-x-full":"translate-x-0"]]),style:kt(V.value)},[r("div",{class:"flex flex-col h-full bg-opacity dark:bg-gray-900 select-none",style:kt(te.value)},[r("main",zv,[r("div",Dv,[r("div",jv,[r("img",{src:A.value,alt:"Logo",class:"h-7 w-7 rounded-lg"},null,8,Pv),r("span",$v,R(y(L)),1),r("div",Uv,[r("button",{type:"button",class:"btn-icon btn-icon-collapse btn-md",onClick:F,"aria-label":"折叠侧边栏"},[X(y(el),{size:"22"})]),y(v)?I("",!0):(d(),g("div",Nv,"折叠侧栏"))])])]),r("div",Fv,[X(Rv)]),r("div",Gv,[C.value?(d(),g("div",{key:0,onClick:Q[0]||(Q[0]=le=>z(void 0)),class:"flex items-center justify-center cursor-pointer group relative mr-3 mb-2",role:"button","aria-label":"打开设置中心",tabindex:"0"},[r("div",Bv,[O.value?(d(),g("img",{key:0,src:O.value,class:"w-full h-full object-cover",alt:"用户头像"},null,8,Ov)):I("",!0),O.value?I("",!0):(d(),pe(y(fi),{key:1,theme:"outline",size:"20",class:"text-white","aria-hidden":"true"}))]),y(v)?I("",!0):(d(),g("div",Wv,"设置"))])):I("",!0),C.value?I("",!0):(d(),g("div",{key:1,onClick:J,class:"flex flex-1 items-center justify-center cursor-pointer"},Q[2]||(Q[2]=[r("button",{type:"button",class:"btn btn-pill mb-2 w-full","aria-label":"登录或注册账号"},[r("span",null,"登录 / 注册")],-1)]))),C.value?(d(),g("div",{key:2,onClick:Q[1]||(Q[1]=le=>z(y(hn).MEMBER)),class:"flex flex-1 items-center justify-center cursor-pointer"},[r("button",{type:"button",class:ne(["btn btn-pill mb-2 w-full h-9",{"text-amber-500":Number(G.value.remainingPoints)<10}]),"aria-label":Number(G.value.remainingPoints)<10?"打开VIP中心":`您有${G.value.remainingPoints}${G.value.buttonText}`},[Number(G.value.remainingPoints)<10||G.value.remainingPoints==="不限"?(d(),pe(y(ta),{key:0,theme:"filled",size:"18",class:"mr-3 text-yellow-400 dark:text-yellow-600"})):I("",!0),r("span",null,R(Number(G.value.remainingPoints)<10?y(oe)("chat.vipCenter"):`${G.value.remainingPoints}${G.value.buttonText}`),1)],10,Hv)])):I("",!0)])])],4)],6),y(v)?Xe((d(),g("div",{key:0,class:"fixed inset-0 z-40 bg-black/40 transition-opacity duration-200 ease-in-out",onClick:F},null,512)),[[va,!T.value]]):I("",!0)]))}}),ur=[{title:"英语翻译官",prompt:"我希望你能担任英语翻译、拼写校对和修辞改进的角色。我会用任何语言和你交流，你会识别语言，将其翻译并用更为优美和精炼的英语回答我。请将我简单的词汇和句子替换成更为优美和高雅的表达方式，确保意思不变，但使其更具文学性。请仅回答更正和改进的部分，不要写解释。我的第一句话是“how are you ?”，请翻译它。",icon:"ri:ai-generate"},{title:"心理学家",prompt:"我想让你扮演一个心理学家。我会告诉你我的想法。我希望你能给我科学的建议，让我感觉更好。我的第一个想法，{ 在这里输入你的想法，如果你解释得更详细，我想你会得到更准确的答案。}",icon:"ri:heart-line"},{title:"产品经理",prompt:"请确认我的以下请求。请您作为产品经理回复我。我将会提供一个主题，您将帮助我编写一份包括以下章节标题的PRD文档：主题、简介、问题陈述、目标与目的、用户故事、技术要求、收益、KPI指标、开发风险以及结论。在我要求具体主题、功能或开发的PRD之前，请不要先写任何一份PRD文档。",icon:"ri:projector-line"},{title:"如何学做菜",prompt:"我要你做我的私人厨师。我会告诉你我的饮食偏好和过敏，你会建议我尝试的食谱。你应该只回复你推荐的食谱，别无其他。不要写解释。我的第一个请求是“我是一名素食主义者，我正在寻找健康的晚餐点子。”",icon:"ri:restaurant-line"},{title:"规划一个去上海的旅游攻略 参观博物馆",prompt:"我想让你做一个旅游指南。我会把我的位置写给你，你会推荐一个靠近我的位置的地方。在某些情况下，我还会告诉您我将访问的地方类型。您还会向我推荐靠近我的第一个位置的类似类型的地方。我的第一个建议请求是“我在上海，我只想参观博物馆。”",icon:"ri:map-pin-line"},{title:"穿越时空",prompt:"如果你能穿越时空，你会去哪个时代？",icon:"ri:time-line"},{title:"量子力学",prompt:"解释一下量子力学是什么？",icon:"ri:flask-line"},{title:"人工智能",prompt:"介绍一下人工智能的历史",icon:"ri:robot-line"},{title:"深度学习",prompt:"讲解一下深度学习是如何工作的？",icon:"ri:brain-line"},{title:"冯诺依曼体系结构",prompt:"请举例说明什么是冯诺依曼体系结构？",icon:"ri:computer-line"},{title:"红楼梦情感分析",prompt:"请分析《红楼梦》中林黛玉与贾宝玉的情感关系。",icon:"ri:book-2-line"},{title:"100米短跑训练",prompt:"如何训练才能提高100米短跑成绩？",icon:"ri:run-line"},{title:"北京旅游攻略",prompt:"请推荐一份适合初次来中国的外国人的北京旅游攻略。",icon:"ri:road-map-line"},{title:"低GI饮食",prompt:"什么是低GI饮食？这种饮食有哪些好处？",icon:"ri:restaurant-2-line",iconColor:"text-orange-500"},{title:"全球环境问题",prompt:"请列出目前全球主要面临的三大环境问题，并简单阐述其影响和应对措施。",icon:"ri:earth-line"},{title:"提高社交影响力",prompt:"在社交场合，如何提高自己的感染力和影响力？",icon:"ri:team-line"},{title:"地中海地理特征",prompt:"请描述一下地中海的地理特征，以及这些特征对于古代世界的影响。",icon:"ri:map-pin-line"},{title:"《肖申克的救赎》影评",prompt:"请评价电影《肖申克的救赎》的剧情、角色塑造和拍摄手法。",icon:"ri:film-line"},{title:"苹果公司成功分析",prompt:"为什么苹果公司的产品总是比其他公司的产品更受欢迎？请从市场策略、产品设计、品牌形象等方面进行分析。",icon:"ri:apple-line"},{title:"健康饮食计划",prompt:"如何制定一份健康的饮食计划？",icon:"ri:heart-line"},{title:"编程学习指南",prompt:"怎样学习编程？",icon:"ri:code-line"},{title:"巴厘岛旅游景点",prompt:"在巴厘岛旅游有哪些值得参观的景点？",icon:"ri:map-pin-2-line"},{title:"处理亲密关系分歧",prompt:"如何处理亲密关系中的分歧？",icon:"ri:heart-2-line"},{title:"费马大定理证明",prompt:"如何证明费马大定理？",icon:"ri:function-line"},{title:"吸烟相关疾病预防",prompt:"长期吸烟引起的疾病有哪些？应该如何预防？",icon:"ri:lungs-line"},{title:"克服拖延症",prompt:"如何克服拖延症？",icon:"ri:time-line"},{title:"减少家庭垃圾",prompt:"如何减少家庭垃圾产生？",icon:"ri:recycle-line"},{title:"股票价值评估",prompt:"如何评估股票的价值？",icon:"ri:stock-line"},{title:"自信的社交表现",prompt:"如何在社交场合自信地表现自己？",icon:"ri:team-line"},{title:"推荐科幻电影",prompt:"给我一个最近评分不错的科幻电影的名字和简介",icon:"ri:movie-line"},{title:"英文翻译校对",prompt:"将下面这句英文翻译成中文并纠正其中的语法错误：'Me and him goes to the store yesterday.'",icon:"ri:translate-2",iconColor:"text-orange-500"},{title:"科技类大市值股票",prompt:"给我一些市值超过1000亿美元的科技类股票",icon:"ri:bar-chart-box-line"},{title:"商品销售量预测",prompt:"基于历史销售数据，预测下周某商品的销售量。",icon:"ri:line-chart-line",iconColor:"text-cyan-500"},{title:"思念诗歌创作",prompt:"请用七言绝句写一首表达思念之情的诗歌。",icon:"ri:quill-pen-line"},{title:"情侣约会餐厅推荐",prompt:"给我一个适合情侣约会的餐厅的名字和地址。",icon:"ri:restaurant-2-line"},{title:"西班牙旅游行程规划",prompt:"我计划去西班牙旅游，请帮我安排一个10天的行程。",icon:"ri:suitcase-3-line",iconColor:"text-orange-500"},{title:"电影分类归类",prompt:"将电影从爱情片、动作片和恐怖片三种分类中分别归类。",icon:"ri:film-line"},{title:"豆腐美食推荐",prompt:"推荐一道以豆腐为主要原料的美食，附上制作方法。",icon:"ri:restaurant-line"},{title:"流行华语歌曲推荐",prompt:"推荐最近流行的三首华语歌曲，并简要介绍它们的风格和歌词主题。",icon:"ri:music-line"},{title:"减少塑料污染生活指南",prompt:"请提供三条减少塑料污染的生活指南。",icon:"ri:leaf-line"},{title:"团队合作处理矛盾",prompt:"如何在团队合作中处理与同事之间的矛盾？",icon:"ri:team-line"},{title:"前景股票投资",prompt:"你认为现在买入哪些股票比较有前景？",icon:"ri:stock-line"},{title:"科幻片推荐",prompt:"你能否给我推荐一部最近上映的好看的科幻片？",icon:"ri:film-line"},{title:"三亚旅游攻略",prompt:"希望去三亚旅游，你能提供一份详细的旅游攻略吗？",icon:"ri:suitcase-2-line",iconColor:"text-orange-500"},{title:"意大利面烹饪技巧",prompt:"我想学做意大利面，你有什么简单易学的做法推荐吗？",icon:"ri:restaurant-line"},{title:"缓解焦虑的方法",prompt:"我感到很紧张，有什么方法能够缓解焦虑吗？",icon:"ri:heart-pulse-line"},{title:"电商平台投诉处理",prompt:"我在某电商平台购买的商品质量不佳，该如何向平台进行投诉处理？",icon:"ri:feedback-line"},{title:"有效学外语的方法",prompt:"你觉得学外语最有效的方法是什么？",icon:"ri:translate-2"},{title:"职场发展建议",prompt:"我正在寻找新的工作机会，有哪些职业领域前景较好？",icon:"ri:briefcase-line",iconColor:"text-cyan-500"},{title:"日本旅游攻略",prompt:"提供至少三个去日本旅游必去的景点，并描述其特色和适合的旅游时间。",icon:"ri:map-pin-line"},{title:"提高保险销售业绩",prompt:"如何提高保险销售员的业绩？",icon:"ri:money-dollar-box-line"},{title:"公司网站改版建议",prompt:"公司网站需要进行改版，请列举至少五个需要更新的页面元素并说明更新的理由。",icon:"ri:layout-5-line"},{title:"印度首都查询",prompt:"请问印度的首都是哪里？",icon:"ri:flag-line"},{title:"红旗渠修建历史",prompt:"请问红旗渠修建的时间和地点分别是什么？",icon:"ri:history-line"},{title:"DNA结构与功能",prompt:"请简要介绍一下DNA的结构及其功能。",icon:"ri:dna-line"},{title:"GDP定义与计算",prompt:"请问什么是GDP？如何计算GDP？",icon:"ri:bar-chart-2-line"},{title:"原子核组成",prompt:"请问原子核由哪些粒子组成？它们各自的电荷和质量分别是多少？",icon:"ri:leaf-line"},{title:"莫扎特代表作",prompt:"请问莫扎特的代表作有哪些？",icon:"ri:music-2-line"},{title:"汉字词源",prompt:"请问“汉字”这个词最早出现的时间和在哪本书中出现的？",icon:"ri:book-line",iconColor:"text-orange-500"},{title:"全运会历史",prompt:"请问全运会是哪年开始举办的？每隔几年举办一次？",icon:"ri:football-line"},{title:"石油用途",prompt:"请问石油的主要用途有哪些？",icon:"ri:oil-line"},{title:"心脏起搏器介绍",prompt:"请简要介绍一下心脏起搏器的原理和使用方法。",icon:"ri:heart-2-line",iconColor:"text-cyan-500"},{title:"观众情感分析",prompt:"这部电影的观众反应如何？",icon:"ri:emotion-laugh-line"},{title:"沙滩美景短文",prompt:"请写出一篇描述橙色阳光下沙滩美景的短文。",icon:"ri:sun-line",iconColor:"text-orange-500"},{title:"亚马逊财报数据查询",prompt:"亚马逊公司的年度财报数据是多少？",icon:"ri:money-dollar-box-line"},{title:"苹果新产品新闻",prompt:"请问最近有关于苹果公司新发布产品的新闻吗？",icon:"ri:apple-line"},{title:"一加与华为手机性能对比",prompt:"请比较一加手机和华为手机的性能差异。",icon:"ri:smartphone-line"},{title:"文章主要观点提取",prompt:"请从这篇文章中提取出主要观点。",icon:"ri:article-line"},{title:"用户意图分类",prompt:"用户输入“我想要预定机票”，它的意图是什么？",icon:"ri:question-line"},{title:"文章可读性修改",prompt:"请编辑这篇文章，使得它更易读。",icon:"ri:edit-line"},{title:"星期推理",prompt:"如果今天是星期三，那么后天是星期几？",icon:"ri:calendar-line",iconColor:"text-cyan-500"},{title:"微软创始人查询",prompt:"谁创办了微软公司？",icon:"ri:building-4-line"},{title:"电影类型分类",prompt:"这个电影是哪个类型的？",icon:"ri:film-line"},{title:"乐器描述",prompt:"描述一下你最喜欢的乐器。",icon:"ri:music-line",iconColor:"text-orange-500"},{title:"句子改写",prompt:"请改写这句话：“天空飘着几朵云彩。”",icon:"ri:edit-2-line"},{title:"书籍对比",prompt:"这本书和那本书有什么区别？",icon:"ri:book-line"},{title:"自然风景描写",prompt:"写一段自然风景的描写。",icon:"ri:landscape-line"},{title:"音乐年代分类",prompt:"这首歌曲属于哪个年代的音乐？",icon:"ri:music-2-line"},{title:"餐厅美食对比",prompt:"这家餐厅和那家餐厅哪家更好吃？",icon:"ri:restaurant-line"},{title:"电影喜好",prompt:"把这句话翻译成英文：“我喜欢看电影，尤其是科幻电影。”",icon:"ri:movie-line"},{title:"理想度假胜地描述",prompt:"描述一下你理想中的度假胜地。",icon:"ri:tree-line",iconColor:"text-orange-500"},{title:"动物分类",prompt:"这个动物属于哪个门类？",icon:"ri:bug-line"},{title:"新闻摘要生成",prompt:"请问如何利用 GPT-3.5 生成一篇 100 字左右的新闻摘要？",icon:"ri:newspaper-line"},{title:"自动翻译实现",prompt:"请问如何让 GPT-3.5 实现从中文到英文的自动翻译？",icon:"ri:translate"},{title:"全球医疗保健评价",prompt:"你如何评价当前全球范围内的医疗保健体系？",icon:"ri:stethoscope-line"},{title:"文化多样性保护",prompt:"请问有哪些国家在法律层面上保护本国的文化多样性？",icon:"ri:global-line"},{title:"新能源普及国家",prompt:"现今世界上使用新能源最为普及的国家是哪些？",icon:"ri:flashlight-line"},{title:"股市走势预测",prompt:"你认为全球股市未来一个季度会走势如何？",icon:"ri:line-chart-line",iconColor:"text-orange-500"},{title:"前沿科技研究",prompt:"请列举一些目前全球前沿的科技研究领域。",icon:"ri:rocket-line"},{title:"社交媒体影响",prompt:"社交媒体对年轻人的影响有哪些？",icon:"ri:chat-3-line"},{title:"电商平台市场份额",prompt:"当前哪些电商平台在全球拥有最大的市场份额？",icon:"ri:shopping-cart-line",iconColor:"text-cyan-500"},{title:"气候变化影响",prompt:"气候变化对世界各地造成了哪些影响？",icon:"ri:sun-cloudy-line"},{title:"全球顶尖大学排名",prompt:"请问哪些国家拥有全球最顶尖的大学排名？",icon:"ri:school-line",iconColor:"text-orange-500"},{title:"手机发明者",prompt:"手机是谁发明的？",icon:"ri:smartphone-line"},{title:"旅行故事创作",prompt:"给我写一个关于旅行的故事。",icon:"ri:suitcase-3-line"},{title:"文章情感分析",prompt:"这篇文章中的情感倾向是积极、消极还是中性？",icon:"ri:emotion-line"},{title:"拼写错误纠正",prompt:"句子中的哪个单词拼写有误：“昨天我去了餐馆，品尝了他们的招牌菜。”",icon:"ri:check-line"},{title:"文章摘要生成",prompt:"请为这篇长文章生成一段简要的摘要。",icon:"ri:file-text-line"},{title:"任务执行指令",prompt:"请告诉我现在怎么做。",icon:"ri:task-line",iconColor:"text-orange-500"},{title:"明朝社会阶层研究",prompt:"针对明朝时期的社会阶层结构，你能列出几种不同的人群并描述他们的特征吗？",icon:"ri:book-line",iconColor:"text-brown-500"},{title:"物种区别解释",prompt:"两个相似物种的区别在哪里？请用一种易于理解的方式解释。",icon:"ri:leaf-line"},{title:"政治参与度分析",prompt:"哪些因素影响政治参与度？你认为如何激发公民参与政治？",icon:"ri:government-line"},{title:"情感分析技术",prompt:"如何利用自然语言处理技术进行情感分析？您可以列举一些常见的情感分析算法和应用场景吗？",icon:"ri:emotion-line"},{title:"经济发展水平衡量",prompt:"如何衡量一个国家的经济发展水平？您如何评估不同国家之间的贸易关系？",icon:"ri:money-dollar-circle-line"},{title:"机器学习简介",prompt:"讲述一下什么是机器学习，以及它在现代计算机科学中扮演的角色。",icon:"ri:robot-line"},{title:"气候变化影响",prompt:"近年来，气候变化对我们的环境造成了哪些影响？未来还可能会引起哪些灾难？",icon:"ri:sun-cloudy-line"},{title:"创新教育方法",prompt:"教师应该如何培养学生的创新思维和实践能力？您认为有效的教育方法是什么？",icon:"ri:lightbulb-line",iconColor:"text-orange-500"},{title:"学习心理素质",prompt:"学习一门新技能需要哪些心理素质？如何在学习过程中保持积极的情绪状态？",icon:"ri:psychotherapy-line"},{title:"未来科技趋势",prompt:"未来科技发展的趋势是什么？您认为会有哪些领域会得到革命性的改变？",icon:"ri:rocket-line"},{title:"电影推荐",prompt:"根据我的口味推荐一部近期上映的电影。",icon:"ri:film-line"},{title:"手机产品比较",prompt:"请分析一下 iPhone 和 Android 手机的优缺点，说明它们适合不同的用户群体。",icon:"ri:smartphone-line"},{title:"新闻头条创作",prompt:"请为明天的头条新闻写一个简短但有吸引力的标题，并提出三个相关问题。",icon:"ri:newspaper-line",iconColor:"text-orange-500"},{title:"市场零食品牌分析",prompt:"请列举五种最受欢迎的零食品牌，并分析其在市场上的竞争优势。",icon:"ri:shopping-bag-line"},{title:"自然之美短文",prompt:"请根据以下关键词写一篇题为“自然之美”的 300 字左右的短文：山水、湖泊、森林、鸟儿、日出日落。",icon:"ri:palette-line"},{title:"英文文本编辑",prompt:"翻译以下这段英文，同时对其进行适当的调整和编辑：He was lay down.",icon:"ri:edit-line"},{title:"近期电影推荐",prompt:"可以给我推荐几部最近比较值得观看的电影吗？",icon:"ri:film-line"},{title:"马克思主义知识问答",prompt:"马克思主义的基本原理是什么？",icon:"ri:questionnaire-line"},{title:"北京旅游攻略",prompt:"如果想去北京旅游，有哪些必去的景点和美食呢？",icon:"ri:road-map-line",iconColor:"text-orange-500"},{title:"经济形势分析",prompt:"分析一下目前国内外经济形势，对未来的发展有何预测？",icon:"ri:line-chart-line"},{title:"文章情感分类",prompt:"这篇文章是正面的还是负面的？",icon:"ri:emotion-line"},{title:"写作效率提升方法",prompt:"有哪些方法可以提高写作效率？",icon:"ri:keyboard-box-line"},{title:"电子书与纸质书对比",prompt:"阅读电子书和纸质书有什么区别？",icon:"ri:book-2-line"},{title:"论文语法修改",prompt:"请帮我修改这篇论文中的语法错误。",icon:"ri:file-edit-line"},{title:"人工智能知识查询",prompt:"什么是人工智能？",icon:"ri:robot-line"},{title:"实体识别",prompt:"在这段文字中，'苹果'指的是手机品牌还是水果？",icon:"ri:barcode-box-line"},{title:"文章主题分类",prompt:"这篇文章的主题是什么？",icon:"ri:layout-line",iconColor:"text-orange-500"},{title:"文章摘要生成",prompt:"请用一句话概括这篇文章的核心内容。",icon:"ri:file-text-line"},{title:"新上映电影推荐",prompt:"有哪些值得一看的新上映电影？",icon:"ri:movie-line"},{title:"欧洲杯赛程",prompt:"请列出近期欧洲杯足球赛程表。",icon:"ri:trophy-line",iconColor:"text-gold-500"},{title:"健康饮食方案",prompt:"有哪些适合控制体重的健康饮食方案？",icon:"ri:restaurant-line",iconColor:"text-orange-500"},{title:"日本旅游攻略",prompt:"如果我想去日本旅游，应该怎样规划我的行程和预算？",icon:"ri:suitcase-line"},{title:"最新科技新闻",prompt:"有哪些最近的科技进展值得关注？",icon:"ri:news-line"},{title:"编程语言选择",prompt:"当你需要开发一个新项目时，该如何选择合适的编程语言？",icon:"ri:code-box-line"},{title:"健康饮食搭配",prompt:"请问在平衡健康饮食方面，应该怎样搭配膳食结构？",icon:"ri:restaurant-2-line"},{title:"科技公司伦理标准",prompt:"微软、谷歌等科技公司是否有明确的伦理标准？如果有，请简要列举这些标准。",icon:"ri:shield-check-line"},{title:"机器人研究方向",prompt:"机器人研究领域都包括哪些方向？",icon:"ri:robot-line"},{title:"气候变化影响",prompt:"你认为气候变化对人类有哪些不利影响？",icon:"ri:cloud-windy-line"}],Yv={class:"mb-10 rounded px-4 py-2 text-center text-3xl font-bold text-primary-500"},Zv={key:0,class:"w-full md:max-w-[40rem]"},Xv=["onClick"],Qv={class:"mt-8 line-clamp-2 break-all overflow-hidden text-gray-600 dark:text-gray-500 flex-grow text-sm"},qv=je({__name:"index",setup(e){var w;const t=Ue("onConversation"),n=ft(),s=Qt(),a=k(()=>s.groupList),{isMobile:o}=it(),i=S();ui();const c=k(()=>{var v;return Number((v=n.globalConfig)==null?void 0:v.isHideDefaultPreset)===1}),l=()=>{i.value=ur.sort(()=>.5-Math.random()).slice(0,4)};Ge(()=>{l()});const u=k(()=>s.currentPlugin),f=((w=n.globalConfig)==null?void 0:w.siteName)||"AIWeb",p=k(()=>{var v;return(v=u.value)==null?void 0:v.parameters.includes("canvas")}),b=Ue("createNewChatGroup",()=>Promise.resolve());function h(v){return H(this,null,function*(){a.value.length===0&&(yield b());const{appId:C,prompt:A}=v;if(C&&C>0)try{yield s.addNewChatGroup(C),yield s.queryMyGroup()}catch(L){}else t({msg:A})})}const x=()=>{const v=["text-red-500","text-blue-500","text-green-500","text-yellow-500","text-purple-500","text-pink-500","text-indigo-500"];return v[Math.floor(Math.random()*v.length)]};return(v,C)=>(d(),g("div",{class:ne([[y(o)?"mb-16":"mt-8"],"px-4 select-none w-full flex flex-col items-center justify-center h-full"])},[r("h1",Yv,R(y(f)),1),c.value?I("",!0):(d(),g("div",Zv,[r("div",{class:ne([p.value?"grid-cols-2 mx-2":"grid-cols-2 sm:grid-cols-2 lg:grid-cols-4","grid gap-4 "])},[(d(!0),g(Pe,null,He(i.value,A=>(d(),g("div",{key:A.title,class:"space-y-4"},[r("button",{onClick:L=>h(A),class:"relative shadow-sm flex flex-col gap-2 rounded-xl ring-1 ring-gray-200 dark:ring-gray-700 px-3 pb-4 pt-3 text-start align-top text-sm transition dark:bg-gray-800 dark:hover:bg-gray-750 hover:bg-gray-50 w-full h-full min-h-[4rem] min-w-[8rem] flex-grow"},[X(ka,{icon:A.icon,class:ne(["mb-3 inline-block text-base absolute top-3 left-3",x()])},null,8,["icon","class"]),r("div",Qv,R(A.title),1)],8,Xv)]))),128))],2)]))],2))}});function Jv(e,t,n){return(t=function(s){var a=function(o,i){if(typeof o!="object"||!o)return o;var c=o[Symbol.toPrimitive];if(c!==void 0){var l=c.call(o,i||"default");if(typeof l!="object")return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(o)}(s,"string");return typeof a=="symbol"?a:a+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ds=[],dr={},Jo={};function Kv(e){for(var t=[],n=e.length,s=[],a=0;n>=a;a++)s.push(!0);return fr(0,e,[],t,s),t}function fr(e,t,n,s,a){var o=t.length;if(e!==o)for(var i=function(){var l=t.substring(e,c+1),u=!1;if(ds.some(function(b){return b.indexOf(l)===0})&&!t[c+1]&&a[c+1]){if(l.length===1)n.push(l);else{var f=[];ds.forEach(function(b){b.indexOf(l)===0&&f.push(b)}),n.push(f)}u=!0}else ds.indexOf(l)!==-1&&a[c+1]&&(n.push(l),u=!0);if(u){var p=s.length;fr(c+1,t,n,s,a),s.length===p&&(a[c+1]=!1),n.pop()}},c=e;o>c;c++)i();else s.push(n.join(" "))}function e0(e,t,n,s){if(!e)return!1;var a=e.split(" ");return a.forEach(function(o){o.length>0&&s&&a.push(o.charAt(0))}),n?a.some(function(o){return o.indexOf(t)===0}):a.indexOf(t)!==-1}var t0=["lü","lüe","nü","nüe"];function n0(e,t){if(!e||!t)return!1;e=e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,""),t=function(c){var l=c.replace(/\s+/g,"").toLowerCase();return t0.some(function(u){return l.includes(u)})&&(l=l.replace("ü","v")),l.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}(t);var n=e.indexOf(t);if(n!==-1)return[n,n+t.length-1];var s=Ko(e.split(""),[t.split("")],t);if(s)return s;var a,o,i=function(c){for(var l=[],u=0,f=c.length;f>u;u++){var p=c.charAt(u);l.push(dr[p]||p)}return l}(e);return Ko(i,Jo[t]||(o=[],Kv(a=t).forEach(function(c){var l=c.split(" "),u=l.length-1;l[u].indexOf(",")?l[u].split(",").forEach(function(f){l.splice(u,1,f),o.push(JSON.parse(JSON.stringify(l)))}):o.push(l)}),o.length!==0&&o[0].length===a.length||o.push(a.split("")),Jo=Jv({},a,o),o),t)}function Ko(e,t,n){for(var s=0;e.length>s;s++)for(var a=0;t.length>a;a++){var o=t[a],i=o.length,c=i===n.length,l=!0,u=0,f=0,p=0;if(e.length>=i){for(;o.length>u;u++)if(u===0&&e[s+u+f]===" ")f+=1,u-=1;else if(e[s+u+p]===" ")p+=1,u-=1;else if(!e0(e[s+u+p],o[u],!e[s+u+1]||!o[u+1],c)){l=!1;break}if(l)return[s+f,p+s+u-1]}}return!1}var pr={match:function(e){var t={},n=["ju","jun","jue","juan","qu","qun","que","xuan","xu","xue","yu","yuan","yue","yun","nve","lve"],s=["lv","lve","nv","nve"];return Object.keys(e).forEach(function(a){if(t[a]=e[a],ds.push(a),n.includes(a)){var o=(i=a).indexOf("u")!==-1?i.replace("u","v"):i.replace("v","u");t[o]=e[a],ds.push(o)}var i;if(s.includes(a)){var c=a.replace("v","ü");t[c]=e[a],ds.push(c)}}),dr=function(a){var o={};for(var i in a)for(var c=a[i],l=0,u=c.length;u>l;l++)o[c[l]]=o[c[l]]?o[c[l]]+" "+i:i;return o}(t),n0}({a:"阿啊呵腌嗄吖锕",e:"额阿俄恶鹅遏鄂厄饿峨扼娥鳄哦蛾噩愕讹锷垩婀鹗萼谔莪腭锇颚呃阏屙苊轭",ai:"爱埃艾碍癌哀挨矮隘蔼唉皑哎霭捱暧嫒嗳瑷嗌锿砹",ei:"诶",xi:"系西席息希习吸喜细析戏洗悉锡溪惜稀袭夕洒晰昔牺腊烯熙媳栖膝隙犀蹊硒兮熄曦禧嬉玺奚汐徙羲铣淅嘻歙熹矽蟋郗唏皙隰樨浠忾蜥檄郄翕阋鳃舾屣葸螅咭粞觋欷僖醯鼷裼穸饩舄禊诶菥蓰",yi:"一以已意议义益亿易医艺食依移衣异伊仪宜射遗疑毅谊亦疫役忆抑尾乙译翼蛇溢椅沂泄逸蚁夷邑怡绎彝裔姨熠贻矣屹颐倚诣胰奕翌疙弈轶蛾驿壹猗臆弋铱旖漪迤佚翊诒怿痍懿饴峄揖眙镒仡黟肄咿翳挹缢呓刈咦嶷羿钇殪荑薏蜴镱噫癔苡悒嗌瘗衤佾埸圯舣酏劓",an:"安案按岸暗鞍氨俺胺铵谙庵黯鹌桉埯犴揞厂广",han:"厂汉韩含旱寒汗涵函喊憾罕焊翰邯撼瀚憨捍酣悍鼾邗颔蚶晗菡旰顸犴焓撖",ang:"昂仰盎肮",ao:"奥澳傲熬凹鳌敖遨鏖袄坳翱嗷拗懊岙螯骜獒鏊艹媪廒聱",wa:"瓦挖娃洼袜蛙凹哇佤娲呙腽",yu:"于与育余预域予遇奥语誉玉鱼雨渔裕愈娱欲吁舆宇羽逾豫郁寓吾狱喻御浴愉禹俞邪榆愚渝尉淤虞屿峪粥驭瑜禺毓钰隅芋熨瘀迂煜昱汩於臾盂聿竽萸妪腴圄谕觎揄龉谀俣馀庾妤瘐鬻欤鹬阈嵛雩鹆圉蜮伛纡窬窳饫蓣狳肀舁蝓燠",niu:"牛纽扭钮拗妞忸狃",o:"哦噢喔",ba:"把八巴拔伯吧坝爸霸罢芭跋扒叭靶疤笆耙鲅粑岜灞钯捌菝魃茇",pa:"怕帕爬扒趴琶啪葩耙杷钯筢",pi:"被批副否皮坏辟啤匹披疲罢僻毗坯脾譬劈媲屁琵邳裨痞癖陂丕枇噼霹吡纰砒铍淠郫埤濞睥芘蚍圮鼙罴蜱疋貔仳庀擗甓陴",bi:"比必币笔毕秘避闭佛辟壁弊彼逼碧鼻臂蔽拂泌璧庇痹毙弼匕鄙陛裨贲敝蓖吡篦纰俾铋毖筚荸薜婢哔跸濞秕荜愎睥妣芘箅髀畀滗狴萆嬖襞舭",bai:"百白败摆伯拜柏佰掰呗擘捭稗",bo:"波博播勃拨薄佛伯玻搏柏泊舶剥渤卜驳簿脖膊簸菠礴箔铂亳钵帛擘饽跛钹趵檗啵鹁擗踣",bei:"北被备倍背杯勃贝辈悲碑臂卑悖惫蓓陂钡狈呗焙碚褙庳鞴孛鹎邶鐾",ban:"办版半班般板颁伴搬斑扮拌扳瓣坂阪绊钣瘢舨癍",pan:"判盘番潘攀盼拚畔胖叛拌蹒磐爿蟠泮袢襻丬",bin:"份宾频滨斌彬濒殡缤鬓槟摈膑玢镔豳髌傧",bang:"帮邦彭旁榜棒膀镑绑傍磅蚌谤梆浜蒡",pang:"旁庞乓磅螃彷滂逄耪胖",beng:"泵崩蚌蹦迸绷甭嘣甏堋",bao:"报保包宝暴胞薄爆炮饱抱堡剥鲍曝葆瀑豹刨褒雹孢苞煲褓趵鸨龅勹",bu:"不部步布补捕堡埔卜埠簿哺怖钚卟瓿逋晡醭钸",pu:"普暴铺浦朴堡葡谱埔扑仆蒲曝瀑溥莆圃璞濮菩蹼匍噗氆攵镨攴镤",mian:"面棉免绵缅勉眠冕娩腼渑湎沔黾宀眄",po:"破繁坡迫颇朴泊婆泼魄粕鄱珀陂叵笸泺皤钋钷",fan:"反范犯繁饭泛翻凡返番贩烦拚帆樊藩矾梵蕃钒幡畈蘩蹯燔",fu:"府服副负富复福夫妇幅付扶父符附腐赴佛浮覆辅傅伏抚赋辐腹弗肤阜袱缚甫氟斧孚敷俯拂俘咐腑孵芙涪釜脯茯馥宓绂讣呋罘麸蝠匐芾蜉跗凫滏蝮驸绋蚨砩桴赙菔呒趺苻拊阝鲋怫稃郛莩幞祓艴黻黼鳆",ben:"本奔苯笨夯贲锛畚坌",feng:"风丰封峰奉凤锋冯逢缝蜂枫疯讽烽俸沣酆砜葑唪",bian:"变便边编遍辩鞭辨贬匾扁卞汴辫砭苄蝙鳊弁窆笾煸褊碥忭缏",pian:"便片篇偏骗翩扁骈胼蹁谝犏缏",zhen:"镇真针圳振震珍阵诊填侦臻贞枕桢赈祯帧甄斟缜箴疹砧榛鸩轸稹溱蓁胗椹朕畛浈",biao:"表标彪镖裱飚膘飙镳婊骠飑杓髟鳔灬瘭",piao:"票朴漂飘嫖瓢剽缥殍瞟骠嘌莩螵",huo:"和活或货获火伙惑霍祸豁嚯藿锪蠖钬耠镬夥灬劐攉",bie:"别鳖憋瘪蹩",min:"民敏闽闵皿泯岷悯珉抿黾缗玟愍苠鳘",fen:"分份纷奋粉氛芬愤粪坟汾焚酚吩忿棼玢鼢瀵偾鲼",bing:"并病兵冰屏饼炳秉丙摒柄槟禀枋邴冫",geng:"更耕颈庚耿梗埂羹哽赓绠鲠",fang:"方放房防访纺芳仿坊妨肪邡舫彷枋鲂匚钫",xian:"现先县见线限显险献鲜洗宪纤陷闲贤仙衔掀咸嫌掺羡弦腺痫娴舷馅酰铣冼涎暹籼锨苋蚬跹岘藓燹鹇氙莶霰跣猃彡祆筅",fou:"不否缶",ca:"拆擦嚓礤",cha:"查察差茶插叉刹茬楂岔诧碴嚓喳姹杈汊衩搽槎镲苴檫馇锸猹",cai:"才采财材菜彩裁蔡猜踩睬",can:"参残餐灿惨蚕掺璨惭粲孱骖黪",shen:"信深参身神什审申甚沈伸慎渗肾绅莘呻婶娠砷蜃哂椹葚吲糁渖诜谂矧胂",cen:"参岑涔",san:"三参散伞叁糁馓毵",cang:"藏仓苍沧舱臧伧",zang:"藏脏葬赃臧奘驵",chen:"称陈沈沉晨琛臣尘辰衬趁忱郴宸谌碜嗔抻榇伧谶龀肜",cao:"草操曹槽糙嘈漕螬艚屮",ce:"策测册侧厕栅恻",ze:"责则泽择侧咋啧仄箦赜笮舴昃迮帻",zhai:"债择齐宅寨侧摘窄斋祭翟砦瘵哜",dao:"到道导岛倒刀盗稻蹈悼捣叨祷焘氘纛刂帱忉",ceng:"层曾蹭噌",zha:"查扎炸诈闸渣咋乍榨楂札栅眨咤柞喳喋铡蚱吒怍砟揸痄哳齄",chai:"差拆柴钗豺侪虿瘥",ci:"次此差词辞刺瓷磁兹慈茨赐祠伺雌疵鹚糍呲粢",zi:"资自子字齐咨滋仔姿紫兹孜淄籽梓鲻渍姊吱秭恣甾孳訾滓锱辎趑龇赀眦缁呲笫谘嵫髭茈粢觜耔",cuo:"措错磋挫搓撮蹉锉厝嵯痤矬瘥脞鹾",chan:"产单阐崭缠掺禅颤铲蝉搀潺蟾馋忏婵孱觇廛谄谗澶骣羼躔蒇冁",shan:"山单善陕闪衫擅汕扇掺珊禅删膳缮赡鄯栅煽姗跚鳝嬗潸讪舢苫疝掸膻钐剡蟮芟埏彡骟",zhan:"展战占站崭粘湛沾瞻颤詹斩盏辗绽毡栈蘸旃谵搌",xin:"新心信辛欣薪馨鑫芯锌忻莘昕衅歆囟忄镡",lian:"联连练廉炼脸莲恋链帘怜涟敛琏镰濂楝鲢殓潋裢裣臁奁莶蠊蔹",chang:"场长厂常偿昌唱畅倡尝肠敞倘猖娼淌裳徜昶怅嫦菖鲳阊伥苌氅惝鬯",zhang:"长张章障涨掌帐胀彰丈仗漳樟账杖璋嶂仉瘴蟑獐幛鄣嫜",chao:"超朝潮炒钞抄巢吵剿绰嘲晁焯耖怊",zhao:"着照招找召朝赵兆昭肇罩钊沼嘲爪诏濯啁棹笊",zhou:"调州周洲舟骤轴昼宙粥皱肘咒帚胄绉纣妯啁诌繇碡籀酎荮",che:"车彻撤尺扯澈掣坼砗屮",ju:"车局据具举且居剧巨聚渠距句拒俱柜菊拘炬桔惧矩鞠驹锯踞咀瞿枸掬沮莒橘飓疽钜趄踽遽琚龃椐苣裾榘狙倨榉苴讵雎锔窭鞫犋屦醵",cheng:"成程城承称盛抢乘诚呈净惩撑澄秤橙骋逞瞠丞晟铛埕塍蛏柽铖酲裎枨",rong:"容荣融绒溶蓉熔戎榕茸冗嵘肜狨蝾",sheng:"生声升胜盛乘圣剩牲甸省绳笙甥嵊晟渑眚",deng:"等登邓灯澄凳瞪蹬噔磴嶝镫簦戥",zhi:"制之治质职只志至指织支值知识直致执置止植纸拓智殖秩旨址滞氏枝芝脂帜汁肢挚稚酯掷峙炙栉侄芷窒咫吱趾痔蜘郅桎雉祉郦陟痣蛭帙枳踯徵胝栀贽祗豸鸷摭轵卮轾彘觯絷跖埴夂黹忮骘膣踬",zheng:"政正证争整征郑丁症挣蒸睁铮筝拯峥怔诤狰徵钲",tang:"堂唐糖汤塘躺趟倘棠烫淌膛搪镗傥螳溏帑羰樘醣螗耥铴瑭",chi:"持吃池迟赤驰尺斥齿翅匙痴耻炽侈弛叱啻坻眙嗤墀哧茌豉敕笞饬踟蚩柢媸魑篪褫彳鸱螭瘛眵傺",shi:"是时实事市十使世施式势视识师史示石食始士失适试什泽室似诗饰殖释驶氏硕逝湿蚀狮誓拾尸匙仕柿矢峙侍噬嗜栅拭嘘屎恃轼虱耆舐莳铈谥炻豕鲥饣螫酾筮埘弑礻蓍鲺贳",qi:"企其起期气七器汽奇齐启旗棋妻弃揭枝歧欺骑契迄亟漆戚岂稽岐琦栖缉琪泣乞砌祁崎绮祺祈凄淇杞脐麒圻憩芪伎俟畦耆葺沏萋骐鳍綦讫蕲屺颀亓碛柒啐汔綮萁嘁蛴槭欹芑桤丌蜞",chuai:"揣踹啜搋膪",tuo:"托脱拓拖妥驼陀沱鸵驮唾椭坨佗砣跎庹柁橐乇铊沲酡鼍箨柝",duo:"多度夺朵躲铎隋咄堕舵垛惰哆踱跺掇剁柁缍沲裰哚隳",xue:"学血雪削薛穴靴谑噱鳕踅泶彐",chong:"重种充冲涌崇虫宠忡憧舂茺铳艟",chou:"筹抽绸酬愁丑臭仇畴稠瞅踌惆俦瘳雠帱",qiu:"求球秋丘邱仇酋裘龟囚遒鳅虬蚯泅楸湫犰逑巯艽俅蝤赇鼽糗",xiu:"修秀休宿袖绣臭朽锈羞嗅岫溴庥馐咻髹鸺貅",chu:"出处础初助除储畜触楚厨雏矗橱锄滁躇怵绌搐刍蜍黜杵蹰亍樗憷楮",tuan:"团揣湍疃抟彖",zhui:"追坠缀揣椎锥赘惴隹骓缒",chuan:"传川船穿串喘椽舛钏遄氚巛舡",zhuan:"专转传赚砖撰篆馔啭颛",yuan:"元员院原源远愿园援圆缘袁怨渊苑宛冤媛猿垣沅塬垸鸳辕鸢瑗圜爰芫鼋橼螈眢箢掾",cuan:"窜攒篡蹿撺爨汆镩",chuang:"创床窗闯幢疮怆",zhuang:"装状庄壮撞妆幢桩奘僮戆",chui:"吹垂锤炊椎陲槌捶棰",chun:"春纯醇淳唇椿蠢鹑朐莼肫蝽",zhun:"准屯淳谆肫窀",cu:"促趋趣粗簇醋卒蹴猝蹙蔟殂徂",dun:"吨顿盾敦蹲墩囤沌钝炖盹遁趸砘礅",qu:"区去取曲趋渠趣驱屈躯衢娶祛瞿岖龋觑朐蛐癯蛆苣阒诎劬蕖蘧氍黢蠼璩麴鸲磲",xu:"需许续须序徐休蓄畜虚吁绪叙旭邪恤墟栩絮圩婿戌胥嘘浒煦酗诩朐盱蓿溆洫顼勖糈砉醑",chuo:"辍绰戳淖啜龊踔辶",zu:"组族足祖租阻卒俎诅镞菹",ji:"济机其技基记计系期际及集级几给积极己纪即继击既激绩急奇吉季齐疾迹鸡剂辑籍寄挤圾冀亟寂暨脊跻肌稽忌饥祭缉棘矶汲畸姬藉瘠骥羁妓讥稷蓟悸嫉岌叽伎鲫诘楫荠戟箕霁嵇觊麂畿玑笈犄芨唧屐髻戢佶偈笄跽蒺乩咭赍嵴虮掎齑殛鲚剞洎丌墼蕺彐芰哜",cong:"从丛匆聪葱囱琮淙枞骢苁璁",zong:"总从综宗纵踪棕粽鬃偬枞腙",cou:"凑辏腠楱",cui:"衰催崔脆翠萃粹摧璀瘁悴淬啐隹毳榱",wei:"为位委未维卫围违威伟危味微唯谓伪慰尾魏韦胃畏帷喂巍萎蔚纬潍尉渭惟薇苇炜圩娓诿玮崴桅偎逶倭猥囗葳隗痿猬涠嵬韪煨艉隹帏闱洧沩隈鲔軎",cun:"村存寸忖皴",zuo:"作做座左坐昨佐琢撮祚柞唑嘬酢怍笮阼胙",zuan:"钻纂攥缵躜",da:"大达打答搭沓瘩惮嗒哒耷鞑靼褡笪怛妲",dai:"大代带待贷毒戴袋歹呆隶逮岱傣棣怠殆黛甙埭诒绐玳呔迨",tai:"台太态泰抬胎汰钛苔薹肽跆邰鲐酞骀炱",ta:"他它她拓塔踏塌榻沓漯獭嗒挞蹋趿遢铊鳎溻闼",dan:"但单石担丹胆旦弹蛋淡诞氮郸耽殚惮儋眈疸澹掸膻啖箪聃萏瘅赕",lu:"路六陆录绿露鲁卢炉鹿禄赂芦庐碌麓颅泸卤潞鹭辘虏璐漉噜戮鲈掳橹轳逯渌蓼撸鸬栌氇胪镥簏舻辂垆",tan:"谈探坦摊弹炭坛滩贪叹谭潭碳毯瘫檀痰袒坍覃忐昙郯澹钽锬",ren:"人任认仁忍韧刃纫饪妊荏稔壬仞轫亻衽",jie:"家结解价界接节她届介阶街借杰洁截姐揭捷劫戒皆竭桔诫楷秸睫藉拮芥诘碣嗟颉蚧孑婕疖桀讦疥偈羯袷哜喈卩鲒骱",yan:"研严验演言眼烟沿延盐炎燕岩宴艳颜殷彦掩淹阎衍铅雁咽厌焰堰砚唁焉晏檐蜒奄俨腌妍谚兖筵焱偃闫嫣鄢湮赝胭琰滟阉魇酽郾恹崦芫剡鼹菸餍埏谳讠厣罨",dang:"当党档荡挡宕砀铛裆凼菪谠",tao:"套讨跳陶涛逃桃萄淘掏滔韬叨洮啕绦饕鼗",tiao:"条调挑跳迢眺苕窕笤佻啁粜髫铫祧龆蜩鲦",te:"特忑忒铽慝",de:"的地得德底锝",dei:"得",di:"的地第提低底抵弟迪递帝敌堤蒂缔滴涤翟娣笛棣荻谛狄邸嘀砥坻诋嫡镝碲骶氐柢籴羝睇觌",ti:"体提题弟替梯踢惕剔蹄棣啼屉剃涕锑倜悌逖嚏荑醍绨鹈缇裼",tui:"推退弟腿褪颓蜕忒煺",you:"有由又优游油友右邮尤忧幼犹诱悠幽佑釉柚铀鱿囿酉攸黝莠猷蝣疣呦蚴莸莜铕宥繇卣牖鼬尢蚰侑",dian:"电点店典奠甸碘淀殿垫颠滇癫巅惦掂癜玷佃踮靛钿簟坫阽",tian:"天田添填甜甸恬腆佃舔钿阗忝殄畋栝掭",zhu:"主术住注助属逐宁著筑驻朱珠祝猪诸柱竹铸株瞩嘱贮煮烛苎褚蛛拄铢洙竺蛀渚伫杼侏澍诛茱箸炷躅翥潴邾槠舳橥丶瘃麈疰",nian:"年念酿辗碾廿捻撵拈蔫鲶埝鲇辇黏",diao:"调掉雕吊钓刁貂凋碉鲷叼铫铞",yao:"要么约药邀摇耀腰遥姚窑瑶咬尧钥谣肴夭侥吆疟妖幺杳舀窕窈曜鹞爻繇徭轺铫鳐崾珧",die:"跌叠蝶迭碟爹谍牒耋佚喋堞瓞鲽垤揲蹀",she:"设社摄涉射折舍蛇拾舌奢慑赦赊佘麝歙畲厍猞揲滠",ye:"业也夜叶射野液冶喝页爷耶邪咽椰烨掖拽曳晔谒腋噎揶靥邺铘揲",xie:"些解协写血叶谢械鞋胁斜携懈契卸谐泄蟹邪歇泻屑挟燮榭蝎撷偕亵楔颉缬邂鲑瀣勰榍薤绁渫廨獬躞",zhe:"喆这者着著浙折哲蔗遮辙辄柘锗褶蜇蛰鹧谪赭摺乇磔螫",ding:"定订顶丁鼎盯钉锭叮仃铤町酊啶碇腚疔玎耵",diu:"丢铥",ting:"听庭停厅廷挺亭艇婷汀铤烃霆町蜓葶梃莛",dong:"动东董冬洞懂冻栋侗咚峒氡恫胴硐垌鸫岽胨",tong:"同通统童痛铜桶桐筒彤侗佟潼捅酮砼瞳恸峒仝嗵僮垌茼",zhong:"中重种众终钟忠仲衷肿踵冢盅蚣忪锺舯螽夂",dou:"都斗读豆抖兜陡逗窦渎蚪痘蔸钭篼",du:"度都独督读毒渡杜堵赌睹肚镀渎笃竺嘟犊妒牍蠹椟黩芏髑",duan:"断段短端锻缎煅椴簖",dui:"对队追敦兑堆碓镦怼憝",rui:"瑞兑锐睿芮蕊蕤蚋枘",yue:"月说约越乐跃兑阅岳粤悦曰钥栎钺樾瀹龠哕刖",tun:"吞屯囤褪豚臀饨暾氽",hui:"会回挥汇惠辉恢徽绘毁慧灰贿卉悔秽溃荟晖彗讳诲珲堕诙蕙晦睢麾烩茴喙桧蛔洄浍虺恚蟪咴隳缋哕",wu:"务物无五武午吴舞伍污乌误亡恶屋晤悟吾雾芜梧勿巫侮坞毋诬呜钨邬捂鹜兀婺妩於戊鹉浯蜈唔骛仵焐芴鋈庑鼯牾怃圬忤痦迕杌寤阢",ya:"亚压雅牙押鸭呀轧涯崖邪芽哑讶鸦娅衙丫蚜碣垭伢氩桠琊揠吖睚痖疋迓岈砑",he:"和合河何核盖贺喝赫荷盒鹤吓呵苛禾菏壑褐涸阂阖劾诃颌嗬貉曷翮纥盍",wo:"我握窝沃卧挝涡斡渥幄蜗喔倭莴龌肟硪",en:"恩摁蒽",n:"嗯唔",er:"而二尔儿耳迩饵洱贰铒珥佴鸸鲕",fa:"发法罚乏伐阀筏砝垡珐",quan:"全权券泉圈拳劝犬铨痊诠荃醛蜷颧绻犭筌鬈悛辁畎",fei:"费非飞肥废菲肺啡沸匪斐蜚妃诽扉翡霏吠绯腓痱芾淝悱狒榧砩鲱篚镄",pei:"配培坏赔佩陪沛裴胚妃霈淠旆帔呸醅辔锫",ping:"平评凭瓶冯屏萍苹乒坪枰娉俜鲆",fo:"佛",hu:"和护户核湖互乎呼胡戏忽虎沪糊壶葫狐蝴弧瑚浒鹄琥扈唬滹惚祜囫斛笏芴醐猢怙唿戽槲觳煳鹕冱瓠虍岵鹱烀轷",ga:"夹咖嘎尬噶旮伽尕钆尜",ge:"个合各革格歌哥盖隔割阁戈葛鸽搁胳舸疙铬骼蛤咯圪镉颌仡硌嗝鬲膈纥袼搿塥哿虼",ha:"哈蛤铪",xia:"下夏峡厦辖霞夹虾狭吓侠暇遐瞎匣瑕唬呷黠硖罅狎瘕柙",gai:"改该盖概溉钙丐芥赅垓陔戤",hai:"海还害孩亥咳骸骇氦嗨胲醢",gan:"干感赶敢甘肝杆赣乾柑尴竿秆橄矸淦苷擀酐绀泔坩旰疳澉",gang:"港钢刚岗纲冈杠缸扛肛罡戆筻",jiang:"将强江港奖讲降疆蒋姜浆匠酱僵桨绛缰犟豇礓洚茳糨耩",hang:"行航杭巷夯吭桁沆绗颃",gong:"工公共供功红贡攻宫巩龚恭拱躬弓汞蚣珙觥肱廾",hong:"红宏洪轰虹鸿弘哄烘泓訇蕻闳讧荭黉薨",guang:"广光逛潢犷胱咣桄",qiong:"穷琼穹邛茕筇跫蛩銎",gao:"高告搞稿膏糕镐皋羔锆杲郜睾诰藁篙缟槁槔",hao:"好号毫豪耗浩郝皓昊皋蒿壕灏嚎濠蚝貉颢嗥薅嚆",li:"理力利立里李历例离励礼丽黎璃厉厘粒莉梨隶栗荔沥犁漓哩狸藜罹篱鲤砺吏澧俐骊溧砾莅锂笠蠡蛎痢雳俪傈醴栎郦俚枥喱逦娌鹂戾砬唳坜疠蜊黧猁鬲粝蓠呖跞疬缡鲡鳢嫠詈悝苈篥轹",jia:"家加价假佳架甲嘉贾驾嫁夹稼钾挟拮迦伽颊浃枷戛荚痂颉镓笳珈岬胛袈郏葭袷瘕铗跏蛱恝哿",luo:"啰落罗络洛逻螺锣骆萝裸漯烙摞骡咯箩珞捋荦硌雒椤镙跞瘰泺脶猡倮蠃",ke:"可科克客刻课颗渴壳柯棵呵坷恪苛咳磕珂稞瞌溘轲窠嗑疴蝌岢铪颏髁蚵缂氪骒钶锞",qia:"卡恰洽掐髂袷咭葜",gei:"给",gen:"根跟亘艮哏茛",hen:"很狠恨痕哏",gou:"构购够句沟狗钩拘勾苟垢枸篝佝媾诟岣彀缑笱鞲觏遘",kou:"口扣寇叩抠佝蔻芤眍筘",gu:"股古顾故固鼓骨估谷贾姑孤雇辜菇沽咕呱锢钴箍汩梏痼崮轱鸪牯蛊诂毂鹘菰罟嘏臌觚瞽蛄酤牿鲴",pai:"牌排派拍迫徘湃俳哌蒎",gua:"括挂瓜刮寡卦呱褂剐胍诖鸹栝呙",tou:"钭投头透偷愉骰亠",guai:"怪拐乖",kuai:"会快块筷脍蒯侩浍郐蒉狯哙",guan:"关管观馆官贯冠惯灌罐莞纶棺斡矜倌鹳鳏盥掼涫",wan:"万完晚湾玩碗顽挽弯蔓丸莞皖宛婉腕蜿惋烷琬畹豌剜纨绾脘菀芄箢",ne:"呢哪呐讷疒",gui:"规贵归轨桂柜圭鬼硅瑰跪龟匮闺诡癸鳜桧皈鲑刽晷傀眭妫炅庋簋刿宄匦",jun:"军均俊君峻菌竣钧骏龟浚隽郡筠皲麇捃",jiong:"窘炯迥炅冂扃",jue:"决绝角觉掘崛诀獗抉爵嚼倔厥蕨攫珏矍蹶谲镢鳜噱桷噘撅橛孓觖劂爝",gun:"滚棍辊衮磙鲧绲丨",hun:"婚混魂浑昏棍珲荤馄诨溷阍",guo:"国过果郭锅裹帼涡椁囗蝈虢聒埚掴猓崞蜾呙馘",hei:"黑嘿嗨",kan:"看刊勘堪坎砍侃嵌槛瞰阚龛戡凵莰",heng:"衡横恒亨哼珩桁蘅",mo:"万没么模末冒莫摩墨默磨摸漠脉膜魔沫陌抹寞蘑摹蓦馍茉嘿谟秣蟆貉嫫镆殁耱嬷麽瘼貊貘",peng:"鹏朋彭膨蓬碰苹棚捧亨烹篷澎抨硼怦砰嘭蟛堋",hou:"后候厚侯猴喉吼逅篌糇骺後鲎瘊堠",hua:"化华划话花画滑哗豁骅桦猾铧砉",huai:"怀坏淮徊槐踝",huan:"还环换欢患缓唤焕幻痪桓寰涣宦垸洹浣豢奂郇圜獾鲩鬟萑逭漶锾缳擐",xun:"讯训迅孙寻询循旬巡汛勋逊熏徇浚殉驯鲟薰荀浔洵峋埙巽郇醺恂荨窨蕈曛獯",huang:"黄荒煌皇凰慌晃潢谎惶簧璜恍幌湟蝗磺隍徨遑肓篁鳇蟥癀",nai:"能乃奶耐奈鼐萘氖柰佴艿",luan:"乱卵滦峦鸾栾銮挛孪脔娈",qie:"切且契窃茄砌锲怯伽惬妾趄挈郄箧慊",jian:"建间件见坚检健监减简艰践兼鉴键渐柬剑尖肩舰荐箭浅剪俭碱茧奸歼拣捡煎贱溅槛涧堑笺谏饯锏缄睑謇蹇腱菅翦戬毽笕犍硷鞯牮枧湔鲣囝裥踺搛缣鹣蒹谫僭戋趼楗",nan:"南难男楠喃囡赧腩囝蝻",qian:"前千钱签潜迁欠纤牵浅遣谦乾铅歉黔谴嵌倩钳茜虔堑钎骞阡掮钤扦芊犍荨仟芡悭缱佥愆褰凵肷岍搴箝慊椠",qiang:"强抢疆墙枪腔锵呛羌蔷襁羟跄樯戕嫱戗炝镪锖蜣",xiang:"向项相想乡象响香降像享箱羊祥湘详橡巷翔襄厢镶飨饷缃骧芗庠鲞葙蟓",jiao:"教交较校角觉叫脚缴胶轿郊焦骄浇椒礁佼蕉娇矫搅绞酵剿嚼饺窖跤蛟侥狡姣皎茭峤铰醮鲛湫徼鹪僬噍艽挢敫",zhuo:"着著缴桌卓捉琢灼浊酌拙茁涿镯淖啄濯焯倬擢斫棹诼浞禚",qiao:"桥乔侨巧悄敲俏壳雀瞧翘窍峭锹撬荞跷樵憔鞘橇峤诮谯愀鞒硗劁缲",xiao:"小效销消校晓笑肖削孝萧俏潇硝宵啸嚣霄淆哮筱逍姣箫骁枭哓绡蛸崤枵魈",si:"司四思斯食私死似丝饲寺肆撕泗伺嗣祀厮驷嘶锶俟巳蛳咝耜笥纟糸鸶缌澌姒汜厶兕",kai:"开凯慨岂楷恺揩锴铠忾垲剀锎蒈",jin:"进金今近仅紧尽津斤禁锦劲晋谨筋巾浸襟靳瑾烬缙钅矜觐堇馑荩噤廑妗槿赆衿卺",qin:"亲勤侵秦钦琴禽芹沁寝擒覃噙矜嗪揿溱芩衾廑锓吣檎螓",jing:"经京精境竞景警竟井惊径静劲敬净镜睛晶颈荆兢靖泾憬鲸茎腈菁胫阱旌粳靓痉箐儆迳婧肼刭弪獍",ying:"应营影英景迎映硬盈赢颖婴鹰荧莹樱瑛蝇萦莺颍膺缨瀛楹罂荥萤鹦滢蓥郢茔嘤璎嬴瘿媵撄潆",jiu:"就究九酒久救旧纠舅灸疚揪咎韭玖臼柩赳鸠鹫厩啾阄桕僦鬏",zui:"最罪嘴醉咀蕞觜",juan:"卷捐圈眷娟倦绢隽镌涓鹃鄄蠲狷锩桊",suan:"算酸蒜狻",yun:"员运云允孕蕴韵酝耘晕匀芸陨纭郧筠恽韫郓氲殒愠昀菀狁",qun:"群裙逡麇",ka:"卡喀咖咔咯佧胩",kang:"康抗扛慷炕亢糠伉钪闶",keng:"坑铿吭",kao:"考靠烤拷铐栲尻犒",ken:"肯垦恳啃龈裉",yin:"因引银印音饮阴隐姻殷淫尹荫吟瘾寅茵圻垠鄞湮蚓氤胤龈窨喑铟洇狺夤廴吲霪茚堙",kong:"空控孔恐倥崆箜",ku:"苦库哭酷裤枯窟挎骷堀绔刳喾",kua:"跨夸垮挎胯侉",kui:"亏奎愧魁馈溃匮葵窥盔逵睽馗聩喟夔篑岿喹揆隗傀暌跬蒉愦悝蝰",kuan:"款宽髋",kuang:"况矿框狂旷眶匡筐邝圹哐贶夼诳诓纩",que:"确却缺雀鹊阙瘸榷炔阕悫",kun:"困昆坤捆琨锟鲲醌髡悃阃",kuo:"扩括阔廓蛞",la:"拉落垃腊啦辣蜡喇剌旯砬邋瘌",lai:"来莱赖睐徕籁涞赉濑癞崃疠铼",lan:"兰览蓝篮栏岚烂滥缆揽澜拦懒榄斓婪阑褴罱啉谰镧漤",lin:"林临邻赁琳磷淋麟霖鳞凛拎遴蔺吝粼嶙躏廪檩啉辚膦瞵懔",lang:"浪朗郎廊狼琅榔螂阆锒莨啷蒗稂",liang:"量两粮良辆亮梁凉谅粱晾靓踉莨椋魉墚",lao:"老劳落络牢捞涝烙姥佬崂唠酪潦痨醪铑铹栳耢",mu:"目模木亩幕母牧莫穆姆墓慕牟牡募睦缪沐暮拇姥钼苜仫毪坶",le:"了乐勒肋叻鳓嘞仂泐",lei:"类累雷勒泪蕾垒磊擂镭肋羸耒儡嫘缧酹嘞诔檑",sui:"随岁虽碎尿隧遂髓穗绥隋邃睢祟濉燧谇眭荽",lie:"列烈劣裂猎冽咧趔洌鬣埒捩躐",leng:"冷愣棱楞塄",ling:"领令另零灵龄陵岭凌玲铃菱棱伶羚苓聆翎泠瓴囹绫呤棂蛉酃鲮柃",lia:"俩",liao:"了料疗辽廖聊寥缪僚燎缭撂撩嘹潦镣寮蓼獠钌尥鹩",liu:"流刘六留柳瘤硫溜碌浏榴琉馏遛鎏骝绺镏旒熘鹨锍",lun:"论轮伦仑纶沦抡囵",lv:"率律旅绿虑履吕铝屡氯缕滤侣驴榈闾偻褛捋膂稆",lou:"楼露漏陋娄搂篓喽镂偻瘘髅耧蝼嵝蒌",mao:"贸毛矛冒貌茂茅帽猫髦锚懋袤牦卯铆耄峁瑁蟊茆蝥旄泖昴瞀",long:"龙隆弄垄笼拢聋陇胧珑窿茏咙砻垅泷栊癃",nong:"农浓弄脓侬哝",shuang:"双爽霜孀泷",shu:"术书数属树输束述署熟殊蔬舒疏鼠淑叔暑枢墅俞曙抒竖蜀薯梳戍恕孰沭赎庶漱塾倏澍纾姝菽黍腧秫毹殳疋摅",shuai:"率衰帅摔甩蟀",lve:"略掠锊",ma:"么马吗摩麻码妈玛嘛骂抹蚂唛蟆犸杩",me:"么麽",mai:"买卖麦迈脉埋霾荬劢",man:"满慢曼漫埋蔓瞒蛮鳗馒幔谩螨熳缦镘颟墁鞔嫚",mi:"米密秘迷弥蜜谜觅靡泌眯麋猕谧咪糜宓汨醚嘧弭脒冖幂祢縻蘼芈糸敉",men:"们门闷瞒汶扪焖懑鞔钔",mang:"忙盲茫芒氓莽蟒邙硭漭",meng:"蒙盟梦猛孟萌氓朦锰檬勐懵蟒蜢虻黾蠓艨甍艋瞢礞",miao:"苗秒妙描庙瞄缪渺淼藐缈邈鹋杪眇喵",mou:"某谋牟缪眸哞鍪蛑侔厶",miu:"缪谬",mei:"美没每煤梅媒枚妹眉魅霉昧媚玫酶镁湄寐莓袂楣糜嵋镅浼猸鹛",wen:"文问闻稳温纹吻蚊雯紊瘟汶韫刎璺玟阌",mie:"灭蔑篾乜咩蠛",ming:"明名命鸣铭冥茗溟酩瞑螟暝",na:"内南那纳拿哪娜钠呐捺衲镎肭",nei:"内那哪馁",nuo:"难诺挪娜糯懦傩喏搦锘",ruo:"若弱偌箬",nang:"囊馕囔曩攮",nao:"脑闹恼挠瑙淖孬垴铙桡呶硇猱蛲",ni:"你尼呢泥疑拟逆倪妮腻匿霓溺旎昵坭铌鲵伲怩睨猊",nen:"嫩恁",neng:"能",nin:"您恁",niao:"鸟尿溺袅脲茑嬲",nie:"摄聂捏涅镍孽捻蘖啮蹑嗫臬镊颞乜陧",niang:"娘酿",ning:"宁凝拧泞柠咛狞佞聍甯",nu:"努怒奴弩驽帑孥胬",nv:"女钕衄恧",ru:"入如女乳儒辱汝茹褥孺濡蠕嚅缛溽铷洳薷襦颥蓐",nuan:"暖",nve:"虐疟",re:"热若惹喏",ou:"区欧偶殴呕禺藕讴鸥瓯沤耦怄",pao:"跑炮泡抛刨袍咆疱庖狍匏脬",pou:"剖掊裒",pen:"喷盆湓",pie:"瞥撇苤氕丿",pin:"品贫聘频拼拚颦姘嫔榀牝",se:"色塞瑟涩啬穑铯槭",qing:"情青清请亲轻庆倾顷卿晴氢擎氰罄磬蜻箐鲭綮苘黥圊檠謦",zan:"赞暂攒堑昝簪糌瓒錾趱拶",shao:"少绍召烧稍邵哨韶捎勺梢鞘芍苕劭艄筲杓潲",sao:"扫骚嫂梢缫搔瘙臊埽缲鳋",sha:"沙厦杀纱砂啥莎刹杉傻煞鲨霎嗄痧裟挲铩唼歃",xuan:"县选宣券旋悬轩喧玄绚渲璇炫萱癣漩眩暄煊铉楦泫谖痃碹揎镟儇",ran:"然染燃冉苒髯蚺",rang:"让壤攘嚷瓤穰禳",rao:"绕扰饶娆桡荛",reng:"仍扔",ri:"日",rou:"肉柔揉糅鞣蹂",ruan:"软阮朊",run:"润闰",sa:"萨洒撒飒卅仨脎",suo:"所些索缩锁莎梭琐嗦唆唢娑蓑羧挲桫嗍睃",sai:"思赛塞腮噻鳃",shui:"说水税谁睡氵",sang:"桑丧嗓搡颡磉",sen:"森",seng:"僧",shai:"筛晒",shang:"上商尚伤赏汤裳墒晌垧觞殇熵绱",xing:"行省星腥猩惺兴刑型形邢饧醒幸杏性姓陉荇荥擤悻硎",shou:"收手受首售授守寿瘦兽狩绶艏扌",shuo:"说数硕烁朔铄妁槊蒴搠",su:"速素苏诉缩塑肃俗宿粟溯酥夙愫簌稣僳谡涑蔌嗉觫",shua:"刷耍唰",shuan:"栓拴涮闩",shun:"顺瞬舜吮",song:"送松宋讼颂耸诵嵩淞怂悚崧凇忪竦菘",sou:"艘搜擞嗽嗖叟馊薮飕嗾溲锼螋瞍",sun:"损孙笋荪榫隼狲飧",teng:"腾疼藤滕誊",tie:"铁贴帖餮萜",tu:"土突图途徒涂吐屠兔秃凸荼钍菟堍酴",wai:"外歪崴",wang:"王望往网忘亡旺汪枉妄惘罔辋魍",weng:"翁嗡瓮蓊蕹",zhua:"抓挝爪",yang:"样养央阳洋扬杨羊详氧仰秧痒漾疡泱殃恙鸯徉佯怏炀烊鞅蛘",xiong:"雄兄熊胸凶匈汹芎",yo:"哟唷",yong:"用永拥勇涌泳庸俑踊佣咏雍甬镛臃邕蛹恿慵壅痈鳙墉饔喁",za:"杂扎咱砸咋匝咂拶",zai:"在再灾载栽仔宰哉崽甾",zao:"造早遭枣噪灶燥糟凿躁藻皂澡蚤唣",zei:"贼",zen:"怎谮",zeng:"增曾综赠憎锃甑罾缯",zhei:"这",zou:"走邹奏揍诹驺陬楱鄹鲰",zhuai:"转拽",zun:"尊遵鳟樽撙",dia:"嗲",nou:"耨"})};const s0={key:1,class:"relative flex-grow mx-1 overflow-hidden",style:{"max-width":"65%"}},a0=["onClick"],o0={key:0,class:"ml-1 text-yellow-500 inline-flex items-center"},i0={class:"relative flex flex-1 w-full items-center"},r0={for:"app-search-field",class:"sr-only"},l0={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},c0=["placeholder"],u0={key:0,class:"flex justify-between items-center flex-shrink-0",style:{"max-width":"100%"}},d0=["onClick"],f0={key:0,class:"ml-1 text-yellow-500 inline-flex items-center"},p0={class:"w-full flex-grow items-start overflow-hidden"},g0=["onClick"],h0={key:0,class:"flex-shrink-0"},m0=["src"],v0={class:"text-white text-lg font-semibold tracking-wider"},y0={class:"flex-grow flex flex-col overflow-hidden"},b0={class:"flex items-center justify-between font-semibold text-sm text-gray-800 dark:text-gray-200 mb-0.5"},w0={class:"line-clamp-1 overflow-hidden text-ellipsis block flex-grow mr-2 whitespace-nowrap"},x0={key:0,class:"flex flex-nowrap items-center mb-1.5 overflow-x-auto scrollbar-hide",style:{gap:"4px"}},k0=["onClick"],A0={key:0,class:"ml-0.5 text-yellow-500 inline-flex items-center"},C0={class:"text-xs line-clamp-2 text-gray-500/90 dark:text-gray-400/80"},I0=je({__name:"index",emits:["run-app","show-member-dialog","run-app-with-data"],setup(e,{emit:t}){const n=Dr(),s=k(()=>n.userBalance),{isMobile:a}=it(),o=It(),i=zt(),c=xi(),l=S("");Qt();const u=k(()=>c.catId),f=S([]),p=S([]),b=k(()=>c.mineApps),h=S([]),x=S(0),w=Ue("showAppConfigModal"),v=Ue("tryParseJson"),C=t;function A(V){return b.value.some(te=>te.appId===V.id)}function L(){return H(this,null,function*(){var te;const V=yield bi();f.value=(te=V==null?void 0:V.data)==null?void 0:te.rows.map(j=>(j.loading=!1,j)),p.value=f.value})}const $=k(()=>{if(l.value){const V=l.value.toLowerCase();return f.value.filter(te=>pr.match(te.name,V))}return x.value===0?f.value:f.value.filter(V=>V.catId?V.catId.split(",").map(j=>Number(j.trim())).includes(x.value):!1)});function E(V){return H(this,null,function*(){V.loading=!0;try{const te=yield wi({appId:V.id});i.success(te.data),yield c.queryMineApps(),V.loading=!1}catch(te){V.loading=!1}})}function D(V){return H(this,null,function*(){var z;if(Number(V.id),(((z=V.catName)==null?void 0:z.split(",").map(Z=>Z.trim()))||[]).some(Z=>J(Z))&&!(s.value.packageId>0||s.value.expirationTime&&new Date(s.value.expirationTime)>new Date)){i.info("当前应用是会员专属应用，请开通会员后使用！"),a.value?(o.settingsActiveTab=hn.MEMBER,o.updateMobileSettingsDialog(!0)):o.updateSettingsDialog(!0,hn.MEMBER);return}if(v&&w){const Z=v(V.prompt);if(Z){w(V,Z);return}}C("run-app",V)})}function O(){return H(this,null,function*(){var j;const V=yield Dl(),te={id:0,name:oe("app.allCategories"),coverImg:"",des:""};h.value=[te,...(j=V==null?void 0:V.data)==null?void 0:j.rows]})}function T(V){x.value=V}ke(u,V=>{V?p.value=f.value.filter(te=>te.catId?te.catId.split(",").map(z=>Number(z.trim())).includes(Number(V)):!1):p.value=f.value});function Y(){const V=["bg-blue-300","bg-red-300","bg-green-300","bg-yellow-300","bg-purple-300","bg-pink-300"];return V[Math.floor(Math.random()*V.length)]}const P=S(null);function se(){P.value&&P.value.scrollBy({left:-100,behavior:"smooth"})}function G(){P.value&&P.value.scrollBy({left:100,behavior:"smooth"})}function F(V){const te=h.value.find(j=>j.name===V);te&&(T(te.id),a.value&&P.value&&P.value.scrollIntoView({behavior:"smooth"}))}function J(V){const te=h.value.find(j=>j.name===V);return te?te.isMember===1:!1}return Ge(()=>{O(),L()}),(V,te)=>(d(),g("div",{class:ne(["bg-white dark:bg-gray-900 flex flex-col h-full w-full",[y(a)?"px-2 py-2":"pb-3"]])},[r("div",{class:ne(["flex justify-between items-center mb-2 flex-shrink-0 mx-auto w-full",[y(a)?"w-full":"px-20"]])},[y(a)?I("",!0):(d(),pe(y(tl),{key:0,size:"20",class:"btn-icon btn-md flex-shrink-0 mr-1",onClick:se})),y(a)?I("",!0):(d(),g("div",s0,[r("div",{ref_key:"scrollContainer",ref:P,class:"flex items-center overflow-x-auto w-full scrollbar-hide",style:{margin:"auto","scrollbar-width":"none","-ms-overflow-style":"none"}},[(d(!0),g(Pe,null,He(h.value,(j,z)=>(d(),g("div",{key:z,onClick:Z=>T(j.id),class:ne(["btn-pill btn-md flex-none mx-1",{"btn-pill-active":x.value===j.id}])},[r("span",null,R(j.name),1),j.isMember===1?(d(),g("span",o0,[X(y(ta),{size:"14"})])):I("",!0)],10,a0))),128))],512)])),y(a)?I("",!0):(d(),pe(y(pi),{key:2,size:"20",class:"btn-icon btn-md flex-shrink-0 mx-1",onClick:G})),r("div",{class:ne(["ml-1 flex relative",[y(a)?"w-full mr-2":"w-[35%]"]])},[r("div",i0,[r("label",r0,R(y(oe)("app.searchAppNameQuickFind")),1),r("div",l0,[X(y(nl),{theme:"outline",size:"18",class:"text-gray-400"})]),Xe(r("input",{id:"app-search-field","onUpdate:modelValue":te[0]||(te[0]=j=>l.value=j),class:"input input-md w-full pl-10",placeholder:y(oe)("app.searchAppNameQuickFind"),type:"search",name:"app-search"},null,8,c0),[[At,l.value]])])],2)],2),y(a)?(d(),g("div",u0,[r("div",{ref_key:"scrollContainer",ref:P,class:"flex items-center overflow-x-auto scrollbar-hide",style:{"scrollbar-width":"none","-ms-overflow-style":"none"}},[(d(!0),g(Pe,null,He(h.value,(j,z)=>(d(),g("div",{key:z,onClick:Z=>T(j.id),class:ne(["btn-pill flex-none mx-1",{"btn-pill-active":x.value===j.id}])},[r("span",null,R(j.name),1),j.isMember===1?(d(),g("span",f0,[X(y(ta),{size:"14"})])):I("",!0)],10,d0))),128))],512)])):I("",!0),r("div",p0,[X(Hr,{name:"list",tag:"div",class:ne(["w-full h-full overflow-y-auto overflow-x-hidden custom-scrollbar grid p-1 mt-4 pb-5",[y(a)?"grid-cols-1 gap-2":" grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-3 mx-auto px-20"]]),style:{"align-content":"start"}},{default:Ct(()=>[(d(!0),g(Pe,null,He($.value,j=>(d(),g("div",{key:j.id,onClick:z=>D(j),class:"group cursor-pointer flex items-center gap-3 rounded-xl px-3 py-3 transition-colors duration-200 bg-gray-50 dark:bg-gray-750 ring-1 ring-gray-100 dark:ring-gray-750 hover:shadow-md",style:{"min-height":"7rem"}},[j.coverImg?(d(),g("div",h0,[r("img",{src:j.coverImg,class:"rounded-full w-12 h-12 shadow-sm",alt:"app-image"},null,8,m0)])):(d(),g("div",{key:1,class:ne([Y(),"flex-shrink-0 rounded-full w-12 h-12 flex items-center justify-center shadow-sm"])},[r("span",v0,R(j.name.slice(0,1)),1)],2)),r("div",y0,[r("div",b0,[r("span",w0,R(j.name),1),X(y(sl),{theme:A(j)?"filled":"outline",size:"16",fill:A(j)?"#facc15":"currentColor",class:"btn-icon-action cursor-pointer flex-shrink-0 group-hover:text-yellow-400 dark:group-hover:text-yellow-500",onClick:sn(z=>E(j),["stop"])},null,8,["theme","fill","onClick"])]),j.catName?(d(),g("div",x0,[(d(!0),g(Pe,null,He(j.catName.split(","),(z,Z)=>(d(),g("span",{key:Z,class:"text-xs px-2 py-0.5 border rounded-md cursor-pointer flex items-center whitespace-nowrap transition-colors duration-150 bg-white border-gray-100 text-gray-500 hover:bg-primary-50 hover:text-primary-600 dark:bg-gray-700 dark:border-gray-750 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-gray-300",onClick:sn(ee=>F(z.trim()),["stop"])},[r("span",null,R(z.trim()),1),J(z.trim())?(d(),g("span",A0,[X(y(ta),{size:"12"})])):I("",!0)],8,k0))),128))])):I("",!0),r("span",C0,R(j.des),1)])],8,g0))),128))]),_:1},8,["class"])])],2))}});const S0=an(I0,[["__scopeId","data-v-80a97d14"]]),_0={class:"w-full md:max-w-[40rem] mb-4"},T0={class:"flex items-center justify-center"},L0={key:0,class:"flex-shrink-0 dark:ring-gray-400 rounded-lg"},M0=["src"],E0={class:"text-white text-sm md:text-lg"},R0={class:"rounded px-4 py-2 text-3xl font-bold text-primary-500"},z0={class:"w-full md:max-w-[40rem] mb-5"},D0={class:"flex items-center justify-center"},j0={class:"w-full md:max-w-[40rem] mt-2"},P0={class:"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 justify-center"},$0=["onClick"],U0={class:"mt-8 line-clamp-2 break-all overflow-hidden text-gray-600 dark:text-gray-500 flex-grow text-sm"},N0=je({__name:"index",props:{appId:{}},setup(e){const{isMobile:t}=it(),n=S(null),s=e,a=Ue("onConversation"),o=l=>H(this,null,function*(){try{const u=yield Ws({id:l});n.value=u.data}catch(u){}});function i(l){var u;a({msg:l,model:(u=n==null?void 0:n.value)==null?void 0:u.model,modelAvatar:n.value.modelAvatar})}function c(){const l=["bg-blue-300","bg-red-300","bg-green-300","bg-yellow-300","bg-purple-300","bg-pink-300"];return l[Math.floor(Math.random()*l.length)]}return Ge(()=>{s.appId&&o(s.appId)}),ke(()=>s.appId,l=>{l&&o(l)}),(l,u)=>{var f,p,b;return n.value?(d(),g("div",{key:0,class:ne([[y(t)?"mb-16":"mt-8"],"px-4 select-none w-full flex flex-col items-center justify-center h-full"])},[r("div",_0,[r("div",T0,[n.value.coverImg?(d(),g("div",L0,[r("img",{src:n.value.coverImg,class:"rounded-full w-10 h-10 mr-4",alt:"app-image"},null,8,M0)])):(d(),g("div",{key:1,class:ne([c(),"flex-shrink-0 dark:ring-gray-400 rounded-full w-10 h-10 flex items-center justify-center mr-4"])},[r("span",E0,R(n.value.name.slice(0,1)),1)],2)),r("h1",R0,R((f=n.value)==null?void 0:f.name),1)])]),r("div",z0,[r("div",D0,[r("p",null,R((p=n.value)==null?void 0:p.des),1)])]),r("div",j0,[r("div",P0,[(d(!0),g(Pe,null,He((b=n.value)==null?void 0:b.demoData,(h,x)=>(d(),g("div",{key:x,class:"space-y-4"},[r("button",{onClick:w=>i(h),class:"relative flex flex-col gap-2 rounded-xl border border-gray-200 dark:border-gray-800 px-3 pb-4 pt-3 text-start align-top text-sm shadow-sm transition dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-50 w-full h-full min-h-[4rem] min-w-[8rem] flex-grow"},[X(ka,{class:"mb-3 inline-block text-base absolute top-3 left-3",icon:"material-symbols:tips-and-updates-outline"}),r("div",U0,R(h),1)],8,$0)]))),128))])])],2)):I("",!0)}}});function ei(e,t){if(!t){const a=new Date,o=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),c=String(a.getDate()).padStart(2,"0");t=`userFiles/${`${o}${i}/${c}`}`}const n=new FormData;n.append("file",e);const s=`/upload/file?dir=${encodeURIComponent(t)}`;return Os({url:s,data:n,headers:{"Content-Type":"multipart/form-data"}})}const F0={key:0,class:"self-start w-full select-none"},G0={class:"self-start w-full rounded-t-2xl mt-2"},B0={key:0,class:"relative w-full mb-2"},O0={key:0,class:"flex px-2 bg-opacity dark:bg-gray-750 rounded-b-md rounded-t-2xl items-center justify-start h-12 text-gray-700 dark:text-gray-400 shadow-sm"},W0={class:"w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center overflow-hidden shadow-sm border border-gray-300 mr-3"},H0=["src"],V0={key:1,class:"w-8 h-8 text-base font-medium text-gray-700 dark:text-gray-400 rounded-full flex items-center justify-center dark:bg-gray-700"},Y0={class:"text-md font-bold text-gray-600 dark:text-gray-400 mr-3 flex-shrink-0 flex justify-start"},Z0={class:"text-base text-gray-400 dark:text-gray-400 truncate pr-10"},X0={class:"flex flex-wrap px-2"},Q0=["src"],q0=["onClick"],J0={class:"px-3 flex items-center justify-start rounded-xl h-10 text-gray-700 dark:text-gray-400 border border-gray-100 shadow-sm dark:border-gray-700 transition-colors relative"},K0={key:0,class:"absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 dark:bg-gray-800 dark:bg-opacity-80 rounded-xl z-10"},ey={class:"text-gray-500 max-w-48 truncate mr-4"},ty=["onClick"],ny={key:0,class:"relative inline-block mr-2 mb-2"},sy=["src"],ay=["onClick"],oy=je({__name:"FilePreview",props:{dataBase64List:{},fileList:{},savedFiles:{},isSelectedApp:{type:Boolean},selectedApp:{}},emits:["clearData","clearSelectApp"],setup(e,{emit:t}){const n=e,s=t,a=S(null),o=S(0),i=(l,u)=>{s("clearData",l,u)},c=()=>{s("clearSelectApp")};return ke(()=>n.savedFiles,l=>{o.value=l.length},{immediate:!0,deep:!0}),(l,u)=>l.dataBase64List.length>0||l.savedFiles.length>0||l.isSelectedApp?(d(),g("div",F0,[r("div",G0,[l.isSelectedApp?(d(),g("div",B0,[l.isSelectedApp?(d(),g("div",O0,[r("div",W0,[l.selectedApp.coverImg?(d(),g("img",{key:0,src:l.selectedApp.coverImg,alt:"Cover Image",class:"w-8 h-8 rounded-full flex justify-start"},null,8,H0)):(d(),g("span",V0,R(l.selectedApp.name.charAt(0)),1))]),r("h3",Y0,R(l.selectedApp.name),1),r("p",Z0,R(l.selectedApp.des),1),r("div",{class:"absolute top-1/2 right-4 transform -translate-y-1/2 cursor-pointer text-gray-300 hover:text-gray-500",onClick:u[0]||(u[0]=f=>c())},[X(y(mn),{size:"18",class:"rounded-full"})])])):I("",!0)])):I("",!0),r("div",X0,[l.savedFiles.length>0?(d(),g(Pe,{key:0},[(d(!0),g(Pe,null,He(l.savedFiles.filter(f=>f.type==="image"),(f,p)=>(d(),g("div",{class:"relative inline-block mr-2 mb-2",key:"saved-img-"+p},[r("img",{src:f.url,class:"max-h-16 border border-gray-100 shadow-sm dark:border-gray-700 rounded-md",alt:"预览图片"},null,8,Q0),r("div",{class:"absolute top-1 right-1 cursor-pointer bg-white dark:bg-gray-700 rounded-full p-1 shadow-sm text-gray-500 hover:text-gray-700 dark:hover:text-gray-300",onClick:b=>i(l.savedFiles.indexOf(f),!0)},[X(y(mn),{class:"w-3 h-3"})],8,q0)]))),128)),(d(!0),g(Pe,null,He(l.savedFiles.filter(f=>f.type==="document"),(f,p)=>(d(),g("div",{class:"relative inline-block mr-2 mb-2",key:"saved-file-"+p},[r("div",J0,[a.value===l.savedFiles.indexOf(f)?(d(),g("div",K0,u[1]||(u[1]=[r("div",{class:"loading-animation"},[r("span")],-1)]))):I("",!0),r("span",ey,R(f.name),1)]),a.value!==l.savedFiles.indexOf(f)?(d(),g("div",{key:0,class:"absolute top-1 right-1 cursor-pointer bg-opacity dark:bg-gray-750 rounded-full p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300",onClick:b=>i(l.savedFiles.indexOf(f),!0)},[X(y(mn),{class:"w-3 h-3"})],8,ty)):I("",!0)]))),128))],64)):I("",!0),l.dataBase64List.length>0?(d(!0),g(Pe,{key:1},He(l.dataBase64List,(f,p)=>{var b;return d(),g(Pe,{key:"item-"+p},[(b=l.fileList[p])!=null&&b.type.startsWith("image/")?(d(),g("div",ny,[r("img",{src:f,class:"max-h-16 border border-gray-100 shadow-sm dark:border-gray-700 rounded-xl",alt:"预览图片"},null,8,sy),r("div",{class:"absolute top-1 right-1 cursor-pointer bg-white dark:bg-gray-700 rounded-full p-1 shadow-sm text-gray-500 hover:text-gray-700 dark:hover:text-gray-300",onClick:h=>i(p,!1)},[X(y(mn),{class:"w-3 h-3"})],8,ay)])):I("",!0)],64)}),128)):I("",!0)])])])):I("",!0)}});const iy=an(oy,[["__scopeId","data-v-a3e6a852"]]),ry=["onClick"],ly={class:"w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center overflow-hidden shadow-sm border border-gray-300 mr-3"},cy=["src"],uy={key:1,class:"w-8 h-8 text-base font-medium text-gray-700 dark:text-gray-400 rounded-full flex items-center justify-center dark:bg-gray-700"},dy={class:"text-md font-bold text-gray-600 dark:text-primary-500 mr-3 flex-shrink-0"},fy={class:"text-base text-gray-400 dark:text-gray-400 flex-grow truncate"},py={class:"relative w-full"},gy={key:0,class:"absolute right-1 top-2 z-10 group"},hy=["aria-label"],my={key:0,class:"tooltip tooltip-bottom"},vy={key:1,class:"h-20 flex items-center justify-center my-2 w-full"},yy={class:"flex flex-col items-center justify-center"},by=["placeholder"],wy={class:"flex justify-start items-center"},xy=["aria-label"],ky={key:0,class:"tooltip tooltip-top"},Ay={key:2,class:"group relative"},Cy=["aria-pressed"],Iy={key:0,class:"ml-1"},Sy={key:0,class:"tooltip tooltip-top"},_y={key:3,class:"group relative"},Ty=["aria-pressed"],Ly={key:0,class:"ml-1"},My={key:0,class:"tooltip tooltip-top"},Ey={key:4,class:"group relative"},Ry=["aria-pressed"],zy={key:0,class:"ml-1"},Dy={key:0,class:"tooltip tooltip-top"},jy={class:"flex justify-end items-center mr-1"},Py={key:0,class:"group relative"},$y=["disabled"],Uy={key:0,class:"tooltip tooltip-top"},Ny={key:1,class:"group relative"},Fy={key:0,class:"tooltip tooltip-top"},Gy=je({__name:"index",props:{dataSourcesLength:{}},emits:["pause-request"],setup(e,{emit:t}){var ro;const n=e,s=Ue("onConversation"),a=It(),o=ft(),i=Qt(),c=t,l=((ro=o.globalConfig)==null?void 0:ro.siteName)||"AIWeb",u=zt(),f=S(!0),p=S(),b=S(),h=S(!1),x=S([]),w=S(null),v=S(null),C=S(!1),A=S(!1),L=S({size:"",style:""}),$=S(!1),E=S(),D=S(!1),O=S([]);let T=null;const Y=S({accept:".pdf, .txt, .doc, .docx,.ppt,.pptx, .xlsx,.xls,.csv .md, .markdown",multiple:!0}),P=k({get:()=>i.prompt,set:M=>{i.setPrompt(M||"")}}),se=k({get:()=>i.usingNetwork,set:M=>{i.setUsingNetwork(M)}}),G=k({get:()=>i.usingDeepThinking,set:M=>{i.setUsingDeepThinking(M)}}),{isMobile:F}=it(),J=k(()=>i.currentPlugin),V=k(()=>i.isStreamIn!==void 0?i.isStreamIn:!1),te=k(()=>i.chatList),j=k(()=>String(Q==null?void 0:Q.value.modelInfo.modelName)),z=k(()=>{var M;return((M=J.value)==null?void 0:M.modelType)||Number(Q==null?void 0:Q.value.modelInfo.keyType)}),Z=k(()=>i.active),ee=k(()=>i.getChatByGroupInfo()),Q=k(()=>{var K;const M=(K=ee.value)==null?void 0:K.config;if(!M)return{};try{return JSON.parse(M)}catch(ie){return{}}}),le=k(()=>{var M,K;return Number((K=(M=Q==null?void 0:Q.value)==null?void 0:M.modelInfo)==null?void 0:K.isFileUpload)}),Ae=k(()=>{var M,K;return Number((K=(M=Q==null?void 0:Q.value)==null?void 0:M.modelInfo)==null?void 0:K.isImageUpload)}),ue=k(()=>le.value!==0),we=k(()=>Ae.value!==0),Re=k(()=>{var M,K;return((K=(M=Q==null?void 0:Q.value)==null?void 0:M.modelInfo)==null?void 0:K.isNetworkSearch)||!1}),xt=k(()=>{var M,K;return((K=(M=Q==null?void 0:Q.value)==null?void 0:M.modelInfo)==null?void 0:K.deepThinkingType)===1||!1}),rt=k(()=>a.clipboardText),nt=k(()=>V.value||(!P.value||P.value.trim()==="")&&!(Ce.value.length>0)),lt=S(!1),Ve=S(!1),ct=()=>{if(w.value){const M=w.value;if(lt.value){M.style.height="60vh";return}M.style.height="auto";const K=M.scrollHeight,ie=parseFloat(window.getComputedStyle(M).lineHeight)||20,be=ie*8,ze=Math.min(K,be);M.style.height=`${ze}px`,Ve.value=K>ie*4}},Be=()=>{const M=lt.value?w.value.style.height:null;lt.value=!lt.value,at(()=>{lt.value?w.value.style.height="60vh":M?(w.value.style.height=M,requestAnimationFrame(()=>{ct()})):ct()})};ke(P,()=>{at(()=>{ct()})},{immediate:!0});const Dt=M=>H(this,null,function*(){const ie=M.target.value;if($.value=ie.startsWith("@"),T&&clearTimeout(T),$.value&&!D.value){const Se=ie.slice(1);T=setTimeout(()=>H(this,null,function*(){if(Se.length>0)try{const be=Se.toLowerCase(),ze=O.value.filter(Ie=>pr.match(Ie.name,be));x.value=ze.slice(0,5)}catch(be){x.value=[]}else{const be=O.value.sort(()=>Math.random()-.5).slice(0,5);x.value=be}}),100)}else x.value=[]});function ae(){return H(this,null,function*(){var K;const M=yield bi();O.value=(K=M==null?void 0:M.data)==null?void 0:K.rows.map(ie=>(ie.loading=!1,ie))})}const _=k(()=>{var M,K;return String(((M=J==null?void 0:J.value)==null?void 0:M.pluginImg)||((K=Q==null?void 0:Q.value.modelInfo)==null?void 0:K.modelAvatar)||"")}),W=Ue("createNewChatGroup",()=>Promise.resolve()),re=k(()=>{var M;return((M=ee.value)==null?void 0:M.fileUrl)||""}),ge=k(()=>{if(!re.value)return[];try{return JSON.parse(re.value)}catch(M){return[]}}),q=M=>H(this,null,function*(){var jt,Mt,Gt,ks,ht,Pt,bn,Jt,Kt,_n,jn,As;if(V.value)return;i.groupList.length===0&&(yield W()),i.setStreamIn(!0);let K="",ie=((jt=J.value)==null?void 0:jt.parameters)==="mermaid"?((Mt=E==null?void 0:E.value)==null?void 0:Mt.model)||(i==null?void 0:i.activeModel):((Gt=J.value)==null?void 0:Gt.parameters)||((ks=E==null?void 0:E.value)==null?void 0:ks.model)||(i==null?void 0:i.activeModel),Se=((ht=J==null?void 0:J.value)==null?void 0:ht.pluginName)||((Pt=E==null?void 0:E.value)==null?void 0:Pt.name)||j.value;const be=(bn=J.value)!=null&&bn.parameters&&((Jt=J.value)==null?void 0:Jt.parameters)!=="mermaid"?2:z.value;let ze=((Kt=E==null?void 0:E.value)==null?void 0:Kt.coverImg)||_.value,Ie;E!=null&&E.value?Ie=(_n=E==null?void 0:E.value)==null?void 0:_n.id:Ie=(jn=ee==null?void 0:ee.value)==null?void 0:jn.appId;let fe="",Ee=re.value||"",Le=P.value||"";if(Ce.value.length>0){const $t=ve.value.filter(Bt=>Bt.type.startsWith("image/"));if($t.length>0){h.value=!0;try{const Bt=$t.map(Ln=>H(this,null,function*(){try{return(yield ei(Ln)).data}catch(Ut){return""}}));fe=(yield Promise.all(Bt)).filter(Ln=>Ln).join(",")}catch(Bt){u.error("图片上传失败")}finally{h.value=!1}}}if(Ie)try{ze=(yield Ws({id:Ie})).data.modelAvatar}catch($t){}yield i.setPrompt(""),w.value.style.height="1rem",s({msg:Le,action:K,model:ie,modelName:Se,modelType:be,modelAvatar:ze,appId:Ie,extraParam:L.value,fileUrl:Ee,imageUrl:fe,pluginParam:(As=J.value)==null?void 0:As.parameters}),i.setStreamIn(!1),h.value=!1;const Ze=ve.value.filter($t=>!$t.type.startsWith("image/")),Oe=Ce.value.filter(($t,Bt)=>{var Tn;return!((Tn=ve.value[Bt])!=null&&Tn.type.startsWith("image/"))&&Bt<ve.value.length});ve.value=Ze,Ce.value=Oe,fe=""}),he=()=>{var ie;const M=ue.value,K=we.value;M&&K?Y.value={accept:".pdf, .txt, .doc, .docx,.ppt,.pptx, .xlsx,.xls,.csv .md, .markdown, image/*",multiple:!0}:K?Y.value={accept:"image/*",multiple:!0}:M&&(Y.value={accept:".pdf, .txt, .doc, .docx, .ppt,.pptx, .xlsx,.xls,.csv .md, .markdown",multiple:!0}),p.value&&(p.value.accept=Y.value.accept,p.value.multiple=Y.value.multiple),(ie=p==null?void 0:p.value)==null||ie.click()},ve=S([]),Ce=S([]),$e=M=>H(this,null,function*(){const K=M.type.startsWith("image/"),ie=ve.value.filter(fe=>fe.type.startsWith("image/")).length;let Se=[];try{re.value&&(Se=JSON.parse(re.value))}catch(fe){Se=[]}const be=Array.isArray(Se)?Se.filter(fe=>fe.type==="image").length:0,ze=Array.isArray(Se)?Se.filter(fe=>fe.type==="document").length:0;if(K&&ie+be>=4){u.warning("图片数量已达上限（最多4张）");return}if(!K&&ze>=5){u.warning("文件数量已达上限（最多5个）");return}ve.value.push(M);const Ie=new FileReader;Ie.onload=fe=>{var Le;const Ee=(Le=fe.target)==null?void 0:Le.result;Ce.value.push(Ee),M.type.startsWith("image/")||Qe(M)},Ie.readAsDataURL(M),p.value=null}),Qe=M=>H(this,null,function*(){if(!M.type.startsWith("image/")){h.value=!0;try{const K=yield ei(M),ie={name:M.name,url:K.data,type:"document"};let Se=[];try{re.value&&(Se=JSON.parse(re.value),Array.isArray(Se)||(Se=[]),Se=Se.filter(be=>be&&typeof be=="object"&&be.name&&be.url&&(be.type==="image"||be.type==="document")))}catch(be){Se=[]}Se.push(ie),yield i.updateGroupInfo({groupId:Z.value,fileUrl:JSON.stringify(Se)}),yield i.queryMyGroup()}catch(K){u.error(`文件"${M.name}"上传失败`)}finally{h.value=!1}}}),Ye=M=>H(this,null,function*(){const ie=(M.clipboardData||window.clipboardData).items;for(const Se of ie)if(Se.kind==="file"){const be=Se.getAsFile();be&&(be.type.startsWith("image/")&&we.value?yield pt(be):!be.type.startsWith("image/")&&ue.value&&(yield Fe(be)))}}),Fe=M=>H(this,null,function*(){if(M.type.startsWith("image/"))return;if(![".pdf",".txt",".doc",".docx",".pptx",".xlsx",".md",".markdown","application/pdf","text/plain","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/markdown"].some(Ze=>Ze.startsWith(".")?M.name.toLowerCase().endsWith(Ze.toLowerCase()):M.type===Ze)){u.warning(`不支持的文件类型: ${M.name}`);return}const Se=20*1024*1024;if(M.size>Se){u.warning(`文件过大: ${M.name}，最大支持20MB`);return}let be=[];try{re.value&&(be=JSON.parse(re.value),Array.isArray(be)||(be=[]))}catch(Ze){be=[]}const ze=be.filter(Ze=>Ze.type==="document").length,Ie=ve.value.filter(Ze=>!Ze.type.startsWith("image/")).length;if(ze+Ie>=5){u.warning("文件数量已达上限");return}let fe=M.name;const Ee=25,Le=fe.split(".").pop()||"";fe.length>Ee&&(fe=fe.substring(0,Ee-Le.length-1)+"…."+Le),f.value=!0,$e(M)}),pt=M=>H(this,null,function*(){if(!M.type.startsWith("image/"))return;if(!["image/jpeg","image/png","image/gif","image/webp","image/bmp"].includes(M.type)){u.warning(`不支持的图片类型: ${M.name}，请使用jpg、png、gif、webp或bmp格式`);return}const ie=10*1024*1024;if(M.size>ie){u.warning(`图片过大: ${M.name}，最大支持10MB`);return}const Se=ve.value.filter(Le=>Le.type.startsWith("image/")).length;let be=[];try{re.value&&(be=JSON.parse(re.value),Array.isArray(be)||(be=[]))}catch(Le){be=[]}const ze=be.filter(Le=>Le.type==="image").length;if(Se+ze>=4){u.warning("图片数量已达上限");return}let Ie=M.name;const fe=25,Ee=Ie.split(".").pop()||"";Ie.length>fe&&(Ie=Ie.substring(0,fe-Ee.length-1)+"…."+Ee),f.value=!1,$e(M)}),Nt=M=>H(this,null,function*(){const K=M.target,ie=K==null?void 0:K.files;if(!ie||ie.length===0)return;const Se=Array.from(ie).filter(ze=>ze.type.startsWith("image/")),be=Array.from(ie).filter(ze=>!ze.type.startsWith("image/"));if(Se.length>0&&we.value){const ze=ve.value.filter(Ze=>Ze.type.startsWith("image/")).length;let Ie=[];try{re.value&&(Ie=JSON.parse(re.value))}catch(Ze){Ie=[]}Array.isArray(Ie)||(Ie=[]);const fe=Ie.filter(Ze=>Ze.type==="image").length,Ee=4-ze-fe;Se.length>Ee&&Ee>0?u.warning(`已选择${Se.length}张图片，但只能再添加${Ee}张图片。将只处理前${Ee}张图片。`):Ee<=0&&u.warning("图片数量已达上限（最多4张）");const Le=Se.slice(0,Math.max(0,Ee));for(const Ze of Le)yield pt(Ze)}else Se.length>0&&!we.value&&u.warning("当前模型不支持图片上传");if(be.length>0&&ue.value){let ze=[];try{re.value&&(ze=JSON.parse(re.value))}catch(Le){ze=[]}Array.isArray(ze)||(ze=[]);const fe=5-ze.filter(Le=>Le.type==="document").length;be.length>fe&&fe>0?u.warning(`已选择${be.length}个文件，但只能再添加${fe}个文件。将只处理前${fe}个文件。`):fe<=0&&u.warning("文件数量已达上限（最多5个）");const Ee=be.slice(0,Math.max(0,fe));Ee.length>0&&(h.value=!0);try{yield Promise.all(Ee.map(Le=>H(this,null,function*(){yield new Promise(Ze=>{const Oe=new FileReader;Oe.onload=jt=>{var Gt;const Mt=(Gt=jt.target)==null?void 0:Gt.result;ve.value.push(Le),Ce.value.push(Mt),Ze()},Oe.readAsDataURL(Le)}),yield Qe(Le)})))}catch(Le){}finally{h.value=!1}}else be.length>0&&!ue.value&&u.warning("当前模型不支持文件上传");K.value=""}),In=M=>H(this,null,function*(){const K=M.target,ie=K==null?void 0:K.files;if(!ie||ie.length===0)return;const Se=ve.value.filter(Le=>Le.type.startsWith("image/")).length;let be=[];try{re.value&&(be=JSON.parse(re.value))}catch(Le){be=[]}Array.isArray(be)||(be=[]);const ze=be.filter(Le=>Le.type==="image").length;if(Se+ze>=4){u.warning("图片数量已达上限（最多4张）"),K.value="";return}const Ie=4-Se-ze,fe=Array.from(ie).filter(Le=>Le.type.startsWith("image/"));fe.length>Ie&&u.warning(`已选择${fe.length}张图片，但只能再添加${Ie}张图片。将只预览前${Ie}张图片。`);const Ee=fe.slice(0,Ie);try{yield Promise.all(Ee.map(Le=>H(this,null,function*(){yield new Promise(Ze=>{const Oe=new FileReader;Oe.onload=jt=>{var Gt;const Mt=(Gt=jt.target)==null?void 0:Gt.result;ve.value.push(Le),Ce.value.push(Mt),Ze()},Oe.readAsDataURL(Le)})})))}catch(Le){}finally{K.value=""}}),Vn=()=>H(this,null,function*(){x.value=[],$.value=!1,D.value=!1,E.value=null,i.setUsingPlugin(null)}),ws=M=>{F.value?M.key==="Enter"&&M.ctrlKey&&(M.preventDefault(),q()):M.key==="Enter"&&!M.shiftKey&&(M.preventDefault(),q())},Sn=M=>H(this,null,function*(){var K;E.value=M,D.value=!0,yield i.setPrompt(""),(K=w.value)==null||K.focus()}),xs=()=>{c("pause-request"),i.setStreamIn(!1)};ke(rt,M=>H(this,null,function*(){var K;yield i.setPrompt(M),(K=w.value)==null||K.focus(),w.value.scrollTop=w.value.scrollHeight})),ke(te,M=>{M.length},{immediate:!0}),ke([le,Ae],(be,ze)=>H(this,[be,ze],function*([M,K],[ie,Se]){if(ie!==0&&M===0&&(ve.value=ve.value.filter(Ie=>Ie.type.startsWith("image/")),Ce.value=Ce.value.filter((Ie,fe)=>{var Ee;return(Ee=ve.value[fe])==null?void 0:Ee.type.startsWith("image/")}),re.value))try{const Ie=JSON.parse(re.value);if(Array.isArray(Ie)){const fe=Ie.filter(Ee=>Ee.type==="image");yield i.updateGroupInfo({groupId:Z.value,fileUrl:fe.length>0?JSON.stringify(fe):""}),yield i.queryMyGroup()}}catch(Ie){}if(Se!==0&&K===0&&(ve.value=ve.value.filter(Ie=>!Ie.type.startsWith("image/")),Ce.value=Ce.value.filter((Ie,fe)=>{var Ee;return!((Ee=ve.value[fe])!=null&&Ee.type.startsWith("image/"))}),re.value))try{const Ie=JSON.parse(re.value);if(Array.isArray(Ie)){const fe=Ie.filter(Ee=>Ee.type==="document");yield i.updateGroupInfo({groupId:Z.value,fileUrl:fe.length>0?JSON.stringify(fe):""}),yield i.queryMyGroup()}}catch(Ie){}}));const Yn=(M,K=!1)=>H(this,null,function*(){if(K)try{if(re.value){const ie=JSON.parse(re.value);Array.isArray(ie)&&M>=0&&M<ie.length&&(ie.splice(M,1),yield i.updateGroupInfo({groupId:Z.value,fileUrl:ie.length>0?JSON.stringify(ie):""}),yield i.queryMyGroup())}}catch(ie){}else M>=0&&M<Ce.value.length&&(Ce.value.splice(M,1),ve.value.splice(M,1))}),qt=S(null),zn=S(0),vn=S(null),Zn=k(()=>{const M=[];return G.value&&M.push("使用AI推理寻找深层次答案"),se.value&&M.push("使用网络搜索获取最新信息"),C.value?"松开鼠标上传文件":M.length>0?M.join("，"):`向 ${l} 发消息，使用 @ 搜索应用`}),B=k(()=>Re.value),N=k(()=>xt.value),U=k(()=>{var M;return!J.value||((M=J.value)==null?void 0:M.parameters)==="mermaid"}),ye=k({get:()=>{var M;return((M=J.value)==null?void 0:M.parameters)==="mermaid"},set:M=>{M?i.setUsingPlugin({pluginName:"Mermaid流程图",description:"支持Mermaid流程图的创建、编辑和查看，轻松可视化流程和关系",parameters:"mermaid"}):i.setUsingPlugin(null)}}),gt=M=>{M.preventDefault(),M.stopPropagation(),C.value=!0},Lt=M=>{M.preventDefault(),M.stopPropagation(),C.value=!1},Me=M=>{Ft(M)},yn=M=>{var K;M.preventDefault(),(K=M.dataTransfer)!=null&&K.types.includes("Files")&&(A.value=!0)},ut=M=>{(M.clientX<=0||M.clientY<=0||M.clientX>=window.innerWidth||M.clientY>=window.innerHeight)&&(A.value=!1)},os=M=>{A.value=!1,C.value||M.preventDefault()};Ge(()=>H(this,null,function*(){i.setPrompt(""),at(()=>{w.value&&!F.value&&w.value.focus()}),yield ae(),document.addEventListener("dragover",yn),document.addEventListener("dragleave",ut),document.addEventListener("drop",os),at(()=>{qt.value&&(zn.value=qt.value.offsetWidth),vn.value=new ResizeObserver(M=>{for(const K of M)K.target===qt.value&&(zn.value=K.contentRect.width)}),qt.value&&vn.value.observe(qt.value)})})),js(()=>{document.removeEventListener("dragover",yn),document.removeEventListener("dragleave",ut),document.removeEventListener("drop",os),vn.value&&qt.value&&(vn.value.unobserve(qt.value),vn.value.disconnect())});const Ft=(M,K)=>H(this,null,function*(){var Ze;M.preventDefault(),M.stopPropagation(),C.value=!1,A.value=!1;const ie=(Ze=M.dataTransfer)==null?void 0:Ze.files;if(!ie||ie.length===0)return;const Se=Array.from(ie),be=Se.filter(Oe=>Oe.type.startsWith("image/")),ze=Se.filter(Oe=>!Oe.type.startsWith("image/")),Ie=we.value,fe=ue.value,Ee=[],Le=[];if(be.length>0&&(Ie?Ee.push(...be):Le.push(...be.map(Oe=>({name:Oe.name,reason:"当前模型不支持图片上传"})))),ze.length>0&&(fe?Ee.push(...ze):Le.push(...ze.map(Oe=>({name:Oe.name,reason:"当前模型不支持文档上传"})))),Le.length>0){const Oe=Le.filter(Gt=>Gt.name.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)).length,jt=Le.length-Oe;let Mt="";Oe>0&&jt>0?Mt=`无法上传 ${Oe} 张图片和 ${jt} 个文档，当前模型不支持`:Oe>0?Mt=`无法上传 ${Oe} 张图片，当前模型不支持图片上传`:jt>0&&(Mt=`无法上传 ${jt} 个文档，当前模型不支持文档上传`),Mt&&u.warning(Mt)}for(const Oe of Ee)Oe.type.startsWith("image/")?yield pt(Oe):yield Fe(Oe)}),Dn=k(()=>ue.value||we.value),Aa=k(()=>ue.value&&we.value?"上传文件或图片":we.value?"上传图片":ue.value?"上传文件":"上传"),is=k(()=>zn.value>300);return(M,K)=>(d(),g("div",null,[da(M.$slots,"before-footer"),r("div",{class:ne(["flex flex-col items-center justify-center w-full",[y(F)?"px-3 pb-3":"px-2"]])},[r("footer",{ref_key:"footerRef",ref:v,class:ne(["flex flex-col items-center justify-center w-full bg-transparent",[te.value.length>0?"max-w-4xl":"max-w-3xl"]]),onDragover:gt,onDragleave:Lt,onDrop:Me},[r("div",{class:ne(["flex w-full border border-gray-400 dark:border-gray-700 hover:ring-1 hover:ring-primary-500 dark:hover:ring-primary-500 focus-within:ring-1 focus-within:ring-primary-500 dark:focus-within:ring-primary-500 justify-center items-center flex-col rounded-3xl resize-none px-2 transition-all duration-200",{"ring-1 ring-primary-500 dark:ring-primary-500":C.value,"bg-gray-50 dark:bg-gray-700/80":A.value}]),style:{minHeight:"1.5rem",position:"relative"}},[$.value&&!D.value&&x.value.length!==0?(d(),g("div",{key:0,class:"w-full z-50 bg-white my-2 px-1 py-1 justify-center items-center flex-col rounded-2xl resize-none dark:bg-gray-800 border border-gray-400 dark:border-gray-700",style:kt({minHeight:"1.5rem",position:"absolute",top:n.dataSourcesLength||y(F)?"auto":"100%",bottom:n.dataSourcesLength||y(F)?"100%":"auto",left:"50%",transform:"translateX(-50%)"})},[x.value.length!==0?(d(!0),g(Pe,{key:0},He(x.value,ie=>(d(),g("div",{key:ie.id,onClick:Se=>Sn(ie),class:"flex items-center bg-white dark:bg-gray-800 hover:bg-opacity py-2 px-2 dark:hover:bg-gray-700 rounded-2xl w-full cursor-pointer duration-150 ease-in-out"},[r("div",ly,[ie.coverImg?(d(),g("img",{key:0,src:ie.coverImg,alt:"Cover Image",class:"w-8 h-8 rounded-full flex justify-start"},null,8,cy)):(d(),g("span",uy,R(ie.name.charAt(0)),1))]),r("h3",dy,R(ie.name),1),r("p",fy,R(ie.des),1)],8,ry))),128)):I("",!0)],4)):I("",!0),X(iy,{"data-base64-list":Ce.value,"file-list":ve.value,"saved-files":ge.value,"is-selected-app":D.value,"selected-app":E.value,onClearData:Yn,onClearSelectApp:Vn},null,8,["data-base64-list","file-list","saved-files","is-selected-app","selected-app"]),r("div",py,[Ve.value?(d(),g("div",gy,[r("button",{onClick:Be,class:"btn-pill btn-sm","aria-label":lt.value?"收起输入框":"展开输入框"},[lt.value?(d(),pe(y(al),{key:0,size:"15"})):(d(),pe(y(ol),{key:1,size:"15"}))],8,hy),y(F)?I("",!0):(d(),g("div",my,R(lt.value?"收起":"展开"),1))])):I("",!0),A.value?(d(),g("div",vy,[r("div",yy,[X(y(il),{size:"28",class:ne(["mb-2",C.value?"text-primary-600 dark:text-primary-400":"text-gray-500 dark:text-gray-400"])},null,8,["class"]),r("p",{class:ne(["text-center text-sm",C.value?"text-primary-600 dark:text-primary-400 font-medium":"text-gray-500 dark:text-gray-400"])},R(C.value?"松开鼠标开始上传":"拖放文件到这里上传"),3)])])):I("",!0),Xe(r("textarea",{ref_key:"inputRef",ref:w,"onUpdate:modelValue":K[0]||(K[0]=ie=>P.value=ie),placeholder:Zn.value,class:"flex flex-grow items-center justify-center mt-3 mb-2 w-full placeholder:text-gray-400 dark:placeholder:text-gray-500 text-base resize-none dark:text-gray-400 px-2 bg-transparent custom-scrollbar transition-all duration-300 ease-in-out",onInput:ct,onKeypress:ws,onKeyup:Dt,onPaste:Ye,style:kt({maxHeight:lt.value?"none":"30vh",minHeight:"4rem"}),"aria-label":"聊天消息输入框",role:"textbox"},null,44,by),[[va,!A.value],[At,P.value]])]),r("div",{ref_key:"buttonContainerRef",ref:qt,class:"flex justify-between flex-grow w-full pb-2"},[r("div",wy,[Dn.value&&!h.value?(d(),g("div",{key:0,class:"group relative",onDragover:K[1]||(K[1]=sn(ie=>{ie.stopPropagation(),C.value=!0},["prevent"])),onDragleave:K[2]||(K[2]=sn(ie=>{ie.stopPropagation(),C.value=!1},["prevent"])),onDrop:K[3]||(K[3]=sn(ie=>{ie.stopPropagation(),C.value=!1,A.value=!1,Ft(ie)},["prevent"]))},[r("button",{type:"button",class:"btn-pill mx-1",onClick:he,"aria-label":Aa.value},[X(y(rl),{size:"15"})],8,xy),y(F)?I("",!0):(d(),g("div",ky,R(Aa.value),1))],32)):I("",!0),h.value?(d(),pe(y(ll),{key:1,size:"15",class:"p-1 mx-2 animate-rotate text-gray-500 dark:text-gray-500"})):I("",!0),r("input",{ref_key:"fileInput",ref:p,type:"file",class:"hidden",onChange:Nt},null,544),r("input",{ref_key:"imageInput",ref:b,type:"file",accept:"image/*",class:"hidden",onChange:In},null,544),N.value?(d(),g("div",Ay,[r("div",{class:ne(["btn-pill btn-md mx-1",[G.value?"btn-pill-active":""]]),onClick:K[4]||(K[4]=ie=>G.value=!G.value),role:"button","aria-pressed":G.value,"aria-label":"启用或禁用推理功能",tabindex:"0"},[X(y(gi),{size:"15"}),is.value?(d(),g("span",Iy,"推理")):I("",!0)],10,Cy),y(F)?I("",!0):(d(),g("div",Sy," AI 推理能力，帮助寻找更深层次的答案 "))])):I("",!0),B.value?(d(),g("div",_y,[r("div",{class:ne(["btn-pill btn-md mx-1",[se.value?"btn-pill-active":""]]),onClick:K[5]||(K[5]=ie=>se.value=!se.value),role:"button","aria-pressed":se.value,"aria-label":"启用或禁用网络搜索",tabindex:"0"},[X(y(hi),{size:"15"}),is.value?(d(),g("span",Ly,"搜索")):I("",!0)],10,Ty),y(F)?I("",!0):(d(),g("div",My,"启用网络搜索，获取最新信息"))])):I("",!0),U.value?(d(),g("div",Ey,[r("div",{class:ne(["btn-pill btn-md mx-1",[ye.value?"btn-pill-active":""]]),onClick:K[6]||(K[6]=ie=>ye.value=!ye.value),role:"button","aria-pressed":ye.value,"aria-label":"启用或禁用流程图功能",tabindex:"0"},[X(y(cl),{size:"15"}),is.value?(d(),g("span",zy,"图表")):I("",!0)],10,Ry),y(F)?I("",!0):(d(),g("div",Dy," 启用图表功能，支持Mermaid图表绘制 "))])):I("",!0)]),r("div",jy,[V.value?I("",!0):(d(),g("div",Py,[r("button",{type:"button",class:ne(["btn-send",{"opacity-60 cursor-not-allowed":nt.value,"h-8 w-8":y(F)}]),disabled:nt.value,onClick:K[7]||(K[7]=ie=>q()),"aria-label":"发送消息"},[X(y(ul),{size:"15"})],10,$y),y(F)?I("",!0):(d(),g("div",Uy,"发送"))])),V.value?(d(),g("div",Ny,[r("button",{type:"button",class:ne(["btn-stop",{"h-8 w-8":y(F)}]),onClick:K[8]||(K[8]=ie=>xs()),"aria-label":"停止生成"},[X(y(dl),{size:"16"})],2),y(F)?I("",!0):(d(),g("div",Fy,"停止生成"))])):I("",!0)])],512)],2)],34)],2),da(M.$slots,"after-footer")]))}}),By={class:"sticky top-0 left-0 right-0 z-30 dark:border-neutral-800 h-16 select-none"},Oy={class:"relative flex items-center justify-center min-w-0 h-full"},Wy={key:0,class:"relative group mx-1"},Hy={key:0,class:"tooltip tooltip-right"},Vy={class:"flex justify-between items-center h-full w-full"},Yy={key:0,class:"relative flex-1 flex ele-drag items-center justify-between h-full"},Zy={class:"py-1 flex items-center space-x-2"},Xy=["src"],Qy={key:1,class:"w-6 h-6 rounded-lg bg-gray-200 flex items-center justify-center"},qy={class:"text-xs"},Jy={class:"text-sm font-medium text-gray-800 dark:text-gray-200 truncate whitespace-nowrap overflow-hidden max-w-[30vw]"},Ky={key:1,class:"flex-1 flex items-center"},eb={class:"menu menu-md relative"},tb={class:"menu-trigger","aria-label":"当前对话",disabled:""},nb={class:"truncate whitespace-nowrap overflow-hidden max-w-[30vw]"},sb={key:2,class:"flex-1 flex items-center"},ab={class:"truncate whitespace-nowrap overflow-hidden max-w-[50vw]"},ob=["onClick","aria-label"],ib={class:"avatar avatar-md"},rb=["src","alt"],lb={key:1},cb={class:"menu-item-content"},ub={class:"menu-item-title"},db={key:0,class:"menu-item-description"},fb={key:0,class:"flex-shrink-0"},pb={class:"flex items-center"},gb={key:0,class:"relative group mx-1"},hb={key:0,class:"tooltip tooltip-bottom"},mb={key:3,class:"relative group mx-1"},vb={key:0,class:"tooltip tooltip-bottom"},yb={key:4,class:"relative group mx-1"},bb={key:0,class:"tooltip tooltip-bottom"},wb={key:5,class:"relative group mx-1"},xb=["disabled"],kb={key:0,class:"tooltip tooltip-bottom"},Ab=je({__name:"index",setup(e){const t=It(),n=bs(),s=Qt(),a=S([]),o=S(null),i=k(()=>s.groupList),c=k(()=>n.siderCollapsed),l=k(()=>s.active),u=k(()=>n.theme==="dark"),{isMobile:f}=it(),p=S(!1),b=S(!1),h=k(()=>s.getChatByGroupInfo()),x=k(()=>s.chatList),w=k(()=>t.showHtmlPreviewer||t.showTextEditor||t.showImagePreviewer),v=k(()=>t.showAppListComponent),C=k(()=>{var z;const j=(z=h.value)==null?void 0:z.config;if(!j)return{};try{return JSON.parse(j)}catch(Z){return{}}});function A(){const j=u.value?"light":"dark";n.setTheme(j)}const L=k(()=>{var j,z,Z;return String((Z=(z=(j=C==null?void 0:C.value)==null?void 0:j.modelInfo)==null?void 0:z.model)!=null?Z:"")}),$=k(()=>{var j;return((j=h==null?void 0:h.value)==null?void 0:j.appId)||0});let E=S({}),D=S([]);ke($,j=>{j?O(j):o.value=null},{immediate:!0});function O(j){return H(this,null,function*(){const z=yield Ws({id:j});o.value=z.data})}const T=k(()=>{var j,z,Z,ee;return((j=h==null?void 0:h.value)==null?void 0:j.appId)&&(((z=C.value.modelInfo)==null?void 0:z.isFixedModel)===1||((Z=C.value.modelInfo)==null?void 0:Z.isGPTs)===1||((ee=C.value.modelInfo)==null?void 0:ee.isFlowith)===1)}),Y=Ue("createNewChatGroup",()=>Promise.resolve());function P(){return H(this,null,function*(){n.setSiderCollapsed(!c.value)})}function se(){t.updateShowAppListComponent(!1),f.value||n.setSiderCollapsed(!1)}function G(j){return H(this,null,function*(){var Re;s.setUsingDeepThinking(!1),s.setUsingNetwork(!1),s.setUsingPlugin(null);const{modelInfo:z,fileInfo:Z}=s.activeConfig,{isGPTs:ee,isFixedModel:Q,modelName:le,isFlowith:Ae}=z,ue={modelInfo:{keyType:j.keyType,modelName:((Re=h==null?void 0:h.value)!=null&&Re.appId?le:j.label)||"",model:j.value,deductType:j.deductType,deduct:j.deduct,isFileUpload:j.isFileUpload,isImageUpload:j.isImageUpload,isNetworkSearch:j.isNetworkSearch,deepThinkingType:j.deepThinkingType,isMcpTool:j.isMcpTool,modelAvatar:j.modelAvatar||"",isGPTs:ee,isFlowith:Ae,isFixedModel:Q},fileInfo:Z||{}},we={groupId:l.value,config:JSON.stringify(ue)};yield Pr(we),yield s.queryMyGroup()})}function F(){return H(this,null,function*(){try{const j=yield jr();if(!j.success)return;const{modelMaps:z,modelTypeList:Z}=j.data;E.value=z,D.value=Z;const Q=Object.values(z).flat().filter(le=>le.keyType===1);a.value=Q.map(le=>({label:le.modelName,value:le.model,deductType:le.deductType,keyType:le.keyType,deduct:le.deduct,isFileUpload:le.isFileUpload,isImageUpload:le.isImageUpload,isNetworkSearch:le.isNetworkSearch,deepThinkingType:le.deepThinkingType,isMcpTool:le.isMcpTool,modelAvatar:le.modelAvatar,modelDescription:le.modelDescription}))}catch(j){}})}Ge(()=>{F()});const J=k(()=>t.externalLinkDialog&&t.currentExternalLink),V=k(()=>{const j=t.currentExternalLink;return typeof j=="object"?j:{}});function te(j){G(j)}return(j,z)=>{var ee,Q,le,Ae;const Z=di("ToolLinks");return d(),g("header",By,[r("div",Oy,[r("div",{class:ne(["flex w-full h-full items-center",{"px-4":!y(f),"px-2":y(f)}])},[c.value&&!J.value&&!w.value?(d(),g("div",Wy,[r("button",{type:"button",class:"btn-icon btn-md",onClick:P,"aria-label":"展开侧边栏"},[X(y(fl),{size:"22"})]),y(f)?I("",!0):(d(),g("div",Hy,"展开侧栏"))])):I("",!0),r("div",Vy,[J.value?(d(),g("div",Yy,[r("div",Zy,[V.value&&V.value.icon?(d(),g("img",{key:0,src:V.value.icon,alt:"网站图标",class:"w-6 h-6 rounded-lg object-cover"},null,8,Xy)):(d(),g("div",Qy,[r("span",qy,R(((Q=(ee=V.value)==null?void 0:ee.name)==null?void 0:Q.charAt(0))||"?"),1)])),r("span",Jy,R(((le=V.value)==null?void 0:le.name)||"外部链接"),1)])])):T.value?(d(),g("div",Ky,[r("div",eb,[r("button",tb,[r("span",nb,R(((Ae=h.value)==null?void 0:Ae.title)||"新对话"),1)])])])):(d(),g("div",sb,[X(y(oo),{modelValue:b.value,"onUpdate:modelValue":z[2]||(z[2]=ue=>b.value=ue),position:"bottom-left","max-height":"40vh"},{trigger:Ct(()=>{var ue,we;return[r("button",{class:"menu-trigger",onMouseover:z[0]||(z[0]=Re=>p.value=!0),onMouseleave:z[1]||(z[1]=Re=>p.value=!1),"aria-label":"选择模型"},[r("span",ab,R(((we=(ue=C.value)==null?void 0:ue.modelInfo)==null?void 0:we.modelName)||"新对话"),1),p.value||y(f)||b.value?(d(),pe(y(pi),{key:0,size:"20",class:ne(["ml-2 justify-center items-center flex-shrink-0",{"text-base font-bold":y(f),"text-sm":!y(f)}]),"aria-hidden":"true"},null,8,["class"])):I("",!0)],32)]}),menu:Ct(({close:ue})=>[r("div",null,[(d(!0),g(Pe,null,He(a.value,(we,Re)=>(d(),g("div",{key:Re,class:ne(["menu-item menu-item-md",{"menu-item-active":L.value===we.value}]),onClick:()=>{te(we),ue()},role:"menuitem",tabindex:"0","aria-label":`选择${we.label}模型`},[r("div",ib,[we.modelAvatar?(d(),g("img",{key:0,src:we.modelAvatar,alt:`${we.label}模型图标`,class:"w-full h-full object-cover"},null,8,rb)):(d(),g("span",lb,R(we.label.charAt(0)),1))]),r("div",cb,[r("div",ub,R(we.label),1),we.modelDescription?(d(),g("div",db,R(we.modelDescription),1)):I("",!0)]),L.value===we.value?(d(),g("div",fb,[X(y(pl),{theme:"filled",size:"16",class:"text-gray-500 dark:text-gray-400","aria-hidden":"true"})])):I("",!0)],10,ob))),128))])]),_:1},8,["modelValue"])])),r("div",pb,[!J.value&&!w.value?(d(),g("div",gb,[r("button",{type:"button",class:"btn-icon btn-md",onClick:z[3]||(z[3]=ue=>A()),"aria-label":"切换主题"},[u.value?(d(),pe(y(hl),{key:1,size:"20","aria-hidden":"true"})):(d(),pe(y(gl),{key:0,size:"20","aria-hidden":"true"}))]),y(f)?I("",!0):(d(),g("div",hb,"切换主题"))])):I("",!0),!J.value&&!w.value&&!v.value?(d(),pe(Z,{key:1})):I("",!0),I("",!0),J.value?(d(),g("div",mb,[r("button",{type:"button",class:"btn-icon btn-md",onClick:z[4]||(z[4]=()=>{y(t).updateExternalLinkDialog(!1),y(f)||y(n).setSiderCollapsed(!1)}),"aria-label":"关闭外部链接"},[X(y(mn),{size:"20","aria-hidden":"true"})]),y(f)?I("",!0):(d(),g("div",vb,"关闭"))])):v.value?(d(),g("div",yb,[r("button",{type:"button",class:"btn-icon btn-md",onClick:se,"aria-label":"关闭应用广场"},[X(y(mn),{size:"20","aria-hidden":"true"})]),y(f)?I("",!0):(d(),g("div",bb,"关闭"))])):w.value?I("",!0):(d(),g("div",wb,[r("button",{type:"button",class:"btn-icon btn-md",onClick:z[5]||(z[5]=ue=>y(Y)()),disabled:x.value.length===0&&!$.value&&i.value.length!==0,"aria-label":"新建对话"},[X(y(ml),{size:"20","aria-hidden":"true"})],8,xb),y(f)?I("",!0):(d(),g("div",kb,"新建对话"))]))])])],2)])])}}}),Cb=""+new URL("../images/360logo-c09b4832.png",import.meta.url).href,Ib="data:image/png;base64,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",Sb=""+new URL("../images/baidulogo-bf43b354.png",import.meta.url).href,_b="data:image/png;base64,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",Tb=""+new URL("../images/dalle-4f19ad1c.png",import.meta.url).href,Lb=""+new URL("../images/google-6f260bf3.gif",import.meta.url).href,Mb="data:image/png;base64,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",Eb="data:image/png;base64,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",Rb="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAAAXNSR0IArs4c6QAAD7NJREFUeF7tncuuHLcRhtl+CGXjjXYJ4Ky9tKRsrctSWdsvETiAJMABkn2QV4iWunjvo2eIn8cTsGc40zPTzSqSVcVL/wcwLGnYvBTrO38VyWFPDj9qFjh8/+rJXPlX7vh//3M4fLfR4KXMpcDDVdlp+nL199/dw/TLh+syaqPZZ8XTPoctO+orEC4ArDm8bMPXtR1BCRABHhFbA5BEM55hmA5vTo9ag5DY4xmadzM7nz68TX94308AEGL+uwSC8mkAQ1no/DkAWTHVDEVPCsGe7tWCDyEsg8Lc2weAhNx5X1BsIzVN7wDKxTy7B2ShFu3nEmVKkfr0rCx7h2WXgACKRFZ2nLPsChCAkQjGWvGdhWC7AOTw4tXb0wYdwigBRk77LbvIVYYG5ARG2K+Qcg3Us7TA4IoyJCAAowLDg4IyFCAAowIYt00OBsoQgCD5bgCMQUHpHpDD85e/Orc4Ldugr+y2SwOoSbeAIJzqCLuOQekOEIRTHYFx3dUud+a7AgSq0S0ci8NNfZ316gIQqMYAYNyoyfT549MeRtU8ICc4fCKOn9Es0EFu0jQgCKlGI2JlPI1D0iQgCKl2AEYnIVdzgCCk2h0cTSfwTQGCkGrHcIShNxZyNQMI4AAcZws0BEkTgAAOwHFngUYgqQ4I4AAcmxZoAJKqgOCgIeBgWOCh5qZiNUAAB8M1UCRYoBokVQABHPD8DAtUgcQcEMCR4Rp4pJqSmAICOODpAhYwVRIzQLBaJeAaqOJoAcPVLRNAAAc8W9wCRpCoAwI4xF0DFRoeS1EFBHDAl9UtoKwkaoAADnXXQAPBAofpqda7GvUAef7ygBmEBcwsoASJCiBYzjVzCzR0sYDK8q84IAit4LPVLKCQj4gCAjiquQYaVspHZAFB3gFHbcECgvmIGCDIO1rwDPThZAGxfEQEEIRWcMzmLCCUjxQDAjiacw10SDAfKQcErx+AQ7ZrgeJQqwgQqEe7noGenSxQGGqVAYJVK/hhBxaYPn/M9vPsB6EeHXgGuhgskB1qZQECOOB53Vkgc28kDxCEVt35BzrsslQkGRCoB1ytWwtkJOzpgEA9uvUPdNy51IQ9CRCoB1ysewskqkgaIFCP7v0DA0hTETYgUA+41jAWSFARPiBQj2H8Yx7IX19fxvPNN8c///bb5f//O/15rFGfR8PNRViAQD0G8RIPxRIMalgekvfvnRsRFqaK8ACBelCu1PbnqWDcjmZMUFj7IiQgUI+2fT/au1Iwbiv/73vn/H+j/DB212lAoB59uoM0HMEKXk1++nufNrnvNakiUUCgHp36gRYcI0JCqAgA6ZSBzW6nwuEVwa9ehZWsP59WtCi7jBNuRVUkDgjCK8pN2vrcO/c/fqb75J3bQ7G1OuXref3aOQqWQSCJLfluAoLwivaz5kp4OGJOnerQHDVKrbM5oznnImHWNiD4rnmLU7ndJ8qZcx2ZUpPcetuy7maYFQMEl0+3NYnx3nz6sP25hBNr11/b1hsqsgoIwqvas5XYfiz3kFqWjSmUBICJQ1Yovqoi64AgOVewv2KVsdzD71lIHRWJQfLileIATarmAQL1MJkM2Ua2wh8p9Qi9jSnVCCqyEmbdKQgAkfVd9dpiTiupHmEgWzCOAIi7/976PSBYvVL36bsGqP2GWI8eP3buxx/WS2gAshXOSauV/Sz4Fu/CrDVAsHqlPTk+lvc71yVgcPqokRds5SFjAHK3J3IFCMIrjtcVlKH2KgqqvntUy2EByOGN5DyhrpMFLOEIRtdQkLFDrLsw61pBsLyrw3MNOPxIkIPkzOdVHgJAckyY8gz3AGFKndyyGitLY69iHS27WO49A4L8g+t1ieWoA4SJ1SUVlwbEekk5abCChQGIoDGpqmJnmKhnJT6XDLNisGvkOxLjz6vjHGZdFAT5R54pY09xwiuJYyB/eOTco0fbPZFw3lgepbViJj8j3BoBCNdSReUoQCRDIM3TttQ4JFWqyOByD4cvUc0Kcvj+1RM3HX6Vqx41zRagVq8kHYtqKxdGqt7x1OMqUT8C8uLVW3fA/oc41pRzSQLiO08tCKTeb0X137cpPQbxScis8JSoA5BM+7EeoxxM2rmoUCh0OgaKr8Mfg+HcwJirSizjVS50unnxCMgoBxTD5N7aNtw5K5EQp8ybNSCcsO62/0ubpJwNGxmOo43mRL1/QMJvOs5vvOAMVvfN1gAkB5IU6H3ZUfOOaztcAdLfCV7K+ahJT43HqfrWPqf6KB1iLfvADbdSxzW+cpwt4leygoL0A4j0xGtOeE1A/DRTN5IAjrgFfKLe1RIv5XCpEx7Ka0FC9VdTQSTVRMs+ufNl9dwMSC9LvJSzlRpNI66m+mwFSLBNiqKEK0lHus091Uf8SlYXgOSEVSEhr7ky0xogt6ri/x7u5A2fxa4kTXWw3st3AQgXjth9swES6/tmWwakd+e16H8XgPS8OwxALNxYs42HqelNQko9cpNHKhbPrfd2qgCIpvNa1N04IDH1KHViLfiW0wZALJxYs42GARnhvlkAoum8FnU3DIjVfbOa35IDIBZOrNnGDEibu+hW983GnLg0jAMgms5rUXejgFhfDqB1UwcAsXBi1TbaVBBrQP71T+f+9Md7Q0NBVJ2vh8r7A0TiAoLbmfnpb859+639fFkfNbEfYfcttgmI9f2vAKR7R9YaQJsbhbHYXUNB/vNv577+WsvG2/VCQextnthim4BY5yAAJNFtdlO80X0Q61d91br9EArSOmmNAuLNprX0ejsl1JETzSkEIJrWlai7YUCs8hBqx77EzH955tyzZ8hBSmxY99lOAZH69p/2fbPYKKzr3qWtN/99kN7vmwUgpS5a9/nmAaEcLHenm/o+iIVC+alHDlIXAKr15gGJJethcKnOTEEn6bhUWwCEctG6n8+AtH6zO3eVyTub/9m6XpR752yuKq1NJQCp6+ClrXdzLxblaLeGCFfW+H/nXEmaq0bUBFD9hoJQFqz6eV83K1LOVmrK1FCN0x7VZwDCsWK1MktA/MtznlTrCbdhyuG49dyWkwyrlnVT/QUguTNm8Vynt7tzcxKuCbXgCOFdLMSrDcjapXrWr4jgzpN9uQUgvVw/mvLbmTJouFJT82rNFhXE98nfphi7cdL6NRHUXNX4/OoFOq2vZG0ZKOxn+M+5V4xagBH62xIgVF+2bGzxmogaAFBtDgHIcpABEP/bcXnfbHi7lC+rqRZrBqec0iLEovpAOUr4fG+gLN9R6G3Q7O0m3AlssRzlnNqAUNe25thMM2fL6Y/SM1evgT4B0sdKlpJBVKqtBYj0QobVqp/KJORVCkDy7Jb2VA1AtOEIFhhZSU75hx/q/Aq2WUF6TdTTXNa2dA1AOGHV8qTBMi8Lx3GoVa7RIQEgRpxYA8KBg/ubnzrxHEyocYmG0fRsNRPCqysFQR6iMCuWgFChFReMWzNQY8itV8HcUlUCEClLUvVYOldMPUrPmVHjGElFFuHVvYIgD6FcPu1z6re6r22ZD6TVfin96JFz/vvvWz8Sy8kxSEZSEQCS64WZz9W6UkgjkY6NZRAVWYZXdwqCPCQTgthjNQEpDa1S8hEJlVIwf2KV8wHF5TPnZd7wj02/szBxtE0Up+J3zU5qOK3VfWWadtmq+ya8WlcQ5CHyU1MLEgCSNpccQBBmpdmUXZqzR8GujFlQIy/Ygl06nGMOUbLYbf6xqiAARNLkN3VZKomWw44KyIp6bAOCMEuPEu4OtUQPoCBsK66pxyYgUBG2XcsKcr/kFWvl8WPnfvxhvYQGIFuhopZilVmY+/Td6lV48G4VK3yAw4tc21Yu18qrInoGZCO8iirISUXafEV0ZZ9srnmrpVfrFxsZGXorvOIAgi9RGU1SUTOxFTLJMCu2yKCxpFxkFObDEfWgAUGyzrRy5WIW56Ri6jFoeEUCgmS9suOnNK/5qgjfD+pFQ53epxULr3iAQEVS3LReWWqPpSQEitU9sHqwAIGK1PP55Japg5Gpx9Ip6HwHS8BLHqDsA5R68AHp8eZFWVv2URvHobn3W3HrCq+d6MNCl14SyXkouLkPcjte3JvViQdwHDsMJXxZy1+uFy7b417Y0HNo5W8r+fyR5fusQnOYBRXphJDTO1Fil2ZLjKTj0Mox1YMdYgV7QkUkPMuojhQlSe1Sz3AkqEc6IFCRVFeqW14aks7DqnkyEtQjGZDTihaOn9R1+7TWpU4Pp66ApfXSrDQ390hO0s9hFlTEbDJFG8oFZRAwctQjS0FOKoIzWqLea1hZOGL/+vWl0fBvYTfcr2r5/zrdHd+yZqp65AOC3XVDj0ZTIhZIzD2yQ6zFihZURGTmUIm6BTLhyFaQBSRI2NVnFw2UWiAntCpWkDkXQcJeOnd4XtsCBepRrCBI2LVnF/UXWmDzu+bcetlHTbYqxHfXuaZGOXMLFKqHiIIg1DKfdjTIsYAAHGKAINTizBjKGFqgOLQSSdKXA0aoZTj9aCpqgZJVq9uKi3MQQAJvbcoCQqGVuIKECrH025S77KszwnCI5iBXSvL8JXbZ9+WaLYxWLO9YDkY0xDqrCM5qteAwu+qDZN6hDsi8qgVIduWgVQerEFqp5SBXoRaOolT1m100rgiHWg4CSHbhmvUHqQyHCSBzuAUlqe9Mo/XAAA4zQADJaN5ZeTxGcJgCMkOC5d/KnjVE8yrLuVuWUVnmjU0DIBnCSWsNwhQOcwU575NASWo5WM/tmsNRDRCEWz37aZW+V4GjKiCApIqj9dhoNTiqA4LVrR791bDPhqtVzSTpax3BPomh0/XSVANwNKEg58Qdm4m9uK5+PxuBoylAEG7p+10XLTQER3OAAJIuXFivk4fp6fTLhwe9BtJrNt8o5HYRG4pcSw1R7sEdpnetwdGkgiynG8n7EM4fH0RjIdVtZ5tVECTvO4CjwZCqO0DOoOB4ykjENBtSdQsIEvhB+Gg8pOoakAUk3znnngziMnsZRjeqsZyQ5nOQLe9BAt8RV52pxhCAIInvApCqBw0lLNStgmA5WGL61eroMpxas8YQgEBN1Bw9veKOw6nhAQEo6f4s9sRgYAS7DKUgt5ONRF7M/bcrGhSMXQACRVED5MFN05fp04e3ai00UvHQCgJFEfeyYZJvrmV2BciVovi/HA5vuIbaebndgbGrECvm3Kc8BTvz90baLRRLU+xSQbaAQVLvjl9WavS7GTVUHICsWH0G5RiC7UFZAEWEPADC+LW0AGaEnOUMhB96i9/iY0yJWREAkmHqzoABEBlzjCS9wGh3y8f+dXNfnY7fH8My/2N9HP8IwjR9cb8fcwmoQ/kkQ0HKbRitYX5XY4AnlLxAFP5lDab12z08AOEHICjPnnP/B6B0AiOP7uzBAAAAAElFTkSuQmCC",zb=""+new URL("../images/network-0f22586e.png",import.meta.url).href,Db=""+new URL("../images/sdxl-7c167940.png",import.meta.url).href,jb=""+new URL("../images/suno-e14fd81c.ico",import.meta.url).href,Pb=""+new URL("../images/tencentlogo-358d1ec1.png",import.meta.url).href,$b="data:image/png;base64,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",Ub="data:image/png;base64,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";function Ra(e){return Object.prototype.toString.call(e)==="[object String]"}const Nb=["src"],Fb=["src"],Gb=["src"],Bb=["src"],Ob={key:4,class:"select-none inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 dark:border-gray-700 border-solid dark:bg-gray-500 text-gray-600 dark:text-white font-semibold text-base"},Wb=je({__name:"Avatar",props:{image:{type:Boolean},model:{},modelAvatar:{}},setup(e){const t=e,n=Qt(),s=k(()=>{var c,l,u,f,p,b,h,x,w,v,C,A,L,$,E;if((c=t.model)!=null&&c.includes("gpt"))return Mb;if((l=t.model)!=null&&l.includes("dall-e"))return Tb;if((u=t.model)!=null&&u.includes("midjourney"))return Eb;if((f=t.model)!=null&&f.includes("gemini"))return Lb;if((p=t.model)!=null&&p.includes("360"))return Cb;if((b=t.model)!=null&&b.includes("qwen"))return Ib;if((h=t.model)!=null&&h.includes("ERNIE"))return Sb;if((x=t.model)!=null&&x.includes("claude"))return _b;if((w=t.model)!=null&&w.includes("hunyuan"))return Pb;if((v=t.model)!=null&&v.includes("SparkDesk"))return $b;if((C=t.model)!=null&&C.includes("glm"))return Ub;if((A=t.model)!=null&&A.includes("suno"))return jb;if((L=t.model)!=null&&L.includes("network"))return zb;if(($=t.model)!=null&&$.includes("mindmap"))return Rb;if((E=t.model)!=null&&E.includes("stable-diffusion"))return Db}),a=ft(),o=k(()=>{var l;return((l=i.value)==null?void 0:l.appLogo)||a.globalConfig.robotAvatar}),i=k(()=>n.groupList.find(c=>c.uuid===n.active));return(c,l)=>y(Ra)(c.modelAvatar)&&c.modelAvatar.length>0?(d(),g("img",{key:0,class:"inline-flex h-8 w-8 items-center justify-center rounded-full border da r border-gray-100 dark:border-gray-750 border-solid shadow-sm",src:c.modelAvatar,alt:"Robot Avatar"},null,8,Nb)):y(Ra)(o.value)&&o.value.length>0?(d(),g("img",{key:1,class:"inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-100 dark:border-gray-750 border-solid shadow-sm",src:o.value,alt:"Robot Avatar"},null,8,Fb)):s.value?(d(),g("img",{key:2,class:"inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-100 dark:border-gray-750 border-solid shadow-sm",src:s.value,alt:"Robot Avatar"},null,8,Gb)):y(Ra)(o.value)&&o.value.length>0?(d(),g("img",{key:3,class:"inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-100 dark:border-gray-750 border-solid shadow-sm",src:o.value,alt:"Robot Avatar"},null,8,Bb)):(d(),g("span",Ob,l[0]||(l[0]=[r("span",{class:"flex items-center justify-center w-full h-full"}," AI ",-1)])))}}),Hb={class:"flex flex-col group max-w-full w-full"},Vb={class:"w-full text-base text-gray-800 dark:text-gray-100 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-850 p-0 border border-gray-100 dark:border-gray-700 shadow-sm flex flex-col relative font-sans leading-7 tracking-wide transition-all duration-300",style:{width:"100%"}},Yb={class:"px-5 pt-3 pb-3 flex-shrink-0 border-b border-gray-200/30 dark:border-gray-700/30 relative"},Zb={class:"font-medium mb-0 text-gray-900 dark:text-gray-200 text-center text-lg pb-2 relative"},Xb={class:"group-btn absolute top-3 right-3"},Qb={key:0,class:"tooltip tooltip-left"},qb={class:"px-5 py-4 flex-1 overflow-hidden relative"},Jb={key:0,class:"py-8 flex flex-col items-center justify-center h-full min-h-[40vh]"},Kb={key:1,class:"flex w-full relative"},ew={key:0,class:"absolute top-10 left-1/2 transform -translate-x-1/2 z-30 flex items-center justify-center"},tw={class:"flex transition-opacity duration-300 text-gray-700 opacity-0 group-hover:opacity-100"},nw={class:"mt-2 flex group"},sw={class:"relative group-btn"},aw={key:0,class:"tooltip tooltip-top"},ow={class:"relative group-btn"},iw={key:0,class:"tooltip tooltip-top"},rw=je({__name:"index",props:{chatId:{},inversion:{type:Boolean},content:{},modelType:{},status:{},loading:{type:Boolean},asRawText:{type:Boolean},fileInfo:{},ttsUrl:{},model:{},drawId:{},extend:{},customId:{},modelName:{},index:{}},emits:["delete","copy"],setup(e,{expose:t,emit:n}){const{isMobile:s}=it(),a=e,o=n,i=S(),c=S();Qt();const l=It(),u=S(!1),f=k(()=>document.documentElement.classList.contains("dark")),p=k(()=>{if(!a.content)return"";const E=/```mermaid\s+([\s\S]*?)```/g,D=[...a.content.matchAll(E)];return D.length>0&&D[0][1]?D[0][1].trim():""}),b=E=>{if(!E)return"";let D=E.replace(/\r\n/g,`
`).replace(/\r/g,`
`).trim();return!D||D.split(`
`).filter(T=>T.trim()).length===0?"":D},h=k(()=>{if(!a.content)return"流程图";const E=a.content.split(`
`);let D=!1;for(const O of E){if(O.trim().startsWith("```mermaid")){D=!0;break}const T=O.trim();if(T.startsWith("# "))return T.substring(2).trim()}if(!D)for(const O of E){const T=O.trim();if(T.startsWith("# "))return T.substring(2).trim()}return"流程图"}),x=()=>{Zs.initialize({startOnLoad:!1,theme:f.value?"dark":"default",securityLevel:"loose",logLevel:"error",suppressErrorRendering:!0})},w=()=>H(this,null,function*(){!i.value||!p.value||u.value||(u.value=!0,setTimeout(()=>H(this,null,function*(){try{i.value.innerHTML="";const E=`mermaid-${Date.now()}-${Math.random().toString(36).substring(2,9)}`;if(typeof Zs.clear=="function")try{Zs.clear()}catch(D){}try{const D=b(p.value);if(!D){v("Mermaid内容为空或无效");return}const{svg:O}=yield Zs.render(E,D);i.value&&(i.value.innerHTML=O)}catch(D){v(D instanceof Error?D.message:String(D))}}catch(E){v(E instanceof Error?E.message:String(E))}finally{u.value=!1}}),50))}),v=E=>{i.value&&(i.value.innerHTML=`
    <div class="p-4 text-red-500 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <h3 class="font-bold mb-2">Mermaid 渲染错误</h3>
      <p class="mb-2">${E}</p>
    </div>
  `)},C=S("");Ge(()=>{x(),C.value=p.value,w()}),js(()=>{}),ke(()=>p.value,E=>{E!==C.value&&(C.value=E,w())}),ke(f,()=>{x(),w()}),ke(()=>a.loading,(E,D)=>{D===!0&&E===!1&&setTimeout(()=>{w()},100)});function A(){o("copy")}function L(){o("delete")}const $=()=>{p.value&&p.value.trim()!==""&&(l.updateHtmlContent(p.value,"mermaid"),l.updateHtmlPreviewer(!0))};return t({textRef:c}),(E,D)=>(d(),g("div",Hb,[r("div",{ref_key:"textRef",ref:c,class:"leading-relaxed break-words w-full"},[r("div",{class:ne(["w-full flex flex-col items-start",y(s)?"":"pr-10"])},[r("div",Vb,[r("div",Yb,[r("div",Zb,[Ne(R(h.value)+" ",1),D[0]||(D[0]=r("div",{class:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[60px] h-[1px] bg-gradient-to-r from-transparent via-primary-500 to-transparent rounded"},null,-1))]),r("div",Xb,[r("button",{onClick:$,disabled:"",class:"btn-icon btn-md btn-icon-action","aria-label":"编辑流程图"},[X(y(qa),{size:"18"})]),y(s)?I("",!0):(d(),g("div",Qb,"编辑功能即将上线"))])]),r("div",qb,[E.loading?(d(),g("div",Jb,D[1]||(D[1]=[r("div",{class:"loading-animation mb-3"},[r("span")],-1),r("span",{class:"text-gray-500 dark:text-gray-400"},"正在生成流程图，请稍候...",-1)]))):(d(),g("div",Kb,[u.value?(d(),g("div",ew,D[2]||(D[2]=[r("div",{class:"loading-animation"},[r("span")],-1)]))):I("",!0),r("div",{ref_key:"mermaidContainerRef",ref:i,class:"w-full min-h-[40vh] flex justify-center items-center mermaid-content"},null,512)]))])])],2)],512),r("div",tw,[r("div",null,[r("div",nw,[r("div",sw,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:A,"aria-label":"复制"},[X(y(mi))]),y(s)?I("",!0):(d(),g("div",aw,"复制"))]),r("div",ow,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:L,"aria-label":"删除"},[X(y(Ja))]),y(s)?I("",!0):(d(),g("div",iw,"删除"))])])])])]))}});const lw=an(rw,[["__scopeId","data-v-33c7c56b"]]);function gr(e){const t=me({origin:!0},e);let n;t.origin?n=document.createElement("textarea"):n=document.createElement("input"),n.setAttribute("readonly","readonly"),n.value=t.text,document.body.appendChild(n),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n)}var za=Ll;function ti(e,t){var n,s,a=e.posMax,o=!0,i=!0;return n=t>0?e.src.charCodeAt(t-1):-1,s=t+1<=a?e.src.charCodeAt(t+1):-1,(n===32||n===9||s>=48&&s<=57)&&(i=!1),(s===32||s===9)&&(o=!1),{can_open:o,can_close:i}}function cw(e,t){var n,s,a,o,i;if(e.src[e.pos]!=="$")return!1;if(o=ti(e,e.pos),!o.can_open)return t||(e.pending+="$"),e.pos+=1,!0;for(n=e.pos+1,s=n;(s=e.src.indexOf("$",s))!==-1;){for(i=s-1;e.src[i]==="\\";)i-=1;if((s-i)%2==1)break;s+=1}return s===-1?(t||(e.pending+="$"),e.pos=n,!0):s-n===0?(t||(e.pending+="$$"),e.pos=n+1,!0):(o=ti(e,s),o.can_close?(t||(a=e.push("math_inline","math",0),a.markup="$",a.content=e.src.slice(n,s)),e.pos=s+1,!0):(t||(e.pending+="$"),e.pos=n,!0))}function uw(e,t,n,s){var a,o,i,c,l=!1,u,f=e.bMarks[t]+e.tShift[t],p=e.eMarks[t];if(f+2>p||e.src.slice(f,f+2)!=="$$")return!1;if(f+=2,a=e.src.slice(f,p),s)return!0;for(a.trim().slice(-2)==="$$"&&(a=a.trim().slice(0,-2),l=!0),i=t;!l&&(i++,!(i>=n||(f=e.bMarks[i]+e.tShift[i],p=e.eMarks[i],f<p&&e.tShift[i]<e.blkIndent)));)e.src.slice(f,p).trim().slice(-2)==="$$"&&(c=e.src.slice(0,p).lastIndexOf("$$"),o=e.src.slice(f,c),l=!0);return e.line=i+1,u=e.push("math_block","math",0),u.block=!0,u.content=(a&&a.trim()?a+`
`:"")+e.getLines(t+1,i,e.tShift[t],!0)+(o&&o.trim()?o:""),u.map=[t,e.line],u.markup="$$",!0}function ea(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}var dw=function(t,n){n=n||{},n.katex&&(za=n.katex),n.blockClass||(n.blockClass="");var s=function(c){n.displayMode=!1;try{return za.renderToString(c,n)}catch(l){return n.throwOnError,`<span class='katex-error' title='${ea(l.toString())}'>${ea(c)}</span>`}},a=function(c,l){return s(c[l].content)},o=function(c){n.displayMode=!0;try{return`<p class="katex-block ${n.blockClass}">`+za.renderToString(c,n)+"</p>"}catch(l){return n.throwOnError,`<p class='katex-block katex-error ${n.blockClass}' title='${ea(l.toString())}'>${ea(c)}</p>`}},i=function(c,l){return o(c[l].content)+`
`};t.inline.ruler.after("escape","math_inline",cw),t.block.ruler.after("blockquote","math_block",uw,{alt:["paragraph","reference","blockquote","list"]}),t.renderer.rules.math_inline=a,t.renderer.rules.math_block=i};const fw=Ka(dw);function pw(e,t){var n,s,a=e.attrs[e.attrIndex("href")][1];for(n=0;n<t.length;++n){if(s=t[n],typeof s.matcher=="function"){if(s.matcher(a,s))return s;continue}return s}}function gw(e,t,n){Object.keys(n).forEach(function(s){var a,o=n[s];s==="className"&&(s="class"),a=t[e].attrIndex(s),a<0?t[e].attrPush([s,o]):t[e].attrs[a][1]=o})}function hr(e,t){t?t=Array.isArray(t)?t:[t]:t=[],Object.freeze(t);var n=e.renderer.rules.link_open||this.defaultRender;e.renderer.rules.link_open=function(s,a,o,i,c){var l=pw(s[a],t),u=l&&l.attrs;return u&&gw(a,s,u),n(s,a,o,i,c)}}hr.defaultRender=function(e,t,n,s,a){return a.renderToken(e,t,n)};var hw=hr;const mw=Ka(hw),vw={class:"text-wrap flex w-full flex-col px-1 group"},yw={key:0,class:"mb-2"},bw={key:0},ww={key:1},xw={key:5,class:"glow-band"},kw={key:0,class:"text-gray-600 dark:text-gray-400 border-l-2 pl-5 mt-2"},Aw={class:"flex flex-col gap-2 text-base"},Cw=["href"],Iw={key:0},Sw={key:1,class:"mb-2"},_w={key:0},Tw={key:1},Lw={key:5,class:"glow-band"},Mw=["innerHTML"],Ew={key:0,class:"w-full"},Rw={key:0,class:"inline-block w-3.5 h-3.5 ml-0.5 align-middle rounded-full animate-breathe dark:bg-gray-100 bg-gray-950"},zw=["innerHTML"],Dw={key:0,class:"p-3 rounded-2xl w-full bg-opacity dark:bg-gray-750 break-words",style:{"max-width":"100%"}},jw={class:"flex justify-end mt-3"},Pw={class:"group relative"},$w={key:0,class:"tooltip tooltip-top"},Uw={class:"group relative"},Nw={key:0,class:"tooltip tooltip-top"},Fw=["textContent"],Gw=["src","onClick"],Bw={key:3,class:"flex-row transition-opacity duration-500"},Ow=["onClick"],Ww={class:"mt-2 flex group"},Hw={key:0,class:"relative group-btn"},Vw={key:0,class:"tooltip tooltip-top"},Yw={key:1,class:"relative group-btn"},Zw={key:0,class:"tooltip tooltip-top"},Xw={key:2,class:"relative group-btn"},Qw={key:0,class:"tooltip tooltip-top"},qw={key:3,class:"relative group-btn"},Jw={key:0,class:"tooltip tooltip-top"},Kw={key:4,class:"relative group-btn"},ex={key:0,class:"tooltip tooltip-top"},tx={key:5,class:"relative group-btn"},nx={key:0,class:"tooltip tooltip-top"},sx=je({__name:"index",props:{chatId:{},index:{},isUserMessage:{type:Boolean},content:{},modelType:{},status:{},loading:{type:Boolean},imageUrl:{},fileUrl:{},ttsUrl:{},model:{},promptReference:{},networkSearchResult:{},fileVectorResult:{},tool_calls:{},isLast:{type:Boolean},usingNetwork:{type:Boolean},usingDeepThinking:{type:Boolean},usingMcpTool:{type:Boolean},reasoningText:{},fileAnalysisProgress:{},useFileSearch:{type:Boolean}},emits:["regenerate","delete","copy"],setup(e,{expose:t,emit:n}){Xs.registerLanguage("mermaid",()=>({name:"mermaid",contains:[],keywords:{keyword:"graph flowchart sequenceDiagram classDiagram stateDiagram gitGraph pie gantt",built_in:"TD TB BT RL LR"},case_insensitive:!0}));const s=()=>{if(document.getElementById("highlight-theme-overrides"))return;const W=document.createElement("style");W.id="highlight-theme-overrides",W.textContent=`
    /* 浅色模式覆盖 */
    html:not(.dark) .hljs {
      background: transparent !important;
      color: #383a42 !important;
    }
    
    /* 深色模式覆盖 */
    html.dark .hljs {
      background: transparent !important;
      color: #abb2bf !important;
    }
    
    /* 容器背景 */
    html:not(.dark) .code-block-wrapper {
      background-color: #fafafa;
    }
    
    html.dark .code-block-wrapper {
      background-color: #2f2f2f;
    }
  `,document.head.appendChild(W)},a=ft(),{isMobile:o}=it(),i=Ue("onConversation"),c=Ue("handleRegenerate"),l=It(),u=e,f=n,p=S(!0),b=S(!1),h=S(),x=S(u.ttsUrl),w=S("paused"),v=S("paused"),C=S(u.content),A=S(!1),L=S(null);let $=null,E=null;const D=Ue("onOpenImagePreviewer"),O=k(()=>{var _;return Number((_=a.globalConfig)==null?void 0:_.isHideTts)===1}),T=k(()=>{var _;return Number((_=a.globalConfig)==null?void 0:_.enableHtmlRender)!==0}),Y=k(()=>{var _;if(u.networkSearchResult)try{const W=JSON.parse(u.networkSearchResult);return(W==null?void 0:W.slice(0,50))||((_=W==null?void 0:W.searchResults)==null?void 0:_.slice(0,50))||[]}catch(W){return[]}return[]}),P=k(()=>w.value!=="paused"||A.value?"opacity-100":"opacity-0 group-hover:opacity-100"),se=()=>H(this,null,function*(){if(!(w.value==="loading"||w.value==="playing")){if(x.value){G(x.value);return}w.value="loading";try{if(!u.chatId||!u.content)return;const W=(yield $r({chatId:u.chatId,prompt:u.content})).ttsUrl;if(W)x.value=W,G(W);else throw new Error("TTS URL is undefined")}catch(_){w.value="paused"}}});function G(_){$&&$.pause(),$=new Audio(_),$.play().then(()=>{w.value="playing"}).catch(W=>{w.value="paused"}),$.onended=()=>{w.value="paused",$=null}}function F(){$&&($.pause(),w.value="paused")}function J(){w.value==="playing"?F():se()}function V(){v.value==="playing"?j():te()}function te(){"speechSynthesis"in window&&(j(),E=new SpeechSynthesisUtterance(u.content),E.lang="zh-CN",E.rate=1,E.pitch=1,E.onstart=()=>{v.value="playing"},E.onend=()=>{v.value="paused",E=null},E.onerror=()=>{v.value="paused",E=null},window.speechSynthesis.speak(E))}function j(){window.speechSynthesis&&(window.speechSynthesis.cancel(),v.value="paused",E=null)}const z=new vi({linkify:!0,html:T.value,highlight(_,W){if(!!(W&&Xs.getLanguage(W))){const ge=W!=null?W:"";return xt(Xs.highlight(_,{language:ge}).value,ge)}return xt(Xs.highlightAuto(_).value,"")}}),Z=new Map;function ee(_,W){var he,ve;const re=W.querySelector(".copied-text");if(re&&getComputedStyle(re).display!=="none")return;const ge=document.getElementById(_);if(!ge)return;const q=ge.querySelector("code");if(!(!q||!q.textContent))try{(he=navigator.clipboard)!=null&&he.writeText?navigator.clipboard.writeText(q.textContent).then(()=>{le(W,_)}).catch(Ce=>{Q(q.textContent,W,_)}):Q(q.textContent,W,_)}catch(Ce){(ve=zt())==null||ve.error("复制失败!")}}function Q(_,W,re){var ge,q;if(!_){(ge=zt())==null||ge.error("复制失败!");return}try{gr({text:_,origin:!0}),le(W,re)}catch(he){(q=zt())==null||q.error("复制失败!")}}function le(_,W){var Ce;if(_.getAttribute("data-copying")==="true")return;_.setAttribute("data-copying","true");const re=_.querySelector(".copy-icon"),ge=_.querySelector(".check-icon"),q=_.querySelector(".copy-text"),he=_.querySelector(".copied-text");re&&ge&&q&&he&&(re.classList.add("hidden"),q.classList.add("hidden"),ge.classList.remove("hidden"),he.classList.remove("hidden")),(Ce=zt())==null||Ce.success("复制成功!"),Z.has(W)&&clearTimeout(Z.get(W));const ve=setTimeout(()=>{if(_){const $e=_.querySelector(".copy-icon"),Qe=_.querySelector(".check-icon"),Ye=_.querySelector(".copy-text"),Fe=_.querySelector(".copied-text");$e&&Qe&&Ye&&Fe&&($e.classList.remove("hidden"),Ye.classList.remove("hidden"),Qe.classList.add("hidden"),Fe.classList.add("hidden")),_.removeAttribute("data-copying")}},3e3);Z.set(W,ve)}z.renderer.rules.image=function(_,W,re,ge,q){const he=_[W],ve=he.attrGet("src"),Ce=he.attrGet("title"),$e=he.content;return ve?`<img src="${ve}" alt="${$e||""}" title="${Ce||$e||""}" class="rounded-md max-h-[30vh] cursor-pointer hover:opacity-90 transition-opacity" 
    onclick="(function(event) { 
      event.stopPropagation();
      const customEvent = new CustomEvent('previewMdImage', { detail: { src: '${ve}' } });
      document.dispatchEvent(customEvent);
    })(event)"
  />`:""};const Ae=k(()=>{const _=u.imageUrl;if(!_)return[];if(typeof _=="string"&&_.trim().startsWith("{")&&_.includes("imageUrls"))try{const W=JSON.parse(_);if(W&&Array.isArray(W.imageUrls))return W.imageUrls.map(re=>re.url).filter(Boolean)}catch(W){}if(typeof _=="string"&&_.trim().startsWith("["))try{const W=JSON.parse(_);if(Array.isArray(W))return W.map(re=>re.url).filter(Boolean)}catch(W){}return typeof _=="string"?_.split(",").map(W=>W.trim()).filter(Boolean):Array.isArray(_)?_:[]}),ue=k(()=>u.imageUrl?Ae.value.length>0?!0:/\.(jpg|jpeg|png|gif|webp)$/i.test(u.imageUrl):!1);z.use(mw,{attrs:{target:"_blank",rel:"noopener"}}),z.use(fw,{blockClass:"katexmath-block p-0 flex h-full items-center justify-start",inlineClass:"katexmath-inline",errorColor:" #cc0000"});const we=k(()=>{let W=(u.content||"").replace(/\\\(\s*/g,"$").replace(/\s*\\\)/g,"$").replace(/\\\[\s*/g,"$$").replace(/\s*\\\]/g,"$$").replace(/\[\[(\d+)\]\((https?:\/\/[^\)]+)\)\]/g,`<button class="bg-gray-500 text-white rounded-full w-4 h-4 mx-1 flex justify-center items-center text-sm hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 inline-flex" onclick="window.open('$2', '_blank')">$1</button>`);return u.isUserMessage?W:z.render(W)}),Re=k(()=>{let W=(u.reasoningText||"").replace(/\\\(\s*/g,"$").replace(/\s*\\\)/g,"$").replace(/\\\[\s*/g,"$$").replace(/\s*\\\]/g,"$$").replace(/\[\[(\d+)\]\((https?:\/\/[^\)]+)\)\]/g,`<button class="bg-gray-500 text-white rounded-full w-4 h-4 mx-1 flex justify-center items-center text-sm hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 inline-flex" onclick="window.open('$2', '_blank')">$1</button>`);return u.isUserMessage?W:z.render(W)});function xt(_,W){const re=`code-block-${Date.now()}-${Math.floor(Math.random()*1e3)}`;return`<pre
    class="max-w-full border border-gray-200 bg-[#AFB8C133] dark:border-gray-700 dark:bg-gray-750 transition-colors"
    id="${re}"
    style="line-height: normal; margin: 0 !important; padding: 0 !important; border-radius: 0.75rem !important; width: 100% !important; overflow: hidden !important;"
  ><div class="code-block-header sticky w-full h-10 flex justify-between items-center px-3 border-b border-gray-100 dark:border-gray-700 z-10">
    <span class="text-gray-600 dark:text-gray-400 text-sm font-medium flex items-center">${W||"text"}</span>
    <div class="flex gap-2">
      <button class="h-7 gap-1 btn-pill btn-copy" data-block-id="${re}">
        <svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" class="copy-icon text-current"><path d="M13 12.4316V7.8125C13 6.2592 14.2592 5 15.8125 5H40.1875C41.7408 5 43 6.2592 43 7.8125V32.1875C43 33.7408 41.7408 35 40.1875 35H35.5163" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/><path d="M32.1875 13H7.8125C6.2592 13 5 14.2592 5 15.8125V40.1875C5 41.7408 6.2592 43 7.8125 43H32.1875C33.7408 43 35 41.7408 35 40.1875V15.8125C35 14.2592 33.7408 13 32.1875 13Z" stroke="currentColor" stroke-width="3" stroke-linejoin="round"/></svg>
        <svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" class="check-icon text-current hidden"><path d="M10 24L20 34L40 14" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/></svg>
        <span class="copy-text">${oe("chat.copyCode")}</span>
        <span class="copied-text hidden">已复制</span>
      </button>
    </div>
  </div><code
    class="hljs code-content-scrollable custom-scrollbar px-4 py-3 text-base bg-white dark:bg-[#282c34] rounded-b-2xl leading-normal code-container"
    style="margin-top: 0; padding-right: 0.75rem !important; padding-left: 0.75rem !important; display: block !important; white-space: pre !important; max-width: 100% !important; width: 100% !important; overflow-x: auto !important;"
  >${_}</code></pre>`}function rt(){return H(this,null,function*(){if(A.value){const _=C.value;yield i({msg:_,imageUrl:u.imageUrl,fileUrl:u.fileUrl,chatId:u.chatId}),A.value=!1}else C.value=u.content,A.value=!0,yield at(),Be()})}function nt(_){return H(this,null,function*(){yield i({msg:_})})}function lt(){f("copy")}function Ve(){f("delete")}const ct=()=>{A.value=!1,C.value=u.content},Be=()=>{L.value&&(L.value.style.height="auto",L.value.style.height=`${L.value.scrollHeight}px`)};ke(()=>u.loading,_=>{u.isUserMessage||at(()=>{const W=h.value;W&&W.querySelectorAll("code.code-content-scrollable").forEach(ge=>{const q=ge,he=q.parentElement;_?(q.style.maxHeight="none",q.style.overflowY="visible",q.style.display="block !important",q.style.whiteSpace="pre !important"):(q.style.maxHeight="50vh",q.style.overflowY="auto",q.style.display="block !important",q.style.whiteSpace="pre !important",q.style.minWidth="fit-content !important",he&&he.classList.contains("custom-scrollbar")&&(he.style.overflowX="auto !important",he.style.maxWidth="100% !important"),setTimeout(()=>{q.style.overflow="hidden",q.offsetHeight,q.style.overflowY="auto",he&&(he.offsetHeight,he.style.overflowX="auto !important")},100))})})},{immediate:!0}),ke(C,()=>{A.value&&at(()=>{Be()})}),ke(A,_=>{_&&at(()=>{Be()})}),t({textRef:h}),Ge(()=>{s();const _=()=>{document.querySelectorAll(".btn-copy[data-block-id]").forEach(he=>{const ve=he.getAttribute("data-block-id");ve&&he.getAttribute("data-listener-attached")!=="true"&&(he.addEventListener("click",Ce=>{Ce.stopPropagation(),Ce.preventDefault(),ee(ve,he)}),he.setAttribute("data-listener-attached","true"))})};_();const W=new MutationObserver(q=>{let he=!1;q.forEach(ve=>{ve.type==="childList"&&ve.addedNodes.forEach(Ce=>{Ce.nodeType===1&&Ce.querySelectorAll('.btn-copy:not([data-listener-attached="true"])').length>0&&(he=!0)})}),he&&_()});W.observe(document.body,{childList:!0,subtree:!0}),js(()=>{W.disconnect(),Z.forEach(q=>clearTimeout(q)),Z.clear()});const re=q=>{var Ce;const he=q.target,ve=(Ce=he.classList)!=null&&Ce.contains("btn-preview")?he:he.closest(".btn-preview");if(ve&&ve.getAttribute("data-block-id")){q.stopPropagation(),q.preventDefault();const $e=ve.getAttribute("data-block-id");if($e){const Qe=document.getElementById($e);if(Qe){const Ye=Qe.querySelector("code");if(Ye&&Ye.textContent){const Fe=ve.classList.contains("preview-mermaid");l.updateHtmlContent(Ye.textContent||"",Fe?"mermaid":"html"),l.updateHtmlPreviewer(!0)}}}}};document.addEventListener("click",re);const ge=q=>{const{src:he}=q.detail;ae(he)};document.addEventListener("previewMdImage",ge),js(()=>{document.removeEventListener("click",re),document.removeEventListener("previewMdImage",ge)}),at(()=>{const q=h.value;q&&q.querySelectorAll("code.code-content-scrollable").forEach(ve=>{const Ce=ve,$e=Ce.parentElement;Ce.style.maxHeight="50vh",Ce.style.overflowY="auto",Ce.style.display="block !important",Ce.style.whiteSpace="pre !important",Ce.style.minWidth="fit-content !important",$e&&$e.classList.contains("custom-scrollbar")&&($e.style.overflowX="auto !important",$e.style.maxWidth="100% !important"),setTimeout(()=>{Ce.style.overflow="hidden",Ce.offsetHeight,Ce.style.overflowY="auto",$e&&($e.offsetHeight,$e.style.overflowX="auto !important")},100)})}),$&&($.pause(),$=null),E&&window.speechSynthesis.cancel(),setTimeout(()=>{const q=document.querySelectorAll(".btn-preview:not(.preview-mermaid):not(.preview-markmap)"),he=document.querySelectorAll(".preview-mermaid"),ve=document.querySelectorAll(".preview-markmap"),Ce=document.querySelectorAll(".btn-copy");q.forEach($e=>{$e.addEventListener("click",Qe=>{var pt;const Ye=Qe.currentTarget.dataset.blockId||"",Fe=document.getElementById(Ye);if(Fe&&Fe.querySelector("code")){const Nt=((pt=Fe.querySelector("code"))==null?void 0:pt.textContent)||"";l.updateHtmlContent(Nt,"html"),l.updateHtmlPreviewer(!0)}})}),he.forEach($e=>{$e.addEventListener("click",Qe=>{var pt;const Ye=Qe.currentTarget.dataset.blockId||"",Fe=document.getElementById(Ye);if(Fe&&Fe.querySelector("code")){const Nt=((pt=Fe.querySelector("code"))==null?void 0:pt.textContent)||"";l.updateHtmlContent(Nt,"mermaid"),l.updateHtmlPreviewer(!0)}})}),ve.forEach($e=>{$e.addEventListener("click",Qe=>{var pt;const Ye=Qe.currentTarget.dataset.blockId||"",Fe=document.getElementById(Ye);if(Fe&&Fe.querySelector("code")){const Nt=((pt=Fe.querySelector("code"))==null?void 0:pt.textContent)||"";l.updateHtmlContent(Nt,"markmap"),l.updateHtmlPreviewer(!0)}})}),Ce.forEach($e=>{$e.addEventListener("click",Qe=>{var pt;const Ye=Qe.currentTarget.dataset.blockId||"",Fe=document.getElementById(Ye);if(Fe&&Fe.querySelector("code")){const Nt=((pt=Fe.querySelector("code"))==null?void 0:pt.textContent)||"";navigator.clipboard.writeText(Nt).then(()=>{const In=Qe.currentTarget,Vn=In.innerHTML;In.innerHTML=`
                <svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-green-500">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M4 24L9 19L19 29L39 9L44 14L19 39L4 24Z" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                已复制
              `,setTimeout(()=>{In.innerHTML=Vn},2e3)})}})})},100)});function Dt(_){D&&Ae.value.length>0&&D(Ae.value,_)}function ae(_){D&&D([_],0)}return(_,W)=>{var re;return d(),g("div",vw,[!_.isUserMessage&&(Y.value.length||_.loading&&_.usingNetwork)?(d(),g("div",yw,[r("div",{onClick:W[0]||(W[0]=ge=>b.value=!b.value),class:"text-gray-600 mb-1 cursor-pointer items-center btn-pill glow-container"},[X(y(hi),{theme:"outline",size:"18",class:"mr-1 flex"}),Y.value.length?(d(),g("span",bw,"已浏览 "+R(Y.value.length)+" 个网页",1)):_.loading&&_.usingNetwork?(d(),g("span",ww,"联网搜索中")):I("",!0),_.loading&&_.usingNetwork?(d(),pe(y(bo),{key:2,class:"rotate-icon flex mx-1"})):I("",!0),!b.value&&Y.value.length?(d(),pe(y(Ls),{key:3,size:"18",class:"ml-1 flex"})):Y.value.length?(d(),pe(y(Ms),{key:4,size:"18",class:"ml-1 flex"})):I("",!0),_.loading&&_.usingNetwork&&!Y.value.length?(d(),g("div",xw)):I("",!0)]),X(Hn,{name:"fold"},{default:Ct(()=>[b.value&&Y.value.length?(d(),g("div",kw,[r("div",Aw,[(d(!0),g(Pe,null,He(Y.value,(ge,q)=>(d(),g("a",{key:q,href:ge.link,target:"_blank",class:"hover:underline mr-2 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"},[Ne(R(q+1)+". "+R(ge.title.slice(0,80))+R(ge.title.length>80?"...":"")+" ",1),ge.media?(d(),g("span",Iw,"["+R(ge.media)+"]",1)):I("",!0)],8,Cw))),128))])])):I("",!0)]),_:1})])):I("",!0),!_.isUserMessage&&(Re.value||_.loading&&_.usingDeepThinking)?(d(),g("div",Sw,[r("div",{onClick:W[1]||(W[1]=ge=>p.value=!p.value),class:"text-gray-600 mb-1 cursor-pointer items-center btn-pill glow-container"},[X(y(gi),{theme:"outline",size:"18",class:"mr-1 flex"}),Re.value?(d(),g("span",_w,R(we.value||!_.loading?"已深度思考":"深度思考中"),1)):_.loading&&_.usingDeepThinking?(d(),g("span",Tw,"深度思考")):I("",!0),_.loading&&_.usingDeepThinking&&!Re.value||!we.value&&_.loading&&Re.value?(d(),pe(y(bo),{key:2,class:"rotate-icon flex mx-1"})):I("",!0),!p.value&&Re.value?(d(),pe(y(Ls),{key:3,size:"18",class:"ml-1 flex"})):Re.value?(d(),pe(y(Ms),{key:4,size:"18",class:"ml-1 flex"})):I("",!0),_.loading&&_.usingDeepThinking&&!Re.value||!we.value&&_.loading&&Re.value?(d(),g("div",Lw)):I("",!0)]),X(Hn,{name:"fold"},{default:Ct(()=>[p.value&&Re.value?(d(),g("div",{key:0,class:ne(["markdown-body text-gray-600 dark:text-gray-400 pl-5 mt-2 border-l-2 border-gray-300 dark:border-gray-600 overflow-hidden transition-opacity duration-500 ease-in-out",{"markdown-body-generate":_.loading&&!we.value}]),innerHTML:Re.value},null,10,Mw)):I("",!0)]),_:1})])):I("",!0),r("div",{ref_key:"textRef",ref:h,class:"flex w-full"},[_.isUserMessage?(d(),g("div",{key:1,class:ne(["flex justify-end w-full",[y(o)?"pl-20":"pl-28"]]),style:{"max-width":"100%"}},[A.value?(d(),g("div",Dw,[Xe(r("textarea",{"onUpdate:modelValue":W[2]||(W[2]=ge=>C.value=ge),class:"min-w-full text-base resize-none overflow-y-auto bg-transparent whitespace-pre-wrap text-gray-950 dark:text-gray-100",style:{"max-height":"60vh"},onInput:Be,ref_key:"textarea",ref:L},null,544),[[At,C.value]]),r("div",jw,[r("div",Pw,[r("button",{type:"button",class:ne(["btn-floating btn-md mx-3",{"h-8 w-8":y(o),"bg-[#F4F4F4] border-[#F4F4F4] dark:bg-[#2f2f2f] dark:border-[#2f2f2f]":y(o)}]),onClick:ct,"aria-label":"取消"},[X(y(mn),{size:"16"})],2),y(o)?I("",!0):(d(),g("div",$w,"取消"))]),r("div",Uw,[r("button",{type:"button",class:ne(["btn-send",{"h-8 w-8":y(o)}]),onClick:rt,"aria-label":"发送"},[X(y(vl),{size:"16"})],2),y(o)?I("",!0):(d(),g("div",Nw,"发送"))])])])):(d(),g("div",{key:1,class:"p-3 rounded-2xl text-base bg-opacity dark:bg-gray-750 break-words whitespace-pre-wrap text-gray-950 dark:text-gray-100",textContent:R(we.value),style:{"max-width":"100%"}},null,8,Fw))],2)):(d(),g("div",Ew,[_.loading&&!we.value&&!Re.value?(d(),g("span",Rw)):I("",!0),r("div",{class:ne(["markdown-body text-gray-950 dark:text-gray-100",{"markdown-body-generate":_.loading||!we.value}]),innerHTML:we.value},null,10,zw)]))],512),Ae.value&&Ae.value.length>0&&ue.value?(d(),g("div",{key:2,class:ne(["my-2 w-full flex",_.isUserMessage?"justify-end":"justify-start"])},[r("div",{class:"gap-2",style:kt({display:"grid",gridTemplateColumns:`repeat(${Math.min(Ae.value.length,4)}, 1fr)`,gridAutoRows:"1fr",maxWidth:_.isUserMessage?y(o)?"60vw":"40vw":"80vw",width:"auto"})},[(d(!0),g(Pe,null,He(Ae.value,(ge,q)=>(d(),g("img",{key:q,src:ge,alt:"图片",onClick:he=>Dt(q),class:"rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:opacity-90 transition-opacity w-auto h-auto max-h-[30vh] object-cover",style:{aspectRatio:"1/1",width:"160px",height:"160px"}},null,8,Gw))),128))],4)],2)):I("",!0),_.promptReference&&!_.isUserMessage&&_.isLast?(d(),g("div",Bw,[(d(!0),g(Pe,null,He(_.promptReference?(re=_.promptReference.match(/{(.*?)}/g))==null?void 0:re.map(ge=>ge.slice(1,-1)).slice(0,3):[],(ge,q)=>(d(),g("button",{key:q,onClick:he=>nt(ge),class:"flex items-center overflow-hidden btn-pill py-4 px-4 mt-3"},[Ne(R(ge)+" ",1),X(y(yl),{class:"ml-1"})],8,Ow))),128))])):I("",!0),r("div",{class:ne(["flex transition-opacity duration-300 text-gray-700",P.value,{"justify-end":_.isUserMessage}])},[r("div",Ww,[A.value?I("",!0):(d(),g("div",Hw,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:lt,"aria-label":"复制"},[X(y(mi))]),y(o)?I("",!0):(d(),g("div",Vw,R(y(oe)("chat.copy")),1))])),A.value?I("",!0):(d(),g("div",Yw,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:Ve,"aria-label":"删除"},[X(y(Ja))]),y(o)?I("",!0):(d(),g("div",Zw,R(y(oe)("chat.delete")),1))])),_.isUserMessage&&!A.value?(d(),g("div",Xw,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:rt,"aria-label":"编辑"},[X(y(qa))]),y(o)?I("",!0):(d(),g("div",Qw,"编辑"))])):I("",!0),_.isUserMessage?I("",!0):(d(),g("div",qw,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:W[3]||(W[3]=ge=>y(c)(_.index,_.chatId)),"aria-label":"重新生成"},[X(y(bl))]),y(o)?I("",!0):(d(),g("div",Jw,"重新生成"))])),!_.isUserMessage&&!O.value?(d(),g("div",Kw,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:J,"aria-label":"朗读"},[w.value==="paused"?(d(),pe(y(wl),{key:0})):I("",!0),w.value==="loading"?(d(),pe(y(xl),{key:1,class:"rotate-icon"})):w.value==="playing"?(d(),pe(y(wo),{key:2})):I("",!0)]),y(o)?I("",!0):(d(),g("div",ex,R(w.value==="playing"?y(oe)("chat.pause"):w.value==="loading"?y(oe)("chat.loading"):y(oe)("chat.readAloud")),1))])):I("",!0),!_.isUserMessage&&O.value?(d(),g("div",tx,[r("button",{class:"btn-icon btn-sm btn-icon-action mx-1",onClick:V,"aria-label":"浏览器朗读"},[v.value==="paused"?(d(),pe(y(kl),{key:0})):v.value==="playing"?(d(),pe(y(wo),{key:1})):I("",!0)]),y(o)?I("",!0):(d(),g("div",nx,R(v.value==="playing"?"停止":"朗读"),1))])):I("",!0)])],2)])}}});const ax={key:0,class:"items-center justify-center mr-2 rounded-full group-btn relative flex-shrink-0"},ox={class:"tooltip tooltip-top"},ix={class:"overflow-visible text-sm items-start w-full"},rx={class:"flex items-end gap-1 flex-row"},lx=je({__name:"index",props:{chatId:{},dateTime:{},content:{},model:{},modelName:{},modelType:{},status:{},role:{},loading:{type:Boolean},imageUrl:{},ttsUrl:{},useFileSearch:{type:Boolean},fileUrl:{},videoUrl:{},audioUrl:{},drawId:{},extend:{},customId:{},modelAvatar:{},action:{},taskData:{},pluginParam:{},progress:{},index:{},promptReference:{},networkSearchResult:{},fileVectorResult:{},tool_calls:{},isLast:{type:Boolean},usingNetwork:{type:Boolean},usingDeepThinking:{type:Boolean},usingMcpTool:{type:Boolean},reasoningText:{},taskId:{},isWorkflowMessage:{type:Boolean},nodeType:{},stepName:{},workflowProgress:{}},emits:["regenerate","delete"],setup(e,{emit:t}){const n=k(()=>i.role==="user");k(()=>!n.value&&i.isWorkflowMessage);const{isMobile:s}=it(),a=It(),o=k(()=>a.showHtmlPreviewer||a.showTextEditor||a.showImagePreviewer||a.isMarkdownPreviewerVisible),i=e,c=t,l=zt(),u=S(),f=S(),p=Ue("onOpenImagePreviewer");Tt("onOpenImagePreviewer",p);function b(){c("delete")}function h(){var w;gr({text:(w=i.content)!=null?w:""}),i.content&&l.success("复制成功！")}function x(){var w;(w=f.value)==null||w.scrollIntoView(),c("regenerate")}return(w,v)=>(d(),g("div",{ref_key:"messageRef",ref:f,class:"flex w-full my-2 overflow-visible items-start flex-row"},[!n.value&&!y(s)&&!o.value?(d(),g("div",ax,[n.value?I("",!0):(d(),pe(Wb,{key:0,image:n.value,model:w.model,modelAvatar:w.modelAvatar},null,8,["image","model","modelAvatar"])),r("div",ox,R(w.modelName),1)])):I("",!0),r("div",ix,[r("div",rx,[w.pluginParam==="mermaid"&&!n.value?(d(),pe(lw,{key:0,ref_key:"textRef",ref:u,isUserMessage:n.value,drawId:w.drawId,extend:w.extend,customId:w.customId,content:w.content,modelType:w.modelType,ttsUrl:w.ttsUrl,model:w.model,modelName:w.modelName,loading:w.loading,status:w.status,index:w.index,onRegenerate:x,onCopy:h,onDelete:b},null,8,["isUserMessage","drawId","extend","customId","content","modelType","ttsUrl","model","modelName","loading","status","index"])):(d(),pe(sx,{key:1,ref_key:"textRef",ref:u,index:w.index,modelName:w.modelName,chatId:w.chatId,isUserMessage:n.value,content:w.content,modelType:w.modelType,imageUrl:w.imageUrl,ttsUrl:w.ttsUrl,fileUrl:w.fileUrl,useFileSearch:w.useFileSearch,model:w.model,loading:w.loading,promptReference:w.promptReference,networkSearchResult:w.networkSearchResult,fileVectorResult:w.fileVectorResult,tool_calls:w.tool_calls,isLast:w.isLast,usingNetwork:w.usingNetwork,usingDeepThinking:w.usingDeepThinking,usingMcpTool:w.usingMcpTool,reasoningText:w.reasoningText,isWorkflowMessage:w.isWorkflowMessage,onRegenerate:x,onCopy:h,onDelete:b},null,8,["index","modelName","chatId","isUserMessage","content","modelType","imageUrl","ttsUrl","fileUrl","useFileSearch","model","loading","promptReference","networkSearchResult","fileVectorResult","tool_calls","isLast","usingNetwork","usingDeepThinking","usingMcpTool","reasoningText","isWorkflowMessage"]))])])],512))}}),cx={key:0,class:"w-full flex justify-center items-center"},ux={class:"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4"},dx=["onClick"],fx={class:"flex items-center gap-2 w-full"},px={class:"flex-grow text-gray-600 dark:text-gray-500 text-sm truncate"},gx=je({__name:"index",props:{isHideDefaultPreset:{type:Boolean}},emits:["click"],setup(e,{emit:t}){const{isMobile:n}=it(),s=t,a=S(ur.sort(()=>.5-Math.random()).slice(0,8)),o=()=>{const i=["text-red-500","text-blue-500","text-green-500","text-yellow-500","text-purple-500","text-pink-500","text-indigo-500"];return i[Math.floor(Math.random()*i.length)]};return(i,c)=>!i.isHideDefaultPreset&&!y(n)?(d(),g("div",cx,[r("div",ux,[(d(!0),g(Pe,null,He(a.value,l=>(d(),g("div",{key:l.title,class:"mx-2"},[r("button",{onClick:u=>s("click",l),class:"btn-pill rounded-2xl p-3 h-full w-[8rem]"},[r("div",fx,[X(ka,{icon:l.icon,class:ne(["mb-0 inline-block text-base",o()])},null,8,["icon","class"]),r("div",px,R(l.title),1)])],8,dx)]))),128))])])):I("",!0)}}),hx={key:0,class:"flex flex-col justify-center items-center select-none"},mx={class:"flex items-center mb-2"},vx=["src"],yx={class:"text-white text-sm md:text-lg"},bx={class:"text-3xl font-bold text-primary-500"},xx={class:"mb-2 rounded px-4 py-2 text-center text-base text-gray-600"},kx={key:1,class:"flex flex-col items-center justify-center select-none"},Ax={class:"flex items-center"},Cx=["src"],Ix={class:"text-3xl font-bold text-primary-500"},Sx={class:"rounded my-3 text-center text-base text-gray-600 dark:text-gray-400"},_x=je({__name:"index",setup(e){var b;const t=S({name:"",des:"",coverImg:""}),n=ft(),s=k(()=>n.globalConfig.clientLogoPath||Ji),a=k(()=>{var h;return((h=n.userInfo)==null?void 0:h.nickname)||""}),o=k(()=>{var w,v;const h=new Date().getHours();let x="";return h<6?x="凌晨好":h<9?x="早上好":h<12?x="上午好":h<14?x="中午好":h<18?x="下午好":x="晚上好",a.value?`${x}，${a.value}，欢迎使用${(w=n.globalConfig)==null?void 0:w.siteName}`:`${x}，欢迎使用${(v=n.globalConfig)==null?void 0:v.siteName}`}),i=((b=n.globalConfig)==null?void 0:b.homeWelcomeContent)||"我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~",c=Qt(),l=k(()=>c.getChatByGroupInfo()),u=k(()=>{var h;return((h=l==null?void 0:l.value)==null?void 0:h.appId)||0}),f=h=>H(this,null,function*(){try{const x=yield Ws({id:h});x.data?t.value=x.data:t.value={name:"",des:"",coverImg:""}}catch(x){}});function p(){const h=["bg-blue-300","bg-red-300","bg-green-300","bg-yellow-300","bg-purple-300","bg-pink-300"];return h[Math.floor(Math.random()*h.length)]}return ke(()=>u.value,h=>{h&&f(h)},{immediate:!0}),(h,x)=>{var w,v,C,A;return u.value?(d(),g("div",hx,[r("div",mx,[(w=t.value)!=null&&w.coverImg?(d(),g("img",{key:0,src:(v=t.value)==null?void 0:v.coverImg,alt:"Logo",class:"h-7 w-7 mr-2"},null,8,vx)):(d(),g("div",{key:1,class:ne(["flex-shrink-0 dark:ring-gray-400 rounded-full w-7 h-7 flex items-center justify-center mr-2",p()])},[r("span",yx,R(t.value.name.slice(0,1)),1)],2)),r("h1",bx,R((C=t.value)==null?void 0:C.name),1)]),r("h2",xx,R((A=t.value)==null?void 0:A.des),1)])):(d(),g("div",kx,[r("div",Ax,[r("img",{src:s.value,alt:"Logo",class:"h-7 w-7 mr-2"},null,8,Cx),r("h1",Ix,R(o.value),1)]),r("h2",Sx,R(y(i)),1)]))}}});function Tx(){const e=Qt(),t=It();return{addGroupChat:f=>{e.addGroupChat(f)},updateGroupChat:(f,p)=>{e.updateGroupChat(f,p)},updateGroupChatSome:(f,p)=>{e.updateGroupChatSome(f,p)},toggleWorkflowPreview:f=>{t.updateWorkflowPreviewer(f)},addWorkflowContent:f=>{t.addWorkflowContent(f)},clearWorkflowContent:()=>{t.clearWorkflowContent()},updateWorkflowContent:(f,p)=>{t.updateWorkflowContentAt(f,p)},addMessageToWorkflow:f=>{t.addWorkflowContent(f),t.updateWorkflowPreviewer(!0)}}}function Lx(){const e=S(null),t=S(!0);let n=0;const s=300,a=()=>H(this,null,function*(){yield at(),e.value&&setTimeout(()=>{var u;(u=e.value)==null||u.scrollTo({top:e.value.scrollHeight,behavior:"smooth"})},300)}),o=()=>H(this,null,function*(){yield at(),e.value&&(e.value.scrollTop=0)}),i=(u=300)=>H(this,null,function*(){const f=Date.now();f-n<s||(n=f,yield at(),e.value&&e.value.scrollHeight-e.value.scrollTop-e.value.clientHeight<=u&&e.value.scrollTo({top:e.value.scrollHeight,behavior:"smooth"}))}),c=()=>{if(e.value){const u=e.value,f=u.scrollTop+u.clientHeight,p=50,b=u.scrollHeight-f;t.value=b<=p}},l=()=>{c()};return Ge(()=>{const u=e.value;u&&(u.addEventListener("scroll",l),window.addEventListener("resize",l),c(),a())}),js(()=>{const u=e.value;u&&(u.removeEventListener("scroll",l),window.removeEventListener("resize",l))}),{scrollRef:e,scrollToBottom:a,scrollToTop:o,scrollToBottomIfAtBottom:i,isAtBottom:t,handleScroll:l}}const Mx={class:"flex h-full w-full"},Ex={class:"relative overflow-hidden h-full w-full flex flex-col transition-all duration-300 ease-in-out transform"},Rx={key:0,class:"relative z-10 flex-1 overflow-hidden"},zx={class:"sticky bottom-2 flex justify-center p-1 z-20"},Dx={key:0,class:"absolute bottom-full mb-4 w-full"},jx={key:1,class:"fixed z-50 h-8 flex items-center justify-center bottom-0 left-0 w-full backdrop-blur-sm"},Px={class:"text-sm text-gray-600 dark:text-gray-400 max-h-6 flex justify-center items-center"},$x={key:0},Ux={class:"ml-2"},Nx={class:"transition-all text-gray-600 hover:text-gray-500 dark:hover:text-gray-400",href:"https://beian.miit.gov.cn",target:"_blank"},Fx={class:"relative overflow-hidden bg-white dark:bg-gray-750 rounded-lg shadow-lg flex flex-col w-full max-w-3xl max-h-[85vh] m-4"},Gx={class:"relative z-10 flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-600 flex-shrink-0 bg-white/80 dark:bg-gray-750/80 backdrop-blur-sm"},Bx={class:"text-xl font-bold dark:text-white"},Ox={class:"relative z-10 flex-grow overflow-y-auto p-4 bg-white/80 dark:bg-gray-750/80 backdrop-blur-sm"},Wx={class:"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4"},Hx={class:"block text-sm font-medium leading-6 text-gray-900 dark:text-gray-300 mb-1"},Vx=["onUpdate:modelValue","placeholder","disabled"],Yx={class:"block text-left truncate"},Zx={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Xx=["onClick"],Qx={class:"relative z-10 flex justify-end p-4 border-t border-gray-200 dark:border-gray-600 flex-shrink-0 space-x-2 bg-white/80 dark:bg-gray-750/80 backdrop-blur-sm"},qx=["disabled"],Jx=["disabled"],Kx=je({__name:"chatBase",setup(e,{expose:t}){const n=zt(),{isMobile:s}=it(),{scrollRef:a,scrollToBottom:o,scrollToBottomIfAtBottom:i,isAtBottom:c,handleScroll:l}=Lx(),{addGroupChat:u,updateGroupChatSome:f}=Tx(),p=(B,N)=>{x.updateGroupChat(B,N)},b=It(),h=ft(),x=Qt(),w=ui(),v=S(),C=S(!0),A=S(new AbortController),L=S(0),$=S(!1),E=S(null),D=S(!1),O=S([]),T=S(null),Y=S(!1),P=S({}),se=k(()=>Y.value),G=S({}),F=k(()=>x.groupList),J=k(()=>{var B;return(B=w==null?void 0:w.query)!=null&&B.trade_status?String(w.query.trade_status):""}),V=k(()=>{var B;return(B=w==null?void 0:w.query)!=null&&B.token?String(w.query.token):""}),te=k(()=>{var B;return(B=h==null?void 0:h.isLogin)!=null?B:!1}),j=k(()=>x.currentPlugin),z=k(()=>x.chatList),Z=k(()=>x.active),ee=k(()=>x.getChatByGroupInfo()),Q=k(()=>h.globalConfig),le=k(()=>{var B;return Number((B=h.globalConfig)==null?void 0:B.isHideDefaultPreset)===1});k(()=>{var B;return(B=h.globalConfig)==null?void 0:B.sideDrawingEditModel});const Ae=k(()=>h.globalConfig.streamCacheEnabled==="1"),ue=k(()=>{var N;const B=(N=ee.value)==null?void 0:N.config;if(!B)return{};try{return JSON.parse(B)}catch(U){return{}}}),we=k(()=>{var B,N;return(B=E.value)!=null&&B.backgroundImg?E.value.backgroundImg:((N=ue.value)==null?void 0:N.backgroundImg)||null}),Re=k(()=>{var B,N;return(N=(B=ue==null?void 0:ue.value)==null?void 0:B.modelInfo)==null?void 0:N.isFlowith}),xt=k(()=>{var B,N;return String(((N=(B=ue==null?void 0:ue.value)==null?void 0:B.fileInfo)==null?void 0:N.fileParsing)||"")}),rt=k(()=>{var B,N,U,ye,gt,Lt;return String(((B=j==null?void 0:j.value)==null?void 0:B.parameters)!=="mermaid"?((N=j==null?void 0:j.value)==null?void 0:N.parameters)||((ye=(U=ue==null?void 0:ue.value)==null?void 0:U.modelInfo)==null?void 0:ye.model)||"":((Lt=(gt=ue==null?void 0:ue.value)==null?void 0:gt.modelInfo)==null?void 0:Lt.model)||"")}),nt=k(()=>{var B;return String(((B=ee.value)==null?void 0:B.fileUrl)||"")}),lt=k(()=>{var B;return String(((B=j==null?void 0:j.value)==null?void 0:B.pluginName)||(ue==null?void 0:ue.value.modelInfo.modelName)||"AI")}),Ve=k(()=>Number((ue==null?void 0:ue.value.modelInfo.keyType)||1)),ct=k(()=>{var B,N;return String(((B=j==null?void 0:j.value)==null?void 0:B.pluginImg)||((N=ue==null?void 0:ue.value.modelInfo)==null?void 0:N.modelAvatar)||"")}),Be=k(()=>{var B;return((B=ee==null?void 0:ee.value)==null?void 0:B.appId)||0}),Dt=k(()=>{var B;return(B=T.value)!=null&&B.backgroundImg?{backgroundImage:`url(${T.value.backgroundImg})`,backgroundSize:"cover",backgroundPosition:"center center",backgroundRepeat:"no-repeat"}:{}});ke(z,B=>{B.length!==0&&C.value&&(C.value=!1,o())},{immediate:!0}),ke(z,()=>H(this,null,function*(){L.value++})),ke(()=>Z.value,()=>H(this,null,function*(){setTimeout(o,100)})),Ge(()=>H(this,null,function*(){try{yield h.getUserInfo()}catch(B){}})),ke(Be,(B,N)=>H(this,null,function*(){B&&B>0?yield vn(B):E.value=null}),{immediate:!1}),ke([()=>O.value,()=>D.value],([B,N])=>{if(N&&B&&B.length>0){const U={},ye={};B.forEach(gt=>{U[gt.title]="",gt.type==="select"&&(ye[gt.title]=!1)}),P.value=U,G.value=ye}},{immediate:!0}),Ge(()=>H(this,null,function*(){yield x.queryActiveChatLogList(),yield at(),V.value&&(yield pt(V.value)),J.value&&(yield Nt()),Be.value&&Be.value>0&&(yield vn(Be.value)),b.clearWorkflowContent()}));function ae(B){if(!B)return null;try{const N=JSON.parse(B);return Array.isArray(N)&&N.every(U=>U.type&&U.title&&U.placeholder)?N:null}catch(N){return null}}function _(B,N){P.value[B]=N}function W(){D.value=!1,O.value=[],T.value=null}function re(B){return H(this,null,function*(){D.value=!1,yield he(B)})}function ge(){if(!T.value)return;let B=!1;for(const ye in P.value){const gt=O.value.find(Lt=>Lt.title===ye);if(gt&&!gt.placeholder.includes("(系统生成)")&&P.value[ye]){B=!0;break}}if(!B){D.value=!1,he(T.value);return}Y.value=!0;const N={};O.value.forEach(ye=>{N[ye.title]=P.value[ye.title]||"",ye.placeholder.includes("(系统生成)")&&delete N[ye.title]});let U="";for(const ye in N)Object.prototype.hasOwnProperty.call(N,ye)&&N[ye]&&(U+=`${ye}: ${N[ye]}
`);U=U.trim(),ve(T.value,U),setTimeout(()=>{D.value=!1,Y.value=!1},300)}function q(B,N){O.value=N,T.value=B,D.value=!0}function he(B){return H(this,null,function*(){F.value.length===0&&(yield Qe(),yield x.queryMyGroup()),yield x.addNewChatGroup(Number(B.id))})}function ve(B,N){return H(this,null,function*(){F.value.length===0&&(yield Qe(),yield x.queryMyGroup()),yield x.addNewChatGroup(Number(B.id)),yield at(),x.active===Number(B.id)?Ye({msg:N,appId:Number(B.id)}):Ye({msg:N})})}const Ce=()=>{o()},$e=(B,N=!1)=>{if(B)if(N){const U=`### AI响应 ${new Date().toLocaleTimeString()}
${B}`;b.addWorkflowContent(U)}else b.updateWorkflowContentLast(B)},Qe=Ue("createNewChatGroup",()=>Promise.resolve()),Ye=ro=>H(this,[ro],function*({msg:B,action:N,drawId:U,customId:ye,model:gt,modelName:Lt,modelType:Me,modelAvatar:yn,appId:ut,extraParam:os,fileUrl:Ft,chatId:Dn,taskId:Aa,imageUrl:is}){var jt,Mt,Gt,ks;F.value.length===0&&(yield Qe(),yield x.queryMyGroup()),Dn&&(yield x.deleteChatsAfterId(Dn),L.value+=1),x.setStreamIn(!0);const M=Lt||lt.value,K=Me||Ve.value||1,ie=yn||ct.value,Se=ut||Be.value;let be=B||"提问",ze=gt||rt.value;A.value=new AbortController,(jt=j.value)!=null&&jt.deductType&&((Mt=j.value)==null?void 0:Mt.deductType)!==0&&(ze=(Gt=j.value)==null?void 0:Gt.parameters),u({content:be,model:ze,modelName:Lt,modelType:K,role:"user",fileUrl:Ft||nt.value||"",imageUrl:is||""});let Ie={groupId:+Z.value,fileParsing:xt.value,usingNetwork:x.usingNetwork,usingDeepThinking:x.usingDeepThinking};u({content:"",model:ze,action:N||"",loading:!0,modelName:M,modelType:K,role:"assistant",error:!1,status:1,useFileSearch:!!(Ft||nt.value)||Re.value,fileUrl:Ft||nt.value||"",modelAvatar:ie,pluginParam:(ks=j.value)==null?void 0:ks.parameters,usingNetwork:x.usingNetwork,usingDeepThinking:x.usingDeepThinking}),yield o();let fe=null;x.setStreamIn(!0),b.updateIsChatIn(!0);let Ee=os;const Le=()=>H(this,null,function*(){yield Ze()}),Ze=()=>H(this,null,function*(){var fo;let ht="",Pt="",bn="",Jt="",Kt="",_n="",jn="",As="",$t="",Bt="",Tn="",Ln=0,Ut="",en="",wn="",Xn="",Hs="",tn=null,lo=!0,co=Date.now();const Qn=Ae.value,St={veryFast:20,fast:30,normal:40,slow:50,verySlow:60,extremelySlow:70},rs={veryLarge:50,large:30,medium:8,small:6,verySmall:3};let xn=me({},rs),on=St.normal,Ot=St.normal;const uo=.2,mr=yt=>{const Wt=Math.min(Math.max(yt/500,1),3);xn={veryLarge:Math.round(rs.veryLarge*Wt),large:Math.round(rs.large*Wt),medium:Math.round(rs.medium*Wt),small:Math.round(rs.small*Wt),verySmall:rs.verySmall}},vr=()=>{Qn&&(tn&&clearInterval(tn),tn=setInterval(()=>{Ca()},Ot))},yr=()=>{if(!Qn)return;const yt=Ut.length+en.length,Wt=ht.length+Hs.length,ot=Date.now()-co;mr(Wt),yt>=xn.veryLarge?on=St.veryFast:yt>=xn.large?on=St.fast:yt>=xn.medium?on=St.normal:yt>=xn.small?on=St.slow:yt>=xn.verySmall?on=St.verySlow:ot>2e3?on=St.extremelySlow:ot>1e3&&(on=St.verySlow),Kt&&(Ut.length>0||en.length>0)&&(on=St.veryFast),As==="stop"&&(Ut.length>0||en.length>0)&&(on=St.veryFast),Ot!==on&&(Ot=Math.round(Ot*(1-uo)+on*uo),Ot=Math.max(Ot,3),tn&&(clearInterval(tn),tn=setInterval(()=>{Ca()},Ot)))},Ca=()=>{if(!Qn)return;const yt=Ut.length>0,Wt=en.length>0;if(!yt&&!Wt){lo||(tn&&clearInterval(tn),tn=null);return}let ot=1;const Pn=Ut.length+en.length;Ot<=St.veryFast?ot=5:Ot<=St.fast?ot=4:Ot<=St.normal?ot=3:Ot<=St.slow?ot=2:ot=1,Pn>=xn.veryLarge*3?ot=ot*6:Pn>=xn.veryLarge*2?ot=ot*5:Pn>=xn.veryLarge?ot=ot*3:Pn>=xn.large&&(ot=ot*2),ot=Math.min(ot,60);let De="";if(yt){const st=Math.min(ot,Ut.length),$n=Ut.substring(0,st);Ut=Ut.substring(st),wn+=$n,De+=$n}if(Wt){const st=Math.min(ot,en.length),$n=en.substring(0,st);en=en.substring(st),Xn+=$n}if(p(z.value.length-1,{chatId:Number(_n),content:wn,reasoningText:Xn,mcpToolUse:jn,networkSearchResult:bn,fileVectorResult:$t,tool_calls:Jt,modelType:1,modelName:M,error:!1,loading:!0,imageUrl:fe==null?void 0:fe.imageUrl,promptReference:Kt,nodeType:Bt,stepName:Tn,workflowProgress:Ln}),De){const st=wn.length===De.length;$e(De,st)}i(),yr()};Qn&&vr();try{yield Nr({model:ze,modelName:M,modelType:K,prompt:B,usingPluginId:(fo=j.value)!=null&&fo.parameters?999:0,imageUrl:is||"",fileUrl:Ft||nt.value||"",appId:Se||0,modelAvatar:ie,options:Ie,signal:A.value.signal,extraParam:Ee,onDownloadProgress:({event:yt})=>{const Wt=yt.target.responseText;co=Date.now();try{Wt.split(`
`).filter(Pn=>Pn.trim()).forEach(Pn=>{try{const De=JSON.parse(Pn);if(De.userBalance&&h.updateUserBalance(De.userBalance),De.nodeType&&(Bt=De.nodeType),De.stepName&&(Tn=De.stepName),De.progress!==void 0&&(Ln=De.progress),De.content){Pt+=De.content;const st=De.content[0].text.replace(/\\n/g,`
`).replace(/\\t/g,"	");if(Qn)Ut+=st,ht+=st;else{if(ht+=st,wn+=st,p(z.value.length-1,{chatId:Number(_n),content:wn,reasoningText:Xn,mcpToolUse:jn,networkSearchResult:bn,fileVectorResult:$t,tool_calls:Jt,modelType:1,modelName:M,error:!1,loading:!0,imageUrl:fe==null?void 0:fe.imageUrl,promptReference:Kt,nodeType:Bt,stepName:Tn,workflowProgress:Ln}),st){const $n=wn.length===st.length;$e(st,$n)}i()}}if(De.fileVectorResult&&($t=De.fileVectorResult),De.reasoning_content){Pt+=De.reasoning_content;const st=De.reasoning_content[0].text.replace(/\\n/g,`
`).replace(/\\t/g,"	");if(Qn)en+=st,Hs+=st;else{if(Hs+=st,Xn+=st,p(z.value.length-1,{chatId:Number(_n),content:wn,reasoningText:Xn,mcpToolUse:jn,networkSearchResult:bn,fileVectorResult:$t,tool_calls:Jt,modelType:1,modelName:M,error:!1,loading:!0,imageUrl:fe==null?void 0:fe.imageUrl,promptReference:Kt,nodeType:Bt,stepName:Tn,workflowProgress:Ln}),st){const $n=wn.length===st.length;$e(st,$n)}i()}}De.mcpToolUse&&(jn=De.mcpToolUse),De.networkSearchResult&&(bn=De.networkSearchResult),De.fileVectorResult&&($t=De.fileVectorResult),De.tool_calls&&(Jt=De.tool_calls),De.promptReference&&(Kt=De.promptReference),De.chatId&&(_n=De.chatId),De.finishReason&&(As=De.finishReason),i()}catch(De){}})}catch(ot){}}})}catch(yt){Oe(yt)}finally{lo=!1,Qn&&(Ut.length>0||en.length>0)&&(Ot=St.veryFast,tn&&(clearInterval(tn),tn=setInterval(()=>{Ca()},Ot)),yield new Promise(yt=>{const Wt=setInterval(()=>{Ut.length===0&&en.length===0&&(clearInterval(Wt),yt(null))},100)})),wn=ht,Xn=Hs,p(z.value.length-1,{chatId:Number(_n),content:wn,reasoningText:Xn,mcpToolUse:jn,networkSearchResult:bn,fileVectorResult:$t,tool_calls:Jt,modelType:1,modelName:M,error:!1,loading:!0,imageUrl:fe==null?void 0:fe.imageUrl,promptReference:Kt,nodeType:Bt,stepName:Tn,workflowProgress:Ln}),yield new Promise(yt=>setTimeout(yt,300)),b.updateIsChatIn(!1),yield x.queryMyGroup(),f(z.value.length-1,{loading:!1}),f(z.value.length-2,{chatId:Number(_n)-1})}}),Oe=ht=>{var Kt;b.updateIsChatIn(!1),x.setStreamIn(!1);const Pt=(ht==null?void 0:ht.message)||"",bn=(ht==null?void 0:ht.status)||((Kt=ht==null?void 0:ht.message)==null?void 0:Kt.code)||0;if(Pt.includes("canceled")){f(z.value.length-1,{loading:!1}),i(),setTimeout(()=>{h.getUserBalance()},200);return}(bn===402||Pt.includes("不足")||Pt.includes("使用完毕"))&&(te.value?b.updateSettingsDialog(!0,hn.MEMBER):(n.error("您的余额不足，请先充值~"),h.setLoginDialog(!0))),Pt.includes("手机号绑定")&&(te.value?b.updateSettingsDialog(!0,hn.ACCOUNT):(n.error("您的手机号未绑定，请先绑定手机号~"),h.setLoginDialog(!0))),Pt.includes("实名认证")&&(te.value?b.updateSettingsDialog(!0,hn.ACCOUNT):h.setLoginDialog(!0)),(Pt.includes("违规")||Pt.includes("合规"))&&b.UpdateBadWordsDialog(!0),Pt.includes("会员专属")&&(n.error("您还不是会员，请先开通会员~"),b.updateSettingsDialog(!0,hn.MEMBER));const Jt=z.value[z.value.length-1];Jt&&(p(z.value.length-1,{content:Jt.content===""?"":Jt.content,role:"assistant",loading:!1}),i())};yield Le(),x.setStreamIn(!1),yield new Promise(ht=>setTimeout(ht,300)),i(300)}),Fe=()=>{A.value.abort(),x.setStreamIn(!1),setTimeout(o,1e3)},pt=B=>H(this,null,function*(){try{h.setToken(B),n.success("账户登录成功、开始体验吧！"),yield h.getUserInfo()}catch(N){}}),Nt=()=>H(this,null,function*(){J.value.toLowerCase().includes("success")?(n.success("感谢你的购买、祝您使用愉快~",{duration:5e3}),yield h.getUserInfo()):n.error("您还没有购买成功哦~")}),In=N=>H(this,[N],function*({chatId:B}){ya().warning({title:oe("chat.deleteMessage"),content:oe("chat.deleteMessageConfirm"),positiveText:oe("common.yes"),negativeText:oe("common.no"),onPositiveClick:()=>H(this,null,function*(){yield x.deleteChatById(B),L.value+=1,n.success(oe("chat.deleteSuccess"))})})}),Vn=(B,N)=>H(this,null,function*(){if(x.groupList.length===0||B===0)return;let U="",ye="",gt="";if(B&&typeof B=="number"){const{content:Lt,role:Me}=z.value[B-1];if(ye=z.value[B-1].imageUrl||"",gt=z.value[B-1].fileUrl||"",Lt&&(U=Lt),Me==="assistant")return}Ye({msg:U,chatId:N-1,imageUrl:ye,fileUrl:gt}),o()});function ws(B){return H(this,null,function*(){F.value.length===0&&(yield Qe());const{appId:N,prompt:U}=B;if(N&&N>0)try{yield x.addNewChatGroup(N),yield x.queryMyGroup()}catch(ye){}else Ye({msg:U})})}const Sn=()=>{b.updateShowAppListComponent(!b.showAppListComponent)},xs=()=>{b.updateTextEditor(!b.showTextEditor)};function Yn(B){return H(this,null,function*(){$.value=!1,yield x.addNewChatGroup(Number(B.id))})}function qt(){b.updateShowAppListComponent(!1),b.updateSettingsDialog(!0,hn.MEMBER)}function zn(U){return H(this,arguments,function*({app:B,formattedData:N}){b.updateShowAppListComponent(!1),yield x.addNewChatGroup(Number(B.id)),yield at(),x.active===Number(B.id)?Ye({msg:N,appId:Number(B.id)}):Ye({msg:N})})}function vn(B){return H(this,null,function*(){if(B)try{const N=yield Ws({id:B});E.value=N.data}catch(N){E.value=null}})}Tt("onConversation",Ye),Tt("handleRegenerate",Vn),t({toggleAppList:Sn,toggleTextEditor:xs});function Zn(B,N,U){Ur({imageUrl:B[0],fileName:B[0]})}return Tt("onOpenImagePreviewer",Zn),Tt("showAppConfigModal",q),Tt("tryParseJson",ae),(B,N)=>{var ye,gt,Lt;const U=di("ExternalLinkComponent");return d(),g(Pe,null,[X(Vv,{class:"h-full"}),r("div",Mx,[r("div",Ex,[we.value?(d(),g("div",{key:0,class:"absolute inset-0 z-0 opacity-30",style:kt({backgroundImage:`url(${we.value})`,backgroundSize:"cover",backgroundPosition:"center center",backgroundRepeat:"no-repeat"})},null,4)):I("",!0),X(Ab,{class:ne(["relative z-10 flex-shrink-0",we.value&&!y(b).showAppListComponent?"bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm":"bg-white dark:bg-gray-800 backdrop-blur-sm"]),onToggleAppList:Sn},null,8,["class"]),y(b).externalLinkDialog?(d(),pe(U,{key:1,class:"relative z-10 flex-1 bg-white dark:bg-gray-900"})):y(b).showAppListComponent?(d(),pe(S0,{key:2,class:"relative z-10 flex-1 overflow-hidden bg-white dark:bg-gray-900",onRunApp:Yn,onShowMemberDialog:qt,onRunAppWithData:zn})):(d(),g(Pe,{key:3},[z.value.length||y(s)?(d(),g("main",Rx,[r("div",{id:"scrollRef",ref_key:"scrollRef",ref:a,class:"relative h-full overflow-y-auto scroll-smooth custom-scrollbar",style:{"background-color":"transparent",position:"relative","z-index":"5"},onScroll:N[0]||(N[0]=(...Me)=>y(l)&&y(l)(...Me))},[r("div",{id:"image-wrapper",class:ne(["w-full m-auto h-full mx-auto pb-10",[y(s)?"p-3":"p-3 w-full max-w-4xl"]])},[!z.value.length&&!Be.value?(d(),g("div",{key:0,class:ne(["flex justify-center items-center text-center",[y(s)?"h-full":"h-4/5 "]])},[X(qv)],2)):I("",!0),!z.value.length&&Be.value?(d(),g("div",{key:1,class:ne(["flex justify-center items-center",[y(s)?"h-full":"h-4/5 "]])},[X(N0,{appId:Be.value},null,8,["appId"])],2)):I("",!0),z.value.length?(d(),g("div",{key:L.value,class:ne({"px-2":y(s)})},[(d(!0),g(Pe,null,He(z.value,(Me,yn)=>(d(),pe(lx,{index:yn,chatId:Me.chatId,content:Me.content,reasoningText:Me.reasoningText,model:Me.model,modelType:Me.modelType,modelName:Me.modelName,modelAvatar:Me.modelAvatar,status:Me.status,imageUrl:Me.imageUrl,ttsUrl:Me.ttsUrl,taskId:Me.taskId,taskData:Me.taskData,videoUrl:Me.videoUrl,audioUrl:Me.audioUrl,action:Me.action,role:Me.role,loading:Me.loading,drawId:Me.drawId,customId:Me.customId,pluginParam:Me.pluginParam,promptReference:Me.promptReference,progress:Me.progress,networkSearchResult:Me.networkSearchResult,fileVectorResult:Me.fileVectorResult,isLast:yn===z.value.length-1,usingNetwork:Me.usingNetwork,usingDeepThinking:!1,useFileSearch:Me.useFileSearch,tool_calls:Me.tool_calls,onDelete:ut=>In(Me)},null,8,["index","chatId","content","reasoningText","model","modelType","modelName","modelAvatar","status","imageUrl","ttsUrl","taskId","taskData","videoUrl","audioUrl","action","role","loading","drawId","customId","pluginParam","promptReference","progress","networkSearchResult","fileVectorResult","isLast","usingNetwork","useFileSearch","tool_calls","onDelete"]))),256)),r("div",zx,[Xe(X(y(Al),{size:"24",class:ne(["p-1 bg-white dark:bg-gray-600 shadow-sm rounded-full border text-gray-700 border-gray-400 dark:border-gray-600 dark:text-gray-500 cursor-pointer transition-all duration-300 ease-in-out",[y(c)?"opacity-0":"opacity-100"]]),onClick:Ce,theme:"outline",strokeWidth:2,"aria-label":"滚动到底部",role:"button",tabindex:"0"},null,8,["class"]),[[va,!y(c)]])])],2)):I("",!0),r("div",{ref_key:"bottomContainer",ref:v,class:"bottom"},null,512)],2)],544)])):I("",!0),r("div",{class:ne(["z-20",!z.value.length&&!y(s)?"absolute left-0 right-0 top-1/2 transform -translate-y-1/2":"relative"])},[X(Gy,{class:ne(["z-20 pb-3 relative",y(s)?"pb-safe":""]),onPauseRequest:Fe,dataSourcesLength:z.value.length},{"before-footer":Ct(()=>[!z.value.length&&!y(s)?(d(),g("div",Dx,[X(_x,{appId:Be.value},null,8,["appId"])])):I("",!0)]),"after-footer":Ct(()=>[!z.value.length&&!y(s)?(d(),pe(gx,{key:0,"is-hide-default-preset":le.value,onClick:ws,class:"absolute top-full mt-4 w-full"},null,8,["is-hide-default-preset"])):I("",!0)]),_:1},8,["class","dataSourcesLength"])],2),!y(s)&&!z.value.length?(d(),g("div",jx,[r("div",Px,[N[2]||(N[2]=Ne(" AI 生成内容仅供参考，不代表本平台立场。 ")),(ye=Q.value)!=null&&ye.companyName?(d(),g("span",$x," 版权所有 @ "+R((gt=Q.value)==null?void 0:gt.companyName),1)):I("",!0),r("span",Ux,[r("a",Nx,R((Lt=Q.value)==null?void 0:Lt.filingNumber),1)])])])):I("",!0)],64))]),X(Hn,{name:"modal-fade"},{default:Ct(()=>{var Me,yn;return[D.value&&T.value?(d(),g("div",{key:0,class:"fixed inset-0 z-[9000] flex items-center justify-center bg-gray-900 bg-opacity-50",onClick:sn(W,["self"])},[r("div",Fx,[(Me=T.value)!=null&&Me.backgroundImg?(d(),g("div",{key:0,class:"absolute inset-0 z-0 opacity-10",style:kt(Dt.value)},null,4)):I("",!0),r("div",Gx,[r("span",Bx,"预设配置: "+R(((yn=T.value)==null?void 0:yn.name)||""),1),r("button",{onClick:W,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[X(y(mn),{size:"18"})])]),r("div",Ox,[r("div",Wx,[(d(!0),g(Pe,null,He(O.value,(ut,os)=>(d(),g("div",{key:os},[r("label",Hx,R(ut.title),1),ut.type==="input"?Xe((d(),g("input",{key:0,type:"text","onUpdate:modelValue":Ft=>P.value[ut.title]=Ft,placeholder:ut.placeholder,disabled:ut.placeholder.includes("(系统生成)")||se.value,class:"input input-md w-full disabled:opacity-50 disabled:cursor-not-allowed"},null,8,Vx)),[[At,P.value[ut.title]]]):I("",!0),ut.type==="select"?(d(),pe(y(oo),{key:1,modelValue:G.value[ut.title],"onUpdate:modelValue":Ft=>G.value[ut.title]=Ft,position:"bottom-left","min-width":"100%",class:"relative block w-full",disabled:se.value},{trigger:Ct(()=>[r("div",{class:ne(["input input-md w-full relative cursor-pointer",se.value?"disabled:opacity-50 disabled:cursor-not-allowed":""])},[r("span",Yx,R(P.value[ut.title]||ut.placeholder),1),r("span",Zx,[X(y(Cl),{class:"text-gray-400",size:"16","aria-hidden":"true"})])],2)]),menu:Ct(({close:Ft})=>[r("div",null,[(d(!0),g(Pe,null,He(ut.options,Dn=>(d(),g("div",{key:Dn,onClick:()=>{_(ut.title,Dn),Ft()},class:"menu-item menu-item-md"},R(Dn),9,Xx))),128))])]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])):I("",!0)]))),128))])]),r("div",Qx,[r("button",{type:"button",onClick:N[1]||(N[1]=ut=>re(T.value)),disabled:se.value,class:"btn btn-secondary btn-md disabled:opacity-50 disabled:cursor-not-allowed"}," 跳过 ",8,qx),r("button",{type:"button",onClick:ge,disabled:se.value,class:"btn btn-primary btn-md disabled:opacity-50 disabled:cursor-not-allowed"}," 开始 ",8,Jx)])])])):I("",!0)]}),_:1})])],64)}}});const e1={class:"h-full transition-all"},t1={class:"overflow-hidden"},f1=je({__name:"chat",setup(e){const t=zt(),n=bs(),s=Qt(),a=ft(),{isMobile:o}=it(),i=k(()=>a.isLogin),c=k(()=>n.siderCollapsed),l=k(()=>{var A;return Number((A=a.globalConfig)==null?void 0:A.isModelInherited)===1}),u=k(()=>s.isStreamIn!==void 0?s.isStreamIn:!1);ke(i,(A,L)=>H(this,null,function*(){A&&!L&&(yield s.queryMyGroup())}));const f=k(()=>o.value?["rounded-none","shadow-none"]:["rounded-none","shadow-md","dark:border-gray-900"]),p=k(()=>["h-full","transition-[padding]","duration-300",{"pl-[260px]":!o.value&&!c.value}]);function b(){return H(this,null,function*(){if(u.value){t.info("AI回复中，请稍后再试");return}s.setStreamIn(!1);try{const{modelInfo:A}=s.activeConfig;if(A&&l.value&&s.activeGroupAppId===0){const L={modelInfo:A};yield s.addNewChatGroup(0,L)}else yield s.addNewChatGroup();s.setUsingPlugin(null),o.value&&n.setSiderCollapsed(!0)}catch(A){}})}Ge(()=>{window.location.pathname!=="/"&&!window.location.pathname.includes(".")&&window.history.replaceState({},document.title,"/")});const h=It(),x=k(()=>a.loginDialog),w=k(()=>h.BadWordsDialog),v=k(()=>h.settingsDialog),C=k(()=>h.mobileSettingsDialog);return Tt("createNewChatGroup",b),(A,L)=>(d(),g("div",e1,[r("div",{class:ne(["h-full overflow-hidden",f.value])},[r("div",{class:ne(["z-40 h-full flex",p.value])},[X(Kx,{class:"w-full flex-1 transition-[margin] duration-500"})],2)],2),r("div",t1,[X(Uc,{visible:x.value},null,8,["visible"]),X(Bl,{visible:w.value},null,8,["visible"]),y(o)?(d(),pe(Bh,{key:1,visible:C.value},null,8,["visible"])):(d(),pe(Jh,{key:0,visible:v.value},null,8,["visible"]))])]))}});export{f1 as default};
