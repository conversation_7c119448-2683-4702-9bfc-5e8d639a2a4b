
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,af as s,a,az as t,r as n,b as o,a0 as u,c as l,e as i,a6 as r,i as p,f as d,g as c,T as _,aE as m,w as g,V as v,W as b,a5 as f,a8 as h,_ as y,Z as M}from"./index-BERX8Mlm.js";import{u as x}from"./index-DhWfG07N.js";import{_ as j}from"./index.vue_vue_type_script_setup_true_lang-AClYjrVV.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang-CReMfaKl.js";import"./sub.vue_vue_type_script_setup_true_lang-SYX2vZ5k.js";import"./item.vue_vue_type_script_setup_true_lang-D5h42eqV.js";import"./HTooltip.vue_vue_type_script_setup_true_lang-T8XkjmIi.js";const C=M(e({name:"SubSidebar",__name:"index",setup(e){const M=s(),C=a(),w=t(),S=n(),H=n(!1),T=n(!1);function z(){const e=S.value.scrollTop;H.value=e>0;const s=S.value.clientHeight,a=S.value.scrollHeight;T.value=Math.ceil(e+s)<a}const O=n();return o((()=>{z();const{height:e}=x(O);u((()=>e.value),(()=>{e.value>0&&z()}),{immediate:!0})})),(e,s)=>{const a=y;return i(),l("div",{class:r(["sub-sidebar-container",{"is-collapse":"pc"===p(C).mode&&p(C).settings.menu.subMenuCollapse}])},[d(j,{"show-logo":"single"===p(C).settings.menu.menuMode,class:r(["sidebar-logo",{"sidebar-logo-bg":"single"===p(C).settings.menu.menuMode}])},null,8,["show-logo","class"]),c("div",{ref_key:"subSidebarRef",ref:S,class:r(["sub-sidebar flex-1 transition-shadow-300",{"shadow-top":p(H),"shadow-bottom":p(T)}]),onScroll:z},[c("div",{ref_key:"menuRef",ref:O},[d(m,{name:"sub-sidebar"},{default:g((()=>[(i(!0),l(v,null,b(p(w).allMenus,((e,s)=>f((i(),l("div",{key:s},[d(k,{menu:e.children,value:p(M).meta.activeMenu||p(M).path,"default-openeds":p(w).defaultOpenedPaths,accordion:p(C).settings.menu.subMenuUniqueOpened,collapse:"pc"===p(C).mode&&p(C).settings.menu.subMenuCollapse,class:"menu"},null,8,["menu","value","default-openeds","accordion","collapse"])],512)),[[h,s===p(w).actived]]))),128))])),_:1})],512)],34),"pc"===p(C).mode?(i(),l("div",{key:0,class:r(["relative flex items-center px-4 py-3",[p(C).settings.menu.subMenuCollapse?"justify-center":"justify-end"]])},[f(c("span",{class:r(["flex-center cursor-pointer rounded bg-stone-1 p-2 transition dark-bg-stone-9 hover-bg-stone-2 dark-hover-bg-stone-8",{"-rotate-z-180":p(C).settings.menu.subMenuCollapse}]),onClick:s[0]||(s[0]=e=>p(C).toggleSidebarCollapse())},[d(a,{name:"toolbar-collapse"})],2),[[h,p(C).settings.menu.enableSubMenuCollapseButton]])],2)):_("",!0)],2)}}}),[["__scopeId","data-v-55637f48"]]);export{C as default};
