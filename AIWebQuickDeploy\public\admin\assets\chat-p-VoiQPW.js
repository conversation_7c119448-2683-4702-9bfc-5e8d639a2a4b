
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{ac as a,d as t,ad as r,r as n,$ as u,P as o,b as s,Q as i,a1 as d,c,e as p,f as m,w as v,g as f,V as _,W as y,a2 as h,a3 as w,a4 as b,h as k,a5 as g,T as U,t as x,i as V,ae as T,k as z}from"./index-BERX8Mlm.js";import{u as M}from"./utcFormatTime-BtFjiA-p.js";const L=e=>a.get("chatLog/chatAll",{params:e}),H={key:0},I={class:"answer"},Y=["innerHTML"],A={key:0,class:"details-container"},C={key:0},S=["innerHTML"],j=["innerHTML"],D={class:"tool-calls"},P={class:"plugin-param"},R=["innerHTML"],$={key:0,class:"media-links"},N={key:1,class:"media-links"},O=["src"],F={key:2,class:"media-links"},J=["src"],K={key:3,class:"media-links"},q=["href"],E={key:4,class:"media-links"},Q=["src"],W={key:5},B=t({__name:"chat",setup(a){const t=new r.Renderer;r.setOptions({renderer:t,gfm:!0,pedantic:!1});const z=n(!1),B=n([]),G=n([]),X=n(),Z=n(0),ee=u({userId:"",prompt:"",page:1,size:10,type:"",model:""}),le=n([]),ae=n(!1),te=n(null),re=o((()=>!!te.value&&!!(te.value.imageUrl||te.value.videoUrl||te.value.audioUrl||te.value.fileUrl||te.value.ttsUrl||te.value.fileInfo)));function ne(e){switch(e){case 1:return"普通对话";case 2:return"创意模型";case 3:return"特殊模型";default:return`类型${e}`}}function ue(e){try{return JSON.stringify(JSON.parse(e),null,2)}catch(l){return e}}function oe(e){if(!e)return"未知文件";const l=e.split("/");return l[l.length-1]}async function se(){z.value=!0;try{const e=await L(ee);z.value=!1;const{rows:l,count:a}=e.data;Z.value=a,le.value=l}catch(e){z.value=!1}}async function ie(e){const l=await T.queryAllUser({size:30,username:e});B.value=l.data.rows}return s((()=>{se(),async function(){try{const e=await L({page:1,size:1e3}),{rows:l}=e.data,a=new Set;l.forEach((e=>{e.model&&a.add(e.model)})),G.value=Array.from(a)}catch(e){}}()})),(a,t)=>{const n=l,u=i("el-option"),o=i("el-select"),s=i("el-form-item"),T=i("el-input"),L=i("el-button"),de=i("el-form"),ce=e,pe=i("el-table-column"),me=i("el-popover"),ve=i("el-table"),fe=i("el-descriptions-item"),_e=i("el-descriptions"),ye=i("el-tab-pane"),he=i("el-image"),we=i("el-tabs"),be=i("el-dialog"),ke=i("el-pagination"),ge=i("el-row"),Ue=d("loading");return p(),c("div",null,[m(n,null,{title:v((()=>t[8]||(t[8]=[f("div",{class:"flex items-center gap-4"},"对话记录",-1)]))),_:1}),m(ce,null,{default:v((()=>[m(de,{ref_key:"formRef",ref:X,inline:!0,model:ee},{default:v((()=>[m(s,{label:"用户名称",prop:"userId"},{default:v((()=>[m(o,{modelValue:ee.userId,"onUpdate:modelValue":t[0]||(t[0]=e=>ee.userId=e),filterable:"",clearable:"",remote:"","reserve-keyword":"",placeholder:"用户姓名[模糊搜索]","remote-show-suffix":"","remote-method":ie,style:{width:"160px"}},{default:v((()=>[(p(!0),c(_,null,y(B.value,(e=>(p(),h(u,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(s,{label:"用户询问的问题",prop:"prompt"},{default:v((()=>[m(T,{modelValue:ee.prompt,"onUpdate:modelValue":t[1]||(t[1]=e=>ee.prompt=e),placeholder:"提问问题[模糊搜索]",onKeydown:w(b(se,["prevent"]),["enter"])},null,8,["modelValue","onKeydown"])])),_:1}),m(s,{label:"模型",prop:"model"},{default:v((()=>[m(o,{modelValue:ee.model,"onUpdate:modelValue":t[2]||(t[2]=e=>ee.model=e),placeholder:"选择模型",style:{width:"160px"},clearable:""},{default:v((()=>[(p(!0),c(_,null,y(G.value,(e=>(p(),h(u,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(s,{label:"类型",prop:"type"},{default:v((()=>[m(o,{modelValue:ee.type,"onUpdate:modelValue":t[3]||(t[3]=e=>ee.type=e),placeholder:"对话类型",style:{width:"160px"},clearable:""},{default:v((()=>[m(u,{label:"基础对话",value:"1"}),m(u,{label:"创意模型",value:"2"}),m(u,{label:"特殊模型",value:"3"})])),_:1},8,["modelValue"])])),_:1}),m(s,null,{default:v((()=>[m(L,{type:"primary",onClick:se},{default:v((()=>t[9]||(t[9]=[k(" 查询 ")]))),_:1}),m(L,{onClick:t[4]||(t[4]=e=>{return null==(l=X.value)||l.resetFields(),void se();var l})},{default:v((()=>t[10]||(t[10]=[k(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),m(ce,{style:{width:"100%"}},{default:v((()=>[g((p(),h(ve,{border:"",data:le.value,style:{width:"100%"},size:"large","tooltip-options":{}},{default:v((()=>[m(pe,{fixed:"",prop:"username",label:"用户信息",width:"150"},{default:v((e=>[k(x(e.row.username)+" ",1),e.row.nickname?(p(),c("span",H,"("+x(e.row.nickname)+")",1)):U("",!0)])),_:1}),m(pe,{prop:"model",label:"模型",width:"150"}),m(pe,{prop:"answer",label:"用户询问/AI回复",width:"400"},{default:v((e=>[m(me,{placement:"top",width:"450",trigger:"click"},{reference:v((()=>[f("div",I,x(e.row.content),1)])),default:v((()=>[f("div",{class:"answer_container",innerHTML:V(r)(e.row.content||"")},null,8,Y)])),_:2},1024)])),_:1}),m(pe,{prop:"completionTokens",label:"Token统计",width:"200",align:"center"},{default:v((e=>[k(x("user"===e.row.role?`提问: ${e.row.promptTokens||0}`:`回答: ${e.row.completionTokens||0}`)+" / 总计: "+x(e.row.totalTokens||0),1)])),_:1}),m(pe,{prop:"createdAt",label:"时间",width:"200"},{default:v((e=>[k(x(V(M)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),m(pe,{label:"操作",width:"100",fixed:"right"},{default:v((e=>[m(L,{type:"primary",link:"",onClick:l=>{return a=e.row,te.value=a,void(ae.value=!0);var a}},{default:v((()=>t[11]||(t[11]=[k("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Ue,z.value]]),m(be,{modelValue:ae.value,"onUpdate:modelValue":t[5]||(t[5]=e=>ae.value=e),title:"详细内容",width:"70%","destroy-on-close":""},{default:v((()=>[te.value?(p(),c("div",A,[m(_e,{column:2,border:""},{default:v((()=>[m(fe,{label:"用户"},{default:v((()=>[k(x(te.value.username)+" ",1),te.value.nickname?(p(),c("span",C,"("+x(te.value.nickname)+")",1)):U("",!0)])),_:1}),m(fe,{label:"角色"},{default:v((()=>[k(x("user"===te.value.role?"用户":"电脑"),1)])),_:1}),m(fe,{label:"模型"},{default:v((()=>[k(x(te.value.model),1)])),_:1}),m(fe,{label:"模型名称"},{default:v((()=>[k(x(te.value.modelName),1)])),_:1}),m(fe,{label:"类型"},{default:v((()=>[k(x(ne(te.value.type)),1)])),_:1}),m(fe,{label:"时间"},{default:v((()=>[k(x(V(M)(te.value.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1})])),_:1}),m(we,{class:"mt-4"},{default:v((()=>[m(ye,{label:"内容"},{default:v((()=>[f("div",{class:"detail-content markdown-body",innerHTML:V(r)(te.value.content||"")},null,8,S)])),_:1}),te.value.reasoning_content?(p(),h(ye,{key:0,label:"推理内容"},{default:v((()=>[f("div",{class:"detail-content markdown-body",innerHTML:V(r)(te.value.reasoning_content||"")},null,8,j)])),_:1})):U("",!0),te.value.tool_calls?(p(),h(ye,{key:1,label:"工具调用"},{default:v((()=>[f("pre",D,x(ue(te.value.tool_calls)),1)])),_:1})):U("",!0),te.value.pluginParam?(p(),h(ye,{key:2,label:"插件参数"},{default:v((()=>[f("pre",P,x(ue(te.value.pluginParam)),1)])),_:1})):U("",!0),te.value.networkSearchResult?(p(),h(ye,{key:3,label:"联网搜索"},{default:v((()=>[f("div",{class:"detail-content markdown-body",innerHTML:V(r)(te.value.networkSearchResult||"")},null,8,R)])),_:1})):U("",!0),re.value?(p(),h(ye,{key:4,label:"其他信息"},{default:v((()=>[te.value.imageUrl?(p(),c("div",$,[t[12]||(t[12]=f("h4",null,"图片链接:",-1)),m(he,{src:te.value.imageUrl,"preview-src-list":[te.value.imageUrl],fit:"contain",style:{width:"200px",height:"200px"}},null,8,["src","preview-src-list"])])):U("",!0),te.value.videoUrl?(p(),c("div",N,[t[13]||(t[13]=f("h4",null,"视频链接:",-1)),f("video",{src:te.value.videoUrl,controls:"",style:{"max-width":"100%"}},null,8,O)])):U("",!0),te.value.audioUrl?(p(),c("div",F,[t[14]||(t[14]=f("h4",null,"音频链接:",-1)),f("audio",{src:te.value.audioUrl,controls:""},null,8,J)])):U("",!0),te.value.fileUrl?(p(),c("div",K,[t[15]||(t[15]=f("h4",null,"文件链接:",-1)),f("a",{href:te.value.fileUrl,target:"_blank"},x(oe(te.value.fileUrl)),9,q)])):U("",!0),te.value.ttsUrl?(p(),c("div",E,[t[16]||(t[16]=f("h4",null,"对话语音链接:",-1)),f("audio",{src:te.value.ttsUrl,controls:""},null,8,Q)])):U("",!0),te.value.fileInfo?(p(),c("div",W,[t[17]||(t[17]=f("h4",null,"文件信息:",-1)),f("pre",null,x(ue(te.value.fileInfo)),1)])):U("",!0)])),_:1})):U("",!0)])),_:1})])):U("",!0)])),_:1},8,["modelValue"]),m(ge,{class:"mt-5 flex justify-end"},{default:v((()=>[m(ke,{"current-page":ee.page,"onUpdate:currentPage":t[6]||(t[6]=e=>ee.page=e),"page-size":ee.size,"onUpdate:pageSize":t[7]||(t[7]=e=>ee.size=e),class:"mr-5","page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:Z.value,onSizeChange:se,onCurrentChange:se},null,8,["current-page","page-size","total"])])),_:1})])),_:1})])}}});"function"==typeof z&&z(B);export{B as default};
