#!/bin/bash

echo "🚀 99AI 快速配置向导"
echo "================================"

# 检查当前目录
if [ ! -f "AIWebQuickDeploy/package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

cd AIWebQuickDeploy

# 交互式配置
echo "请输入配置信息（按回车使用默认值）:"
echo ""

# 数据库配置
echo "📊 数据库配置:"
read -p "MySQL主机地址 [127.0.0.1]: " db_host
db_host=${db_host:-127.0.0.1}

read -p "MySQL端口 [3306]: " db_port
db_port=${db_port:-3306}

read -p "MySQL用户名 [root]: " db_user
db_user=${db_user:-root}

read -s -p "MySQL密码: " db_pass
echo ""

read -p "数据库名 [chatgpt]: " db_name
db_name=${db_name:-chatgpt}

echo ""
echo "🔴 Redis配置:"
read -p "Redis主机地址 [127.0.0.1]: " redis_host
redis_host=${redis_host:-127.0.0.1}

read -p "Redis端口 [6379]: " redis_port
redis_port=${redis_port:-6379}

read -s -p "Redis密码（可选）: " redis_pass
echo ""

read -p "Redis数据库编号 [0]: " redis_db
redis_db=${redis_db:-0}

echo ""
echo "⚙️  服务配置:"
read -p "服务端口 [9520]: " service_port
service_port=${service_port:-9520}

# 生成配置文件
echo ""
echo "📝 生成配置文件..."

cat > .env << EOF
# 99AI 环境配置文件
# 由快速配置向导生成于 $(date)

# 服务端口
PORT=$service_port

# MySQL 数据库配置
DB_HOST=$db_host
DB_PORT=$db_port
DB_USER=$db_user
DB_PASS=$db_pass
DB_DATABASE=$db_name

# Redis 配置
REDIS_HOST=$redis_host
REDIS_PORT=$redis_port
REDIS_PASSWORD=$redis_pass
REDIS_USER=
REDIS_DB=$redis_db

# 是否测试环境
ISDEV=false

# 自定义微信URL
weChatOpenUrl=https://open.weixin.qq.com
weChatApiUrl=https://api.weixin.qq.com
weChatApiUrlToken=https://api.weixin.qq.com/cgi-bin/token
weChatMpUrl=https://mp.weixin.qq.com

# 自定义后台路径
ADMIN_SERVE_ROOT=/admin

# 机器码及授权码（如需要）
# MACHINE_CODE=
# LICENSE_CODE=
EOF

echo "✅ 配置文件已生成: $(pwd)/.env"

# 测试连接
echo ""
echo "🔍 测试服务连接..."

# 测试MySQL
echo "测试MySQL连接..."
if command -v mysql >/dev/null 2>&1; then
    if mysql -h"$db_host" -P"$db_port" -u"$db_user" -p"$db_pass" -e "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ MySQL连接成功"
        
        # 检查数据库是否存在
        if mysql -h"$db_host" -P"$db_port" -u"$db_user" -p"$db_pass" -e "USE $db_name;" >/dev/null 2>&1; then
            echo "✅ 数据库 '$db_name' 存在"
        else
            echo "⚠️  数据库 '$db_name' 不存在"
            read -p "是否创建数据库? (y/N): " create_db
            if [[ $create_db =~ ^[Yy]$ ]]; then
                mysql -h"$db_host" -P"$db_port" -u"$db_user" -p"$db_pass" -e "CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
                echo "✅ 数据库已创建"
            fi
        fi
    else
        echo "❌ MySQL连接失败，请检查配置"
    fi
else
    echo "⚠️  mysql命令不可用，跳过连接测试"
fi

# 测试Redis
echo "测试Redis连接..."
if command -v redis-cli >/dev/null 2>&1; then
    if [ -n "$redis_pass" ]; then
        redis_cmd="redis-cli -h $redis_host -p $redis_port -a $redis_pass"
    else
        redis_cmd="redis-cli -h $redis_host -p $redis_port"
    fi
    
    if $redis_cmd ping >/dev/null 2>&1; then
        echo "✅ Redis连接成功"
    else
        echo "❌ Redis连接失败，请检查配置"
    fi
else
    echo "⚠️  redis-cli命令不可用，跳过连接测试"
fi

echo ""
echo "🎉 配置完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 启动服务: npm start"
echo "2. 查看日志: pm2 logs"
echo "3. 访问应用: http://localhost:$service_port"
echo ""
echo "🔧 如果遇到问题:"
echo "1. 检查服务状态: pm2 status"
echo "2. 重启服务: pm2 restart 99AI"
echo "3. 查看错误日志: pm2 logs 99AI --err"

echo ""
echo "================================"
echo "配置向导完成！"
