var l0=Object.defineProperty,O0=Object.defineProperties;var c0=Object.getOwnPropertyDescriptors;var bn=Object.getOwnPropertySymbols;var u0=Object.prototype.hasOwnProperty,f0=Object.prototype.propertyIsEnumerable;var xa=(e,t,a)=>t in e?l0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,ke=(e,t)=>{for(var a in t||(t={}))u0.call(t,a)&&xa(e,a,t[a]);if(bn)for(var a of bn(t))f0.call(t,a)&&xa(e,a,t[a]);return e},_t=(e,t)=>O0(e,c0(t));var Vt=(e,t,a)=>(xa(e,typeof t!="symbol"?t+"":t,a),a);var fe=(e,t,a)=>new Promise((r,n)=>{var i=c=>{try{o(a.next(c))}catch(O){n(O)}},s=c=>{try{o(a.throw(c))}catch(O){n(O)}},o=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);o((a=a.apply(e,t)).next())});import{bu as pr}from"./chart-vendor-e1d59b84.js";import{D as Zr,a5 as d0,q as it,d as Se,v as be,Q as et,l as Ye,R as h0,x as Ve,U as p0,J as Zt,u as Pe,E as ce,H as qe,G as gs,F as ca,a3 as x0,S as Xr,p as Pn,a6 as m0,a7 as g0,j as Ce,I as vs,f as yn,e as ua,n as $n,o as Rr,w as St,K as Xt,W as v0,a4 as wn,m as Qs,a8 as Q0,i as S0,A as Ss,b as Rt,s as b0,a9 as P0,aa as y0}from"./vue-vendor-d751b0f5.js";import{j as $0,c as K,g as w0,k as _0,l as k0,m as C0}from"./utils-vendor-c35799af.js";import{q as E0,N as A0,Q as T0,X as Z0,C as bs,Y as X0,j as R0,x as D0,Z as B0}from"./ui-vendor-70145f70.js";import{P as q0,N as L0,a as F0,D as Y0,b as Dr,T as zt,I as Br,s as qr,t as _,p as I0,L as Lr,i as Fr,c as Nt,f as Yr,d as Ps,e as Ir,g as It,h as ys,j as V0,k as U0,l as $s,m as M0,n as W0,E as Dt,o as ws,q as Ae,r as z0,u as N0,v as G0,w as j0,x as H0,y as K0}from"./editor-vendor-e2dea24d.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function a(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=a(n);fetch(n.href,i)}})();function J0(){const e=document.documentElement;return{init:()=>{const r=localStorage.getItem("theme");if(r&&(r==="dark"||r==="light"))e.dataset.theme=r,e.classList.toggle("dark",r==="dark");else{const n=window.matchMedia("(prefers-color-scheme: dark)").matches;e.dataset.theme=n?"dark":"light",e.classList.toggle("dark",n),localStorage.setItem("theme",e.dataset.theme)}},toggle:()=>{const r=e.dataset.theme==="dark"?"light":"dark";e.dataset.theme=r,e.classList.toggle("dark",r==="dark"),localStorage.setItem("theme",r)}}}var _s={exports:{}};function el(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var ma={exports:{}};const tl={},al=Object.freeze(Object.defineProperty({__proto__:null,default:tl},Symbol.toStringTag,{value:"Module"})),rl=$0(al);var _n;function ie(){return _n||(_n=1,function(e,t){(function(a,r){e.exports=r()})(K,function(){var a=a||function(r,n){var i;if(typeof window!="undefined"&&window.crypto&&(i=window.crypto),typeof self!="undefined"&&self.crypto&&(i=self.crypto),typeof globalThis!="undefined"&&globalThis.crypto&&(i=globalThis.crypto),!i&&typeof window!="undefined"&&window.msCrypto&&(i=window.msCrypto),!i&&typeof K!="undefined"&&K.crypto&&(i=K.crypto),!i&&typeof el=="function")try{i=rl}catch(x){}var s=function(){if(i){if(typeof i.getRandomValues=="function")try{return i.getRandomValues(new Uint32Array(1))[0]}catch(x){}if(typeof i.randomBytes=="function")try{return i.randomBytes(4).readInt32LE()}catch(x){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function x(){}return function(m){var p;return x.prototype=m,p=new x,x.prototype=null,p}}(),c={},O=c.lib={},d=O.Base=function(){return{extend:function(x){var m=o(this);return x&&m.mixIn(x),(!m.hasOwnProperty("init")||this.init===m.init)&&(m.init=function(){m.$super.init.apply(this,arguments)}),m.init.prototype=m,m.$super=this,m},create:function(){var x=this.extend();return x.init.apply(x,arguments),x},init:function(){},mixIn:function(x){for(var m in x)x.hasOwnProperty(m)&&(this[m]=x[m]);x.hasOwnProperty("toString")&&(this.toString=x.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),g=O.WordArray=d.extend({init:function(x,m){x=this.words=x||[],m!=n?this.sigBytes=m:this.sigBytes=x.length*4},toString:function(x){return(x||u).stringify(this)},concat:function(x){var m=this.words,p=x.words,S=this.sigBytes,P=x.sigBytes;if(this.clamp(),S%4)for(var b=0;b<P;b++){var k=p[b>>>2]>>>24-b%4*8&255;m[S+b>>>2]|=k<<24-(S+b)%4*8}else for(var D=0;D<P;D+=4)m[S+D>>>2]=p[D>>>2];return this.sigBytes+=P,this},clamp:function(){var x=this.words,m=this.sigBytes;x[m>>>2]&=4294967295<<32-m%4*8,x.length=r.ceil(m/4)},clone:function(){var x=d.clone.call(this);return x.words=this.words.slice(0),x},random:function(x){for(var m=[],p=0;p<x;p+=4)m.push(s());return new g.init(m,x)}}),l=c.enc={},u=l.Hex={stringify:function(x){for(var m=x.words,p=x.sigBytes,S=[],P=0;P<p;P++){var b=m[P>>>2]>>>24-P%4*8&255;S.push((b>>>4).toString(16)),S.push((b&15).toString(16))}return S.join("")},parse:function(x){for(var m=x.length,p=[],S=0;S<m;S+=2)p[S>>>3]|=parseInt(x.substr(S,2),16)<<24-S%8*4;return new g.init(p,m/2)}},f=l.Latin1={stringify:function(x){for(var m=x.words,p=x.sigBytes,S=[],P=0;P<p;P++){var b=m[P>>>2]>>>24-P%4*8&255;S.push(String.fromCharCode(b))}return S.join("")},parse:function(x){for(var m=x.length,p=[],S=0;S<m;S++)p[S>>>2]|=(x.charCodeAt(S)&255)<<24-S%4*8;return new g.init(p,m)}},h=l.Utf8={stringify:function(x){try{return decodeURIComponent(escape(f.stringify(x)))}catch(m){throw new Error("Malformed UTF-8 data")}},parse:function(x){return f.parse(unescape(encodeURIComponent(x)))}},v=O.BufferedBlockAlgorithm=d.extend({reset:function(){this._data=new g.init,this._nDataBytes=0},_append:function(x){typeof x=="string"&&(x=h.parse(x)),this._data.concat(x),this._nDataBytes+=x.sigBytes},_process:function(x){var m,p=this._data,S=p.words,P=p.sigBytes,b=this.blockSize,k=b*4,D=P/k;x?D=r.ceil(D):D=r.max((D|0)-this._minBufferSize,0);var w=D*b,C=r.min(w*4,P);if(w){for(var Z=0;Z<w;Z+=b)this._doProcessBlock(S,Z);m=S.splice(0,w),p.sigBytes-=C}return new g.init(m,C)},clone:function(){var x=d.clone.call(this);return x._data=this._data.clone(),x},_minBufferSize:0});O.Hasher=v.extend({cfg:d.extend(),init:function(x){this.cfg=this.cfg.extend(x),this.reset()},reset:function(){v.reset.call(this),this._doReset()},update:function(x){return this._append(x),this._process(),this},finalize:function(x){x&&this._append(x);var m=this._doFinalize();return m},blockSize:16,_createHelper:function(x){return function(m,p){return new x.init(p).finalize(m)}},_createHmacHelper:function(x){return function(m,p){return new Q.HMAC.init(x,p).finalize(m)}}});var Q=c.algo={};return c}(Math);return a})}(ma)),ma.exports}var ga={exports:{}},kn;function fa(){return kn||(kn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(r){var n=a,i=n.lib,s=i.Base,o=i.WordArray,c=n.x64={};c.Word=s.extend({init:function(O,d){this.high=O,this.low=d}}),c.WordArray=s.extend({init:function(O,d){O=this.words=O||[],d!=r?this.sigBytes=d:this.sigBytes=O.length*8},toX32:function(){for(var O=this.words,d=O.length,g=[],l=0;l<d;l++){var u=O[l];g.push(u.high),g.push(u.low)}return o.create(g,this.sigBytes)},clone:function(){for(var O=s.clone.call(this),d=O.words=this.words.slice(0),g=d.length,l=0;l<g;l++)d[l]=d[l].clone();return O}})}(),a})}(ga)),ga.exports}var va={exports:{}},Cn;function nl(){return Cn||(Cn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(){if(typeof ArrayBuffer=="function"){var r=a,n=r.lib,i=n.WordArray,s=i.init,o=i.init=function(c){if(c instanceof ArrayBuffer&&(c=new Uint8Array(c)),(c instanceof Int8Array||typeof Uint8ClampedArray!="undefined"&&c instanceof Uint8ClampedArray||c instanceof Int16Array||c instanceof Uint16Array||c instanceof Int32Array||c instanceof Uint32Array||c instanceof Float32Array||c instanceof Float64Array)&&(c=new Uint8Array(c.buffer,c.byteOffset,c.byteLength)),c instanceof Uint8Array){for(var O=c.byteLength,d=[],g=0;g<O;g++)d[g>>>2]|=c[g]<<24-g%4*8;s.call(this,d,O)}else s.apply(this,arguments)};o.prototype=i}}(),a.lib.WordArray})}(va)),va.exports}var Qa={exports:{}},En;function il(){return En||(En=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(){var r=a,n=r.lib,i=n.WordArray,s=r.enc;s.Utf16=s.Utf16BE={stringify:function(c){for(var O=c.words,d=c.sigBytes,g=[],l=0;l<d;l+=2){var u=O[l>>>2]>>>16-l%4*8&65535;g.push(String.fromCharCode(u))}return g.join("")},parse:function(c){for(var O=c.length,d=[],g=0;g<O;g++)d[g>>>1]|=c.charCodeAt(g)<<16-g%2*16;return i.create(d,O*2)}},s.Utf16LE={stringify:function(c){for(var O=c.words,d=c.sigBytes,g=[],l=0;l<d;l+=2){var u=o(O[l>>>2]>>>16-l%4*8&65535);g.push(String.fromCharCode(u))}return g.join("")},parse:function(c){for(var O=c.length,d=[],g=0;g<O;g++)d[g>>>1]|=o(c.charCodeAt(g)<<16-g%2*16);return i.create(d,O*2)}};function o(c){return c<<8&4278255360|c>>>8&16711935}}(),a.enc.Utf16})}(Qa)),Qa.exports}var Sa={exports:{}},An;function xt(){return An||(An=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(){var r=a,n=r.lib,i=n.WordArray,s=r.enc;s.Base64={stringify:function(c){var O=c.words,d=c.sigBytes,g=this._map;c.clamp();for(var l=[],u=0;u<d;u+=3)for(var f=O[u>>>2]>>>24-u%4*8&255,h=O[u+1>>>2]>>>24-(u+1)%4*8&255,v=O[u+2>>>2]>>>24-(u+2)%4*8&255,Q=f<<16|h<<8|v,x=0;x<4&&u+x*.75<d;x++)l.push(g.charAt(Q>>>6*(3-x)&63));var m=g.charAt(64);if(m)for(;l.length%4;)l.push(m);return l.join("")},parse:function(c){var O=c.length,d=this._map,g=this._reverseMap;if(!g){g=this._reverseMap=[];for(var l=0;l<d.length;l++)g[d.charCodeAt(l)]=l}var u=d.charAt(64);if(u){var f=c.indexOf(u);f!==-1&&(O=f)}return o(c,O,g)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function o(c,O,d){for(var g=[],l=0,u=0;u<O;u++)if(u%4){var f=d[c.charCodeAt(u-1)]<<u%4*2,h=d[c.charCodeAt(u)]>>>6-u%4*2,v=f|h;g[l>>>2]|=v<<24-l%4*8,l++}return i.create(g,l)}}(),a.enc.Base64})}(Sa)),Sa.exports}var ba={exports:{}},Tn;function sl(){return Tn||(Tn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(){var r=a,n=r.lib,i=n.WordArray,s=r.enc;s.Base64url={stringify:function(c,O){O===void 0&&(O=!0);var d=c.words,g=c.sigBytes,l=O?this._safe_map:this._map;c.clamp();for(var u=[],f=0;f<g;f+=3)for(var h=d[f>>>2]>>>24-f%4*8&255,v=d[f+1>>>2]>>>24-(f+1)%4*8&255,Q=d[f+2>>>2]>>>24-(f+2)%4*8&255,x=h<<16|v<<8|Q,m=0;m<4&&f+m*.75<g;m++)u.push(l.charAt(x>>>6*(3-m)&63));var p=l.charAt(64);if(p)for(;u.length%4;)u.push(p);return u.join("")},parse:function(c,O){O===void 0&&(O=!0);var d=c.length,g=O?this._safe_map:this._map,l=this._reverseMap;if(!l){l=this._reverseMap=[];for(var u=0;u<g.length;u++)l[g.charCodeAt(u)]=u}var f=g.charAt(64);if(f){var h=c.indexOf(f);h!==-1&&(d=h)}return o(c,d,l)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function o(c,O,d){for(var g=[],l=0,u=0;u<O;u++)if(u%4){var f=d[c.charCodeAt(u-1)]<<u%4*2,h=d[c.charCodeAt(u)]>>>6-u%4*2,v=f|h;g[l>>>2]|=v<<24-l%4*8,l++}return i.create(g,l)}}(),a.enc.Base64url})}(ba)),ba.exports}var Pa={exports:{}},Zn;function mt(){return Zn||(Zn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(r){var n=a,i=n.lib,s=i.WordArray,o=i.Hasher,c=n.algo,O=[];(function(){for(var h=0;h<64;h++)O[h]=r.abs(r.sin(h+1))*4294967296|0})();var d=c.MD5=o.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(h,v){for(var Q=0;Q<16;Q++){var x=v+Q,m=h[x];h[x]=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360}var p=this._hash.words,S=h[v+0],P=h[v+1],b=h[v+2],k=h[v+3],D=h[v+4],w=h[v+5],C=h[v+6],Z=h[v+7],R=h[v+8],U=h[v+9],M=h[v+10],G=h[v+11],ne=h[v+12],Y=h[v+13],F=h[v+14],j=h[v+15],T=p[0],X=p[1],q=p[2],B=p[3];T=g(T,X,q,B,S,7,O[0]),B=g(B,T,X,q,P,12,O[1]),q=g(q,B,T,X,b,17,O[2]),X=g(X,q,B,T,k,22,O[3]),T=g(T,X,q,B,D,7,O[4]),B=g(B,T,X,q,w,12,O[5]),q=g(q,B,T,X,C,17,O[6]),X=g(X,q,B,T,Z,22,O[7]),T=g(T,X,q,B,R,7,O[8]),B=g(B,T,X,q,U,12,O[9]),q=g(q,B,T,X,M,17,O[10]),X=g(X,q,B,T,G,22,O[11]),T=g(T,X,q,B,ne,7,O[12]),B=g(B,T,X,q,Y,12,O[13]),q=g(q,B,T,X,F,17,O[14]),X=g(X,q,B,T,j,22,O[15]),T=l(T,X,q,B,P,5,O[16]),B=l(B,T,X,q,C,9,O[17]),q=l(q,B,T,X,G,14,O[18]),X=l(X,q,B,T,S,20,O[19]),T=l(T,X,q,B,w,5,O[20]),B=l(B,T,X,q,M,9,O[21]),q=l(q,B,T,X,j,14,O[22]),X=l(X,q,B,T,D,20,O[23]),T=l(T,X,q,B,U,5,O[24]),B=l(B,T,X,q,F,9,O[25]),q=l(q,B,T,X,k,14,O[26]),X=l(X,q,B,T,R,20,O[27]),T=l(T,X,q,B,Y,5,O[28]),B=l(B,T,X,q,b,9,O[29]),q=l(q,B,T,X,Z,14,O[30]),X=l(X,q,B,T,ne,20,O[31]),T=u(T,X,q,B,w,4,O[32]),B=u(B,T,X,q,R,11,O[33]),q=u(q,B,T,X,G,16,O[34]),X=u(X,q,B,T,F,23,O[35]),T=u(T,X,q,B,P,4,O[36]),B=u(B,T,X,q,D,11,O[37]),q=u(q,B,T,X,Z,16,O[38]),X=u(X,q,B,T,M,23,O[39]),T=u(T,X,q,B,Y,4,O[40]),B=u(B,T,X,q,S,11,O[41]),q=u(q,B,T,X,k,16,O[42]),X=u(X,q,B,T,C,23,O[43]),T=u(T,X,q,B,U,4,O[44]),B=u(B,T,X,q,ne,11,O[45]),q=u(q,B,T,X,j,16,O[46]),X=u(X,q,B,T,b,23,O[47]),T=f(T,X,q,B,S,6,O[48]),B=f(B,T,X,q,Z,10,O[49]),q=f(q,B,T,X,F,15,O[50]),X=f(X,q,B,T,w,21,O[51]),T=f(T,X,q,B,ne,6,O[52]),B=f(B,T,X,q,k,10,O[53]),q=f(q,B,T,X,M,15,O[54]),X=f(X,q,B,T,P,21,O[55]),T=f(T,X,q,B,R,6,O[56]),B=f(B,T,X,q,j,10,O[57]),q=f(q,B,T,X,C,15,O[58]),X=f(X,q,B,T,Y,21,O[59]),T=f(T,X,q,B,D,6,O[60]),B=f(B,T,X,q,G,10,O[61]),q=f(q,B,T,X,b,15,O[62]),X=f(X,q,B,T,U,21,O[63]),p[0]=p[0]+T|0,p[1]=p[1]+X|0,p[2]=p[2]+q|0,p[3]=p[3]+B|0},_doFinalize:function(){var h=this._data,v=h.words,Q=this._nDataBytes*8,x=h.sigBytes*8;v[x>>>5]|=128<<24-x%32;var m=r.floor(Q/4294967296),p=Q;v[(x+64>>>9<<4)+15]=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360,v[(x+64>>>9<<4)+14]=(p<<8|p>>>24)&16711935|(p<<24|p>>>8)&4278255360,h.sigBytes=(v.length+1)*4,this._process();for(var S=this._hash,P=S.words,b=0;b<4;b++){var k=P[b];P[b]=(k<<8|k>>>24)&16711935|(k<<24|k>>>8)&4278255360}return S},clone:function(){var h=o.clone.call(this);return h._hash=this._hash.clone(),h}});function g(h,v,Q,x,m,p,S){var P=h+(v&Q|~v&x)+m+S;return(P<<p|P>>>32-p)+v}function l(h,v,Q,x,m,p,S){var P=h+(v&x|Q&~x)+m+S;return(P<<p|P>>>32-p)+v}function u(h,v,Q,x,m,p,S){var P=h+(v^Q^x)+m+S;return(P<<p|P>>>32-p)+v}function f(h,v,Q,x,m,p,S){var P=h+(Q^(v|~x))+m+S;return(P<<p|P>>>32-p)+v}n.MD5=o._createHelper(d),n.HmacMD5=o._createHmacHelper(d)}(Math),a.MD5})}(Pa)),Pa.exports}var ya={exports:{}},Xn;function ks(){return Xn||(Xn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(){var r=a,n=r.lib,i=n.WordArray,s=n.Hasher,o=r.algo,c=[],O=o.SHA1=s.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(d,g){for(var l=this._hash.words,u=l[0],f=l[1],h=l[2],v=l[3],Q=l[4],x=0;x<80;x++){if(x<16)c[x]=d[g+x]|0;else{var m=c[x-3]^c[x-8]^c[x-14]^c[x-16];c[x]=m<<1|m>>>31}var p=(u<<5|u>>>27)+Q+c[x];x<20?p+=(f&h|~f&v)+1518500249:x<40?p+=(f^h^v)+1859775393:x<60?p+=(f&h|f&v|h&v)-1894007588:p+=(f^h^v)-899497514,Q=v,v=h,h=f<<30|f>>>2,f=u,u=p}l[0]=l[0]+u|0,l[1]=l[1]+f|0,l[2]=l[2]+h|0,l[3]=l[3]+v|0,l[4]=l[4]+Q|0},_doFinalize:function(){var d=this._data,g=d.words,l=this._nDataBytes*8,u=d.sigBytes*8;return g[u>>>5]|=128<<24-u%32,g[(u+64>>>9<<4)+14]=Math.floor(l/4294967296),g[(u+64>>>9<<4)+15]=l,d.sigBytes=g.length*4,this._process(),this._hash},clone:function(){var d=s.clone.call(this);return d._hash=this._hash.clone(),d}});r.SHA1=s._createHelper(O),r.HmacSHA1=s._createHmacHelper(O)}(),a.SHA1})}(ya)),ya.exports}var $a={exports:{}},Rn;function Vr(){return Rn||(Rn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){return function(r){var n=a,i=n.lib,s=i.WordArray,o=i.Hasher,c=n.algo,O=[],d=[];(function(){function u(Q){for(var x=r.sqrt(Q),m=2;m<=x;m++)if(!(Q%m))return!1;return!0}function f(Q){return(Q-(Q|0))*4294967296|0}for(var h=2,v=0;v<64;)u(h)&&(v<8&&(O[v]=f(r.pow(h,1/2))),d[v]=f(r.pow(h,1/3)),v++),h++})();var g=[],l=c.SHA256=o.extend({_doReset:function(){this._hash=new s.init(O.slice(0))},_doProcessBlock:function(u,f){for(var h=this._hash.words,v=h[0],Q=h[1],x=h[2],m=h[3],p=h[4],S=h[5],P=h[6],b=h[7],k=0;k<64;k++){if(k<16)g[k]=u[f+k]|0;else{var D=g[k-15],w=(D<<25|D>>>7)^(D<<14|D>>>18)^D>>>3,C=g[k-2],Z=(C<<15|C>>>17)^(C<<13|C>>>19)^C>>>10;g[k]=w+g[k-7]+Z+g[k-16]}var R=p&S^~p&P,U=v&Q^v&x^Q&x,M=(v<<30|v>>>2)^(v<<19|v>>>13)^(v<<10|v>>>22),G=(p<<26|p>>>6)^(p<<21|p>>>11)^(p<<7|p>>>25),ne=b+G+R+d[k]+g[k],Y=M+U;b=P,P=S,S=p,p=m+ne|0,m=x,x=Q,Q=v,v=ne+Y|0}h[0]=h[0]+v|0,h[1]=h[1]+Q|0,h[2]=h[2]+x|0,h[3]=h[3]+m|0,h[4]=h[4]+p|0,h[5]=h[5]+S|0,h[6]=h[6]+P|0,h[7]=h[7]+b|0},_doFinalize:function(){var u=this._data,f=u.words,h=this._nDataBytes*8,v=u.sigBytes*8;return f[v>>>5]|=128<<24-v%32,f[(v+64>>>9<<4)+14]=r.floor(h/4294967296),f[(v+64>>>9<<4)+15]=h,u.sigBytes=f.length*4,this._process(),this._hash},clone:function(){var u=o.clone.call(this);return u._hash=this._hash.clone(),u}});n.SHA256=o._createHelper(l),n.HmacSHA256=o._createHmacHelper(l)}(Math),a.SHA256})}($a)),$a.exports}var wa={exports:{}},Dn;function ol(){return Dn||(Dn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),Vr())})(K,function(a){return function(){var r=a,n=r.lib,i=n.WordArray,s=r.algo,o=s.SHA256,c=s.SHA224=o.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var O=o._doFinalize.call(this);return O.sigBytes-=4,O}});r.SHA224=o._createHelper(c),r.HmacSHA224=o._createHmacHelper(c)}(),a.SHA224})}(wa)),wa.exports}var _a={exports:{}},Bn;function Cs(){return Bn||(Bn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),fa())})(K,function(a){return function(){var r=a,n=r.lib,i=n.Hasher,s=r.x64,o=s.Word,c=s.WordArray,O=r.algo;function d(){return o.create.apply(o,arguments)}var g=[d(1116352408,3609767458),d(1899447441,602891725),d(3049323471,3964484399),d(3921009573,2173295548),d(961987163,4081628472),d(1508970993,3053834265),d(2453635748,2937671579),d(2870763221,3664609560),d(3624381080,2734883394),d(310598401,1164996542),d(607225278,1323610764),d(1426881987,3590304994),d(1925078388,4068182383),d(2162078206,991336113),d(2614888103,633803317),d(3248222580,3479774868),d(3835390401,2666613458),d(4022224774,944711139),d(264347078,2341262773),d(604807628,2007800933),d(770255983,1495990901),d(1249150122,1856431235),d(1555081692,3175218132),d(1996064986,2198950837),d(2554220882,3999719339),d(2821834349,766784016),d(2952996808,2566594879),d(3210313671,3203337956),d(3336571891,1034457026),d(3584528711,2466948901),d(113926993,3758326383),d(338241895,168717936),d(666307205,1188179964),d(773529912,1546045734),d(1294757372,1522805485),d(1396182291,2643833823),d(1695183700,2343527390),d(1986661051,1014477480),d(2177026350,1206759142),d(2456956037,344077627),d(2730485921,1290863460),d(2820302411,3158454273),d(3259730800,3505952657),d(3345764771,106217008),d(3516065817,3606008344),d(3600352804,1432725776),d(4094571909,1467031594),d(275423344,851169720),d(430227734,3100823752),d(506948616,1363258195),d(659060556,3750685593),d(883997877,3785050280),d(958139571,3318307427),d(1322822218,3812723403),d(1537002063,2003034995),d(1747873779,3602036899),d(1955562222,1575990012),d(2024104815,1125592928),d(2227730452,2716904306),d(2361852424,442776044),d(2428436474,593698344),d(2756734187,3733110249),d(3204031479,2999351573),d(3329325298,3815920427),d(3391569614,3928383900),d(3515267271,566280711),d(3940187606,3454069534),d(4118630271,4000239992),d(116418474,1914138554),d(174292421,2731055270),d(289380356,3203993006),d(460393269,320620315),d(685471733,587496836),d(852142971,1086792851),d(1017036298,365543100),d(1126000580,2618297676),d(1288033470,3409855158),d(1501505948,4234509866),d(1607167915,987167468),d(1816402316,1246189591)],l=[];(function(){for(var f=0;f<80;f++)l[f]=d()})();var u=O.SHA512=i.extend({_doReset:function(){this._hash=new c.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(f,h){for(var v=this._hash.words,Q=v[0],x=v[1],m=v[2],p=v[3],S=v[4],P=v[5],b=v[6],k=v[7],D=Q.high,w=Q.low,C=x.high,Z=x.low,R=m.high,U=m.low,M=p.high,G=p.low,ne=S.high,Y=S.low,F=P.high,j=P.low,T=b.high,X=b.low,q=k.high,B=k.low,oe=D,J=w,Qe=C,H=Z,Ze=R,Xe=U,Me=M,ve=G,me=ne,pe=Y,ee=F,te=j,ae=T,$=X,y=q,E=B,I=0;I<80;I++){var z,N,A=l[I];if(I<16)N=A.high=f[h+I*2]|0,z=A.low=f[h+I*2+1]|0;else{var L=l[I-15],se=L.high,he=L.low,Ge=(se>>>1|he<<31)^(se>>>8|he<<24)^se>>>7,ht=(he>>>1|se<<31)^(he>>>8|se<<24)^(he>>>7|se<<25),je=l[I-2],ot=je.high,gt=je.low,jo=(ot>>>19|gt<<13)^(ot<<3|gt>>>29)^ot>>>6,dn=(gt>>>19|ot<<13)^(gt<<3|ot>>>29)^(gt>>>6|ot<<26),hn=l[I-7],Ho=hn.high,Ko=hn.low,pn=l[I-16],Jo=pn.high,xn=pn.low;z=ht+Ko,N=Ge+Ho+(z>>>0<ht>>>0?1:0),z=z+dn,N=N+jo+(z>>>0<dn>>>0?1:0),z=z+xn,N=N+Jo+(z>>>0<xn>>>0?1:0),A.high=N,A.low=z}var e0=me&ee^~me&ae,mn=pe&te^~pe&$,t0=oe&Qe^oe&Ze^Qe&Ze,a0=J&H^J&Xe^H&Xe,r0=(oe>>>28|J<<4)^(oe<<30|J>>>2)^(oe<<25|J>>>7),gn=(J>>>28|oe<<4)^(J<<30|oe>>>2)^(J<<25|oe>>>7),n0=(me>>>14|pe<<18)^(me>>>18|pe<<14)^(me<<23|pe>>>9),i0=(pe>>>14|me<<18)^(pe>>>18|me<<14)^(pe<<23|me>>>9),vn=g[I],s0=vn.high,Qn=vn.low,Re=E+i0,lt=y+n0+(Re>>>0<E>>>0?1:0),Re=Re+mn,lt=lt+e0+(Re>>>0<mn>>>0?1:0),Re=Re+Qn,lt=lt+s0+(Re>>>0<Qn>>>0?1:0),Re=Re+z,lt=lt+N+(Re>>>0<z>>>0?1:0),Sn=gn+a0,o0=r0+t0+(Sn>>>0<gn>>>0?1:0);y=ae,E=$,ae=ee,$=te,ee=me,te=pe,pe=ve+Re|0,me=Me+lt+(pe>>>0<ve>>>0?1:0)|0,Me=Ze,ve=Xe,Ze=Qe,Xe=H,Qe=oe,H=J,J=Re+Sn|0,oe=lt+o0+(J>>>0<Re>>>0?1:0)|0}w=Q.low=w+J,Q.high=D+oe+(w>>>0<J>>>0?1:0),Z=x.low=Z+H,x.high=C+Qe+(Z>>>0<H>>>0?1:0),U=m.low=U+Xe,m.high=R+Ze+(U>>>0<Xe>>>0?1:0),G=p.low=G+ve,p.high=M+Me+(G>>>0<ve>>>0?1:0),Y=S.low=Y+pe,S.high=ne+me+(Y>>>0<pe>>>0?1:0),j=P.low=j+te,P.high=F+ee+(j>>>0<te>>>0?1:0),X=b.low=X+$,b.high=T+ae+(X>>>0<$>>>0?1:0),B=k.low=B+E,k.high=q+y+(B>>>0<E>>>0?1:0)},_doFinalize:function(){var f=this._data,h=f.words,v=this._nDataBytes*8,Q=f.sigBytes*8;h[Q>>>5]|=128<<24-Q%32,h[(Q+128>>>10<<5)+30]=Math.floor(v/4294967296),h[(Q+128>>>10<<5)+31]=v,f.sigBytes=h.length*4,this._process();var x=this._hash.toX32();return x},clone:function(){var f=i.clone.call(this);return f._hash=this._hash.clone(),f},blockSize:1024/32});r.SHA512=i._createHelper(u),r.HmacSHA512=i._createHmacHelper(u)}(),a.SHA512})}(_a)),_a.exports}var ka={exports:{}},qn;function ll(){return qn||(qn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),fa(),Cs())})(K,function(a){return function(){var r=a,n=r.x64,i=n.Word,s=n.WordArray,o=r.algo,c=o.SHA512,O=o.SHA384=c.extend({_doReset:function(){this._hash=new s.init([new i.init(3418070365,3238371032),new i.init(1654270250,914150663),new i.init(2438529370,812702999),new i.init(355462360,4144912697),new i.init(1731405415,4290775857),new i.init(2394180231,1750603025),new i.init(3675008525,1694076839),new i.init(1203062813,3204075428)])},_doFinalize:function(){var d=c._doFinalize.call(this);return d.sigBytes-=16,d}});r.SHA384=c._createHelper(O),r.HmacSHA384=c._createHmacHelper(O)}(),a.SHA384})}(ka)),ka.exports}var Ca={exports:{}},Ln;function Ol(){return Ln||(Ln=1,function(e,t){(function(a,r,n){e.exports=r(ie(),fa())})(K,function(a){return function(r){var n=a,i=n.lib,s=i.WordArray,o=i.Hasher,c=n.x64,O=c.Word,d=n.algo,g=[],l=[],u=[];(function(){for(var v=1,Q=0,x=0;x<24;x++){g[v+5*Q]=(x+1)*(x+2)/2%64;var m=Q%5,p=(2*v+3*Q)%5;v=m,Q=p}for(var v=0;v<5;v++)for(var Q=0;Q<5;Q++)l[v+5*Q]=Q+(2*v+3*Q)%5*5;for(var S=1,P=0;P<24;P++){for(var b=0,k=0,D=0;D<7;D++){if(S&1){var w=(1<<D)-1;w<32?k^=1<<w:b^=1<<w-32}S&128?S=S<<1^113:S<<=1}u[P]=O.create(b,k)}})();var f=[];(function(){for(var v=0;v<25;v++)f[v]=O.create()})();var h=d.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var v=this._state=[],Q=0;Q<25;Q++)v[Q]=new O.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(v,Q){for(var x=this._state,m=this.blockSize/2,p=0;p<m;p++){var S=v[Q+2*p],P=v[Q+2*p+1];S=(S<<8|S>>>24)&16711935|(S<<24|S>>>8)&4278255360,P=(P<<8|P>>>24)&16711935|(P<<24|P>>>8)&4278255360;var b=x[p];b.high^=P,b.low^=S}for(var k=0;k<24;k++){for(var D=0;D<5;D++){for(var w=0,C=0,Z=0;Z<5;Z++){var b=x[D+5*Z];w^=b.high,C^=b.low}var R=f[D];R.high=w,R.low=C}for(var D=0;D<5;D++)for(var U=f[(D+4)%5],M=f[(D+1)%5],G=M.high,ne=M.low,w=U.high^(G<<1|ne>>>31),C=U.low^(ne<<1|G>>>31),Z=0;Z<5;Z++){var b=x[D+5*Z];b.high^=w,b.low^=C}for(var Y=1;Y<25;Y++){var w,C,b=x[Y],F=b.high,j=b.low,T=g[Y];T<32?(w=F<<T|j>>>32-T,C=j<<T|F>>>32-T):(w=j<<T-32|F>>>64-T,C=F<<T-32|j>>>64-T);var X=f[l[Y]];X.high=w,X.low=C}var q=f[0],B=x[0];q.high=B.high,q.low=B.low;for(var D=0;D<5;D++)for(var Z=0;Z<5;Z++){var Y=D+5*Z,b=x[Y],oe=f[Y],J=f[(D+1)%5+5*Z],Qe=f[(D+2)%5+5*Z];b.high=oe.high^~J.high&Qe.high,b.low=oe.low^~J.low&Qe.low}var b=x[0],H=u[k];b.high^=H.high,b.low^=H.low}},_doFinalize:function(){var v=this._data,Q=v.words;this._nDataBytes*8;var x=v.sigBytes*8,m=this.blockSize*32;Q[x>>>5]|=1<<24-x%32,Q[(r.ceil((x+1)/m)*m>>>5)-1]|=128,v.sigBytes=Q.length*4,this._process();for(var p=this._state,S=this.cfg.outputLength/8,P=S/8,b=[],k=0;k<P;k++){var D=p[k],w=D.high,C=D.low;w=(w<<8|w>>>24)&16711935|(w<<24|w>>>8)&4278255360,C=(C<<8|C>>>24)&16711935|(C<<24|C>>>8)&4278255360,b.push(C),b.push(w)}return new s.init(b,S)},clone:function(){for(var v=o.clone.call(this),Q=v._state=this._state.slice(0),x=0;x<25;x++)Q[x]=Q[x].clone();return v}});n.SHA3=o._createHelper(h),n.HmacSHA3=o._createHmacHelper(h)}(Math),a.SHA3})}(Ca)),Ca.exports}var Ea={exports:{}},Fn;function cl(){return Fn||(Fn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){/** @preserve
			(c) 2012 by Cédric Mesnil. All rights reserved.

			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
			*/return function(r){var n=a,i=n.lib,s=i.WordArray,o=i.Hasher,c=n.algo,O=s.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),d=s.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),g=s.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=s.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=s.create([0,1518500249,1859775393,2400959708,2840853838]),f=s.create([1352829926,1548603684,1836072691,2053994217,0]),h=c.RIPEMD160=o.extend({_doReset:function(){this._hash=s.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(P,b){for(var k=0;k<16;k++){var D=b+k,w=P[D];P[D]=(w<<8|w>>>24)&16711935|(w<<24|w>>>8)&4278255360}var C=this._hash.words,Z=u.words,R=f.words,U=O.words,M=d.words,G=g.words,ne=l.words,Y,F,j,T,X,q,B,oe,J,Qe;q=Y=C[0],B=F=C[1],oe=j=C[2],J=T=C[3],Qe=X=C[4];for(var H,k=0;k<80;k+=1)H=Y+P[b+U[k]]|0,k<16?H+=v(F,j,T)+Z[0]:k<32?H+=Q(F,j,T)+Z[1]:k<48?H+=x(F,j,T)+Z[2]:k<64?H+=m(F,j,T)+Z[3]:H+=p(F,j,T)+Z[4],H=H|0,H=S(H,G[k]),H=H+X|0,Y=X,X=T,T=S(j,10),j=F,F=H,H=q+P[b+M[k]]|0,k<16?H+=p(B,oe,J)+R[0]:k<32?H+=m(B,oe,J)+R[1]:k<48?H+=x(B,oe,J)+R[2]:k<64?H+=Q(B,oe,J)+R[3]:H+=v(B,oe,J)+R[4],H=H|0,H=S(H,ne[k]),H=H+Qe|0,q=Qe,Qe=J,J=S(oe,10),oe=B,B=H;H=C[1]+j+J|0,C[1]=C[2]+T+Qe|0,C[2]=C[3]+X+q|0,C[3]=C[4]+Y+B|0,C[4]=C[0]+F+oe|0,C[0]=H},_doFinalize:function(){var P=this._data,b=P.words,k=this._nDataBytes*8,D=P.sigBytes*8;b[D>>>5]|=128<<24-D%32,b[(D+64>>>9<<4)+14]=(k<<8|k>>>24)&16711935|(k<<24|k>>>8)&4278255360,P.sigBytes=(b.length+1)*4,this._process();for(var w=this._hash,C=w.words,Z=0;Z<5;Z++){var R=C[Z];C[Z]=(R<<8|R>>>24)&16711935|(R<<24|R>>>8)&4278255360}return w},clone:function(){var P=o.clone.call(this);return P._hash=this._hash.clone(),P}});function v(P,b,k){return P^b^k}function Q(P,b,k){return P&b|~P&k}function x(P,b,k){return(P|~b)^k}function m(P,b,k){return P&k|b&~k}function p(P,b,k){return P^(b|~k)}function S(P,b){return P<<b|P>>>32-b}n.RIPEMD160=o._createHelper(h),n.HmacRIPEMD160=o._createHmacHelper(h)}(),a.RIPEMD160})}(Ea)),Ea.exports}var Aa={exports:{}},Yn;function Ur(){return Yn||(Yn=1,function(e,t){(function(a,r){e.exports=r(ie())})(K,function(a){(function(){var r=a,n=r.lib,i=n.Base,s=r.enc,o=s.Utf8,c=r.algo;c.HMAC=i.extend({init:function(O,d){O=this._hasher=new O.init,typeof d=="string"&&(d=o.parse(d));var g=O.blockSize,l=g*4;d.sigBytes>l&&(d=O.finalize(d)),d.clamp();for(var u=this._oKey=d.clone(),f=this._iKey=d.clone(),h=u.words,v=f.words,Q=0;Q<g;Q++)h[Q]^=1549556828,v[Q]^=909522486;u.sigBytes=f.sigBytes=l,this.reset()},reset:function(){var O=this._hasher;O.reset(),O.update(this._iKey)},update:function(O){return this._hasher.update(O),this},finalize:function(O){var d=this._hasher,g=d.finalize(O);d.reset();var l=d.finalize(this._oKey.clone().concat(g));return l}})})()})}(Aa)),Aa.exports}var Ta={exports:{}},In;function ul(){return In||(In=1,function(e,t){(function(a,r,n){e.exports=r(ie(),Vr(),Ur())})(K,function(a){return function(){var r=a,n=r.lib,i=n.Base,s=n.WordArray,o=r.algo,c=o.SHA256,O=o.HMAC,d=o.PBKDF2=i.extend({cfg:i.extend({keySize:128/32,hasher:c,iterations:25e4}),init:function(g){this.cfg=this.cfg.extend(g)},compute:function(g,l){for(var u=this.cfg,f=O.create(u.hasher,g),h=s.create(),v=s.create([1]),Q=h.words,x=v.words,m=u.keySize,p=u.iterations;Q.length<m;){var S=f.update(l).finalize(v);f.reset();for(var P=S.words,b=P.length,k=S,D=1;D<p;D++){k=f.finalize(k),f.reset();for(var w=k.words,C=0;C<b;C++)P[C]^=w[C]}h.concat(S),x[0]++}return h.sigBytes=m*4,h}});r.PBKDF2=function(g,l,u){return d.create(u).compute(g,l)}}(),a.PBKDF2})}(Ta)),Ta.exports}var Za={exports:{}},Vn;function ct(){return Vn||(Vn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),ks(),Ur())})(K,function(a){return function(){var r=a,n=r.lib,i=n.Base,s=n.WordArray,o=r.algo,c=o.MD5,O=o.EvpKDF=i.extend({cfg:i.extend({keySize:128/32,hasher:c,iterations:1}),init:function(d){this.cfg=this.cfg.extend(d)},compute:function(d,g){for(var l,u=this.cfg,f=u.hasher.create(),h=s.create(),v=h.words,Q=u.keySize,x=u.iterations;v.length<Q;){l&&f.update(l),l=f.update(d).finalize(g),f.reset();for(var m=1;m<x;m++)l=f.finalize(l),f.reset();h.concat(l)}return h.sigBytes=Q*4,h}});r.EvpKDF=function(d,g,l){return O.create(l).compute(d,g)}}(),a.EvpKDF})}(Za)),Za.exports}var Xa={exports:{}},Un;function _e(){return Un||(Un=1,function(e,t){(function(a,r,n){e.exports=r(ie(),ct())})(K,function(a){a.lib.Cipher||function(r){var n=a,i=n.lib,s=i.Base,o=i.WordArray,c=i.BufferedBlockAlgorithm,O=n.enc;O.Utf8;var d=O.Base64,g=n.algo,l=g.EvpKDF,u=i.Cipher=c.extend({cfg:s.extend(),createEncryptor:function(w,C){return this.create(this._ENC_XFORM_MODE,w,C)},createDecryptor:function(w,C){return this.create(this._DEC_XFORM_MODE,w,C)},init:function(w,C,Z){this.cfg=this.cfg.extend(Z),this._xformMode=w,this._key=C,this.reset()},reset:function(){c.reset.call(this),this._doReset()},process:function(w){return this._append(w),this._process()},finalize:function(w){w&&this._append(w);var C=this._doFinalize();return C},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function w(C){return typeof C=="string"?D:P}return function(C){return{encrypt:function(Z,R,U){return w(R).encrypt(C,Z,R,U)},decrypt:function(Z,R,U){return w(R).decrypt(C,Z,R,U)}}}}()});i.StreamCipher=u.extend({_doFinalize:function(){var w=this._process(!0);return w},blockSize:1});var f=n.mode={},h=i.BlockCipherMode=s.extend({createEncryptor:function(w,C){return this.Encryptor.create(w,C)},createDecryptor:function(w,C){return this.Decryptor.create(w,C)},init:function(w,C){this._cipher=w,this._iv=C}}),v=f.CBC=function(){var w=h.extend();w.Encryptor=w.extend({processBlock:function(Z,R){var U=this._cipher,M=U.blockSize;C.call(this,Z,R,M),U.encryptBlock(Z,R),this._prevBlock=Z.slice(R,R+M)}}),w.Decryptor=w.extend({processBlock:function(Z,R){var U=this._cipher,M=U.blockSize,G=Z.slice(R,R+M);U.decryptBlock(Z,R),C.call(this,Z,R,M),this._prevBlock=G}});function C(Z,R,U){var M,G=this._iv;G?(M=G,this._iv=r):M=this._prevBlock;for(var ne=0;ne<U;ne++)Z[R+ne]^=M[ne]}return w}(),Q=n.pad={},x=Q.Pkcs7={pad:function(w,C){for(var Z=C*4,R=Z-w.sigBytes%Z,U=R<<24|R<<16|R<<8|R,M=[],G=0;G<R;G+=4)M.push(U);var ne=o.create(M,R);w.concat(ne)},unpad:function(w){var C=w.words[w.sigBytes-1>>>2]&255;w.sigBytes-=C}};i.BlockCipher=u.extend({cfg:u.cfg.extend({mode:v,padding:x}),reset:function(){var w;u.reset.call(this);var C=this.cfg,Z=C.iv,R=C.mode;this._xformMode==this._ENC_XFORM_MODE?w=R.createEncryptor:(w=R.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==w?this._mode.init(this,Z&&Z.words):(this._mode=w.call(R,this,Z&&Z.words),this._mode.__creator=w)},_doProcessBlock:function(w,C){this._mode.processBlock(w,C)},_doFinalize:function(){var w,C=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(C.pad(this._data,this.blockSize),w=this._process(!0)):(w=this._process(!0),C.unpad(w)),w},blockSize:128/32});var m=i.CipherParams=s.extend({init:function(w){this.mixIn(w)},toString:function(w){return(w||this.formatter).stringify(this)}}),p=n.format={},S=p.OpenSSL={stringify:function(w){var C,Z=w.ciphertext,R=w.salt;return R?C=o.create([1398893684,1701076831]).concat(R).concat(Z):C=Z,C.toString(d)},parse:function(w){var C,Z=d.parse(w),R=Z.words;return R[0]==1398893684&&R[1]==1701076831&&(C=o.create(R.slice(2,4)),R.splice(0,4),Z.sigBytes-=16),m.create({ciphertext:Z,salt:C})}},P=i.SerializableCipher=s.extend({cfg:s.extend({format:S}),encrypt:function(w,C,Z,R){R=this.cfg.extend(R);var U=w.createEncryptor(Z,R),M=U.finalize(C),G=U.cfg;return m.create({ciphertext:M,key:Z,iv:G.iv,algorithm:w,mode:G.mode,padding:G.padding,blockSize:w.blockSize,formatter:R.format})},decrypt:function(w,C,Z,R){R=this.cfg.extend(R),C=this._parse(C,R.format);var U=w.createDecryptor(Z,R).finalize(C.ciphertext);return U},_parse:function(w,C){return typeof w=="string"?C.parse(w,this):w}}),b=n.kdf={},k=b.OpenSSL={execute:function(w,C,Z,R,U){if(R||(R=o.random(64/8)),U)var M=l.create({keySize:C+Z,hasher:U}).compute(w,R);else var M=l.create({keySize:C+Z}).compute(w,R);var G=o.create(M.words.slice(C),Z*4);return M.sigBytes=C*4,m.create({key:M,iv:G,salt:R})}},D=i.PasswordBasedCipher=P.extend({cfg:P.cfg.extend({kdf:k}),encrypt:function(w,C,Z,R){R=this.cfg.extend(R);var U=R.kdf.execute(Z,w.keySize,w.ivSize,R.salt,R.hasher);R.iv=U.iv;var M=P.encrypt.call(this,w,C,U.key,R);return M.mixIn(U),M},decrypt:function(w,C,Z,R){R=this.cfg.extend(R),C=this._parse(C,R.format);var U=R.kdf.execute(Z,w.keySize,w.ivSize,C.salt,R.hasher);R.iv=U.iv;var M=P.decrypt.call(this,w,C,U.key,R);return M}})}()})}(Xa)),Xa.exports}var Ra={exports:{}},Mn;function fl(){return Mn||(Mn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.mode.CFB=function(){var r=a.lib.BlockCipherMode.extend();r.Encryptor=r.extend({processBlock:function(i,s){var o=this._cipher,c=o.blockSize;n.call(this,i,s,c,o),this._prevBlock=i.slice(s,s+c)}}),r.Decryptor=r.extend({processBlock:function(i,s){var o=this._cipher,c=o.blockSize,O=i.slice(s,s+c);n.call(this,i,s,c,o),this._prevBlock=O}});function n(i,s,o,c){var O,d=this._iv;d?(O=d.slice(0),this._iv=void 0):O=this._prevBlock,c.encryptBlock(O,0);for(var g=0;g<o;g++)i[s+g]^=O[g]}return r}(),a.mode.CFB})}(Ra)),Ra.exports}var Da={exports:{}},Wn;function dl(){return Wn||(Wn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.mode.CTR=function(){var r=a.lib.BlockCipherMode.extend(),n=r.Encryptor=r.extend({processBlock:function(i,s){var o=this._cipher,c=o.blockSize,O=this._iv,d=this._counter;O&&(d=this._counter=O.slice(0),this._iv=void 0);var g=d.slice(0);o.encryptBlock(g,0),d[c-1]=d[c-1]+1|0;for(var l=0;l<c;l++)i[s+l]^=g[l]}});return r.Decryptor=n,r}(),a.mode.CTR})}(Da)),Da.exports}var Ba={exports:{}},zn;function hl(){return zn||(zn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){/** @preserve
 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
 * derived from CryptoJS.mode.CTR
 * <NAME_EMAIL>
 */return a.mode.CTRGladman=function(){var r=a.lib.BlockCipherMode.extend();function n(o){if((o>>24&255)===255){var c=o>>16&255,O=o>>8&255,d=o&255;c===255?(c=0,O===255?(O=0,d===255?d=0:++d):++O):++c,o=0,o+=c<<16,o+=O<<8,o+=d}else o+=1<<24;return o}function i(o){return(o[0]=n(o[0]))===0&&(o[1]=n(o[1])),o}var s=r.Encryptor=r.extend({processBlock:function(o,c){var O=this._cipher,d=O.blockSize,g=this._iv,l=this._counter;g&&(l=this._counter=g.slice(0),this._iv=void 0),i(l);var u=l.slice(0);O.encryptBlock(u,0);for(var f=0;f<d;f++)o[c+f]^=u[f]}});return r.Decryptor=s,r}(),a.mode.CTRGladman})}(Ba)),Ba.exports}var qa={exports:{}},Nn;function pl(){return Nn||(Nn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.mode.OFB=function(){var r=a.lib.BlockCipherMode.extend(),n=r.Encryptor=r.extend({processBlock:function(i,s){var o=this._cipher,c=o.blockSize,O=this._iv,d=this._keystream;O&&(d=this._keystream=O.slice(0),this._iv=void 0),o.encryptBlock(d,0);for(var g=0;g<c;g++)i[s+g]^=d[g]}});return r.Decryptor=n,r}(),a.mode.OFB})}(qa)),qa.exports}var La={exports:{}},Gn;function xl(){return Gn||(Gn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.mode.ECB=function(){var r=a.lib.BlockCipherMode.extend();return r.Encryptor=r.extend({processBlock:function(n,i){this._cipher.encryptBlock(n,i)}}),r.Decryptor=r.extend({processBlock:function(n,i){this._cipher.decryptBlock(n,i)}}),r}(),a.mode.ECB})}(La)),La.exports}var Fa={exports:{}},jn;function ml(){return jn||(jn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.pad.AnsiX923={pad:function(r,n){var i=r.sigBytes,s=n*4,o=s-i%s,c=i+o-1;r.clamp(),r.words[c>>>2]|=o<<24-c%4*8,r.sigBytes+=o},unpad:function(r){var n=r.words[r.sigBytes-1>>>2]&255;r.sigBytes-=n}},a.pad.Ansix923})}(Fa)),Fa.exports}var Ya={exports:{}},Hn;function gl(){return Hn||(Hn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.pad.Iso10126={pad:function(r,n){var i=n*4,s=i-r.sigBytes%i;r.concat(a.lib.WordArray.random(s-1)).concat(a.lib.WordArray.create([s<<24],1))},unpad:function(r){var n=r.words[r.sigBytes-1>>>2]&255;r.sigBytes-=n}},a.pad.Iso10126})}(Ya)),Ya.exports}var Ia={exports:{}},Kn;function vl(){return Kn||(Kn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.pad.Iso97971={pad:function(r,n){r.concat(a.lib.WordArray.create([2147483648],1)),a.pad.ZeroPadding.pad(r,n)},unpad:function(r){a.pad.ZeroPadding.unpad(r),r.sigBytes--}},a.pad.Iso97971})}(Ia)),Ia.exports}var Va={exports:{}},Jn;function Ql(){return Jn||(Jn=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.pad.ZeroPadding={pad:function(r,n){var i=n*4;r.clamp(),r.sigBytes+=i-(r.sigBytes%i||i)},unpad:function(r){for(var n=r.words,i=r.sigBytes-1,i=r.sigBytes-1;i>=0;i--)if(n[i>>>2]>>>24-i%4*8&255){r.sigBytes=i+1;break}}},a.pad.ZeroPadding})}(Va)),Va.exports}var Ua={exports:{}},ei;function Sl(){return ei||(ei=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return a.pad.NoPadding={pad:function(){},unpad:function(){}},a.pad.NoPadding})}(Ua)),Ua.exports}var Ma={exports:{}},ti;function bl(){return ti||(ti=1,function(e,t){(function(a,r,n){e.exports=r(ie(),_e())})(K,function(a){return function(r){var n=a,i=n.lib,s=i.CipherParams,o=n.enc,c=o.Hex,O=n.format;O.Hex={stringify:function(d){return d.ciphertext.toString(c)},parse:function(d){var g=c.parse(d);return s.create({ciphertext:g})}}}(),a.format.Hex})}(Ma)),Ma.exports}var Wa={exports:{}},ai;function Pl(){return ai||(ai=1,function(e,t){(function(a,r,n){e.exports=r(ie(),xt(),mt(),ct(),_e())})(K,function(a){return function(){var r=a,n=r.lib,i=n.BlockCipher,s=r.algo,o=[],c=[],O=[],d=[],g=[],l=[],u=[],f=[],h=[],v=[];(function(){for(var m=[],p=0;p<256;p++)p<128?m[p]=p<<1:m[p]=p<<1^283;for(var S=0,P=0,p=0;p<256;p++){var b=P^P<<1^P<<2^P<<3^P<<4;b=b>>>8^b&255^99,o[S]=b,c[b]=S;var k=m[S],D=m[k],w=m[D],C=m[b]*257^b*16843008;O[S]=C<<24|C>>>8,d[S]=C<<16|C>>>16,g[S]=C<<8|C>>>24,l[S]=C;var C=w*16843009^D*65537^k*257^S*16843008;u[b]=C<<24|C>>>8,f[b]=C<<16|C>>>16,h[b]=C<<8|C>>>24,v[b]=C,S?(S=k^m[m[m[w^k]]],P^=m[m[P]]):S=P=1}})();var Q=[0,1,2,4,8,16,32,64,128,27,54],x=s.AES=i.extend({_doReset:function(){var m;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var p=this._keyPriorReset=this._key,S=p.words,P=p.sigBytes/4,b=this._nRounds=P+6,k=(b+1)*4,D=this._keySchedule=[],w=0;w<k;w++)w<P?D[w]=S[w]:(m=D[w-1],w%P?P>6&&w%P==4&&(m=o[m>>>24]<<24|o[m>>>16&255]<<16|o[m>>>8&255]<<8|o[m&255]):(m=m<<8|m>>>24,m=o[m>>>24]<<24|o[m>>>16&255]<<16|o[m>>>8&255]<<8|o[m&255],m^=Q[w/P|0]<<24),D[w]=D[w-P]^m);for(var C=this._invKeySchedule=[],Z=0;Z<k;Z++){var w=k-Z;if(Z%4)var m=D[w];else var m=D[w-4];Z<4||w<=4?C[Z]=m:C[Z]=u[o[m>>>24]]^f[o[m>>>16&255]]^h[o[m>>>8&255]]^v[o[m&255]]}}},encryptBlock:function(m,p){this._doCryptBlock(m,p,this._keySchedule,O,d,g,l,o)},decryptBlock:function(m,p){var S=m[p+1];m[p+1]=m[p+3],m[p+3]=S,this._doCryptBlock(m,p,this._invKeySchedule,u,f,h,v,c);var S=m[p+1];m[p+1]=m[p+3],m[p+3]=S},_doCryptBlock:function(m,p,S,P,b,k,D,w){for(var C=this._nRounds,Z=m[p]^S[0],R=m[p+1]^S[1],U=m[p+2]^S[2],M=m[p+3]^S[3],G=4,ne=1;ne<C;ne++){var Y=P[Z>>>24]^b[R>>>16&255]^k[U>>>8&255]^D[M&255]^S[G++],F=P[R>>>24]^b[U>>>16&255]^k[M>>>8&255]^D[Z&255]^S[G++],j=P[U>>>24]^b[M>>>16&255]^k[Z>>>8&255]^D[R&255]^S[G++],T=P[M>>>24]^b[Z>>>16&255]^k[R>>>8&255]^D[U&255]^S[G++];Z=Y,R=F,U=j,M=T}var Y=(w[Z>>>24]<<24|w[R>>>16&255]<<16|w[U>>>8&255]<<8|w[M&255])^S[G++],F=(w[R>>>24]<<24|w[U>>>16&255]<<16|w[M>>>8&255]<<8|w[Z&255])^S[G++],j=(w[U>>>24]<<24|w[M>>>16&255]<<16|w[Z>>>8&255]<<8|w[R&255])^S[G++],T=(w[M>>>24]<<24|w[Z>>>16&255]<<16|w[R>>>8&255]<<8|w[U&255])^S[G++];m[p]=Y,m[p+1]=F,m[p+2]=j,m[p+3]=T},keySize:256/32});r.AES=i._createHelper(x)}(),a.AES})}(Wa)),Wa.exports}var za={exports:{}},ri;function yl(){return ri||(ri=1,function(e,t){(function(a,r,n){e.exports=r(ie(),xt(),mt(),ct(),_e())})(K,function(a){return function(){var r=a,n=r.lib,i=n.WordArray,s=n.BlockCipher,o=r.algo,c=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],O=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],d=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],g=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=o.DES=s.extend({_doReset:function(){for(var Q=this._key,x=Q.words,m=[],p=0;p<56;p++){var S=c[p]-1;m[p]=x[S>>>5]>>>31-S%32&1}for(var P=this._subKeys=[],b=0;b<16;b++){for(var k=P[b]=[],D=d[b],p=0;p<24;p++)k[p/6|0]|=m[(O[p]-1+D)%28]<<31-p%6,k[4+(p/6|0)]|=m[28+(O[p+24]-1+D)%28]<<31-p%6;k[0]=k[0]<<1|k[0]>>>31;for(var p=1;p<7;p++)k[p]=k[p]>>>(p-1)*4+3;k[7]=k[7]<<5|k[7]>>>27}for(var w=this._invSubKeys=[],p=0;p<16;p++)w[p]=P[15-p]},encryptBlock:function(Q,x){this._doCryptBlock(Q,x,this._subKeys)},decryptBlock:function(Q,x){this._doCryptBlock(Q,x,this._invSubKeys)},_doCryptBlock:function(Q,x,m){this._lBlock=Q[x],this._rBlock=Q[x+1],f.call(this,4,252645135),f.call(this,16,65535),h.call(this,2,858993459),h.call(this,8,16711935),f.call(this,1,1431655765);for(var p=0;p<16;p++){for(var S=m[p],P=this._lBlock,b=this._rBlock,k=0,D=0;D<8;D++)k|=g[D][((b^S[D])&l[D])>>>0];this._lBlock=b,this._rBlock=P^k}var w=this._lBlock;this._lBlock=this._rBlock,this._rBlock=w,f.call(this,1,1431655765),h.call(this,8,16711935),h.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),Q[x]=this._lBlock,Q[x+1]=this._rBlock},keySize:64/32,ivSize:64/32,blockSize:64/32});function f(Q,x){var m=(this._lBlock>>>Q^this._rBlock)&x;this._rBlock^=m,this._lBlock^=m<<Q}function h(Q,x){var m=(this._rBlock>>>Q^this._lBlock)&x;this._lBlock^=m,this._rBlock^=m<<Q}r.DES=s._createHelper(u);var v=o.TripleDES=s.extend({_doReset:function(){var Q=this._key,x=Q.words;if(x.length!==2&&x.length!==4&&x.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var m=x.slice(0,2),p=x.length<4?x.slice(0,2):x.slice(2,4),S=x.length<6?x.slice(0,2):x.slice(4,6);this._des1=u.createEncryptor(i.create(m)),this._des2=u.createEncryptor(i.create(p)),this._des3=u.createEncryptor(i.create(S))},encryptBlock:function(Q,x){this._des1.encryptBlock(Q,x),this._des2.decryptBlock(Q,x),this._des3.encryptBlock(Q,x)},decryptBlock:function(Q,x){this._des3.decryptBlock(Q,x),this._des2.encryptBlock(Q,x),this._des1.decryptBlock(Q,x)},keySize:192/32,ivSize:64/32,blockSize:64/32});r.TripleDES=s._createHelper(v)}(),a.TripleDES})}(za)),za.exports}var Na={exports:{}},ni;function $l(){return ni||(ni=1,function(e,t){(function(a,r,n){e.exports=r(ie(),xt(),mt(),ct(),_e())})(K,function(a){return function(){var r=a,n=r.lib,i=n.StreamCipher,s=r.algo,o=s.RC4=i.extend({_doReset:function(){for(var d=this._key,g=d.words,l=d.sigBytes,u=this._S=[],f=0;f<256;f++)u[f]=f;for(var f=0,h=0;f<256;f++){var v=f%l,Q=g[v>>>2]>>>24-v%4*8&255;h=(h+u[f]+Q)%256;var x=u[f];u[f]=u[h],u[h]=x}this._i=this._j=0},_doProcessBlock:function(d,g){d[g]^=c.call(this)},keySize:256/32,ivSize:0});function c(){for(var d=this._S,g=this._i,l=this._j,u=0,f=0;f<4;f++){g=(g+1)%256,l=(l+d[g])%256;var h=d[g];d[g]=d[l],d[l]=h,u|=d[(d[g]+d[l])%256]<<24-f*8}return this._i=g,this._j=l,u}r.RC4=i._createHelper(o);var O=s.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var d=this.cfg.drop;d>0;d--)c.call(this)}});r.RC4Drop=i._createHelper(O)}(),a.RC4})}(Na)),Na.exports}var Ga={exports:{}},ii;function wl(){return ii||(ii=1,function(e,t){(function(a,r,n){e.exports=r(ie(),xt(),mt(),ct(),_e())})(K,function(a){return function(){var r=a,n=r.lib,i=n.StreamCipher,s=r.algo,o=[],c=[],O=[],d=s.Rabbit=i.extend({_doReset:function(){for(var l=this._key.words,u=this.cfg.iv,f=0;f<4;f++)l[f]=(l[f]<<8|l[f]>>>24)&16711935|(l[f]<<24|l[f]>>>8)&4278255360;var h=this._X=[l[0],l[3]<<16|l[2]>>>16,l[1],l[0]<<16|l[3]>>>16,l[2],l[1]<<16|l[0]>>>16,l[3],l[2]<<16|l[1]>>>16],v=this._C=[l[2]<<16|l[2]>>>16,l[0]&4294901760|l[1]&65535,l[3]<<16|l[3]>>>16,l[1]&4294901760|l[2]&65535,l[0]<<16|l[0]>>>16,l[2]&4294901760|l[3]&65535,l[1]<<16|l[1]>>>16,l[3]&4294901760|l[0]&65535];this._b=0;for(var f=0;f<4;f++)g.call(this);for(var f=0;f<8;f++)v[f]^=h[f+4&7];if(u){var Q=u.words,x=Q[0],m=Q[1],p=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,S=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360,P=p>>>16|S&4294901760,b=S<<16|p&65535;v[0]^=p,v[1]^=P,v[2]^=S,v[3]^=b,v[4]^=p,v[5]^=P,v[6]^=S,v[7]^=b;for(var f=0;f<4;f++)g.call(this)}},_doProcessBlock:function(l,u){var f=this._X;g.call(this),o[0]=f[0]^f[5]>>>16^f[3]<<16,o[1]=f[2]^f[7]>>>16^f[5]<<16,o[2]=f[4]^f[1]>>>16^f[7]<<16,o[3]=f[6]^f[3]>>>16^f[1]<<16;for(var h=0;h<4;h++)o[h]=(o[h]<<8|o[h]>>>24)&16711935|(o[h]<<24|o[h]>>>8)&4278255360,l[u+h]^=o[h]},blockSize:128/32,ivSize:64/32});function g(){for(var l=this._X,u=this._C,f=0;f<8;f++)c[f]=u[f];u[0]=u[0]+1295307597+this._b|0,u[1]=u[1]+3545052371+(u[0]>>>0<c[0]>>>0?1:0)|0,u[2]=u[2]+886263092+(u[1]>>>0<c[1]>>>0?1:0)|0,u[3]=u[3]+1295307597+(u[2]>>>0<c[2]>>>0?1:0)|0,u[4]=u[4]+3545052371+(u[3]>>>0<c[3]>>>0?1:0)|0,u[5]=u[5]+886263092+(u[4]>>>0<c[4]>>>0?1:0)|0,u[6]=u[6]+1295307597+(u[5]>>>0<c[5]>>>0?1:0)|0,u[7]=u[7]+3545052371+(u[6]>>>0<c[6]>>>0?1:0)|0,this._b=u[7]>>>0<c[7]>>>0?1:0;for(var f=0;f<8;f++){var h=l[f]+u[f],v=h&65535,Q=h>>>16,x=((v*v>>>17)+v*Q>>>15)+Q*Q,m=((h&4294901760)*h|0)+((h&65535)*h|0);O[f]=x^m}l[0]=O[0]+(O[7]<<16|O[7]>>>16)+(O[6]<<16|O[6]>>>16)|0,l[1]=O[1]+(O[0]<<8|O[0]>>>24)+O[7]|0,l[2]=O[2]+(O[1]<<16|O[1]>>>16)+(O[0]<<16|O[0]>>>16)|0,l[3]=O[3]+(O[2]<<8|O[2]>>>24)+O[1]|0,l[4]=O[4]+(O[3]<<16|O[3]>>>16)+(O[2]<<16|O[2]>>>16)|0,l[5]=O[5]+(O[4]<<8|O[4]>>>24)+O[3]|0,l[6]=O[6]+(O[5]<<16|O[5]>>>16)+(O[4]<<16|O[4]>>>16)|0,l[7]=O[7]+(O[6]<<8|O[6]>>>24)+O[5]|0}r.Rabbit=i._createHelper(d)}(),a.Rabbit})}(Ga)),Ga.exports}var ja={exports:{}},si;function _l(){return si||(si=1,function(e,t){(function(a,r,n){e.exports=r(ie(),xt(),mt(),ct(),_e())})(K,function(a){return function(){var r=a,n=r.lib,i=n.StreamCipher,s=r.algo,o=[],c=[],O=[],d=s.RabbitLegacy=i.extend({_doReset:function(){var l=this._key.words,u=this.cfg.iv,f=this._X=[l[0],l[3]<<16|l[2]>>>16,l[1],l[0]<<16|l[3]>>>16,l[2],l[1]<<16|l[0]>>>16,l[3],l[2]<<16|l[1]>>>16],h=this._C=[l[2]<<16|l[2]>>>16,l[0]&4294901760|l[1]&65535,l[3]<<16|l[3]>>>16,l[1]&4294901760|l[2]&65535,l[0]<<16|l[0]>>>16,l[2]&4294901760|l[3]&65535,l[1]<<16|l[1]>>>16,l[3]&4294901760|l[0]&65535];this._b=0;for(var v=0;v<4;v++)g.call(this);for(var v=0;v<8;v++)h[v]^=f[v+4&7];if(u){var Q=u.words,x=Q[0],m=Q[1],p=(x<<8|x>>>24)&16711935|(x<<24|x>>>8)&4278255360,S=(m<<8|m>>>24)&16711935|(m<<24|m>>>8)&4278255360,P=p>>>16|S&4294901760,b=S<<16|p&65535;h[0]^=p,h[1]^=P,h[2]^=S,h[3]^=b,h[4]^=p,h[5]^=P,h[6]^=S,h[7]^=b;for(var v=0;v<4;v++)g.call(this)}},_doProcessBlock:function(l,u){var f=this._X;g.call(this),o[0]=f[0]^f[5]>>>16^f[3]<<16,o[1]=f[2]^f[7]>>>16^f[5]<<16,o[2]=f[4]^f[1]>>>16^f[7]<<16,o[3]=f[6]^f[3]>>>16^f[1]<<16;for(var h=0;h<4;h++)o[h]=(o[h]<<8|o[h]>>>24)&16711935|(o[h]<<24|o[h]>>>8)&4278255360,l[u+h]^=o[h]},blockSize:128/32,ivSize:64/32});function g(){for(var l=this._X,u=this._C,f=0;f<8;f++)c[f]=u[f];u[0]=u[0]+1295307597+this._b|0,u[1]=u[1]+3545052371+(u[0]>>>0<c[0]>>>0?1:0)|0,u[2]=u[2]+886263092+(u[1]>>>0<c[1]>>>0?1:0)|0,u[3]=u[3]+1295307597+(u[2]>>>0<c[2]>>>0?1:0)|0,u[4]=u[4]+3545052371+(u[3]>>>0<c[3]>>>0?1:0)|0,u[5]=u[5]+886263092+(u[4]>>>0<c[4]>>>0?1:0)|0,u[6]=u[6]+1295307597+(u[5]>>>0<c[5]>>>0?1:0)|0,u[7]=u[7]+3545052371+(u[6]>>>0<c[6]>>>0?1:0)|0,this._b=u[7]>>>0<c[7]>>>0?1:0;for(var f=0;f<8;f++){var h=l[f]+u[f],v=h&65535,Q=h>>>16,x=((v*v>>>17)+v*Q>>>15)+Q*Q,m=((h&4294901760)*h|0)+((h&65535)*h|0);O[f]=x^m}l[0]=O[0]+(O[7]<<16|O[7]>>>16)+(O[6]<<16|O[6]>>>16)|0,l[1]=O[1]+(O[0]<<8|O[0]>>>24)+O[7]|0,l[2]=O[2]+(O[1]<<16|O[1]>>>16)+(O[0]<<16|O[0]>>>16)|0,l[3]=O[3]+(O[2]<<8|O[2]>>>24)+O[1]|0,l[4]=O[4]+(O[3]<<16|O[3]>>>16)+(O[2]<<16|O[2]>>>16)|0,l[5]=O[5]+(O[4]<<8|O[4]>>>24)+O[3]|0,l[6]=O[6]+(O[5]<<16|O[5]>>>16)+(O[4]<<16|O[4]>>>16)|0,l[7]=O[7]+(O[6]<<8|O[6]>>>24)+O[5]|0}r.RabbitLegacy=i._createHelper(d)}(),a.RabbitLegacy})}(ja)),ja.exports}var Ha={exports:{}},oi;function kl(){return oi||(oi=1,function(e,t){(function(a,r,n){e.exports=r(ie(),xt(),mt(),ct(),_e())})(K,function(a){return function(){var r=a,n=r.lib,i=n.BlockCipher,s=r.algo;const o=16,c=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],O=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var d={pbox:[],sbox:[]};function g(v,Q){let x=Q>>24&255,m=Q>>16&255,p=Q>>8&255,S=Q&255,P=v.sbox[0][x]+v.sbox[1][m];return P=P^v.sbox[2][p],P=P+v.sbox[3][S],P}function l(v,Q,x){let m=Q,p=x,S;for(let P=0;P<o;++P)m=m^v.pbox[P],p=g(v,m)^p,S=m,m=p,p=S;return S=m,m=p,p=S,p=p^v.pbox[o],m=m^v.pbox[o+1],{left:m,right:p}}function u(v,Q,x){let m=Q,p=x,S;for(let P=o+1;P>1;--P)m=m^v.pbox[P],p=g(v,m)^p,S=m,m=p,p=S;return S=m,m=p,p=S,p=p^v.pbox[1],m=m^v.pbox[0],{left:m,right:p}}function f(v,Q,x){for(let b=0;b<4;b++){v.sbox[b]=[];for(let k=0;k<256;k++)v.sbox[b][k]=O[b][k]}let m=0;for(let b=0;b<o+2;b++)v.pbox[b]=c[b]^Q[m],m++,m>=x&&(m=0);let p=0,S=0,P=0;for(let b=0;b<o+2;b+=2)P=l(v,p,S),p=P.left,S=P.right,v.pbox[b]=p,v.pbox[b+1]=S;for(let b=0;b<4;b++)for(let k=0;k<256;k+=2)P=l(v,p,S),p=P.left,S=P.right,v.sbox[b][k]=p,v.sbox[b][k+1]=S;return!0}var h=s.Blowfish=i.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var v=this._keyPriorReset=this._key,Q=v.words,x=v.sigBytes/4;f(d,Q,x)}},encryptBlock:function(v,Q){var x=l(d,v[Q],v[Q+1]);v[Q]=x.left,v[Q+1]=x.right},decryptBlock:function(v,Q){var x=u(d,v[Q],v[Q+1]);v[Q]=x.left,v[Q+1]=x.right},blockSize:64/32,keySize:128/32,ivSize:64/32});r.Blowfish=i._createHelper(h)}(),a.Blowfish})}(Ha)),Ha.exports}(function(e,t){(function(a,r,n){e.exports=r(ie(),fa(),nl(),il(),xt(),sl(),mt(),ks(),Vr(),ol(),Cs(),ll(),Ol(),cl(),Ur(),ul(),ct(),_e(),fl(),dl(),hl(),pl(),xl(),ml(),gl(),vl(),Ql(),Sl(),bl(),Pl(),yl(),$l(),wl(),_l(),kl())})(K,function(a){return a})})(_s);var Cl=_s.exports;const xr=w0(Cl),Es="__CRYPTO_SECRET__";function El(e){const t=JSON.stringify(e);return xr.AES.encrypt(t,Es).toString()}function Al(e){const a=xr.AES.decrypt(e,Es).toString(xr.enc.Utf8);return a?JSON.parse(a):null}function As(e){const{expire:a,crypto:r}=Object.assign({expire:604800,crypto:!0},e);function n(c,O){const d={data:O,expire:a!==null?new Date().getTime()+a*1e3:null},g=r?El(d):JSON.stringify(d);window.localStorage.setItem(c,g)}function i(c){const O=window.localStorage.getItem(c);if(O){let d=null;try{d=r?Al(O):JSON.parse(O)}catch(g){}if(d){const{data:g,expire:l}=d;if(l===null||l>=Date.now())return g}return s(c),null}}function s(c){window.localStorage.removeItem(c)}function o(){window.localStorage.clear()}return{set:n,get:i,remove:s,clear:o}}As();const rt=As({expire:null,crypto:!1}),Tl={ACCOUNT:0,MEMBER:1,NOTICE:2,AGREEMENT:3},Mr=Zr("global-store",{state:()=>({loading:!1,showAppListComponent:!1,settingsDialog:!1,showLoginDialog:!1,showBadWordsDialog:!1,showHtmlPreviewer:!1,showTextEditor:!1,showImagePreviewer:!1,showWorkflowPreviewer:!1,showMarkdownPreviewer:!1,previewImageUrls:[],initialImageIndex:0,pythonDialog:!1,htmlDialog:!1,isChatIn:!1,settingsActiveTab:0,htmlContent:"",contentType:"",textContent:"",pythonContent:"",full_json:"",externalLinkDialog:!1,currentExternalLink:null,mobileSettingsDialog:!1,goodsDialog:!1,fingerprint:0,noticeDialog:!1,bindWxDialog:!1,signInDialog:!1,appDialog:!1,identityDialog:!1,phoneIdentityDialog:!1,userAgreementDialog:!1,BadWordsDialog:!1,isCacheEnabled:!1,orderInfo:{pkgInfo:{id:0,des:"",name:"",price:"",model3Count:0,model4Count:0,drawMjCount:0,coverImg:"",days:0}},model:0,iframeUrl:"",clipboardText:"",mjImageData:{},mobileInitialTab:void 0,workflowContent:[],markdownContent:"",isMarkdownPreviewerVisible:!1}),actions:{updateClipboardText(e){this.clipboardText=e},updateTextContent(e){this.textContent=e},updateFullJson(e){this.full_json=e},updateFingerprint(e){let t=e;t>2147483647&&(t=Number(t.toString().slice(-9)),t=Number(String(Number(t)))),rt.set("fingerprint",t),this.fingerprint=t},updateIframeUrl(e){this.iframeUrl=e},updateUserAgreementDialog(e){this.userAgreementDialog=e},UpdateBadWordsDialog(e){this.BadWordsDialog=e},updateHtmlContent(e,t="html"){this.htmlContent=e,this.contentType=t},updateHtmlPreviewer(e){this.showHtmlPreviewer=e},updateTextEditor(e){this.showTextEditor=e},updateImagePreviewer(e,t=[],a=0,r){this.showImagePreviewer=e,e&&(this.previewImageUrls=t,this.initialImageIndex=a,this.mjImageData=r||{},Xs().setUsingPlugin(null))},updateIsChatIn(e){this.isChatIn=e},updateGoodsDialog(e){this.goodsDialog=e},updateBindwxDialog(e){this.bindWxDialog=e},updateSignInDialog(e){this.signInDialog=e},updateNoticeDialog(e){this.noticeDialog=e},updateAppDialog(e){this.appDialog=e},updateIdentityDialog(e){this.identityDialog=e},updatePhoneDialog(e){this.phoneIdentityDialog=e},updateHtmlDialog(e){this.htmlDialog=e},updateModel(e){rt.set("model",e),this.model=e},updateOrderInfo(e){this.orderInfo=e},updatePythonDialog(e){this.pythonDialog=e},updatePythonContent(e){this.pythonContent=e},updateExternalLinkDialog(e,t=null){this.externalLinkDialog=e,this.currentExternalLink=t},updateSettingsDialog(e,t){this.settingsDialog=e,e&&t!==void 0&&(this.settingsActiveTab=t)},updateMobileSettingsDialog(e,t){if(this.mobileSettingsDialog=e,t!==void 0)if(typeof t=="number"){const a=["account","member","notice","agreement"];this.mobileInitialTab=a[t]||void 0}else this.mobileInitialTab=t;else this.mobileInitialTab=void 0},updateShowAppListComponent(e){this.showAppListComponent=e},setCurrentExternalLink(e){this.currentExternalLink=e,e&&(this.externalLinkDialog=!0)},updateSettingsActiveTab(e){this.settingsActiveTab=e},updateWorkflowPreviewer(e){this.showWorkflowPreviewer=e,e||(this.workflowContent=[])},addWorkflowContent(e){this.workflowContent.push(e)},clearWorkflowContent(){this.workflowContent=[]},updateWorkflowContentAt(e,t){e>=0&&e<this.workflowContent.length&&(this.workflowContent[e]=t)},updateWorkflowContentLast(e){const t=this.workflowContent;if(t.length>0){const a=t.length-1;this.workflowContent[a]+=e}else this.workflowContent.push(e)},updateMarkdownPreviewer(e,t){this.isMarkdownPreviewerVisible=e,t&&(this.markdownContent=t),e||(this.markdownContent="")}}});function Wr(){return Mr(zr)}const zr=d0();function Zl(e){e.use(zr)}function Ts(){const e=_0(k0),t=e.smaller("sm"),a=e.smaller("md"),r=e.smaller("lg"),n=e.smaller("xl");return{isMobile:t,isSmallMd:a,isSmallLg:r,isSmallXl:n}}const Xl={class:"flex items-center gap-2 overflow-hidden"},Rl={class:"text-gray-900 dark:text-gray-100 truncate"},Dl=it({__name:"index",setup(e,{expose:t}){const a=Se([]);let r=0;const{isMobile:n}=Ts();return t({show:s=>{const o=r++,c={id:o,type:s.type||"info",content:s.content};a.value.push(c),setTimeout(()=>{a.value=a.value.filter(O=>O.id!==o)},s.duration||3e3)}}),(s,o)=>(be(),et(Xr,{to:"body"},[Ye(x0,{"enter-active-class":"transition duration-200 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"scale-100 opacity-100","leave-active-class":"transition duration-150 ease-in","leave-from-class":"opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:h0(()=>[(be(!0),Ve(ca,null,p0(a.value,c=>(be(),Ve("div",{key:c.id,class:Zt(["fixed top-8 left-1/2 -translate-x-1/2 z-[999999] flex items-center px-4 py-2 rounded-lg shadow-sm overflow-hidden whitespace-nowrap",{"bg-emerald-50 dark:bg-emerald-500/10":c.type==="success","bg-red-50 dark:bg-red-500/10":c.type==="error","bg-yellow-50 dark:bg-yellow-500/10":c.type==="warning","bg-blue-50 dark:bg-blue-500/10":c.type==="info","max-w-[70vw]":Pe(n),"max-w-[40vw]":!Pe(n)}])},[ce("div",Xl,[c.type==="success"?(be(),et(Pe(E0),{key:0,theme:"filled",size:"20",class:"text-emerald-500 dark:text-emerald-400 flex-shrink-0"})):qe("",!0),c.type==="error"?(be(),et(Pe(A0),{key:1,theme:"filled",size:"20",class:"text-red-500 dark:text-red-400 flex-shrink-0"})):qe("",!0),c.type==="warning"?(be(),et(Pe(T0),{key:2,theme:"filled",size:"20",class:"text-yellow-500 dark:text-yellow-400 flex-shrink-0"})):qe("",!0),c.type==="info"?(be(),et(Pe(Z0),{key:3,theme:"filled",size:"20",class:"text-blue-500 dark:text-blue-400 flex-shrink-0"})):qe("",!0),ce("span",Rl,gs(c.content),1)])],2))),128))]),_:1})]))}});let kt=null;function Nr(){var r;if(kt)return kt;const e=document.createElement("div");e.setAttribute("class","message-container"),document.body.appendChild(e);const t=Ye(Dl);Pn(t,e);const a=(r=t.component)==null?void 0:r.exposed;return kt={success(n,i){a.show(ke({type:"success",content:n},i))},error(n,i){a.show(ke({type:"error",content:n},i))},warning(n,i){a.show(ke({type:"warning",content:n},i))},info(n,i){a.show(ke({type:"info",content:n},i))},destroy(){Pn(null,e),document.body.removeChild(e),kt=null}},kt}const ta=C0.create({baseURL:"/api",timeout:2400*1e3,adapter:"fetch"});ta.interceptors.request.use(e=>{var n;const t=nt().token,a=(n=Mr())==null?void 0:n.fingerprint,r=window.location.origin;return e.headers["X-Website-Domain"]=r,a&&(e.headers.Fingerprint=a),t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e.response));ta.interceptors.response.use(e=>{if([200,201].includes(e.status))return e;throw new Error(e.status.toString())},e=>Promise.reject(e));let li=0;const Bl=["/chatlog/chatList","/group/query"];function ql(e){return e?Bl.some(t=>e.includes(t)):!1}function Zs({url:e,data:t,method:a,headers:r,onDownloadProgress:n,signal:i,beforeRequest:s,afterRequest:o}){const c=Nr(),O=l=>{const u=nt(),f=l.data.code;return f&&f>=200&&f<300||!f?l.data:(f===401&&(u.removeToken(),window.location.reload()),Promise.reject(l.data))},d=l=>{var v,Q,x,m,p,S,P;const u=nt();let f="";if((v=l==null?void 0:l.response)!=null&&v.data&&(f=l.response.data),o==null||o(),((Q=l==null?void 0:l.response)==null?void 0:Q.status)===401){if(u.removeToken(),!ql((x=l==null?void 0:l.request)==null?void 0:x.responseURL)){u.loadInit&&u.setLoginDialog(!0);const b=((p=(m=l==null?void 0:l.response)==null?void 0:m.data)==null?void 0:p.message)||"请先登录后再进行使用！";Date.now()-li>3e3&&c.error(b)}li=Date.now()}else f&&!(f!=null&&f.success)&&c.error((f==null?void 0:f.message)||"请求接口错误！");throw new Error(((P=(S=l==null?void 0:l.response)==null?void 0:S.data)==null?void 0:P.message)||(l==null?void 0:l.message)||"Error")};s==null||s(),a=a||"GET";const g=Object.assign(typeof t=="function"?t():t!=null?t:{},{});return e.includes("getOldQRCode"),a==="GET"?ta.get(e,{params:g,signal:i,onDownloadProgress:n}).then(O,d):ta.post(e,g,{headers:r,signal:i,onDownloadProgress:n}).then(O,d)}function st({url:e,data:t,method:a="GET",onDownloadProgress:r,signal:n,beforeRequest:i,afterRequest:s}){return Zs({url:e,method:a,data:t,onDownloadProgress:r,signal:n,beforeRequest:i,afterRequest:s})}function ge({url:e,data:t,method:a="POST",headers:r,onDownloadProgress:n,signal:i,beforeRequest:s,afterRequest:o}){return Zs({url:e,method:a,data:t,headers:r,onDownloadProgress:n,signal:i,beforeRequest:s,afterRequest:o})}function Ll(e){return ge({url:"/group/create",data:e})}function Fl(){return st({url:"/group/query"})}function Yl(e){return ge({url:"/group/update",data:e})}function Il(e){return ge({url:"/group/del",data:e})}function Vl(e){return ge({url:"/group/delAll",data:e})}const mr="chatStorage";function Ul(){return{active:0,usingContext:!0,usingNetwork:!1,usingDeepThinking:!1,usingMcpTool:!1,groupList:[],chatList:[],groupKeyWord:"",baseConfig:null,currentPlugin:void 0,pluginList:[],prompt:"",reasoningText:""}}function Ml(){const e=rt.get(mr);return ke(ke({},Ul()),e)}function Wl({active:e}){rt.set(mr,_t(ke({},rt.get(mr)),{active:e}))}function zl(e){return ge({url:"/chatlog/del",data:e})}function Nl(e){return ge({url:"/chatlog/delByGroupId",data:e})}function Gl(e){return ge({url:"/chatlog/deleteChatsAfterId",data:e})}function jl(e){return st({url:"/chatlog/chatList",data:e})}function sp(){return st({url:"/models/list"})}function Hl(){return st({url:"/models/baseConfig"})}function Kl(){return st({url:"/plugin/pluginList"})}const Jl=Wr(),Xs=Zr("chat-store",{state:()=>Ml(),getters:{activeConfig:e=>{var n;const t=e.active;if(!t)return{};const a=(n=e.groupList.find(i=>i.uuid===t))==null?void 0:n.config;return a?JSON.parse(a):e.baseConfig},activeGroupAppId:e=>{var a;const t=e.active;return t?(a=e.groupList.find(r=>r.uuid===t))==null?void 0:a.appId:null},activeGroupFileUrl:e=>{var a;const t=e.active;return t?(a=e.groupList.find(r=>r.uuid===t))==null?void 0:a.fileUrl:null},activeModel(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.model},activeModelName(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.modelName},activeModelAvatar(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.modelAvatar},activeModelDeductType(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.deductType},activeModelKeyType(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.keyType},activeModelFileUpload(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.isFileUpload},activeModelPrice(e){var t,a;return(a=(t=this.activeConfig)==null?void 0:t.modelInfo)==null?void 0:a.deduct}},actions:{queryPlugins(){return fe(this,null,function*(){try{const e=yield Kl();e.success&&e.code===200&&(this.pluginList=e.data.rows.filter(t=>t.isEnabled===1).map(t=>({pluginId:t.id,pluginName:t.name,description:t.description,pluginImg:t.pluginImg,parameters:t.parameters,deductType:t.deductType,drawingType:t.drawingType,modelType:t.modelType})))}catch(e){}})},setGroupKeyWord(e){this.groupKeyWord=e},getChatByGroupInfo(){if(this.active)return this.groupList.find(e=>e.uuid===this.active)},getConfigFromUuid(e){var t;return(t=this.groupList.find(a=>a.uuid===e))==null?void 0:t.config},addNewChatGroup(e=0,t,a){return fe(this,null,function*(){try{const r=yield Ll({appId:e,modelConfig:t,params:a});this.active=r.data.id,this.usingNetwork=!1,this.usingDeepThinking=!1,this.usingMcpTool=!1,this.recordState(),yield this.queryMyGroup(),yield this.setActiveGroup(r.data.id)}catch(r){}})},getBaseModelConfig(){return fe(this,null,function*(){const e=yield Hl();this.baseConfig=e==null?void 0:e.data})},queryMyGroup(){return fe(this,null,function*(){const e=yield Fl();this.groupList=[...e.data.map(a=>{const{id:r,title:n,isSticky:i,createdAt:s,updatedAt:o,appId:c,config:O,appLogo:d,isFixedModel:g,isGpts:l,params:u,fileUrl:f,content:h,appModel:v}=a;return{uuid:r,title:n,isEdit:!1,appId:c,config:O,isSticky:i,appLogo:d,createdAt:s,isFixedModel:g,isGpts:l,params:u,fileUrl:f,content:h,appModel:v,updatedAt:new Date(o).getTime()}})];const t=this.groupList.some(a=>Number(a.uuid)===Number(this.active));(!this.active||!t)&&this.groupList.length&&this.setActiveGroup(this.groupList[0].uuid),this.groupList.length===0&&(yield this.addNewChatGroup()),this.recordState()})},updateGroupInfo(e){return fe(this,null,function*(){yield Yl(e),yield this.queryMyGroup()})},setActiveGroup(e){return fe(this,null,function*(){Jl.updateShowAppListComponent(!1),this.active=e,this.groupList.forEach(t=>t.isEdit=!1),yield this.queryActiveChatLogList(),this.active?yield this.queryActiveChatLogList():this.chatList=[],this.active=e,this.recordState()})},deleteGroup(e){return fe(this,null,function*(){const t=this.groupList.findIndex(r=>r.uuid===e.uuid),{uuid:a}=e;yield Il({groupId:a}),yield this.queryMyGroup(),this.groupList.length===0&&(yield this.setActiveGroup(0)),t>0&&t<this.groupList.length&&(yield this.setActiveGroup(this.groupList[t].uuid)),t===0&&this.groupList.length>0&&(yield this.setActiveGroup(this.groupList[0].uuid)),(t>this.groupList.length||t===0&&this.groupList.length===0)&&(yield this.setActiveGroup(0)),t>0&&t===this.groupList.length&&(yield this.setActiveGroup(this.groupList[t-1].uuid)),this.recordState()})},delAllGroup(){return fe(this,null,function*(){!this.active||!this.groupList.length||(yield Vl(),yield this.queryMyGroup(),this.groupList.length===0?yield this.setActiveGroup(0):yield this.setActiveGroup(this.groupList[0].uuid))})},queryActiveChatLogList(){return fe(this,null,function*(){if(!this.active||Number(this.active)===0){this.chatList=[];return}try{const e=yield jl({groupId:this.active});e&&e.data?this.chatList=e.data:this.chatList=[]}catch(e){this.chatList=[]}finally{this.recordState()}})},addGroupChat(e){this.chatList=[...this.chatList,e]},updateGroupChat(e,t){this.chatList[e]=ke(ke({},this.chatList[e]),t)},updateGroupChatSome(e,t){this.chatList[e]=ke(ke({},this.chatList[e]),t)},deleteChatById(e){return fe(this,null,function*(){e&&(yield zl({id:e}),yield this.queryActiveChatLogList())})},deleteChatsAfterId(e){return fe(this,null,function*(){e&&(yield Gl({id:e}),yield this.queryActiveChatLogList())})},setUsingContext(e){this.usingContext=e,this.recordState()},setUsingNetwork(e){this.usingNetwork=e,this.recordState()},setUsingDeepThinking(e){this.usingDeepThinking=e,this.recordState()},setUsingMcpTool(e){this.usingMcpTool=e,this.recordState()},setUsingPlugin(e){this.currentPlugin=e||void 0,this.recordState()},setPrompt(e){return fe(this,null,function*(){this.prompt=e,this.recordState()})},setStreamIn(e){this.isStreamIn=e,this.recordState()},clearChatByGroupId(){return fe(this,null,function*(){this.active&&(yield Nl({groupId:this.active}),yield this.queryActiveChatLogList())})},recordState(){Wl(this.$state)},clearChat(){this.chatList=[],this.groupList=[],this.active=0,this.recordState()}}}),eO="/api";class Gt extends Error{constructor(a,r,n,i){super(a);Vt(this,"status");Vt(this,"statusText");Vt(this,"data");this.name="FetchError",this.status=r,this.statusText=n,this.data=i}}const tO=(a,...r)=>fe(void 0,[a,...r],function*(e,t={}){var O;const n=nt().token,i=(O=Mr())==null?void 0:O.fingerprint,s=window.location.origin,o=new Headers;if(t.headers){const d=t.headers;Object.keys(d).forEach(g=>{o.set(g,String(d[g]))})}o.set("X-Website-Domain",s),i&&o.set("Fingerprint",String(i)),n&&o.set("Authorization",`Bearer ${n}`);const c=_t(ke({},t),{headers:o});try{const d=yield fetch(`${eO}${e}`,c);if(![200,201].includes(d.status)){let g;try{g=yield d.clone().json()}catch(l){try{g=yield d.clone().text()}catch(u){g=null}}throw new Gt(d.status.toString(),d.status,d.statusText,g)}return d}catch(d){if(d instanceof Gt)return Promise.reject(d);const g=new Gt(d.message||"Network Error",0,"Network Error");return Promise.reject(g)}}),aO=(r,...n)=>fe(void 0,[r,...n],function*(e,t={},a){var O;const s=(O=(yield tO(e,t)).body)==null?void 0:O.getReader();if(!s)throw new Gt("Failed to get reader from response",0,"Reader Error");let o="";const c=new TextDecoder;for(;;){const{done:d,value:g}=yield s.read();if(d)break;const l=c.decode(g,{stream:!0});o+=l,a&&a(l)}return o});function op(e){const t={model:e.model,modelName:e.modelName,modelType:e.modelType,prompt:e.prompt,fileInfo:e==null?void 0:e.fileInfo,imageUrl:e==null?void 0:e.imageUrl,fileUrl:e==null?void 0:e.fileUrl,extraParam:e==null?void 0:e.extraParam,appId:e==null?void 0:e.appId,options:e.options,action:e==null?void 0:e.action,customId:e==null?void 0:e.customId,usingPluginId:e==null?void 0:e.usingPluginId,drawId:e==null?void 0:e.drawId,modelAvatar:e==null?void 0:e.modelAvatar,taskId:e==null?void 0:e.taskId};return e.onDownloadProgress?new Promise((a,r)=>{const n={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)};e.signal&&(n.signal=e.signal),aO("/chatgpt/chat-process",n,i=>{if(e.onDownloadProgress){const s={event:{target:{responseText:i,getResponseHeader:o=>null}},loaded:i.length,total:0,bytes:i.length,lengthComputable:!1,progress:0};e.onDownloadProgress(s)}}).then(i=>{a({data:i})}).catch(i=>{r(i)})}):ge({url:"/chatgpt/chat-process",data:t,signal:e.signal})}function lp(e){return ge({url:"/chatgpt/tts-process",data:e})}function rO(){return st({url:"/auth/getInfo"})}function Op(e){return ge({url:"/auth/login",data:e})}function cp(e){return ge({url:"/user/update",data:e})}function up(e){return ge({url:"/auth/updatePassword",data:e})}function fp(e){return ge({url:"/auth/sendCode",data:e})}function dp(e){return ge({url:"/auth/sendPhoneCode",data:e})}function nO(){return st({url:"/balance/query"})}function iO(e){return st({url:"/config/queryFront",data:e})}const Gr="SECRET_TOKEN";function sO(){return rt.get(Gr)}function oO(e){return rt.set(Gr,e)}function Ka(){return rt.remove(Gr)}const nt=Zr("auth-store",{state:()=>({token:sO(),loginDialog:!1,globalConfigLoading:!0,userInfo:{},userBalance:{},globalConfig:{},loadInit:!1}),getters:{isLogin:e=>!!e.token},actions:{getUserInfo(){return fe(this,null,function*(){try{this.loadInit||(yield this.getGlobalConfig());const e=yield rO();if(!e)return Promise.resolve(e);const{data:t}=e,{userInfo:a,userBalance:r}=t;return this.userInfo=ke({},a),this.userBalance=ke({},r),Promise.resolve(t)}catch(e){return Promise.reject(e)}})},updateUserBalance(e){this.userBalance=e},getUserBalance(){return fe(this,null,function*(){const e=yield nO(),{success:t,data:a}=e;t&&(this.userBalance=a)})},getGlobalConfig(e=""){return fe(this,null,function*(){const t=yield iO({domain:e});this.globalConfig=t.data,this.globalConfigLoading=!1,this.loadInit=!0})},setToken(e){this.token=e,oO(e)},removeToken(){this.token=void 0,Ka()},setLoginDialog(e){this.loginDialog=e},logOut(){this.token=void 0,Ka(),this.userInfo={},this.userBalance={},Xs().clearChat(),window.location.reload()},updatePasswordSuccess(){this.token=void 0,Ka(),this.userInfo={},this.userBalance={},this.loginDialog=!0}}});function lO(){return nt(zr)}const OO=[{path:"/",name:"Chat",component:()=>pr(()=>import("./chat-30409f0b.js"),["./chat-30409f0b.js","./vue-vendor-d751b0f5.js","./ui-vendor-70145f70.js","./utils-vendor-c35799af.js","./chart-vendor-e1d59b84.js","./editor-vendor-e2dea24d.js","../css/chat-f45736e0.css"],import.meta.url)},{path:"/:catchAll(.*)",redirect:"/"}],cO=m0({history:g0(),routes:OO});const uO=""+new URL("../images/favicon-25a41591.ico",import.meta.url).href,fO=it({__name:"index",setup(e){const t=nt(),a=Ce(()=>t.isLogin),r=Ce(()=>{const i=t.userInfo.id,s=t.userInfo.nickname;return a.value?s?`${s}(${i})`:`(${i})`:`游客(${i})`});function n(){if(!r.value)return"";const i=document.createElement("canvas"),s=i.getContext("2d");return s?(i.width=240,i.height=180,s.rotate(-20*Math.PI/180),s.fillStyle="rgba(0, 0, 0, 0.08)",s.font="16px Arial",s.fillText(r.value,-20,100),`url(${i.toDataURL()})`):""}return(i,s)=>r.value?(be(),Ve("div",{key:0,class:"fixed inset-0 pointer-events-none select-none z-50",style:vs({backgroundImage:n(),backgroundRepeat:"repeat"})},null,4)):qe("",!0)}});function dO(e){return ge({url:"/share/create",data:e})}class aa{constructor(t,a,r,n,i,s,o,c,O,d=0,g){this.p=t,this.stack=a,this.state=r,this.reducePos=n,this.pos=i,this.score=s,this.buffer=o,this.bufferBase=c,this.curContext=O,this.lookAhead=d,this.parent=g}toString(){return`[${this.stack.filter((t,a)=>a%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,a,r=0){let n=t.parser.context;return new aa(t,[],a,r,r,0,[],0,n?new Oi(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,a){this.stack.push(this.state,a,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var a;let r=t>>19,n=t&65535,{parser:i}=this.p,s=this.reducePos<this.pos-25;s&&this.setLookAhead(this.pos);let o=i.dynamicPrecedence(n);if(o&&(this.score+=o),r==0){this.pushState(i.getGoto(this.state,n,!0),this.reducePos),n<i.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,s?8:4,!0),this.reduceContext(n,this.reducePos);return}let c=this.stack.length-(r-1)*3-(t&262144?6:0),O=c?this.stack[c-2]:this.p.ranges[0].from,d=this.reducePos-O;d>=2e3&&!(!((a=this.p.parser.nodeSet.types[n])===null||a===void 0)&&a.isAnonymous)&&(O==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=d):this.p.lastBigReductionSize<d&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=O,this.p.lastBigReductionSize=d));let g=c?this.stack[c-1]:0,l=this.bufferBase+this.buffer.length-g;if(n<i.minRepeatTerm||t&131072){let u=i.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,O,u,l+4,!0)}if(t&262144)this.state=this.stack[c];else{let u=this.stack[c-3];this.state=i.getGoto(u,n,!0)}for(;this.stack.length>c;)this.stack.pop();this.reduceContext(n,O)}storeNode(t,a,r,n=4,i=!1){if(t==0&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let s=this,o=this.buffer.length;if(o==0&&s.parent&&(o=s.bufferBase-s.parent.bufferBase,s=s.parent),o>0&&s.buffer[o-4]==0&&s.buffer[o-1]>-1){if(a==r)return;if(s.buffer[o-2]>=a){s.buffer[o-2]=r;return}}}if(!i||this.pos==r)this.buffer.push(t,a,r,n);else{let s=this.buffer.length;if(s>0&&this.buffer[s-4]!=0){let o=!1;for(let c=s;c>0&&this.buffer[c-2]>r;c-=4)if(this.buffer[c-1]>=0){o=!0;break}if(o)for(;s>0&&this.buffer[s-2]>r;)this.buffer[s]=this.buffer[s-4],this.buffer[s+1]=this.buffer[s-3],this.buffer[s+2]=this.buffer[s-2],this.buffer[s+3]=this.buffer[s-1],s-=4,n>4&&(n-=4)}this.buffer[s]=t,this.buffer[s+1]=a,this.buffer[s+2]=r,this.buffer[s+3]=n}}shift(t,a,r,n){if(t&131072)this.pushState(t&65535,this.pos);else if(t&262144)this.pos=n,this.shiftContext(a,r),a<=this.p.parser.maxNode&&this.buffer.push(a,r,n,4);else{let i=t,{parser:s}=this.p;(n>this.pos||a<=s.maxNode)&&(this.pos=n,s.stateFlag(i,1)||(this.reducePos=n)),this.pushState(i,r),this.shiftContext(a,r),a<=s.maxNode&&this.buffer.push(a,r,n,4)}}apply(t,a,r,n){t&65536?this.reduce(t):this.shift(t,a,r,n)}useNode(t,a){let r=this.p.reused.length-1;(r<0||this.p.reused[r]!=t)&&(this.p.reused.push(t),r++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(a,n),this.buffer.push(r,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,a=t.buffer.length;for(;a>0&&t.buffer[a-2]>t.reducePos;)a-=4;let r=t.buffer.slice(a),n=t.bufferBase+a;for(;t&&n==t.bufferBase;)t=t.parent;return new aa(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,r,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,a){let r=t<=this.p.parser.maxNode;r&&this.storeNode(t,this.pos,a,4),this.storeNode(0,this.pos,a,r?8:4),this.pos=this.reducePos=a,this.score-=190}canShift(t){for(let a=new hO(this);;){let r=this.p.parser.stateSlot(a.state,4)||this.p.parser.hasAction(a.state,t);if(r==0)return!1;if(!(r&65536))return!0;a.reduce(r)}}recoverByInsert(t){if(this.stack.length>=300)return[];let a=this.p.parser.nextStates(this.state);if(a.length>8||this.stack.length>=120){let n=[];for(let i=0,s;i<a.length;i+=2)(s=a[i+1])!=this.state&&this.p.parser.hasAction(s,t)&&n.push(a[i],s);if(this.stack.length<120)for(let i=0;n.length<8&&i<a.length;i+=2){let s=a[i+1];n.some((o,c)=>c&1&&o==s)||n.push(a[i],s)}a=n}let r=[];for(let n=0;n<a.length&&r.length<4;n+=2){let i=a[n+1];if(i==this.state)continue;let s=this.split();s.pushState(i,this.pos),s.storeNode(0,s.pos,s.pos,4,!0),s.shiftContext(a[n],this.pos),s.reducePos=this.pos,s.score-=200,r.push(s)}return r}forceReduce(){let{parser:t}=this.p,a=t.stateSlot(this.state,5);if(!(a&65536))return!1;if(!t.validAction(this.state,a)){let r=a>>19,n=a&65535,i=this.stack.length-r*3;if(i<0||t.getGoto(this.stack[i],n,!1)<0){let s=this.findForcedReduction();if(s==null)return!1;a=s}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(a),!0}findForcedReduction(){let{parser:t}=this.p,a=[],r=(n,i)=>{if(!a.includes(n))return a.push(n),t.allActions(n,s=>{if(!(s&393216))if(s&65536){let o=(s>>19)-i;if(o>1){let c=s&65535,O=this.stack.length-o*3;if(O>=0&&t.getGoto(this.stack[O],c,!1)>=0)return o<<19|65536|c}}else{let o=r(s,i+1);if(o!=null)return o}})};return r(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(this.stack.length!=3)return!1;let{parser:t}=this.p;return t.data[t.stateSlot(this.state,1)]==65535&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let a=0;a<this.stack.length;a+=3)if(this.stack[a]!=t.stack[a])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,a){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(a)))}reduceContext(t,a){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(a)))}emitContext(){let t=this.buffer.length-1;(t<0||this.buffer[t]!=-3)&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||this.buffer[t]!=-4)&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let a=new Oi(this.curContext.tracker,t);a.hash!=this.curContext.hash&&this.emitContext(),this.curContext=a}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class Oi{constructor(t,a){this.tracker=t,this.context=a,this.hash=t.strict?t.hash(a):0}}class hO{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let a=t&65535,r=t>>19;r==0?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=(r-1)*3;let n=this.start.p.parser.getGoto(this.stack[this.base-3],a,!0);this.state=n}}class ra{constructor(t,a,r){this.stack=t,this.pos=a,this.index=r,this.buffer=t.buffer,this.index==0&&this.maybeNext()}static create(t,a=t.bufferBase+t.buffer.length){return new ra(t,a,a-t.bufferBase)}maybeNext(){let t=this.stack.parent;t!=null&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,this.index==0&&this.maybeNext()}fork(){return new ra(this.stack,this.pos,this.index)}}function Tt(e,t=Uint16Array){if(typeof e!="string")return e;let a=null;for(let r=0,n=0;r<e.length;){let i=0;for(;;){let s=e.charCodeAt(r++),o=!1;if(s==126){i=65535;break}s>=92&&s--,s>=34&&s--;let c=s-32;if(c>=46&&(c-=46,o=!0),i+=c,o)break;i*=46}a?a[n++]=i:a=new t(i)}return a}class jt{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const ci=new jt;class pO{constructor(t,a){this.input=t,this.ranges=a,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=ci,this.rangeIndex=0,this.pos=this.chunkPos=a[0].from,this.range=a[0],this.end=a[a.length-1].to,this.readNext()}resolveOffset(t,a){let r=this.range,n=this.rangeIndex,i=this.pos+t;for(;i<r.from;){if(!n)return null;let s=this.ranges[--n];i-=r.from-s.to,r=s}for(;a<0?i>r.to:i>=r.to;){if(n==this.ranges.length-1)return null;let s=this.ranges[++n];i+=s.from-r.to,r=s}return i}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let a of this.ranges)if(a.to>t)return Math.max(t,a.from);return this.end}peek(t){let a=this.chunkOff+t,r,n;if(a>=0&&a<this.chunk.length)r=this.pos+t,n=this.chunk.charCodeAt(a);else{let i=this.resolveOffset(t,1);if(i==null)return-1;if(r=i,r>=this.chunk2Pos&&r<this.chunk2Pos+this.chunk2.length)n=this.chunk2.charCodeAt(r-this.chunk2Pos);else{let s=this.rangeIndex,o=this.range;for(;o.to<=r;)o=this.ranges[++s];this.chunk2=this.input.chunk(this.chunk2Pos=r),r+this.chunk2.length>o.to&&(this.chunk2=this.chunk2.slice(0,o.to-r)),n=this.chunk2.charCodeAt(0)}}return r>=this.token.lookAhead&&(this.token.lookAhead=r+1),n}acceptToken(t,a=0){let r=a?this.resolveOffset(a,-1):this.pos;if(r==null||r<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=r}acceptTokenTo(t,a){this.token.value=t,this.token.end=a}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:a}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=a,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),a=this.pos+t.length;this.chunk=a>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,a){if(a?(this.token=a,a.start=t,a.lookAhead=t+1,a.value=a.extended=-1):this.token=ci,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,a){if(t>=this.chunkPos&&a<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,a-this.chunkPos);if(t>=this.chunk2Pos&&a<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,a-this.chunk2Pos);if(t>=this.range.from&&a<=this.range.to)return this.input.read(t,a);let r="";for(let n of this.ranges){if(n.from>=a)break;n.to>t&&(r+=this.input.read(Math.max(n.from,t),Math.min(n.to,a)))}return r}}class Qt{constructor(t,a){this.data=t,this.id=a}token(t,a){let{parser:r}=a.p;Rs(this.data,t,a,this.id,r.data,r.tokenPrecTable)}}Qt.prototype.contextual=Qt.prototype.fallback=Qt.prototype.extend=!1;class na{constructor(t,a,r){this.precTable=a,this.elseToken=r,this.data=typeof t=="string"?Tt(t):t}token(t,a){let r=t.pos,n=0;for(;;){let i=t.next<0,s=t.resolveOffset(1,1);if(Rs(this.data,t,a,0,this.data,this.precTable),t.token.value>-1)break;if(this.elseToken==null)return;if(i||n++,s==null)break;t.reset(s,t.token)}n&&(t.reset(r,t.token),t.acceptToken(this.elseToken,n))}}na.prototype.contextual=Qt.prototype.fallback=Qt.prototype.extend=!1;class Ie{constructor(t,a={}){this.token=t,this.contextual=!!a.contextual,this.fallback=!!a.fallback,this.extend=!!a.extend}}function Rs(e,t,a,r,n,i){let s=0,o=1<<r,{dialect:c}=a.p.parser;e:for(;o&e[s];){let O=e[s+1];for(let u=s+3;u<O;u+=2)if((e[u+1]&o)>0){let f=e[u];if(c.allows(f)&&(t.token.value==-1||t.token.value==f||xO(f,t.token.value,n,i))){t.acceptToken(f);break}}let d=t.next,g=0,l=e[s+2];if(t.next<0&&l>g&&e[O+l*3-3]==65535){s=e[O+l*3-1];continue e}for(;g<l;){let u=g+l>>1,f=O+u+(u<<1),h=e[f],v=e[f+1]||65536;if(d<h)l=u;else if(d>=v)g=u+1;else{s=e[f+2],t.advance();continue e}}break}}function ui(e,t,a){for(let r=t,n;(n=e[r])!=65535;r++)if(n==a)return r-t;return-1}function xO(e,t,a,r){let n=ui(a,r,t);return n<0||ui(a,r,e)<n}const De=typeof process!="undefined"&&process.env&&/\bparse\b/.test({}.LOG);let Ja=null;function fi(e,t,a){let r=e.cursor(Br.IncludeAnonymous);for(r.moveTo(t);;)if(!(a<0?r.childBefore(t):r.childAfter(t)))for(;;){if((a<0?r.to<t:r.from>t)&&!r.type.isError)return a<0?Math.max(0,Math.min(r.to-1,t-25)):Math.min(e.length,Math.max(r.from+1,t+25));if(a<0?r.prevSibling():r.nextSibling())break;if(!r.parent())return a<0?0:e.length}}class mO{constructor(t,a){this.fragments=t,this.nodeSet=a,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?fi(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?fi(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let a=this.trees.length-1;if(a<0)return this.nextFragment(),null;let r=this.trees[a],n=this.index[a];if(n==r.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let i=r.children[n],s=this.start[a]+r.positions[n];if(s>t)return this.nextStart=s,null;if(i instanceof zt){if(s==t){if(s<this.safeFrom)return null;let o=s+i.length;if(o<=this.safeTo){let c=i.prop(Dr.lookAhead);if(!c||o+c<this.fragment.to)return i}}this.index[a]++,s+i.length>=Math.max(this.safeFrom,t)&&(this.trees.push(i),this.start.push(s),this.index.push(0))}else this.index[a]++,this.nextStart=s+i.length}}}class gO{constructor(t,a){this.stream=a,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map(r=>new jt)}getActions(t){let a=0,r=null,{parser:n}=t.p,{tokenizers:i}=n,s=n.stateSlot(t.state,3),o=t.curContext?t.curContext.hash:0,c=0;for(let O=0;O<i.length;O++){if(!(1<<O&s))continue;let d=i[O],g=this.tokens[O];if(!(r&&!d.fallback)&&((d.contextual||g.start!=t.pos||g.mask!=s||g.context!=o)&&(this.updateCachedToken(g,d,t),g.mask=s,g.context=o),g.lookAhead>g.end+25&&(c=Math.max(g.lookAhead,c)),g.value!=0)){let l=a;if(g.extended>-1&&(a=this.addActions(t,g.extended,g.end,a)),a=this.addActions(t,g.value,g.end,a),!d.extend&&(r=g,a>l))break}}for(;this.actions.length>a;)this.actions.pop();return c&&t.setLookAhead(c),!r&&t.pos==this.stream.end&&(r=new jt,r.value=t.p.parser.eofTerm,r.start=r.end=t.pos,a=this.addActions(t,r.value,r.end,a)),this.mainToken=r,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let a=new jt,{pos:r,p:n}=t;return a.start=r,a.end=Math.min(r+1,n.stream.end),a.value=r==n.stream.end?n.parser.eofTerm:0,a}updateCachedToken(t,a,r){let n=this.stream.clipPos(r.pos);if(a.token(this.stream.reset(n,t),r),t.value>-1){let{parser:i}=r.p;for(let s=0;s<i.specialized.length;s++)if(i.specialized[s]==t.value){let o=i.specializers[s](this.stream.read(t.start,t.end),r);if(o>=0&&r.p.parser.dialect.allows(o>>1)){o&1?t.extended=o>>1:t.value=o>>1;break}}}else t.value=0,t.end=this.stream.clipPos(n+1)}putAction(t,a,r,n){for(let i=0;i<n;i+=3)if(this.actions[i]==t)return n;return this.actions[n++]=t,this.actions[n++]=a,this.actions[n++]=r,n}addActions(t,a,r,n){let{state:i}=t,{parser:s}=t.p,{data:o}=s;for(let c=0;c<2;c++)for(let O=s.stateSlot(i,c?2:1);;O+=3){if(o[O]==65535)if(o[O+1]==1)O=Je(o,O+2);else{n==0&&o[O+1]==2&&(n=this.putAction(Je(o,O+2),a,r,n));break}o[O]==a&&(n=this.putAction(Je(o,O+1),a,r,n))}return n}}class vO{constructor(t,a,r,n){this.parser=t,this.input=a,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new pO(a,n),this.tokens=new gO(t,this.stream),this.topTerm=t.top[1];let{from:i}=n[0];this.stacks=[aa.start(this,t.top[0],i)],this.fragments=r.length&&this.stream.end-i>t.bufferLength*4?new mO(r,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t=this.stacks,a=this.minStackPos,r=this.stacks=[],n,i;if(this.bigReductionCount>300&&t.length==1){let[s]=t;for(;s.forceReduce()&&s.stack.length&&s.stack[s.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let s=0;s<t.length;s++){let o=t[s];for(;;){if(this.tokens.mainToken=null,o.pos>a)r.push(o);else{if(this.advanceStack(o,r,t))continue;{n||(n=[],i=[]),n.push(o);let c=this.tokens.getMainToken(o);i.push(c.value,c.end)}}break}}if(!r.length){let s=n&&SO(n);if(s)return this.stackToTree(s);if(this.parser.strict)throw new SyntaxError("No parse at "+a);this.recovering||(this.recovering=5)}if(this.recovering&&n){let s=this.stoppedAt!=null&&n[0].pos>this.stoppedAt?n[0]:this.runRecovery(n,i,r);if(s)return this.stackToTree(s.forceAll())}if(this.recovering){let s=this.recovering==1?1:this.recovering*3;if(r.length>s)for(r.sort((o,c)=>c.score-o.score);r.length>s;)r.pop();r.some(o=>o.reducePos>a)&&this.recovering--}else if(r.length>1){e:for(let s=0;s<r.length-1;s++){let o=r[s];for(let c=s+1;c<r.length;c++){let O=r[c];if(o.sameState(O)||o.buffer.length>500&&O.buffer.length>500)if((o.score-O.score||o.buffer.length-O.buffer.length)>0)r.splice(c--,1);else{r.splice(s--,1);continue e}}}r.length>12&&r.splice(12,r.length-12)}this.minStackPos=r[0].pos;for(let s=1;s<r.length;s++)r[s].pos<this.minStackPos&&(this.minStackPos=r[s].pos);return null}stopAt(t){if(this.stoppedAt!=null&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,a,r){let n=t.pos,{parser:i}=this,s=De?this.stackID(t)+" -> ":"";if(this.stoppedAt!=null&&n>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let O=t.curContext&&t.curContext.tracker.strict,d=O?t.curContext.hash:0;for(let g=this.fragments.nodeAt(n);g;){let l=this.parser.nodeSet.types[g.type.id]==g.type?i.getGoto(t.state,g.type.id):-1;if(l>-1&&g.length&&(!O||(g.prop(Dr.contextHash)||0)==d))return t.useNode(g,l),!0;if(!(g instanceof zt)||g.children.length==0||g.positions[0]>0)break;let u=g.children[0];if(u instanceof zt&&g.positions[0]==0)g=u;else break}}let o=i.stateSlot(t.state,4);if(o>0)return t.reduce(o),!0;if(t.stack.length>=8400)for(;t.stack.length>6e3&&t.forceReduce(););let c=this.tokens.getActions(t);for(let O=0;O<c.length;){let d=c[O++],g=c[O++],l=c[O++],u=O==c.length||!r,f=u?t:t.split(),h=this.tokens.mainToken;if(f.apply(d,g,h?h.start:f.pos,l),u)return!0;f.pos>n?a.push(f):r.push(f)}return!1}advanceFully(t,a){let r=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>r)return di(t,a),!0}}runRecovery(t,a,r){let n=null,i=!1;for(let s=0;s<t.length;s++){let o=t[s],c=a[s<<1],O=a[(s<<1)+1],d=De?this.stackID(o)+" -> ":"";if(o.deadEnd&&(i||(i=!0,o.restart(),this.advanceFully(o,r))))continue;let g=o.split(),l=d;for(let u=0;g.forceReduce()&&u<10&&!this.advanceFully(g,r);u++)De&&(l=this.stackID(g)+" -> ");for(let u of o.recoverByInsert(c))this.advanceFully(u,r);this.stream.end>o.pos?(O==o.pos&&(O++,c=0),o.recoverByDelete(c,O),di(o,r)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(t){return t.close(),zt.build({buffer:ra.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let a=(Ja||(Ja=new WeakMap)).get(t);return a||Ja.set(t,a=String.fromCodePoint(this.nextStackID++)),a+t}}function di(e,t){for(let a=0;a<t.length;a++){let r=t[a];if(r.pos==e.pos&&r.sameState(e)){t[a].score<e.score&&(t[a]=e);return}}t.push(e)}class QO{constructor(t,a,r){this.source=t,this.flags=a,this.disabled=r}allows(t){return!this.disabled||this.disabled[t]==0}}const er=e=>e;class Ds{constructor(t){this.start=t.start,this.shift=t.shift||er,this.reduce=t.reduce||er,this.reuse=t.reuse||er,this.hash=t.hash||(()=>0),this.strict=t.strict!==!1}}class bt extends q0{constructor(t){if(super(),this.wrappers=[],t.version!=14)throw new RangeError(`Parser version (${t.version}) doesn't match runtime version (14)`);let a=t.nodeNames.split(" ");this.minRepeatTerm=a.length;for(let o=0;o<t.repeatNodeCount;o++)a.push("");let r=Object.keys(t.topRules).map(o=>t.topRules[o][1]),n=[];for(let o=0;o<a.length;o++)n.push([]);function i(o,c,O){n[o].push([c,c.deserialize(String(O))])}if(t.nodeProps)for(let o of t.nodeProps){let c=o[0];typeof c=="string"&&(c=Dr[c]);for(let O=1;O<o.length;){let d=o[O++];if(d>=0)i(d,c,o[O++]);else{let g=o[O+-d];for(let l=-d;l>0;l--)i(o[O++],c,g);O++}}}this.nodeSet=new L0(a.map((o,c)=>F0.define({name:c>=this.minRepeatTerm?void 0:o,id:c,props:n[c],top:r.indexOf(c)>-1,error:c==0,skipped:t.skippedNodes&&t.skippedNodes.indexOf(c)>-1}))),t.propSources&&(this.nodeSet=this.nodeSet.extend(...t.propSources)),this.strict=!1,this.bufferLength=Y0;let s=Tt(t.tokenData);this.context=t.context,this.specializerSpecs=t.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let o=0;o<this.specializerSpecs.length;o++)this.specialized[o]=this.specializerSpecs[o].term;this.specializers=this.specializerSpecs.map(hi),this.states=Tt(t.states,Uint32Array),this.data=Tt(t.stateData),this.goto=Tt(t.goto),this.maxTerm=t.maxTerm,this.tokenizers=t.tokenizers.map(o=>typeof o=="number"?new Qt(s,o):o),this.topRules=t.topRules,this.dialects=t.dialects||{},this.dynamicPrecedences=t.dynamicPrecedences||null,this.tokenPrecTable=t.tokenPrec,this.termNames=t.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,a,r){let n=new vO(this,t,a,r);for(let i of this.wrappers)n=i(n,t,a,r);return n}getGoto(t,a,r=!1){let n=this.goto;if(a>=n[0])return-1;for(let i=n[a+1];;){let s=n[i++],o=s&1,c=n[i++];if(o&&r)return c;for(let O=i+(s>>1);i<O;i++)if(n[i]==t)return c;if(o)return-1}}hasAction(t,a){let r=this.data;for(let n=0;n<2;n++)for(let i=this.stateSlot(t,n?2:1),s;;i+=3){if((s=r[i])==65535)if(r[i+1]==1)s=r[i=Je(r,i+2)];else{if(r[i+1]==2)return Je(r,i+2);break}if(s==a||s==0)return Je(r,i+1)}return 0}stateSlot(t,a){return this.states[t*6+a]}stateFlag(t,a){return(this.stateSlot(t,0)&a)>0}validAction(t,a){return!!this.allActions(t,r=>r==a?!0:null)}allActions(t,a){let r=this.stateSlot(t,4),n=r?a(r):void 0;for(let i=this.stateSlot(t,1);n==null;i+=3){if(this.data[i]==65535)if(this.data[i+1]==1)i=Je(this.data,i+2);else break;n=a(Je(this.data,i+1))}return n}nextStates(t){let a=[];for(let r=this.stateSlot(t,1);;r+=3){if(this.data[r]==65535)if(this.data[r+1]==1)r=Je(this.data,r+2);else break;if(!(this.data[r+2]&1)){let n=this.data[r+1];a.some((i,s)=>s&1&&i==n)||a.push(this.data[r],n)}}return a}configure(t){let a=Object.assign(Object.create(bt.prototype),this);if(t.props&&(a.nodeSet=this.nodeSet.extend(...t.props)),t.top){let r=this.topRules[t.top];if(!r)throw new RangeError(`Invalid top rule name ${t.top}`);a.top=r}return t.tokenizers&&(a.tokenizers=this.tokenizers.map(r=>{let n=t.tokenizers.find(i=>i.from==r);return n?n.to:r})),t.specializers&&(a.specializers=this.specializers.slice(),a.specializerSpecs=this.specializerSpecs.map((r,n)=>{let i=t.specializers.find(o=>o.from==r.external);if(!i)return r;let s=Object.assign(Object.assign({},r),{external:i.to});return a.specializers[n]=hi(s),s})),t.contextTracker&&(a.context=t.contextTracker),t.dialect&&(a.dialect=this.parseDialect(t.dialect)),t.strict!=null&&(a.strict=t.strict),t.wrap&&(a.wrappers=a.wrappers.concat(t.wrap)),t.bufferLength!=null&&(a.bufferLength=t.bufferLength),a}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let a=this.dynamicPrecedences;return a==null?0:a[t]||0}parseDialect(t){let a=Object.keys(this.dialects),r=a.map(()=>!1);if(t)for(let i of t.split(" ")){let s=a.indexOf(i);s>=0&&(r[s]=!0)}let n=null;for(let i=0;i<a.length;i++)if(!r[i])for(let s=this.dialects[a[i]],o;(o=this.data[s++])!=65535;)(n||(n=new Uint8Array(this.maxTerm+1)))[o]=1;return new QO(t,r,n)}static deserialize(t){return new bt(t)}}function Je(e,t){return e[t]|e[t+1]<<16}function SO(e){let t=null;for(let a of e){let r=a.p.stoppedAt;(a.pos==a.p.stream.end||r!=null&&a.pos>r)&&a.p.parser.stateFlag(a.state,2)&&(!t||t.score<a.score)&&(t=a)}return t}function hi(e){if(e.external){let t=e.extend?1:0;return(a,r)=>e.external(a,r)<<1|t}return e.get}const bO=54,PO=1,yO=55,$O=2,wO=56,_O=3,pi=4,kO=5,ia=6,Bs=7,qs=8,Ls=9,Fs=10,CO=11,EO=12,AO=13,tr=57,TO=14,xi=58,Ys=20,ZO=22,Is=23,XO=24,gr=26,Vs=27,RO=28,DO=31,BO=34,qO=36,LO=37,FO=0,YO=1,IO={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},VO={dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},mi={dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}};function UO(e){return e==45||e==46||e==58||e>=65&&e<=90||e==95||e>=97&&e<=122||e>=161}function Us(e){return e==9||e==10||e==13||e==32}let gi=null,vi=null,Qi=0;function vr(e,t){let a=e.pos+t;if(Qi==a&&vi==e)return gi;let r=e.peek(t);for(;Us(r);)r=e.peek(++t);let n="";for(;UO(r);)n+=String.fromCharCode(r),r=e.peek(++t);return vi=e,Qi=a,gi=n?n.toLowerCase():r==MO||r==WO?void 0:null}const Ms=60,sa=62,jr=47,MO=63,WO=33,zO=45;function Si(e,t){this.name=e,this.parent=t}const NO=[ia,Fs,Bs,qs,Ls],GO=new Ds({start:null,shift(e,t,a,r){return NO.indexOf(t)>-1?new Si(vr(r,1)||"",e):e},reduce(e,t){return t==Ys&&e?e.parent:e},reuse(e,t,a,r){let n=t.type.id;return n==ia||n==qO?new Si(vr(r,1)||"",e):e},strict:!1}),jO=new Ie((e,t)=>{if(e.next!=Ms){e.next<0&&t.context&&e.acceptToken(tr);return}e.advance();let a=e.next==jr;a&&e.advance();let r=vr(e,0);if(r===void 0)return;if(!r)return e.acceptToken(a?TO:ia);let n=t.context?t.context.name:null;if(a){if(r==n)return e.acceptToken(CO);if(n&&VO[n])return e.acceptToken(tr,-2);if(t.dialectEnabled(FO))return e.acceptToken(EO);for(let i=t.context;i;i=i.parent)if(i.name==r)return;e.acceptToken(AO)}else{if(r=="script")return e.acceptToken(Bs);if(r=="style")return e.acceptToken(qs);if(r=="textarea")return e.acceptToken(Ls);if(IO.hasOwnProperty(r))return e.acceptToken(Fs);n&&mi[n]&&mi[n][r]?e.acceptToken(tr,-1):e.acceptToken(ia)}},{contextual:!0}),HO=new Ie(e=>{for(let t=0,a=0;;a++){if(e.next<0){a&&e.acceptToken(xi);break}if(e.next==zO)t++;else if(e.next==sa&&t>=2){a>=3&&e.acceptToken(xi,-2);break}else t=0;e.advance()}});function KO(e){for(;e;e=e.parent)if(e.name=="svg"||e.name=="math")return!0;return!1}const JO=new Ie((e,t)=>{if(e.next==jr&&e.peek(1)==sa){let a=t.dialectEnabled(YO)||KO(t.context);e.acceptToken(a?kO:pi,2)}else e.next==sa&&e.acceptToken(pi,1)});function Hr(e,t,a){let r=2+e.length;return new Ie(n=>{for(let i=0,s=0,o=0;;o++){if(n.next<0){o&&n.acceptToken(t);break}if(i==0&&n.next==Ms||i==1&&n.next==jr||i>=2&&i<r&&n.next==e.charCodeAt(i-2))i++,s++;else if((i==2||i==r)&&Us(n.next))s++;else if(i==r&&n.next==sa){o>s?n.acceptToken(t,-s):n.acceptToken(a,-(s-2));break}else if((n.next==10||n.next==13)&&o){n.acceptToken(t,1);break}else i=s=0;n.advance()}})}const ec=Hr("script",bO,PO),tc=Hr("style",yO,$O),ac=Hr("textarea",wO,_O),rc=qr({"Text RawText":_.content,"StartTag StartCloseTag SelfClosingEndTag EndTag":_.angleBracket,TagName:_.tagName,"MismatchedCloseTag/TagName":[_.tagName,_.invalid],AttributeName:_.attributeName,"AttributeValue UnquotedAttributeValue":_.attributeValue,Is:_.definitionOperator,"EntityReference CharacterReference":_.character,Comment:_.blockComment,ProcessingInst:_.processingInstruction,DoctypeDecl:_.documentMeta}),nc=bt.deserialize({version:14,states:",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%ZQ&rO,59fO%fQ&rO,59iO%qQ&rO,59lO%|Q&rO,59nOOOa'#D^'#D^O&XOaO'#CxO&dOaO,59[OOOb'#D_'#D_O&lObO'#C{O&wObO,59[OOOd'#D`'#D`O'POdO'#DOO'[OdO,59[OOO`'#Da'#DaO'dO!rO,59[O'kQ#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'pO$fO,59oOOO`,59o,59oO'xQ#|O,59qO'}Q#|O,59rOOO`-E7W-E7WO(SQ&rO'#CsOOQW'#DZ'#DZO(bQ&rO1G.wOOOa1G.w1G.wOOO`1G/Y1G/YO(mQ&rO1G/QOOOb1G/Q1G/QO(xQ&rO1G/TOOOd1G/T1G/TO)TQ&rO1G/WOOO`1G/W1G/WO)`Q&rO1G/YOOOa-E7[-E7[O)kQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)pQ#tO'#C|OOOd-E7^-E7^O)uQ#tO'#DPOOO`-E7_-E7_O)zQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O*PQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOO`7+$t7+$tOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rO*[Q#|O,59eO*aQ#|O,59hO*fQ#|O,59kOOO`1G/X1G/XO*kO7[O'#CvO*|OMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O+_O7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+pOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z",stateData:",]~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OT}OhyO~OS!POT}OhyO~OS!ROT}OhyO~OS!TOT}OhyO~OS}OT}OhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXTgXhgX~OS!fOT!gOhyO~OS!hOT!gOhyO~OS!iOT!gOhyO~OS!jOT!gOhyO~OS!gOT!gOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~",goto:"%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{!O!Q!S!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ!ObQ!QcQ!SdQ!UeZ!e{!O!Q!S!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp",nodeNames:"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl",maxTerm:67,context:GO,nodeProps:[["closedBy",-10,1,2,3,7,8,9,10,11,12,13,"EndTag",6,"EndTag SelfClosingEndTag",-4,21,30,33,36,"CloseTag"],["openedBy",4,"StartTag StartCloseTag",5,"StartTag",-4,29,32,35,37,"OpenTag"],["group",-9,14,17,18,19,20,39,40,41,42,"Entity",16,"Entity TextContent",-3,28,31,34,"TextContent Entity"],["isolate",-11,21,29,30,32,33,35,36,37,38,41,42,"ltr",-3,26,27,39,""]],propSources:[rc],skippedNodes:[0],repeatNodeCount:9,tokenData:"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X",tokenizers:[ec,tc,ac,JO,jO,HO,0,1,2,3,4,5],topRules:{Document:[0,15]},dialects:{noMatch:0,selfClosing:509},tokenPrec:511});function Ws(e,t){let a=Object.create(null);for(let r of e.getChildren(Is)){let n=r.getChild(XO),i=r.getChild(gr)||r.getChild(Vs);n&&(a[t.read(n.from,n.to)]=i?i.type.id==gr?t.read(i.from+1,i.to-1):t.read(i.from,i.to):"")}return a}function bi(e,t){let a=e.getChild(ZO);return a?t.read(a.from,a.to):" "}function ar(e,t,a){let r;for(let n of a)if(!n.attrs||n.attrs(r||(r=Ws(e.node.parent.firstChild,t))))return{parser:n.parser};return null}function zs(e=[],t=[]){let a=[],r=[],n=[],i=[];for(let o of e)(o.tag=="script"?a:o.tag=="style"?r:o.tag=="textarea"?n:i).push(o);let s=t.length?Object.create(null):null;for(let o of t)(s[o.name]||(s[o.name]=[])).push(o);return I0((o,c)=>{let O=o.type.id;if(O==RO)return ar(o,c,a);if(O==DO)return ar(o,c,r);if(O==BO)return ar(o,c,n);if(O==Ys&&i.length){let d=o.node,g=d.firstChild,l=g&&bi(g,c),u;if(l){for(let f of i)if(f.tag==l&&(!f.attrs||f.attrs(u||(u=Ws(g,c))))){let h=d.lastChild,v=h.type.id==LO?h.from:d.to;if(v>g.to)return{parser:f.parser,overlay:[{from:g.to,to:v}]}}}}if(s&&O==Is){let d=o.node,g;if(g=d.firstChild){let l=s[c.read(g.from,g.to)];if(l)for(let u of l){if(u.tagName&&u.tagName!=bi(d.parent,c))continue;let f=d.lastChild;if(f.type.id==gr){let h=f.from+1,v=f.lastChild,Q=f.to-(v&&v.isError?0:1);if(Q>h)return{parser:u.parser,overlay:[{from:h,to:Q}]}}else if(f.type.id==Vs)return{parser:u.parser,overlay:[{from:f.from,to:f.to}]}}}}return null})}const ic=101,Pi=1,sc=102,oc=103,yi=2,Ns=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],lc=58,Oc=40,Gs=95,cc=91,Ht=45,uc=46,fc=35,dc=37,hc=38,pc=92,xc=10;function Bt(e){return e>=65&&e<=90||e>=97&&e<=122||e>=161}function js(e){return e>=48&&e<=57}const mc=new Ie((e,t)=>{for(let a=!1,r=0,n=0;;n++){let{next:i}=e;if(Bt(i)||i==Ht||i==Gs||a&&js(i))!a&&(i!=Ht||n>0)&&(a=!0),r===n&&i==Ht&&r++,e.advance();else if(i==pc&&e.peek(1)!=xc)e.advance(),e.next>-1&&e.advance(),a=!0;else{a&&e.acceptToken(i==Oc?sc:r==2&&t.canShift(yi)?yi:oc);break}}}),gc=new Ie(e=>{if(Ns.includes(e.peek(-1))){let{next:t}=e;(Bt(t)||t==Gs||t==fc||t==uc||t==cc||t==lc&&Bt(e.peek(1))||t==Ht||t==hc)&&e.acceptToken(ic)}}),vc=new Ie(e=>{if(!Ns.includes(e.peek(-1))){let{next:t}=e;if(t==dc&&(e.advance(),e.acceptToken(Pi)),Bt(t)){do e.advance();while(Bt(e.next)||js(e.next));e.acceptToken(Pi)}}}),Qc=qr({"AtKeyword import charset namespace keyframes media supports":_.definitionKeyword,"from to selector":_.keyword,NamespaceName:_.namespace,KeyframeName:_.labelName,KeyframeRangeName:_.operatorKeyword,TagName:_.tagName,ClassName:_.className,PseudoClassName:_.constant(_.className),IdName:_.labelName,"FeatureName PropertyName":_.propertyName,AttributeName:_.attributeName,NumberLiteral:_.number,KeywordQuery:_.keyword,UnaryQueryOp:_.operatorKeyword,"CallTag ValueName":_.atom,VariableName:_.variableName,Callee:_.operatorKeyword,Unit:_.unit,"UniversalSelector NestingSelector":_.definitionOperator,MatchOp:_.compareOperator,"ChildOp SiblingOp, LogicOp":_.logicOperator,BinOp:_.arithmeticOperator,Important:_.modifier,Comment:_.blockComment,ColorLiteral:_.color,"ParenthesizedContent StringLiteral":_.string,":":_.punctuation,"PseudoOp #":_.derefOperator,"; ,":_.separator,"( )":_.paren,"[ ]":_.squareBracket,"{ }":_.brace}),Sc={__proto__:null,lang:34,"nth-child":34,"nth-last-child":34,"nth-of-type":34,"nth-last-of-type":34,dir:34,"host-context":34,url:62,"url-prefix":62,domain:62,regexp:62,selector:140},bc={__proto__:null,"@import":120,"@media":144,"@charset":148,"@namespace":152,"@keyframes":158,"@supports":170},Pc={__proto__:null,not:134,only:134},yc=bt.deserialize({version:14,states:":|QYQ[OOO#_Q[OOP#fOWOOOOQP'#Cd'#CdOOQP'#Cc'#CcO#kQ[O'#CfO$[QXO'#CaO$fQ[O'#CiO$qQ[O'#DUO$vQ[O'#DXOOQP'#Eo'#EoO${QdO'#DhO%jQ[O'#DuO${QdO'#DwO%{Q[O'#DyO&WQ[O'#D|O&`Q[O'#ESO&nQ[O'#EUOOQS'#En'#EnOOQS'#EX'#EXQYQ[OOO&uQXO'#CdO'jQWO'#DdO'oQWO'#EtO'zQ[O'#EtQOQWOOP(UO#tO'#C_POOO)C@^)C@^OOQP'#Ch'#ChOOQP,59Q,59QO#kQ[O,59QO(aQ[O,59TO$qQ[O,59pO$vQ[O,59sO(lQ[O,59vO(lQ[O,59xO(lQ[O,59yO(lQ[O'#E^O)WQWO,58{O)`Q[O'#DcOOQS,58{,58{OOQP'#Cl'#ClOOQO'#DS'#DSOOQP,59T,59TO)gQWO,59TO)lQWO,59TOOQP'#DW'#DWOOQP,59p,59pOOQO'#DY'#DYO)qQ`O,59sOOQS'#Cq'#CqO${QdO'#CrO)yQvO'#CtO+ZQtO,5:SOOQO'#Cy'#CyO)lQWO'#CxO+oQWO'#CzO+tQ[O'#DPOOQS'#Eq'#EqOOQO'#Dk'#DkO+|Q[O'#DrO,[QWO'#EuO&`Q[O'#DpO,jQWO'#DsOOQO'#Ev'#EvO)ZQWO,5:aO,oQpO,5:cOOQS'#D{'#D{O,wQWO,5:eO,|Q[O,5:eOOQO'#EO'#EOO-UQWO,5:hO-ZQWO,5:nO-cQWO,5:pOOQS-E8V-E8VO-kQdO,5:OO-{Q[O'#E`O.YQWO,5;`O.YQWO,5;`POOO'#EW'#EWP.eO#tO,58yPOOO,58y,58yOOQP1G.l1G.lOOQP1G.o1G.oO)gQWO1G.oO)lQWO1G.oOOQP1G/[1G/[O.pQ`O1G/_O/ZQXO1G/bO/qQXO1G/dO0XQXO1G/eO0oQXO,5:xOOQO-E8[-E8[OOQS1G.g1G.gO0yQWO,59}O1OQ[O'#DTO1VQdO'#CpOOQP1G/_1G/_O${QdO1G/_O1^QpO,59^OOQS,59`,59`O${QdO,59bO1fQWO1G/nOOQS,59d,59dO1kQ!bO,59fOOQS'#DQ'#DQOOQS'#EZ'#EZO1vQ[O,59kOOQS,59k,59kO2OQWO'#DkO2ZQWO,5:WO2`QWO,5:^O&`Q[O,5:YO2hQ[O'#EaO3PQWO,5;aO3[QWO,5:[O(lQ[O,5:_OOQS1G/{1G/{OOQS1G/}1G/}OOQS1G0P1G0PO3mQWO1G0PO3rQdO'#EPOOQS1G0S1G0SOOQS1G0Y1G0YOOQS1G0[1G0[O3}QtO1G/jOOQO1G/j1G/jOOQO,5:z,5:zO4eQ[O,5:zOOQO-E8^-E8^O4rQWO1G0zPOOO-E8U-E8UPOOO1G.e1G.eOOQP7+$Z7+$ZOOQP7+$y7+$yO${QdO7+$yOOQS1G/i1G/iO4}QXO'#EsO5XQWO,59oO5^QtO'#EYO6UQdO'#EpO6`QWO,59[O6eQpO7+$yOOQS1G.x1G.xOOQS1G.|1G.|OOQS7+%Y7+%YOOQS1G/Q1G/QO6mQWO1G/QOOQS-E8X-E8XOOQS1G/V1G/VO${QdO1G/rOOQO1G/x1G/xOOQO1G/t1G/tO6rQWO,5:{OOQO-E8_-E8_O7QQXO1G/yOOQS7+%k7+%kO7XQYO'#CtOOQO'#ER'#ERO7dQ`O'#EQOOQO'#EQ'#EQO7oQWO'#EbO7wQdO,5:kOOQS,5:k,5:kO8SQtO'#E_O${QdO'#E_O9TQdO7+%UOOQO7+%U7+%UOOQO1G0f1G0fO9hQpO<<HeO9pQ[O'#E]O9zQWO,5;_OOQP1G/Z1G/ZOOQS-E8W-E8WO:SQdO'#E[O:^QWO,5;[OOQT1G.v1G.vOOQP<<He<<HeOOQS7+$l7+$lO:fQdO7+%^OOQO7+%e7+%eOOQO,5:l,5:lO3uQdO'#EcO7oQWO,5:|OOQS,5:|,5:|OOQS-E8`-E8`OOQS1G0V1G0VO:mQtO,5:yOOQS-E8]-E8]OOQO<<Hp<<HpOOQPAN>PAN>PO;nQXO,5:wOOQO-E8Z-E8ZO;xQdO,5:vOOQO-E8Y-E8YOOQO<<Hx<<HxOOQO,5:},5:}OOQO-E8a-E8aOOQS1G0h1G0h",stateData:"<[~O#]OS#^QQ~OUYOXYOZTO^VO_VOrXOyWO!]aO!^ZO!j[O!l]O!n^O!q_O!w`O#ZRO~OQfOUYOXYOZTO^VO_VOrXOyWO!]aO!^ZO!j[O!l]O!n^O!q_O!w`O#ZeO~O#W#hP~P!ZO#^jO~O#ZlO~OZnO^oO_oOrqOypO!PrO!StO#XsO~OuuO!UwO~P#pOa}O#YzO#ZyO~O#Z!OO~O#Z!QO~OQ![Oc!TOg![Oi![Oo!YOr!ZO#Y!WO#Z!SO#f!UO~Oc!^O!e!`O!h!aO#Z!]O!U#iP~Oi!fOo!YO#Z!eO~Oi!hO#Z!hO~Oc!^O!e!`O!h!aO#Z!]O~O!Z#iP~P%jOZWX^WX^!XX_WXrWXuWXyWX!PWX!SWX!UWX#XWX~O^!mO~O!Z!nO#W#hX!T#hX~O#W#hX!T#hX~P!ZO#_!qO#`!qO#a!sO~Oa!wO#YzO#ZyO~OUYOXYOZTO^VO_VOrXOyWO#ZRO~OuuO!UwO~O!T#hP~P!ZOc#RO~Oc#SO~Oq#TO}#UO~OP#WOchXkhX!ZhX!ehX!hhX#ZhXbhXQhXghXihXohXrhXuhX!YhX#WhX#YhX#fhXqhX!ThX~Oc!^Ok#XO!e!`O!h!aO#Z!]O!Z#iP~Oc#[O~Oq#`O#Z#]O~Oc!^O!e!`O!h!aO#Z#aO~Ou#eO!c#dO!U#iX!Z#iX~Oc#hO~Ok#XO!Z#jO~O!Z#kO~Oi#lOo!YO~O!U#mO~O!UwO!c#dO~O!UwO!Z#pO~O!Y#rO!Z!Wa#W!Wa!T!Wa~P${O!Z#SX#W#SX!T#SX~P!ZO!Z!nO#W#ha!T#ha~O#_!qO#`!qO#a#xO~Oq#zO}#{O~OZnO^oO_oOrqOypO~Ou!Oi!P!Oi!S!Oi!U!Oi#X!Oib!Oi~P.xOu!Qi!P!Qi!S!Qi!U!Qi#X!Qib!Qi~P.xOu!Ri!P!Ri!S!Ri!U!Ri#X!Rib!Ri~P.xOu#Qa!U#Qa~P#pO!T#|O~Ob#gP~P(lOb#dP~P${Ob$TOk#XO~O!Z$VO~Ob$WOi$XOp$XO~Oq$ZO#Z#]O~O^!aXb!_X!c!_X~O^$[O~Ob$]O!c#dO~Oc!^O!e!`O!h!aO#Z!]Ou#TX!U#TX!Z#TX~Ou#eO!U#ia!Z#ia~O!c#dOu!da!U!da!Z!dab!da~O!Z$bO~O!T$iO#Z$dO#f$cO~Ok#XOu$kO!Y$mO!Z!Wi#W!Wi!T!Wi~P${O!Z#Sa#W#Sa!T#Sa~P!ZO!Z!nO#W#hi!T#hi~Ou$pOb#gX~P#pOb$rO~Ok#XOQ!|Xb!|Xc!|Xg!|Xi!|Xo!|Xr!|Xu!|X#Y!|X#Z!|X#f!|X~Ou$tOb#dX~P${Ob$vO~Ok#XOq$wO~Ob$xO~O!c#dOu#Ta!U#Ta!Z#Ta~Ob$zO~P#pOP#WOuhX!UhX~O#f$cOu!tX!U!tX~Ou$|O!UwO~O!T%QO#Z$dO#f$cO~Ok#XOQ#RXc#RXg#RXi#RXo#RXr#RXu#RX!Y#RX!Z#RX#W#RX#Y#RX#Z#RX#f#RX!T#RX~Ou$kO!Y%TO!Z!Wq#W!Wq!T!Wq~P${Ok#XOq%UO~Ob#PXu#PX~P(lOu$pOb#ga~Ob#OXu#OX~P${Ou$tOb#da~Ob%ZO~P${Ok#XOQ#Rac#Rag#Rai#Rao#Rar#Rau#Ra!Y#Ra!Z#Ra#W#Ra#Y#Ra#Z#Ra#f#Ra!T#Ra~Ob#Pau#Pa~P#pOb#Oau#Oa~P${O#]p#^#fk!S#f~",goto:"-o#kPPP#lP#oP#x$YP#xP$j#xPP$pPPP$v%P%PP%cP%PP%P%}&aPPPP%P&yP&}'T#xP'Z#x'aP#xP#x#xPPP'g'|(ZPP#oPP(b(b(l(bP(bP(b(bP#oP#oP#oP(o#oP(r(u(x)P#oP#oP)U)[)k)y*P*V*]*c*i*s*y+PPPPPPPPPPP+V+`,O,RP,w,z-Q-ZRkQ_bOPdhw!n#tmYOPdhrstuw!n#R#h#t$pmSOPdhrstuw!n#R#h#t$pQmTR!tnQ{VR!uoQ!u}Q#Z!XR#y!wq![Z]!T!m#S#U#X#q#{$Q$[$k$l$t$y%Xp![Z]!T!m#S#U#X#q#{$Q$[$k$l$t$y%XU$f#m$h$|R${$eq!XZ]!T!m#S#U#X#q#{$Q$[$k$l$t$y%Xp![Z]!T!m#S#U#X#q#{$Q$[$k$l$t$y%XQ!f^R#l!gT#^!Z#_Q|VR!voQ!u|R#y!vQ!PWR!xpQ!RXR!yqQxUQ#PvQ#i!cQ#o!jQ#p!kQ%O$gR%^$}SgPwQ!phQ#s!nR$n#tZfPhw!n#ta!b[`a!V!^!`#d#eR#b!^R!g^R!i_R#n!iS$g#m$hR%[$|V$e#m$h$|Q!rjR#w!rQdOShPwU!ldh#tR#t!nQ$Q#SU$s$Q$y%XQ$y$[R%X$tQ#_!ZR$Y#_Q$u$QR%Y$uQ$q#}R%W$qQvUR#OvQ$l#qR%S$lQ!ogS#u!o#vR#v!pQ#f!_R$`#fQ$h#mR%P$hQ$}$gR%]$}_cOPdhw!n#t^UOPdhw!n#tQ!zrQ!{sQ!|tQ!}uQ#}#RQ$a#hR%V$pR$R#SQ!VZQ!d]Q#V!TQ#q!m[$P#S$Q$[$t$y%XQ$S#UQ$U#XS$j#q$lQ$o#{R%R$kR$O#RQiPR#QwQ!c[Q!kaR#Y!VU!_[a!VQ!j`Q#c!^Q#g!`Q$^#dR$_#e",nodeNames:"⚠ Unit VariableName Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee CallLiteral CallTag ParenthesizedContent ] [ LineNames LineName , PseudoClassName ArgList IdSelector # IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp } { Block Declaration PropertyName Important ; ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports AtRule Styles",maxTerm:118,nodeProps:[["isolate",-2,3,25,""],["openedBy",18,"(",33,"[",51,"{"],["closedBy",19,")",34,"]",52,"}"]],propSources:[Qc],skippedNodes:[0,3,88],repeatNodeCount:12,tokenData:"J^~R!^OX$}X^%u^p$}pq%uqr)Xrs.Rst/utu6duv$}vw7^wx7oxy9^yz9oz{9t{|:_|}?Q}!O?c!O!P@Q!P!Q@i!Q![Ab![!]B]!]!^CX!^!_$}!_!`Cj!`!aC{!a!b$}!b!cDw!c!}$}!}#OFa#O#P$}#P#QFr#Q#R6d#R#T$}#T#UGT#U#c$}#c#dHf#d#o$}#o#pH{#p#q6d#q#rI^#r#sIo#s#y$}#y#z%u#z$f$}$f$g%u$g#BY$}#BY#BZ%u#BZ$IS$}$IS$I_%u$I_$I|$}$I|$JO%u$JO$JT$}$JT$JU%u$JU$KV$}$KV$KW%u$KW&FU$}&FU&FV%u&FV;'S$};'S;=`JW<%lO$}`%QSOy%^z;'S%^;'S;=`%o<%lO%^`%cSp`Oy%^z;'S%^;'S;=`%o<%lO%^`%rP;=`<%l%^~%zh#]~OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^~'mh#]~p`OX%^X^'f^p%^pq'fqy%^z#y%^#y#z'f#z$f%^$f$g'f$g#BY%^#BY#BZ'f#BZ$IS%^$IS$I_'f$I_$I|%^$I|$JO'f$JO$JT%^$JT$JU'f$JU$KV%^$KV$KW'f$KW&FU%^&FU&FV'f&FV;'S%^;'S;=`%o<%lO%^l)[UOy%^z#]%^#]#^)n#^;'S%^;'S;=`%o<%lO%^l)sUp`Oy%^z#a%^#a#b*V#b;'S%^;'S;=`%o<%lO%^l*[Up`Oy%^z#d%^#d#e*n#e;'S%^;'S;=`%o<%lO%^l*sUp`Oy%^z#c%^#c#d+V#d;'S%^;'S;=`%o<%lO%^l+[Up`Oy%^z#f%^#f#g+n#g;'S%^;'S;=`%o<%lO%^l+sUp`Oy%^z#h%^#h#i,V#i;'S%^;'S;=`%o<%lO%^l,[Up`Oy%^z#T%^#T#U,n#U;'S%^;'S;=`%o<%lO%^l,sUp`Oy%^z#b%^#b#c-V#c;'S%^;'S;=`%o<%lO%^l-[Up`Oy%^z#h%^#h#i-n#i;'S%^;'S;=`%o<%lO%^l-uS!Y[p`Oy%^z;'S%^;'S;=`%o<%lO%^~.UWOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o<%lO.R~.sOi~~.vRO;'S.R;'S;=`/P;=`O.R~/SXOY.RZr.Rrs.ns#O.R#O#P.s#P;'S.R;'S;=`/o;=`<%l.R<%lO.R~/rP;=`<%l.Rn/zYyQOy%^z!Q%^!Q![0j![!c%^!c!i0j!i#T%^#T#Z0j#Z;'S%^;'S;=`%o<%lO%^l0oYp`Oy%^z!Q%^!Q![1_![!c%^!c!i1_!i#T%^#T#Z1_#Z;'S%^;'S;=`%o<%lO%^l1dYp`Oy%^z!Q%^!Q![2S![!c%^!c!i2S!i#T%^#T#Z2S#Z;'S%^;'S;=`%o<%lO%^l2ZYg[p`Oy%^z!Q%^!Q![2y![!c%^!c!i2y!i#T%^#T#Z2y#Z;'S%^;'S;=`%o<%lO%^l3QYg[p`Oy%^z!Q%^!Q![3p![!c%^!c!i3p!i#T%^#T#Z3p#Z;'S%^;'S;=`%o<%lO%^l3uYp`Oy%^z!Q%^!Q![4e![!c%^!c!i4e!i#T%^#T#Z4e#Z;'S%^;'S;=`%o<%lO%^l4lYg[p`Oy%^z!Q%^!Q![5[![!c%^!c!i5[!i#T%^#T#Z5[#Z;'S%^;'S;=`%o<%lO%^l5aYp`Oy%^z!Q%^!Q![6P![!c%^!c!i6P!i#T%^#T#Z6P#Z;'S%^;'S;=`%o<%lO%^l6WSg[p`Oy%^z;'S%^;'S;=`%o<%lO%^d6gUOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^d7QS}Sp`Oy%^z;'S%^;'S;=`%o<%lO%^b7cSXQOy%^z;'S%^;'S;=`%o<%lO%^~7rWOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W<%lO7o~8_RO;'S7o;'S;=`8h;=`O7o~8kXOY7oZw7owx.nx#O7o#O#P8[#P;'S7o;'S;=`9W;=`<%l7o<%lO7o~9ZP;=`<%l7on9cSc^Oy%^z;'S%^;'S;=`%o<%lO%^~9tOb~n9{UUQkWOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^n:fWkW!SQOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^l;TUp`Oy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^l;nYp`#f[Oy%^z!Q%^!Q![;g![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^l<cYp`Oy%^z{%^{|=R|}%^}!O=R!O!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=WUp`Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l=qUp`#f[Oy%^z!Q%^!Q![=j![;'S%^;'S;=`%o<%lO%^l>[[p`#f[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^n?VSu^Oy%^z;'S%^;'S;=`%o<%lO%^l?hWkWOy%^z!O%^!O!P;O!P!Q%^!Q![>T![;'S%^;'S;=`%o<%lO%^n@VUZQOy%^z!Q%^!Q![;g![;'S%^;'S;=`%o<%lO%^~@nTkWOy%^z{@}{;'S%^;'S;=`%o<%lO%^~AUSp`#^~Oy%^z;'S%^;'S;=`%o<%lO%^lAg[#f[Oy%^z!O%^!O!P;g!P!Q%^!Q![>T![!g%^!g!h<^!h#X%^#X#Y<^#Y;'S%^;'S;=`%o<%lO%^bBbU^QOy%^z![%^![!]Bt!];'S%^;'S;=`%o<%lO%^bB{S_Qp`Oy%^z;'S%^;'S;=`%o<%lO%^nC^S!Z^Oy%^z;'S%^;'S;=`%o<%lO%^dCoS}SOy%^z;'S%^;'S;=`%o<%lO%^bDQU!PQOy%^z!`%^!`!aDd!a;'S%^;'S;=`%o<%lO%^bDkS!PQp`Oy%^z;'S%^;'S;=`%o<%lO%^bDzWOy%^z!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^bEk[!]Qp`Oy%^z}%^}!OEd!O!Q%^!Q![Ed![!c%^!c!}Ed!}#T%^#T#oEd#o;'S%^;'S;=`%o<%lO%^nFfSr^Oy%^z;'S%^;'S;=`%o<%lO%^nFwSq^Oy%^z;'S%^;'S;=`%o<%lO%^bGWUOy%^z#b%^#b#cGj#c;'S%^;'S;=`%o<%lO%^bGoUp`Oy%^z#W%^#W#XHR#X;'S%^;'S;=`%o<%lO%^bHYS!cQp`Oy%^z;'S%^;'S;=`%o<%lO%^bHiUOy%^z#f%^#f#gHR#g;'S%^;'S;=`%o<%lO%^fIQS!UUOy%^z;'S%^;'S;=`%o<%lO%^nIcS!T^Oy%^z;'S%^;'S;=`%o<%lO%^fItU!SQOy%^z!_%^!_!`6y!`;'S%^;'S;=`%o<%lO%^`JZP;=`<%l$}",tokenizers:[gc,vc,mc,1,2,3,4,new na("m~RRYZ[z{a~~g~aO#`~~dP!P!Qg~lO#a~~",28,107)],topRules:{StyleSheet:[0,4],Styles:[1,87]},specialized:[{term:102,get:e=>Sc[e]||-1},{term:59,get:e=>bc[e]||-1},{term:103,get:e=>Pc[e]||-1}],tokenPrec:1246});let rr=null;function nr(){if(!rr&&typeof document=="object"&&document.body){let{style:e}=document.body,t=[],a=new Set;for(let r in e)r!="cssText"&&r!="cssFloat"&&typeof e[r]=="string"&&(/[A-Z]/.test(r)&&(r=r.replace(/[A-Z]/g,n=>"-"+n.toLowerCase())),a.has(r)||(t.push(r),a.add(r)));rr=t.sort().map(r=>({type:"property",label:r,apply:r+": "}))}return rr||[]}const $i=["active","after","any-link","autofill","backdrop","before","checked","cue","default","defined","disabled","empty","enabled","file-selector-button","first","first-child","first-letter","first-line","first-of-type","focus","focus-visible","focus-within","fullscreen","has","host","host-context","hover","in-range","indeterminate","invalid","is","lang","last-child","last-of-type","left","link","marker","modal","not","nth-child","nth-last-child","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","part","placeholder","placeholder-shown","read-only","read-write","required","right","root","scope","selection","slotted","target","target-text","valid","visited","where"].map(e=>({type:"class",label:e})),wi=["above","absolute","activeborder","additive","activecaption","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","antialiased","appworkspace","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic-abegede-gez","ethiopic-halehame-aa-er","ethiopic-halehame-gez","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","graytext","grid","groove","hand","hard-light","help","hidden","hide","higher","highlight","highlighttext","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","justify","keep-all","landscape","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-hexadecimal","lower-latin","lower-norwegian","lowercase","ltr","luminosity","manipulation","match","matrix","matrix3d","medium","menu","menutext","message-box","middle","min-intrinsic","mix","monospace","move","multiple","multiple_mask_images","multiply","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","opacity","open-quote","optimizeLegibility","optimizeSpeed","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","text","text-bottom","text-top","textarea","textfield","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","to","top","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-latin","uppercase","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"].map(e=>({type:"keyword",label:e})).concat(["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"].map(e=>({type:"constant",label:e}))),$c=["a","abbr","address","article","aside","b","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","dd","del","details","dfn","dialog","div","dl","dt","em","figcaption","figure","footer","form","header","hgroup","h1","h2","h3","h4","h5","h6","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","main","meter","nav","ol","output","p","pre","ruby","section","select","small","source","span","strong","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","tr","u","ul"].map(e=>({type:"type",label:e})),wc=["@charset","@color-profile","@container","@counter-style","@font-face","@font-feature-values","@font-palette-values","@import","@keyframes","@layer","@media","@namespace","@page","@position-try","@property","@scope","@starting-style","@supports","@view-transition"].map(e=>({type:"keyword",label:e})),He=/^(\w[\w-]*|-\w[\w-]*|)$/,_c=/^-(-[\w-]*)?$/;function kc(e,t){var a;if((e.name=="("||e.type.isError)&&(e=e.parent||e),e.name!="ArgList")return!1;let r=(a=e.parent)===null||a===void 0?void 0:a.firstChild;return(r==null?void 0:r.name)!="Callee"?!1:t.sliceString(r.from,r.to)=="var"}const _i=new ys,Cc=["Declaration"];function Ec(e){for(let t=e;;){if(t.type.isTop)return t;if(!(t=t.parent))return e}}function Hs(e,t,a){if(t.to-t.from>4096){let r=_i.get(t);if(r)return r;let n=[],i=new Set,s=t.cursor(Br.IncludeAnonymous);if(s.firstChild())do for(let o of Hs(e,s.node,a))i.has(o.label)||(i.add(o.label),n.push(o));while(s.nextSibling());return _i.set(t,n),n}else{let r=[],n=new Set;return t.cursor().iterate(i=>{var s;if(a(i)&&i.matchContext(Cc)&&((s=i.node.nextSibling)===null||s===void 0?void 0:s.name)==":"){let o=e.sliceString(i.from,i.to);n.has(o)||(n.add(o),r.push({label:o,type:"variable"}))}}),r}}const Ac=e=>t=>{let{state:a,pos:r}=t,n=It(a).resolveInner(r,-1),i=n.type.isError&&n.from==n.to-1&&a.doc.sliceString(n.from,n.to)=="-";if(n.name=="PropertyName"||(i||n.name=="TagName")&&/^(Block|Styles)$/.test(n.resolve(n.to).name))return{from:n.from,options:nr(),validFor:He};if(n.name=="ValueName")return{from:n.from,options:wi,validFor:He};if(n.name=="PseudoClassName")return{from:n.from,options:$i,validFor:He};if(e(n)||(t.explicit||i)&&kc(n,a.doc))return{from:e(n)||i?n.from:r,options:Hs(a.doc,Ec(n),e),validFor:_c};if(n.name=="TagName"){for(let{parent:c}=n;c;c=c.parent)if(c.name=="Block")return{from:n.from,options:nr(),validFor:He};return{from:n.from,options:$c,validFor:He}}if(n.name=="AtKeyword")return{from:n.from,options:wc,validFor:He};if(!t.explicit)return null;let s=n.resolve(r),o=s.childBefore(r);return o&&o.name==":"&&s.name=="PseudoClassSelector"?{from:r,options:$i,validFor:He}:o&&o.name==":"&&s.name=="Declaration"||s.name=="ArgList"?{from:r,options:wi,validFor:He}:s.name=="Block"||s.name=="Styles"?{from:r,options:nr(),validFor:He}:null},Tc=Ac(e=>e.name=="VariableName"),oa=Lr.define({name:"css",parser:yc.configure({props:[Fr.add({Declaration:Nt()}),Yr.add({"Block KeyframeList":Ps})]}),languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:"-"}});function Zc(){return new Ir(oa,oa.data.of({autocomplete:Tc}))}const Xc=315,Rc=316,ki=1,Dc=2,Bc=3,qc=4,Lc=317,Fc=319,Yc=320,Ic=5,Vc=6,Uc=0,Qr=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],Ks=125,Mc=59,Sr=47,Wc=42,zc=43,Nc=45,Gc=60,jc=44,Hc=63,Kc=46,Jc=91,eu=new Ds({start:!1,shift(e,t){return t==Ic||t==Vc||t==Fc?e:t==Yc},strict:!1}),tu=new Ie((e,t)=>{let{next:a}=e;(a==Ks||a==-1||t.context)&&e.acceptToken(Lc)},{contextual:!0,fallback:!0}),au=new Ie((e,t)=>{let{next:a}=e,r;Qr.indexOf(a)>-1||a==Sr&&((r=e.peek(1))==Sr||r==Wc)||a!=Ks&&a!=Mc&&a!=-1&&!t.context&&e.acceptToken(Xc)},{contextual:!0}),ru=new Ie((e,t)=>{e.next==Jc&&!t.context&&e.acceptToken(Rc)},{contextual:!0}),nu=new Ie((e,t)=>{let{next:a}=e;if(a==zc||a==Nc){if(e.advance(),a==e.next){e.advance();let r=!t.context&&t.canShift(ki);e.acceptToken(r?ki:Dc)}}else a==Hc&&e.peek(1)==Kc&&(e.advance(),e.advance(),(e.next<48||e.next>57)&&e.acceptToken(Bc))},{contextual:!0});function ir(e,t){return e>=65&&e<=90||e>=97&&e<=122||e==95||e>=192||!t&&e>=48&&e<=57}const iu=new Ie((e,t)=>{if(e.next!=Gc||!t.dialectEnabled(Uc)||(e.advance(),e.next==Sr))return;let a=0;for(;Qr.indexOf(e.next)>-1;)e.advance(),a++;if(ir(e.next,!0)){for(e.advance(),a++;ir(e.next,!1);)e.advance(),a++;for(;Qr.indexOf(e.next)>-1;)e.advance(),a++;if(e.next==jc)return;for(let r=0;;r++){if(r==7){if(!ir(e.next,!0))return;break}if(e.next!="extends".charCodeAt(r))break;e.advance(),a++}}e.acceptToken(qc,-a)}),su=qr({"get set async static":_.modifier,"for while do if else switch try catch finally return throw break continue default case":_.controlKeyword,"in of await yield void typeof delete instanceof as satisfies":_.operatorKeyword,"let var const using function class extends":_.definitionKeyword,"import export from":_.moduleKeyword,"with debugger new":_.keyword,TemplateString:_.special(_.string),super:_.atom,BooleanLiteral:_.bool,this:_.self,null:_.null,Star:_.modifier,VariableName:_.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":_.function(_.variableName),VariableDefinition:_.definition(_.variableName),Label:_.labelName,PropertyName:_.propertyName,PrivatePropertyName:_.special(_.propertyName),"CallExpression/MemberExpression/PropertyName":_.function(_.propertyName),"FunctionDeclaration/VariableDefinition":_.function(_.definition(_.variableName)),"ClassDeclaration/VariableDefinition":_.definition(_.className),"NewExpression/VariableName":_.className,PropertyDefinition:_.definition(_.propertyName),PrivatePropertyDefinition:_.definition(_.special(_.propertyName)),UpdateOp:_.updateOperator,"LineComment Hashbang":_.lineComment,BlockComment:_.blockComment,Number:_.number,String:_.string,Escape:_.escape,ArithOp:_.arithmeticOperator,LogicOp:_.logicOperator,BitOp:_.bitwiseOperator,CompareOp:_.compareOperator,RegExp:_.regexp,Equals:_.definitionOperator,Arrow:_.function(_.punctuation),": Spread":_.punctuation,"( )":_.paren,"[ ]":_.squareBracket,"{ }":_.brace,"InterpolationStart InterpolationEnd":_.special(_.brace),".":_.derefOperator,", ;":_.separator,"@":_.meta,TypeName:_.typeName,TypeDefinition:_.definition(_.typeName),"type enum interface implements namespace module declare":_.definitionKeyword,"abstract global Privacy readonly override":_.modifier,"is keyof unique infer asserts":_.operatorKeyword,JSXAttributeValue:_.attributeValue,JSXText:_.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":_.angleBracket,"JSXIdentifier JSXNameSpacedName":_.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":_.attributeName,"JSXBuiltin/JSXIdentifier":_.standard(_.tagName)}),ou={__proto__:null,export:20,as:25,from:33,default:36,async:41,function:42,in:52,out:55,const:56,extends:60,this:64,true:72,false:72,null:84,void:88,typeof:92,super:108,new:142,delete:154,yield:163,await:167,class:172,public:235,private:235,protected:235,readonly:237,instanceof:256,satisfies:259,import:292,keyof:349,unique:353,infer:359,asserts:395,is:397,abstract:417,implements:419,type:421,let:424,var:426,using:429,interface:435,enum:439,namespace:445,module:447,declare:451,global:455,for:474,of:483,while:486,with:490,do:494,if:498,else:500,switch:504,case:510,try:516,catch:520,finally:524,return:528,throw:532,break:536,continue:540,debugger:544},lu={__proto__:null,async:129,get:131,set:133,declare:195,public:197,private:197,protected:197,static:199,abstract:201,override:203,readonly:209,accessor:211,new:401},Ou={__proto__:null,"<":193},cu=bt.deserialize({version:14,states:"$EOQ%TQlOOO%[QlOOO'_QpOOP(lO`OOO*zQ!0MxO'#CiO+RO#tO'#CjO+aO&jO'#CjO+oO#@ItO'#DaO.QQlO'#DgO.bQlO'#DrO%[QlO'#DzO0fQlO'#ESOOQ!0Lf'#E['#E[O1PQ`O'#EXOOQO'#Ep'#EpOOQO'#Ik'#IkO1XQ`O'#GsO1dQ`O'#EoO1iQ`O'#EoO3hQ!0MxO'#JqO6[Q!0MxO'#JrO6uQ`O'#F]O6zQ,UO'#FtOOQ!0Lf'#Ff'#FfO7VO7dO'#FfO7eQMhO'#F|O9[Q`O'#F{OOQ!0Lf'#Jr'#JrOOQ!0Lb'#Jq'#JqO9aQ`O'#GwOOQ['#K^'#K^O9lQ`O'#IXO9qQ!0LrO'#IYOOQ['#J_'#J_OOQ['#I^'#I^Q`QlOOQ`QlOOO9yQ!L^O'#DvO:QQlO'#EOO:XQlO'#EQO9gQ`O'#GsO:`QMhO'#CoO:nQ`O'#EnO:yQ`O'#EyO;OQMhO'#FeO;mQ`O'#GsOOQO'#K_'#K_O;rQ`O'#K_O<QQ`O'#G{O<QQ`O'#G|O<QQ`O'#HOO9gQ`O'#HRO<wQ`O'#HUO>`Q`O'#CeO>pQ`O'#HbO>xQ`O'#HhO>xQ`O'#HjO`QlO'#HlO>xQ`O'#HnO>xQ`O'#HqO>}Q`O'#HwO?SQ!0LsO'#H}O%[QlO'#IPO?_Q!0LsO'#IRO?jQ!0LsO'#ITO9qQ!0LrO'#IVO?uQ!0MxO'#CiO@wQpO'#DlQOQ`OOO%[QlO'#EQOA_Q`O'#ETO:`QMhO'#EnOAjQ`O'#EnOAuQ!bO'#FeOOQ['#Cg'#CgOOQ!0Lb'#Dq'#DqOOQ!0Lb'#Ju'#JuO%[QlO'#JuOOQO'#Jx'#JxOOQO'#Ig'#IgOBuQpO'#EgOOQ!0Lb'#Ef'#EfOOQ!0Lb'#J|'#J|OCqQ!0MSO'#EgOC{QpO'#EWOOQO'#Jw'#JwODaQpO'#JxOEnQpO'#EWOC{QpO'#EgPE{O&2DjO'#CbPOOO)CD|)CD|OOOO'#I_'#I_OFWO#tO,59UOOQ!0Lh,59U,59UOOOO'#I`'#I`OFfO&jO,59UOFtQ!L^O'#DcOOOO'#Ib'#IbOF{O#@ItO,59{OOQ!0Lf,59{,59{OGZQlO'#IcOGnQ`O'#JsOImQ!fO'#JsO+}QlO'#JsOItQ`O,5:ROJ[Q`O'#EpOJiQ`O'#KSOJtQ`O'#KROJtQ`O'#KROJ|Q`O,5;^OKRQ`O'#KQOOQ!0Ln,5:^,5:^OKYQlO,5:^OMWQ!0MxO,5:fOMwQ`O,5:nONbQ!0LrO'#KPONiQ`O'#KOO9aQ`O'#KOON}Q`O'#KOO! VQ`O,5;]O! [Q`O'#KOO!#aQ!fO'#JrOOQ!0Lh'#Ci'#CiO%[QlO'#ESO!$PQ!fO,5:sOOQS'#Jy'#JyOOQO-E<i-E<iO9gQ`O,5=_O!$gQ`O,5=_O!$lQlO,5;ZO!&oQMhO'#EkO!(YQ`O,5;ZO!(_QlO'#DyO!(iQpO,5;dO!(qQpO,5;dO%[QlO,5;dOOQ['#FT'#FTOOQ['#FV'#FVO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eOOQ['#FZ'#FZO!)PQlO,5;tOOQ!0Lf,5;y,5;yOOQ!0Lf,5;z,5;zOOQ!0Lf,5;|,5;|O%[QlO'#IoO!+SQ!0LrO,5<iO%[QlO,5;eO!&oQMhO,5;eO!+qQMhO,5;eO!-cQMhO'#E^O%[QlO,5;wOOQ!0Lf,5;{,5;{O!-jQ,UO'#FjO!.gQ,UO'#KWO!.RQ,UO'#KWO!.nQ,UO'#KWOOQO'#KW'#KWO!/SQ,UO,5<SOOOW,5<`,5<`O!/eQlO'#FvOOOW'#In'#InO7VO7dO,5<QO!/lQ,UO'#FxOOQ!0Lf,5<Q,5<QO!0]Q$IUO'#CyOOQ!0Lh'#C}'#C}O!0pO#@ItO'#DRO!1^QMjO,5<eO!1eQ`O,5<hO!3QQ(CWO'#GXO!3_Q`O'#GYO!3dQ`O'#GYO!5SQ(CWO'#G^O!6XQpO'#GbOOQO'#Gn'#GnO!+xQMhO'#GmOOQO'#Gp'#GpO!+xQMhO'#GoO!6zQ$IUO'#JkOOQ!0Lh'#Jk'#JkO!7UQ`O'#JjO!7dQ`O'#JiO!7lQ`O'#CuOOQ!0Lh'#C{'#C{O!7}Q`O'#C}OOQ!0Lh'#DV'#DVOOQ!0Lh'#DX'#DXO1SQ`O'#DZO!+xQMhO'#GPO!+xQMhO'#GRO!8SQ`O'#GTO!8XQ`O'#GUO!3dQ`O'#G[O!+xQMhO'#GaO<QQ`O'#JjO!8^Q`O'#EqO!8{Q`O,5<gOOQ!0Lb'#Cr'#CrO!9TQ`O'#ErO!9}QpO'#EsOOQ!0Lb'#KQ'#KQO!:UQ!0LrO'#K`O9qQ!0LrO,5=cO`QlO,5>sOOQ['#Jg'#JgOOQ[,5>t,5>tOOQ[-E<[-E<[O!<TQ!0MxO,5:bO!9xQpO,5:`O!>nQ!0MxO,5:jO%[QlO,5:jO!AUQ!0MxO,5:lOOQO,5@y,5@yO!AuQMhO,5=_O!BTQ!0LrO'#JhO9[Q`O'#JhO!BfQ!0LrO,59ZO!BqQpO,59ZO!ByQMhO,59ZO:`QMhO,59ZO!CUQ`O,5;ZO!C^Q`O'#HaO!CrQ`O'#KcO%[QlO,5;}O!9xQpO,5<PO!CzQ`O,5=zO!DPQ`O,5=zO!DUQ`O,5=zO9qQ!0LrO,5=zO<QQ`O,5=jOOQO'#Cy'#CyO!DdQpO,5=gO!DlQMhO,5=hO!DwQ`O,5=jO!D|Q!bO,5=mO!EUQ`O'#K_O>}Q`O'#HWO9gQ`O'#HYO!EZQ`O'#HYO:`QMhO'#H[O!E`Q`O'#H[OOQ[,5=p,5=pO!EeQ`O'#H]O!EvQ`O'#CoO!E{Q`O,59PO!FVQ`O,59PO!H[QlO,59POOQ[,59P,59PO!HlQ!0LrO,59PO%[QlO,59PO!JwQlO'#HdOOQ['#He'#HeOOQ['#Hf'#HfO`QlO,5=|O!K_Q`O,5=|O`QlO,5>SO`QlO,5>UO!KdQ`O,5>WO`QlO,5>YO!KiQ`O,5>]O!KnQlO,5>cOOQ[,5>i,5>iO%[QlO,5>iO9qQ!0LrO,5>kOOQ[,5>m,5>mO# xQ`O,5>mOOQ[,5>o,5>oO# xQ`O,5>oOOQ[,5>q,5>qO#!fQpO'#D_O%[QlO'#JuO##XQpO'#JuO##cQpO'#DmO##tQpO'#DmO#&VQlO'#DmO#&^Q`O'#JtO#&fQ`O,5:WO#&kQ`O'#EtO#&yQ`O'#KTO#'RQ`O,5;_O#'WQpO'#DmO#'eQpO'#EVOOQ!0Lf,5:o,5:oO%[QlO,5:oO#'lQ`O,5:oO>}Q`O,5;YO!BqQpO,5;YO!ByQMhO,5;YO:`QMhO,5;YO#'tQ`O,5@aO#'yQ07dO,5:sOOQO-E<e-E<eO#)PQ!0MSO,5;ROC{QpO,5:rO#)ZQpO,5:rOC{QpO,5;RO!BfQ!0LrO,5:rOOQ!0Lb'#Ej'#EjOOQO,5;R,5;RO%[QlO,5;RO#)hQ!0LrO,5;RO#)sQ!0LrO,5;RO!BqQpO,5:rOOQO,5;X,5;XO#*RQ!0LrO,5;RPOOO'#I]'#I]P#*gO&2DjO,58|POOO,58|,58|OOOO-E<]-E<]OOQ!0Lh1G.p1G.pOOOO-E<^-E<^OOOO,59},59}O#*rQ!bO,59}OOOO-E<`-E<`OOQ!0Lf1G/g1G/gO#*wQ!fO,5>}O+}QlO,5>}OOQO,5?T,5?TO#+RQlO'#IcOOQO-E<a-E<aO#+`Q`O,5@_O#+hQ!fO,5@_O#+oQ`O,5@mOOQ!0Lf1G/m1G/mO%[QlO,5@nO#+wQ`O'#IiOOQO-E<g-E<gO#+oQ`O,5@mOOQ!0Lb1G0x1G0xOOQ!0Ln1G/x1G/xOOQ!0Ln1G0Y1G0YO%[QlO,5@kO#,]Q!0LrO,5@kO#,nQ!0LrO,5@kO#,uQ`O,5@jO9aQ`O,5@jO#,}Q`O,5@jO#-]Q`O'#IlO#,uQ`O,5@jOOQ!0Lb1G0w1G0wO!(iQpO,5:uO!(tQpO,5:uOOQS,5:w,5:wO#-}QdO,5:wO#.VQMhO1G2yO9gQ`O1G2yOOQ!0Lf1G0u1G0uO#.eQ!0MxO1G0uO#/jQ!0MvO,5;VOOQ!0Lh'#GW'#GWO#0WQ!0MzO'#JkO!$lQlO1G0uO#2cQ!fO'#JvO%[QlO'#JvO#2mQ`O,5:eOOQ!0Lh'#D_'#D_OOQ!0Lf1G1O1G1OO%[QlO1G1OOOQ!0Lf1G1f1G1fO#2rQ`O1G1OO#5WQ!0MxO1G1PO#5_Q!0MxO1G1PO#7uQ!0MxO1G1PO#7|Q!0MxO1G1PO#:dQ!0MxO1G1PO#<zQ!0MxO1G1PO#=RQ!0MxO1G1PO#=YQ!0MxO1G1PO#?pQ!0MxO1G1PO#?wQ!0MxO1G1PO#BUQ?MtO'#CiO#DPQ?MtO1G1`O#DWQ?MtO'#JrO#DkQ!0MxO,5?ZOOQ!0Lb-E<m-E<mO#FxQ!0MxO1G1PO#GuQ!0MzO1G1POOQ!0Lf1G1P1G1PO#HxQMjO'#J{O#ISQ`O,5:xO#IXQ!0MxO1G1cO#I{Q,UO,5<WO#JTQ,UO,5<XO#J]Q,UO'#FoO#JtQ`O'#FnOOQO'#KX'#KXOOQO'#Im'#ImO#JyQ,UO1G1nOOQ!0Lf1G1n1G1nOOOW1G1y1G1yO#K[Q?MtO'#JqO#KfQ`O,5<bO!)PQlO,5<bOOOW-E<l-E<lOOQ!0Lf1G1l1G1lO#KkQpO'#KWOOQ!0Lf,5<d,5<dO#KsQpO,5<dO#KxQMhO'#DTOOOO'#Ia'#IaO#LPO#@ItO,59mOOQ!0Lh,59m,59mO%[QlO1G2PO!8XQ`O'#IqO#L[Q`O,5<zOOQ!0Lh,5<w,5<wO!+xQMhO'#ItO#LxQMjO,5=XO!+xQMhO'#IvO#MkQMjO,5=ZO!&oQMhO,5=]OOQO1G2S1G2SO#MuQ!dO'#CrO#NYQ(CWO'#ErO$ _QpO'#GbO$ uQ!dO,5<sO$ |Q`O'#KZO9aQ`O'#KZO$![Q`O,5<uO!+xQMhO,5<tO$!aQ`O'#GZO$!rQ`O,5<tO$!wQ!dO'#GWO$#UQ!dO'#K[O$#`Q`O'#K[O!&oQMhO'#K[O$#eQ`O,5<xO$#jQlO'#JuO$#tQpO'#GcO##tQpO'#GcO$$VQ`O'#GgO!3dQ`O'#GkO$$[Q!0LrO'#IsO$$gQpO,5<|OOQ!0Lp,5<|,5<|O$$nQpO'#GcO$${QpO'#GdO$%^QpO'#GdO$%cQMjO,5=XO$%sQMjO,5=ZOOQ!0Lh,5=^,5=^O!+xQMhO,5@UO!+xQMhO,5@UO$&TQ`O'#IxO$&iQ`O,5@TO$&qQ`O,59aOOQ!0Lh,59i,59iO$'hQ$IYO,59uOOQ!0Lh'#Jo'#JoO$(ZQMjO,5<kO$(|QMjO,5<mO@oQ`O,5<oOOQ!0Lh,5<p,5<pO$)WQ`O,5<vO$)]QMjO,5<{O$)mQ`O,5@UO$){Q`O'#KOO!$lQlO1G2RO$*QQ`O1G2RO9aQ`O'#KRO9aQ`O'#EtO%[QlO'#EtO9aQ`O'#IzO$*VQ!0LrO,5@zOOQ[1G2}1G2}OOQ[1G4_1G4_OOQ!0Lf1G/|1G/|OOQ!0Lf1G/z1G/zO$,XQ!0MxO1G0UOOQ[1G2y1G2yO!&oQMhO1G2yO%[QlO1G2yO#.YQ`O1G2yO$.]QMhO'#EkOOQ!0Lb,5@S,5@SO$.jQ!0LrO,5@SOOQ[1G.u1G.uO!BfQ!0LrO1G.uO!BqQpO1G.uO!ByQMhO1G.uO$.{Q`O1G0uO$/QQ`O'#CiO$/]Q`O'#KdO$/eQ`O,5={O$/jQ`O'#KdO$/oQ`O'#KdO$/}Q`O'#JQO$0]Q`O,5@}O$0eQ!fO1G1iOOQ!0Lf1G1k1G1kO9gQ`O1G3fO@oQ`O1G3fO$0lQ`O1G3fO$0qQ`O1G3fOOQ[1G3f1G3fO!DwQ`O1G3UO!&oQMhO1G3RO$0vQ`O1G3ROOQ[1G3S1G3SO!&oQMhO1G3SO$0{Q`O1G3SO$1TQpO'#HQOOQ[1G3U1G3UO!6SQpO'#I|O!D|Q!bO1G3XOOQ[1G3X1G3XOOQ[,5=r,5=rO$1]QMhO,5=tO9gQ`O,5=tO$$VQ`O,5=vO9[Q`O,5=vO!BqQpO,5=vO!ByQMhO,5=vO:`QMhO,5=vO$1kQ`O'#KbO$1vQ`O,5=wOOQ[1G.k1G.kO$1{Q!0LrO1G.kO@oQ`O1G.kO$2WQ`O1G.kO9qQ!0LrO1G.kO$4`Q!fO,5APO$4mQ`O,5APO9aQ`O,5APO$4xQlO,5>OO$5PQ`O,5>OOOQ[1G3h1G3hO`QlO1G3hOOQ[1G3n1G3nOOQ[1G3p1G3pO>xQ`O1G3rO$5UQlO1G3tO$9YQlO'#HsOOQ[1G3w1G3wO$9gQ`O'#HyO>}Q`O'#H{OOQ[1G3}1G3}O$9oQlO1G3}O9qQ!0LrO1G4TOOQ[1G4V1G4VOOQ!0Lb'#G_'#G_O9qQ!0LrO1G4XO9qQ!0LrO1G4ZO$=vQ`O,5@aO!)PQlO,5;`O9aQ`O,5;`O>}Q`O,5:XO!)PQlO,5:XO!BqQpO,5:XO$={Q?MtO,5:XOOQO,5;`,5;`O$>VQpO'#IdO$>mQ`O,5@`OOQ!0Lf1G/r1G/rO$>uQpO'#IjO$?PQ`O,5@oOOQ!0Lb1G0y1G0yO##tQpO,5:XOOQO'#If'#IfO$?XQpO,5:qOOQ!0Ln,5:q,5:qO#'oQ`O1G0ZOOQ!0Lf1G0Z1G0ZO%[QlO1G0ZOOQ!0Lf1G0t1G0tO>}Q`O1G0tO!BqQpO1G0tO!ByQMhO1G0tOOQ!0Lb1G5{1G5{O!BfQ!0LrO1G0^OOQO1G0m1G0mO%[QlO1G0mO$?`Q!0LrO1G0mO$?kQ!0LrO1G0mO!BqQpO1G0^OC{QpO1G0^O$?yQ!0LrO1G0mOOQO1G0^1G0^O$@_Q!0MxO1G0mPOOO-E<Z-E<ZPOOO1G.h1G.hOOOO1G/i1G/iO$@iQ!bO,5<iO$@qQ!fO1G4iOOQO1G4o1G4oO%[QlO,5>}O$@{Q`O1G5yO$ATQ`O1G6XO$A]Q!fO1G6YO9aQ`O,5?TO$AgQ!0MxO1G6VO%[QlO1G6VO$AwQ!0LrO1G6VO$BYQ`O1G6UO$BYQ`O1G6UO9aQ`O1G6UO$BbQ`O,5?WO9aQ`O,5?WOOQO,5?W,5?WO$BvQ`O,5?WO$){Q`O,5?WOOQO-E<j-E<jOOQS1G0a1G0aOOQS1G0c1G0cO#.QQ`O1G0cOOQ[7+(e7+(eO!&oQMhO7+(eO%[QlO7+(eO$CUQ`O7+(eO$CaQMhO7+(eO$CoQ!0MzO,5=XO$EzQ!0MzO,5=ZO$HVQ!0MzO,5=XO$JhQ!0MzO,5=ZO$LyQ!0MzO,59uO% OQ!0MzO,5<kO%#ZQ!0MzO,5<mO%%fQ!0MzO,5<{OOQ!0Lf7+&a7+&aO%'wQ!0MxO7+&aO%(kQlO'#IeO%(xQ`O,5@bO%)QQ!fO,5@bOOQ!0Lf1G0P1G0PO%)[Q`O7+&jOOQ!0Lf7+&j7+&jO%)aQ?MtO,5:fO%[QlO7+&zO%)kQ?MtO,5:bO%)xQ?MtO,5:jO%*SQ?MtO,5:lO%*^QMhO'#IhO%*hQ`O,5@gOOQ!0Lh1G0d1G0dOOQO1G1r1G1rOOQO1G1s1G1sO%*pQ!jO,5<ZO!)PQlO,5<YOOQO-E<k-E<kOOQ!0Lf7+'Y7+'YOOOW7+'e7+'eOOOW1G1|1G1|O%*{Q`O1G1|OOQ!0Lf1G2O1G2OOOOO,59o,59oO%+QQ!dO,59oOOOO-E<_-E<_OOQ!0Lh1G/X1G/XO%+XQ!0MxO7+'kOOQ!0Lh,5?],5?]O%+{QMhO1G2fP%,SQ`O'#IqPOQ!0Lh-E<o-E<oO%,pQMjO,5?`OOQ!0Lh-E<r-E<rO%-cQMjO,5?bOOQ!0Lh-E<t-E<tO%-mQ!dO1G2wO%-tQ!dO'#CrO%.[QMhO'#KRO$#jQlO'#JuOOQ!0Lh1G2_1G2_O%.cQ`O'#IpO%.wQ`O,5@uO%.wQ`O,5@uO%/PQ`O,5@uO%/[Q`O,5@uOOQO1G2a1G2aO%/jQMjO1G2`O!+xQMhO1G2`O%/zQ(CWO'#IrO%0XQ`O,5@vO!&oQMhO,5@vO%0aQ!dO,5@vOOQ!0Lh1G2d1G2dO%2qQ!fO'#CiO%2{Q`O,5=POOQ!0Lb,5<},5<}O%3TQpO,5<}OOQ!0Lb,5=O,5=OOClQ`O,5<}O%3`QpO,5<}OOQ!0Lb,5=R,5=RO$){Q`O,5=VOOQO,5?_,5?_OOQO-E<q-E<qOOQ!0Lp1G2h1G2hO##tQpO,5<}O$#jQlO,5=PO%3nQ`O,5=OO%3yQpO,5=OO!+xQMhO'#ItO%4sQMjO1G2sO!+xQMhO'#IvO%5fQMjO1G2uO%5pQMjO1G5pO%5zQMjO1G5pOOQO,5?d,5?dOOQO-E<v-E<vOOQO1G.{1G.{O!9xQpO,59wO%[QlO,59wOOQ!0Lh,5<j,5<jO%6XQ`O1G2ZO!+xQMhO1G2bO!+xQMhO1G5pO!+xQMhO1G5pO%6^Q!0MxO7+'mOOQ!0Lf7+'m7+'mO!$lQlO7+'mO%7QQ`O,5;`OOQ!0Lb,5?f,5?fOOQ!0Lb-E<x-E<xO%7VQ!dO'#K]O#'oQ`O7+(eO4UQ!fO7+(eO$CXQ`O7+(eO%7aQ!0MvO'#CiO%7tQ!0MvO,5=SO%8fQ`O,5=SO%8nQ`O,5=SOOQ!0Lb1G5n1G5nOOQ[7+$a7+$aO!BfQ!0LrO7+$aO!BqQpO7+$aO!$lQlO7+&aO%8sQ`O'#JPO%9[Q`O,5AOOOQO1G3g1G3gO9gQ`O,5AOO%9[Q`O,5AOO%9dQ`O,5AOOOQO,5?l,5?lOOQO-E=O-E=OOOQ!0Lf7+'T7+'TO%9iQ`O7+)QO9qQ!0LrO7+)QO9gQ`O7+)QO@oQ`O7+)QOOQ[7+(p7+(pO%9nQ!0MvO7+(mO!&oQMhO7+(mO!DrQ`O7+(nOOQ[7+(n7+(nO!&oQMhO7+(nO%9xQ`O'#KaO%:TQ`O,5=lOOQO,5?h,5?hOOQO-E<z-E<zOOQ[7+(s7+(sO%;gQpO'#HZOOQ[1G3`1G3`O!&oQMhO1G3`O%[QlO1G3`O%;nQ`O1G3`O%;yQMhO1G3`O9qQ!0LrO1G3bO$$VQ`O1G3bO9[Q`O1G3bO!BqQpO1G3bO!ByQMhO1G3bO%<XQ`O'#JOO%<mQ`O,5@|O%<uQpO,5@|OOQ!0Lb1G3c1G3cOOQ[7+$V7+$VO@oQ`O7+$VO9qQ!0LrO7+$VO%=QQ`O7+$VO%[QlO1G6kO%[QlO1G6lO%=VQ!0LrO1G6kO%=aQlO1G3jO%=hQ`O1G3jO%=mQlO1G3jOOQ[7+)S7+)SO9qQ!0LrO7+)^O`QlO7+)`OOQ['#Kg'#KgOOQ['#JR'#JRO%=tQlO,5>_OOQ[,5>_,5>_O%[QlO'#HtO%>RQ`O'#HvOOQ[,5>e,5>eO9aQ`O,5>eOOQ[,5>g,5>gOOQ[7+)i7+)iOOQ[7+)o7+)oOOQ[7+)s7+)sOOQ[7+)u7+)uO%>WQpO1G5{O%>rQ?MtO1G0zO%>|Q`O1G0zOOQO1G/s1G/sO%?XQ?MtO1G/sO>}Q`O1G/sO!)PQlO'#DmOOQO,5?O,5?OOOQO-E<b-E<bOOQO,5?U,5?UOOQO-E<h-E<hO!BqQpO1G/sOOQO-E<d-E<dOOQ!0Ln1G0]1G0]OOQ!0Lf7+%u7+%uO#'oQ`O7+%uOOQ!0Lf7+&`7+&`O>}Q`O7+&`O!BqQpO7+&`OOQO7+%x7+%xO$@_Q!0MxO7+&XOOQO7+&X7+&XO%[QlO7+&XO%?cQ!0LrO7+&XO!BfQ!0LrO7+%xO!BqQpO7+%xO%?nQ!0LrO7+&XO%?|Q!0MxO7++qO%[QlO7++qO%@^Q`O7++pO%@^Q`O7++pOOQO1G4r1G4rO9aQ`O1G4rO%@fQ`O1G4rOOQS7+%}7+%}O#'oQ`O<<LPO4UQ!fO<<LPO%@tQ`O<<LPOOQ[<<LP<<LPO!&oQMhO<<LPO%[QlO<<LPO%@|Q`O<<LPO%AXQ!0MzO,5?`O%CdQ!0MzO,5?bO%EoQ!0MzO1G2`O%HQQ!0MzO1G2sO%J]Q!0MzO1G2uO%LhQ!fO,5?PO%[QlO,5?POOQO-E<c-E<cO%LrQ`O1G5|OOQ!0Lf<<JU<<JUO%LzQ?MtO1G0uO& RQ?MtO1G1PO& YQ?MtO1G1PO&#ZQ?MtO1G1PO&#bQ?MtO1G1PO&%cQ?MtO1G1PO&'dQ?MtO1G1PO&'kQ?MtO1G1PO&'rQ?MtO1G1PO&)sQ?MtO1G1PO&)zQ?MtO1G1PO&*RQ!0MxO<<JfO&+yQ?MtO1G1PO&,vQ?MvO1G1PO&-yQ?MvO'#JkO&0PQ?MtO1G1cO&0^Q?MtO1G0UO&0hQMjO,5?SOOQO-E<f-E<fO!)PQlO'#FqOOQO'#KY'#KYOOQO1G1u1G1uO&0rQ`O1G1tO&0wQ?MtO,5?ZOOOW7+'h7+'hOOOO1G/Z1G/ZO&1RQ!dO1G4wOOQ!0Lh7+(Q7+(QP!&oQMhO,5?]O!+xQMhO7+(cO&1YQ`O,5?[O9aQ`O,5?[OOQO-E<n-E<nO&1hQ`O1G6aO&1hQ`O1G6aO&1pQ`O1G6aO&1{QMjO7+'zO&2]Q!dO,5?^O&2gQ`O,5?^O!&oQMhO,5?^OOQO-E<p-E<pO&2lQ!dO1G6bO&2vQ`O1G6bO&3OQ`O1G2kO!&oQMhO1G2kOOQ!0Lb1G2i1G2iOOQ!0Lb1G2j1G2jO%3TQpO1G2iO!BqQpO1G2iOClQ`O1G2iOOQ!0Lb1G2q1G2qO&3TQpO1G2iO&3cQ`O1G2kO$){Q`O1G2jOClQ`O1G2jO$#jQlO1G2kO&3kQ`O1G2jO&4_QMjO,5?`OOQ!0Lh-E<s-E<sO&5QQMjO,5?bOOQ!0Lh-E<u-E<uO!+xQMhO7++[OOQ!0Lh1G/c1G/cO&5[Q`O1G/cOOQ!0Lh7+'u7+'uO&5aQMjO7+'|O&5qQMjO7++[O&5{QMjO7++[O&6YQ!0MxO<<KXOOQ!0Lf<<KX<<KXO&6|Q`O1G0zO!&oQMhO'#IyO&7RQ`O,5@wO&9TQ!fO<<LPO!&oQMhO1G2nO&9[Q!0LrO1G2nOOQ[<<G{<<G{O!BfQ!0LrO<<G{O&9mQ!0MxO<<I{OOQ!0Lf<<I{<<I{OOQO,5?k,5?kO&:aQ`O,5?kO&:fQ`O,5?kOOQO-E<}-E<}O&:tQ`O1G6jO&:tQ`O1G6jO9gQ`O1G6jO@oQ`O<<LlOOQ[<<Ll<<LlO&:|Q`O<<LlO9qQ!0LrO<<LlOOQ[<<LX<<LXO%9nQ!0MvO<<LXOOQ[<<LY<<LYO!DrQ`O<<LYO&;RQpO'#I{O&;^Q`O,5@{O!)PQlO,5@{OOQ[1G3W1G3WOOQO'#I}'#I}O9qQ!0LrO'#I}O&;fQpO,5=uOOQ[,5=u,5=uO&;mQpO'#EgO&;tQpO'#GeO&;yQ`O7+(zO&<OQ`O7+(zOOQ[7+(z7+(zO!&oQMhO7+(zO%[QlO7+(zO&<WQ`O7+(zOOQ[7+(|7+(|O9qQ!0LrO7+(|O$$VQ`O7+(|O9[Q`O7+(|O!BqQpO7+(|O&<cQ`O,5?jOOQO-E<|-E<|OOQO'#H^'#H^O&<nQ`O1G6hO9qQ!0LrO<<GqOOQ[<<Gq<<GqO@oQ`O<<GqO&<vQ`O7+,VO&<{Q`O7+,WO%[QlO7+,VO%[QlO7+,WOOQ[7+)U7+)UO&=QQ`O7+)UO&=VQlO7+)UO&=^Q`O7+)UOOQ[<<Lx<<LxOOQ[<<Lz<<LzOOQ[-E=P-E=POOQ[1G3y1G3yO&=cQ`O,5>`OOQ[,5>b,5>bO&=hQ`O1G4PO9aQ`O7+&fO!)PQlO7+&fOOQO7+%_7+%_O&=mQ?MtO1G6YO>}Q`O7+%_OOQ!0Lf<<Ia<<IaOOQ!0Lf<<Iz<<IzO>}Q`O<<IzOOQO<<Is<<IsO$@_Q!0MxO<<IsO%[QlO<<IsOOQO<<Id<<IdO!BfQ!0LrO<<IdO&=wQ!0LrO<<IsO&>SQ!0MxO<= ]O&>dQ`O<= [OOQO7+*^7+*^O9aQ`O7+*^OOQ[ANAkANAkO&>lQ!fOANAkO!&oQMhOANAkO#'oQ`OANAkO4UQ!fOANAkO&>sQ`OANAkO%[QlOANAkO&>{Q!0MzO7+'zO&A^Q!0MzO,5?`O&CiQ!0MzO,5?bO&EtQ!0MzO7+'|O&HVQ!fO1G4kO&HaQ?MtO7+&aO&JeQ?MvO,5=XO&LlQ?MvO,5=ZO&L|Q?MvO,5=XO&M^Q?MvO,5=ZO&MnQ?MvO,59uO' tQ?MvO,5<kO'#wQ?MvO,5<mO'&]Q?MvO,5<{O'(RQ?MtO7+'kO'(`Q?MtO7+'mO'(mQ`O,5<]OOQO7+'`7+'`OOQ!0Lh7+*c7+*cO'(rQMjO<<K}OOQO1G4v1G4vO'(yQ`O1G4vO')UQ`O1G4vO')dQ`O7++{O')dQ`O7++{O!&oQMhO1G4xO')lQ!dO1G4xO')vQ`O7++|O'*OQ`O7+(VO'*ZQ!dO7+(VOOQ!0Lb7+(T7+(TOOQ!0Lb7+(U7+(UO!BqQpO7+(TOClQ`O7+(TO'*eQ`O7+(VO!&oQMhO7+(VO$){Q`O7+(UO'*jQ`O7+(VOClQ`O7+(UO'*rQMjO<<NvOOQ!0Lh7+$}7+$}O!+xQMhO<<NvO'*|Q!dO,5?eOOQO-E<w-E<wO'+WQ!0MvO7+(YO!&oQMhO7+(YOOQ[AN=gAN=gO9gQ`O1G5VOOQO1G5V1G5VO'+hQ`O1G5VO'+mQ`O7+,UO'+mQ`O7+,UO9qQ!0LrOANBWO@oQ`OANBWOOQ[ANBWANBWOOQ[ANAsANAsOOQ[ANAtANAtO'+uQ`O,5?gOOQO-E<y-E<yO',QQ?MtO1G6gOOQO,5?i,5?iOOQO-E<{-E<{OOQ[1G3a1G3aO',[Q`O,5=POOQ[<<Lf<<LfO!&oQMhO<<LfO&;yQ`O<<LfO',aQ`O<<LfO%[QlO<<LfOOQ[<<Lh<<LhO9qQ!0LrO<<LhO$$VQ`O<<LhO9[Q`O<<LhO',iQpO1G5UO',tQ`O7+,SOOQ[AN=]AN=]O9qQ!0LrOAN=]OOQ[<= q<= qOOQ[<= r<= rO',|Q`O<= qO'-RQ`O<= rOOQ[<<Lp<<LpO'-WQ`O<<LpO'-]QlO<<LpOOQ[1G3z1G3zO>}Q`O7+)kO'-dQ`O<<JQO'-oQ?MtO<<JQOOQO<<Hy<<HyOOQ!0LfAN?fAN?fOOQOAN?_AN?_O$@_Q!0MxOAN?_OOQOAN?OAN?OO%[QlOAN?_OOQO<<Mx<<MxOOQ[G27VG27VO!&oQMhOG27VO#'oQ`OG27VO'-yQ!fOG27VO4UQ!fOG27VO'.QQ`OG27VO'.YQ?MtO<<JfO'.gQ?MvO1G2`O'0]Q?MvO,5?`O'2`Q?MvO,5?bO'4cQ?MvO1G2sO'6fQ?MvO1G2uO'8iQ?MtO<<KXO'8vQ?MtO<<I{OOQO1G1w1G1wO!+xQMhOANAiOOQO7+*b7+*bO'9TQ`O7+*bO'9`Q`O<= gO'9hQ!dO7+*dOOQ!0Lb<<Kq<<KqO$){Q`O<<KqOClQ`O<<KqO'9rQ`O<<KqO!&oQMhO<<KqOOQ!0Lb<<Ko<<KoO!BqQpO<<KoO'9}Q!dO<<KqOOQ!0Lb<<Kp<<KpO':XQ`O<<KqO!&oQMhO<<KqO$){Q`O<<KpO':^QMjOANDbO':hQ!0MvO<<KtOOQO7+*q7+*qO9gQ`O7+*qO':xQ`O<= pOOQ[G27rG27rO9qQ!0LrOG27rO!)PQlO1G5RO';QQ`O7+,RO';YQ`O1G2kO&;yQ`OANBQOOQ[ANBQANBQO!&oQMhOANBQO';_Q`OANBQOOQ[ANBSANBSO9qQ!0LrOANBSO$$VQ`OANBSOOQO'#H_'#H_OOQO7+*p7+*pOOQ[G22wG22wOOQ[ANE]ANE]OOQ[ANE^ANE^OOQ[ANB[ANB[O';gQ`OANB[OOQ[<<MV<<MVO!)PQlOAN?lOOQOG24yG24yO$@_Q!0MxOG24yO#'oQ`OLD,qOOQ[LD,qLD,qO!&oQMhOLD,qO';lQ!fOLD,qO';sQ?MvO7+'zO'=iQ?MvO,5?`O'?lQ?MvO,5?bO'AoQ?MvO7+'|O'CeQMjOG27TOOQO<<M|<<M|OOQ!0LbANA]ANA]O$){Q`OANA]OClQ`OANA]O'CuQ!dOANA]OOQ!0LbANAZANAZO'C|Q`OANA]O!&oQMhOANA]O'DXQ!dOANA]OOQ!0LbANA[ANA[OOQO<<N]<<N]OOQ[LD-^LD-^O'DcQ?MtO7+*mOOQO'#Gf'#GfOOQ[G27lG27lO&;yQ`OG27lO!&oQMhOG27lOOQ[G27nG27nO9qQ!0LrOG27nOOQ[G27vG27vO'DmQ?MtOG25WOOQOLD*eLD*eOOQ[!$(!]!$(!]O#'oQ`O!$(!]O!&oQMhO!$(!]O'DwQ!0MzOG27TOOQ!0LbG26wG26wO$){Q`OG26wO'GYQ`OG26wOClQ`OG26wO'GeQ!dOG26wO!&oQMhOG26wOOQ[LD-WLD-WO&;yQ`OLD-WOOQ[LD-YLD-YOOQ[!)9Ew!)9EwO#'oQ`O!)9EwOOQ!0LbLD,cLD,cO$){Q`OLD,cOClQ`OLD,cO'GlQ`OLD,cO'GwQ!dOLD,cOOQ[!$(!r!$(!rOOQ[!.K;c!.K;cO'HOQ?MvOG27TOOQ!0Lb!$( }!$( }O$){Q`O!$( }OClQ`O!$( }O'ItQ`O!$( }OOQ!0Lb!)9Ei!)9EiO$){Q`O!)9EiOClQ`O!)9EiOOQ!0Lb!.K;T!.K;TO$){Q`O!.K;TOOQ!0Lb!4/0o!4/0oO!)PQlO'#DzO1PQ`O'#EXO'JPQ!fO'#JqO'JWQ!L^O'#DvO'J_QlO'#EOO'JfQ!fO'#CiO'L|Q!fO'#CiO!)PQlO'#EQO'M^QlO,5;ZO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO'#IoO( aQ`O,5<iO!)PQlO,5;eO( iQMhO,5;eO(#SQMhO,5;eO!)PQlO,5;wO!&oQMhO'#GmO( iQMhO'#GmO!&oQMhO'#GoO( iQMhO'#GoO1SQ`O'#DZO1SQ`O'#DZO!&oQMhO'#GPO( iQMhO'#GPO!&oQMhO'#GRO( iQMhO'#GRO!&oQMhO'#GaO( iQMhO'#GaO!)PQlO,5:jO(#ZQpO'#D_O(#eQpO'#JuO!)PQlO,5@nO'M^QlO1G0uO(#oQ?MtO'#CiO!)PQlO1G2PO!&oQMhO'#ItO( iQMhO'#ItO!&oQMhO'#IvO( iQMhO'#IvO(#yQ!dO'#CrO!&oQMhO,5<tO( iQMhO,5<tO'M^QlO1G2RO!)PQlO7+&zO!&oQMhO1G2`O( iQMhO1G2`O!&oQMhO'#ItO( iQMhO'#ItO!&oQMhO'#IvO( iQMhO'#IvO!&oQMhO1G2bO( iQMhO1G2bO'M^QlO7+'mO'M^QlO7+&aO!&oQMhOANAiO( iQMhOANAiO($^Q`O'#EoO($cQ`O'#EoO($kQ`O'#F]O($pQ`O'#EyO($uQ`O'#KSO(%QQ`O'#KQO(%]Q`O,5;ZO(%bQMjO,5<eO(%iQ`O'#GYO(%nQ`O'#GYO(%sQ`O,5<gO(%{Q`O,5;ZO(&TQ?MtO1G1`O(&[Q`O,5<tO(&aQ`O,5<tO(&fQ`O,5<vO(&kQ`O,5<vO(&pQ`O1G2RO(&uQ`O1G0uO(&zQMjO<<K}O('RQMjO<<K}O7eQMhO'#F|O9[Q`O'#F{OAjQ`O'#EnO!)PQlO,5;tO!3dQ`O'#GYO!3dQ`O'#GYO!3dQ`O'#G[O!3dQ`O'#G[O!+xQMhO7+(cO!+xQMhO7+(cO%-mQ!dO1G2wO%-mQ!dO1G2wO!&oQMhO,5=]O!&oQMhO,5=]",stateData:"((X~O'{OS'|OSTOS'}RQ~OPYOQYOSfOY!VOaqOdzOeyOl!POpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!uwO!xxO!|]O$W|O$niO%h}O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO&V!WO&]!XO&_!YO&a!ZO&c![O&f!]O&l!^O&r!_O&t!`O&v!aO&x!bO&z!cO(SSO(UTO(XUO(`VO(n[O~OWtO~P`OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(S!dO(UTO(XUO(`VO(n[O~Oa!wOs!nO!S!oO!b!yO!c!vO!d!vO!|;wO#T!pO#U!pO#V!xO#W!pO#X!pO#[!zO#]!zO(T!lO(UTO(XUO(d!mO(n!sO~O'}!{O~OP]XR]X[]Xa]Xj]Xr]X!Q]X!S]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X'y]X(`]X(q]X(x]X(y]X~O!g%RX~P(qO_!}O(U#PO(V!}O(W#PO~O_#QO(W#PO(X#PO(Y#QO~Ox#SO!U#TO(a#TO(b#VO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(S;{O(UTO(XUO(`VO(n[O~O![#ZO!]#WO!Y(gP!Y(uP~P+}O!^#cO~P`OPYOQYOSfOd!jOe!iOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(UTO(XUO(`VO(n[O~Op#mO![#iO!|]O#i#lO#j#iO(S;|O!k(rP~P.iO!l#oO(S#nO~O!x#sO!|]O%h#tO~O#k#uO~O!g#vO#k#uO~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!]$_O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(`VO(q$YO(x#|O(y#}O~Oa(eX'y(eX'v(eX!k(eX!Y(eX!_(eX%i(eX!g(eX~P1qO#S$dO#`$eO$Q$eOP(fXR(fX[(fXj(fXr(fX!Q(fX!S(fX!](fX!l(fX!p(fX#R(fX#n(fX#o(fX#p(fX#q(fX#r(fX#s(fX#t(fX#u(fX#v(fX#x(fX#z(fX#{(fX(`(fX(q(fX(x(fX(y(fX!_(fX%i(fX~Oa(fX'y(fX'v(fX!Y(fX!k(fXv(fX!g(fX~P4UO#`$eO~O$]$hO$_$gO$f$mO~OSfO!_$nO$i$oO$k$qO~Oh%VOj%cOk%cOl%cOp%WOr%XOs$tOt$tOz%YO|%ZO!O%[O!S${O!_$|O!i%aO!l$xO#j%bO$W%_O$t%]O$v%^O$y%`O(S$sO(UTO(XUO(`$uO(x$}O(y%POg(]P~O!l%dO~O!S%gO!_%hO(S%fO~O!g%lO~Oa%mO'y%mO~O!Q%qO~P%[O(T!lO~P%[O%n%uO~P%[Oh%VO!l%dO(S%fO(T!lO~Oe%|O!l%dO(S%fO~Oj$RO~O!Q&RO!_&OO!l&QO%j&UO(S%fO(T!lO(UTO(XUO`)VP~O!x#sO~O%s&WO!S)RX!_)RX(S)RX~O(S&XO~Ol!PO!u&^O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO~Od&cOe&bO!x&`O%h&aO%{&_O~P<VOd&fOeyOl!PO!_&eO!u&^O!xxO!|]O%h}O%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO~Ob&iO#`&lO%j&gO(T!lO~P=[O!l&mO!u&qO~O!l#oO~O!_XO~Oa%mO'w&yO'y%mO~Oa%mO'w&|O'y%mO~Oa%mO'w'OO'y%mO~O'v]X!Y]Xv]X!k]X&Z]X!_]X%i]X!g]X~P(qO!b']O!c'UO!d'UO(T!lO(UTO(XUO~Os'SO!S'RO!['VO(d'QO!^(hP!^(wP~P@cOn'`O!_'^O(S%fO~Oe'eO!l%dO(S%fO~O!Q&RO!l&QO~Os!nO!S!oO!|;wO#T!pO#U!pO#W!pO#X!pO(T!lO(UTO(XUO(d!mO(n!sO~O!b'kO!c'jO!d'jO#V!pO#['lO#]'lO~PA}Oa%mOh%VO!g#vO!l%dO'y%mO(q'nO~O!p'rO#`'pO~PC]Os!nO!S!oO(UTO(XUO(d!mO(n!sO~O!_XOs(lX!S(lX!b(lX!c(lX!d(lX!|(lX#T(lX#U(lX#V(lX#W(lX#X(lX#[(lX#](lX(T(lX(U(lX(X(lX(d(lX(n(lX~O!c'jO!d'jO(T!lO~PC{O(O'vO(P'vO(Q'xO~O_!}O(U'zO(V!}O(W'zO~O_#QO(W'zO(X'zO(Y#QO~Ov'|O~P%[Ox#SO!U#TO(a#TO(b(PO~O![(RO!Y'VX!Y']X!]'VX!]']X~P+}O!](TO!Y(gX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!](TO!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(`VO(q$YO(x#|O(y#}O~O!Y(gX~PGvO!Y(YO~O!Y(tX!](tX!g(tX!k(tX(q(tX~O#`(tX#k#dX!^(tX~PIyO#`(ZO!Y(vX!](vX~O!]([O!Y(uX~O!Y(_O~O#`$eO~PIyO!^(`O~P`OR#zO!Q#yO!S#{O!l#xO(`VOP!na[!naj!nar!na!]!na!p!na#R!na#n!na#o!na#p!na#q!na#r!na#s!na#t!na#u!na#v!na#x!na#z!na#{!na(q!na(x!na(y!na~Oa!na'y!na'v!na!Y!na!k!nav!na!_!na%i!na!g!na~PKaO!k(aO~O!g#vO#`(bO(q'nO!](sXa(sX'y(sX~O!k(sX~PM|O!S%gO!_%hO!|]O#i(gO#j(fO(S%fO~O!](hO!k(rX~O!k(jO~O!S%gO!_%hO#j(fO(S%fO~OP(fXR(fX[(fXj(fXr(fX!Q(fX!S(fX!](fX!l(fX!p(fX#R(fX#n(fX#o(fX#p(fX#q(fX#r(fX#s(fX#t(fX#u(fX#v(fX#x(fX#z(fX#{(fX(`(fX(q(fX(x(fX(y(fX~O!g#vO!k(fX~P! jOR(lO!Q(kO!l#xO#S$dO!|!{a!S!{a~O!x!{a%h!{a!_!{a#i!{a#j!{a(S!{a~P!#kO!x(pO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(S!dO(UTO(XUO(`VO(n[O~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<eO!S${O!_$|O!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(S(tO(UTO(XUO(`$uO(x$}O(y%PO~O#k(vO~O![(xO!k(jP~P%[O(d(zO(n[O~O!S(|O!l#xO(d(zO(n[O~OP;vOQ;vOSfOd=rOe!iOpkOr;vOskOtkOzkO|;vO!O;vO!SWO!WkO!XkO!_!eO!i;yO!lZO!o;vO!p;vO!q;vO!s;zO!u;}O!x!hO$W!kO$n=pO(S)ZO(UTO(XUO(`VO(n[O~O!]$_Oa$qa'y$qa'v$qa!k$qa!Y$qa!_$qa%i$qa!g$qa~Ol)bO~P!&oOh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O%[O!S${O!_$|O!i%aO!l$xO#j%bO$W%_O$t%]O$v%^O$y%`O(S(tO(UTO(XUO(`$uO(x$}O(y%PO~Og(oP~P!+xO!Q)gO!g)fO!_$^X$Z$^X$]$^X$_$^X$f$^X~O!g)fO!_(zX$Z(zX$](zX$_(zX$f(zX~O!Q)gO~P!.RO!Q)gO!_(zX$Z(zX$](zX$_(zX$f(zX~O!_)iO$Z)mO$])hO$_)hO$f)nO~O![)qO~P!)PO$]$hO$_$gO$f)uO~On$zX!Q$zX#S$zX'x$zX(x$zX(y$zX~OgmXg$zXnmX!]mX#`mX~P!/wOx)wO(a)xO(b)zO~On*TO!Q)|O'x)}O(x$}O(y%PO~Og){O~P!0{Og*UO~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<eO!S*WO!_*XO!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(UTO(XUO(`$uO(x$}O(y%PO~O![*[O(S*VO!k(}P~P!1jO#k*^O~O!l*_O~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<eO!S${O!_$|O!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(S*aO(UTO(XUO(`$uO(x$}O(y%PO~O![*dO!Y)OP~P!3iOr*pOs!nO!S*fO!b*nO!c*hO!d*hO!l*_O#[*oO%`*jO(T!lO(UTO(XUO(d!mO~O!^*mO~P!5^O#S$dOn(_X!Q(_X'x(_X(x(_X(y(_X!](_X#`(_X~Og(_X$O(_X~P!6`On*uO#`*tOg(^X!](^X~O!]*vOg(]X~Oj%cOk%cOl%cO(S&XOg(]P~Os*yO~O!l+OO~O(S(tO~Op+TO!S%gO![#iO!_%hO!|]O#i#lO#j#iO(S%fO!k(rP~O!g#vO#k+UO~O!S%gO![+WO!]([O!_%hO(S%fO!Y(uP~Os'YO!S+YO![+XO(UTO(XUO(d(zO~O!^(wP~P!9iO!]+ZOa)SX'y)SX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(`VO(q$YO(x#|O(y#}O~Oa!ja!]!ja'y!ja'v!ja!Y!ja!k!jav!ja!_!ja%i!ja!g!ja~P!:aOR#zO!Q#yO!S#{O!l#xO(`VOP!ra[!raj!rar!ra!]!ra!p!ra#R!ra#n!ra#o!ra#p!ra#q!ra#r!ra#s!ra#t!ra#u!ra#v!ra#x!ra#z!ra#{!ra(q!ra(x!ra(y!ra~Oa!ra'y!ra'v!ra!Y!ra!k!rav!ra!_!ra%i!ra!g!ra~P!<wOR#zO!Q#yO!S#{O!l#xO(`VOP!ta[!taj!tar!ta!]!ta!p!ta#R!ta#n!ta#o!ta#p!ta#q!ta#r!ta#s!ta#t!ta#u!ta#v!ta#x!ta#z!ta#{!ta(q!ta(x!ta(y!ta~Oa!ta'y!ta'v!ta!Y!ta!k!tav!ta!_!ta%i!ta!g!ta~P!?_Oh%VOn+dO!_'^O%i+cO~O!g+fOa([X!_([X'y([X!]([X~Oa%mO!_XO'y%mO~Oh%VO!l%dO~Oh%VO!l%dO(S%fO~O!g#vO#k(vO~Ob+qO%j+rO(S+nO(UTO(XUO!^)WP~O!]+sO`)VX~O[+wO~O`+xO~O!_&OO(S%fO(T!lO`)VP~Oh%VO#`+}O~Oh%VOn,QO!_$|O~O!_,SO~O!Q,UO!_XO~O%n%uO~O!x,ZO~Oe,`O~Ob,aO(S#nO(UTO(XUO!^)UP~Oe%|O~O%j!QO(S&XO~P=[O[,fO`,eO~OPYOQYOSfOdzOeyOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!iuO!lZO!oYO!pYO!qYO!svO!xxO!|]O$niO%h}O(UTO(XUO(`VO(n[O~O!_!eO!u!gO$W!kO(S!dO~P!F_O`,eOa%mO'y%mO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!x!hO$W!kO$niO(S!dO(UTO(XUO(`VO(n[O~Oa,kOl!OO!uwO%l!OO%m!OO%n!OO~P!HwO!l&mO~O&],qO~O!_,sO~O&n,uO&p,vOP&kaQ&kaS&kaY&kaa&kad&kae&kal&kap&kar&kas&kat&kaz&ka|&ka!O&ka!S&ka!W&ka!X&ka!_&ka!i&ka!l&ka!o&ka!p&ka!q&ka!s&ka!u&ka!x&ka!|&ka$W&ka$n&ka%h&ka%j&ka%l&ka%m&ka%n&ka%q&ka%s&ka%v&ka%w&ka%y&ka&V&ka&]&ka&_&ka&a&ka&c&ka&f&ka&l&ka&r&ka&t&ka&v&ka&x&ka&z&ka'v&ka(S&ka(U&ka(X&ka(`&ka(n&ka!^&ka&d&kab&ka&i&ka~O(S,{O~Oh!eX!]!RX!^!RX!g!RX!g!eX!l!eX#`!RX~O!]!eX!^!eX~P# }O!g-QO#`-POh(iX!]#hX!^#hX!g(iX!l(iX~O!](iX!^(iX~P#!pOh%VO!g-SO!l%dO!]!aX!^!aX~Os!nO!S!oO(UTO(XUO(d!mO~OP;vOQ;vOSfOd=rOe!iOpkOr;vOskOtkOzkO|;vO!O;vO!SWO!WkO!XkO!_!eO!i;yO!lZO!o;vO!p;vO!q;vO!s;zO!u;}O!x!hO$W!kO$n=pO(UTO(XUO(`VO(n[O~O(S<rO~P#$VO!]-WO!^(hX~O!^-YO~O!g-QO#`-PO!]#hX!^#hX~O!]-ZO!^(wX~O!^-]O~O!c-^O!d-^O(T!lO~P##tO!^-aO~P'_On-dO!_'^O~O!Y-iO~Os!{a!b!{a!c!{a!d!{a#T!{a#U!{a#V!{a#W!{a#X!{a#[!{a#]!{a(T!{a(U!{a(X!{a(d!{a(n!{a~P!#kO!p-nO#`-lO~PC]O!c-pO!d-pO(T!lO~PC{Oa%mO#`-lO'y%mO~Oa%mO!g#vO#`-lO'y%mO~Oa%mO!g#vO!p-nO#`-lO'y%mO(q'nO~O(O'vO(P'vO(Q-uO~Ov-vO~O!Y'Va!]'Va~P!:aO![-zO!Y'VX!]'VX~P%[O!](TO!Y(ga~O!Y(ga~PGvO!]([O!Y(ua~O!S%gO![.OO!_%hO(S%fO!Y']X!]']X~O#`.QO!](sa!k(saa(sa'y(sa~O!g#vO~P#,]O!](hO!k(ra~O!S%gO!_%hO#j.UO(S%fO~Op.ZO!S%gO![.WO!_%hO!|]O#i.YO#j.WO(S%fO!]'`X!k'`X~OR._O!l#xO~Oh%VOn.bO!_'^O%i.aO~Oa#ci!]#ci'y#ci'v#ci!Y#ci!k#civ#ci!_#ci%i#ci!g#ci~P!:aOn=|O!Q)|O'x)}O(x$}O(y%PO~O#k#_aa#_a#`#_a'y#_a!]#_a!k#_a!_#_a!Y#_a~P#/XO#k(_XP(_XR(_X[(_Xa(_Xj(_Xr(_X!S(_X!l(_X!p(_X#R(_X#n(_X#o(_X#p(_X#q(_X#r(_X#s(_X#t(_X#u(_X#v(_X#x(_X#z(_X#{(_X'y(_X(`(_X(q(_X!k(_X!Y(_X'v(_Xv(_X!_(_X%i(_X!g(_X~P!6`O!].oO!k(jX~P!:aO!k.rO~O!Y.tO~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(`VO[#mia#mij#mir#mi!]#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'y#mi(q#mi(x#mi(y#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#n#mi~P#2wO#n$OO~P#2wOP$[OR#zOr$aO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(`VO[#mia#mij#mi!]#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'y#mi(q#mi(x#mi(y#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#r#mi~P#5fO#r$QO~P#5fOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO(`VOa#mi!]#mi#x#mi#z#mi#{#mi'y#mi(q#mi(x#mi(y#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#v#mi~P#8TOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO(`VO(y#}Oa#mi!]#mi#z#mi#{#mi'y#mi(q#mi(x#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#x$UO~P#:kO#x#mi~P#:kO#v$SO~P#8TOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO(`VO(x#|O(y#}Oa#mi!]#mi#{#mi'y#mi(q#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#z#mi~P#=aO#z$WO~P#=aOP]XR]X[]Xj]Xr]X!Q]X!S]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(`]X(q]X(x]X(y]X!]]X!^]X~O$O]X~P#@OOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O#v<TO#x<VO#z<XO#{<YO(`VO(q$YO(x#|O(y#}O~O$O.vO~P#B]O#S$dO#`<`O$Q<`O$O(fX!^(fX~P! jOa'ca!]'ca'y'ca'v'ca!k'ca!Y'cav'ca!_'ca%i'ca!g'ca~P!:aO[#mia#mij#mir#mi!]#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'y#mi(q#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(`VO(x#mi(y#mi~P#E_On=|O!Q)|O'x)}O(x$}O(y%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(`#mi~P#E_O!].zOg(oX~P!0{Og.|O~Oa$Pi!]$Pi'y$Pi'v$Pi!Y$Pi!k$Piv$Pi!_$Pi%i$Pi!g$Pi~P!:aO$].}O$_.}O~O$]/OO$_/OO~O!g)fO#`/PO!_$cX$Z$cX$]$cX$_$cX$f$cX~O![/QO~O!_)iO$Z/SO$])hO$_)hO$f/TO~O!]<ZO!^(eX~P#B]O!^/UO~O!g)fO$f(zX~O$f/WO~Ov/XO~P!&oOx)wO(a)xO(b/[O~O!S/_O~O(x$}On%aa!Q%aa'x%aa(y%aa!]%aa#`%aa~Og%aa$O%aa~P#LaO(y%POn%ca!Q%ca'x%ca(x%ca!]%ca#`%ca~Og%ca$O%ca~P#MSO!]fX!gfX!kfX!k$zX(qfX~P!/wO![/hO!]([O(S/gO!Y(uP!Y)OP~P!1jOr*pO!b*nO!c*hO!d*hO!l*_O#[*oO%`*jO(T!lO(UTO(XUO~Os<oO!S/iO![+XO!^*mO(d<nO!^(wP~P#NmO!k/jO~P#/XO!]/kO!g#vO(q'nO!k(}X~O!k/pO~O!S%gO![*[O!_%hO(S%fO!k(}P~O#k/rO~O!Y$zX!]$zX!g%RX~P!/wO!]/sO!Y)OX~P#/XO!g/uO~O!Y/wO~OpkO(S/xO~P.iOh%VOr/}O!g#vO!l%dO(q'nO~O!g+fO~Oa%mO!]0RO'y%mO~O!^0TO~P!5^O!c0UO!d0UO(T!lO~P##tOs!nO!S0VO(UTO(XUO(d!mO~O#[0XO~Og%aa!]%aa#`%aa$O%aa~P!0{Og%ca!]%ca#`%ca$O%ca~P!0{Oj%cOk%cOl%cO(S&XOg'lX!]'lX~O!]*vOg(]a~Og0bO~OR0cO!Q0cO!S0dO#S$dOn}a'x}a(x}a(y}a!]}a#`}a~Og}a$O}a~P$&vO!Q)|O'x)}On$sa(x$sa(y$sa!]$sa#`$sa~Og$sa$O$sa~P$'rO!Q)|O'x)}On$ua(x$ua(y$ua!]$ua#`$ua~Og$ua$O$ua~P$(eO#k0gO~Og%Ta!]%Ta#`%Ta$O%Ta~P!0{On0iO#`0hOg(^a!](^a~O!g#vO~O#k0lO~O!]+ZOa)Sa'y)Sa~OR#zO!Q#yO!S#{O!l#xO(`VOP!ri[!rij!rir!ri!]!ri!p!ri#R!ri#n!ri#o!ri#p!ri#q!ri#r!ri#s!ri#t!ri#u!ri#v!ri#x!ri#z!ri#{!ri(q!ri(x!ri(y!ri~Oa!ri'y!ri'v!ri!Y!ri!k!riv!ri!_!ri%i!ri!g!ri~P$*bOh%VOr%XOs$tOt$tOz%YO|%ZO!O<eO!S${O!_$|O!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(UTO(XUO(`$uO(x$}O(y%PO~Op0uO%]0vO(S0tO~P$,xO!g+fOa([a!_([a'y([a!]([a~O#k0|O~O[]X!]fX!^fX~O!]0}O!^)WX~O!^1PO~O[1QO~Ob1SO(S+nO(UTO(XUO~O!_&OO(S%fO`'tX!]'tX~O!]+sO`)Va~O!k1VO~P!:aO[1YO~O`1ZO~O#`1^O~On1aO!_$|O~O(d(zO!^)TP~Oh%VOn1jO!_1gO%i1iO~O[1tO!]1rO!^)UX~O!^1uO~O`1wOa%mO'y%mO~O(S#nO(UTO(XUO~O#S$dO#`$eO$Q$eOP(fXR(fX[(fXr(fX!Q(fX!S(fX!](fX!l(fX!p(fX#R(fX#n(fX#o(fX#p(fX#q(fX#r(fX#s(fX#t(fX#u(fX#v(fX#x(fX#z(fX#{(fX(`(fX(q(fX(x(fX(y(fX~Oj1zO&Z1{Oa(fX~P$2cOj1zO#`$eO&Z1{O~Oa1}O~P%[Oa2PO~O&d2SOP&biQ&biS&biY&bia&bid&bie&bil&bip&bir&bis&bit&biz&bi|&bi!O&bi!S&bi!W&bi!X&bi!_&bi!i&bi!l&bi!o&bi!p&bi!q&bi!s&bi!u&bi!x&bi!|&bi$W&bi$n&bi%h&bi%j&bi%l&bi%m&bi%n&bi%q&bi%s&bi%v&bi%w&bi%y&bi&V&bi&]&bi&_&bi&a&bi&c&bi&f&bi&l&bi&r&bi&t&bi&v&bi&x&bi&z&bi'v&bi(S&bi(U&bi(X&bi(`&bi(n&bi!^&bib&bi&i&bi~Ob2YO!^2WO&i2XO~P`O!_XO!l2[O~O&p,vOP&kiQ&kiS&kiY&kia&kid&kie&kil&kip&kir&kis&kit&kiz&ki|&ki!O&ki!S&ki!W&ki!X&ki!_&ki!i&ki!l&ki!o&ki!p&ki!q&ki!s&ki!u&ki!x&ki!|&ki$W&ki$n&ki%h&ki%j&ki%l&ki%m&ki%n&ki%q&ki%s&ki%v&ki%w&ki%y&ki&V&ki&]&ki&_&ki&a&ki&c&ki&f&ki&l&ki&r&ki&t&ki&v&ki&x&ki&z&ki'v&ki(S&ki(U&ki(X&ki(`&ki(n&ki!^&ki&d&kib&ki&i&ki~O!Y2bO~O!]!aa!^!aa~P#B]Os!nO!S!oO![2hO(d!mO!]'WX!^'WX~P@cO!]-WO!^(ha~O!]'^X!^'^X~P!9iO!]-ZO!^(wa~O!^2oO~P'_Oa%mO#`2xO'y%mO~Oa%mO!g#vO#`2xO'y%mO~Oa%mO!g#vO!p2|O#`2xO'y%mO(q'nO~Oa%mO'y%mO~P!:aO!]$_Ov$qa~O!Y'Vi!]'Vi~P!:aO!](TO!Y(gi~O!]([O!Y(ui~O!Y(vi!](vi~P!:aO!](si!k(sia(si'y(si~P!:aO#`3OO!](si!k(sia(si'y(si~O!](hO!k(ri~O!S%gO!_%hO!|]O#i3TO#j3SO(S%fO~O!S%gO!_%hO#j3SO(S%fO~On3[O!_'^O%i3ZO~Oh%VOn3[O!_'^O%i3ZO~O#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'y%aa(`%aa(q%aa!k%aa!Y%aa'v%aav%aa!_%aa%i%aa!g%aa~P#LaO#k%caP%caR%ca[%caa%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'y%ca(`%ca(q%ca!k%ca!Y%ca'v%cav%ca!_%ca%i%ca!g%ca~P#MSO#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!]%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'y%aa(`%aa(q%aa!k%aa!Y%aa'v%aa#`%aav%aa!_%aa%i%aa!g%aa~P#/XO#k%caP%caR%ca[%caa%caj%car%ca!S%ca!]%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'y%ca(`%ca(q%ca!k%ca!Y%ca'v%ca#`%cav%ca!_%ca%i%ca!g%ca~P#/XO#k}aP}a[}aa}aj}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a'y}a(`}a(q}a!k}a!Y}a'v}av}a!_}a%i}a!g}a~P$&vO#k$saP$saR$sa[$saa$saj$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa'y$sa(`$sa(q$sa!k$sa!Y$sa'v$sav$sa!_$sa%i$sa!g$sa~P$'rO#k$uaP$uaR$ua[$uaa$uaj$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua'y$ua(`$ua(q$ua!k$ua!Y$ua'v$uav$ua!_$ua%i$ua!g$ua~P$(eO#k%TaP%TaR%Ta[%Taa%Taj%Tar%Ta!S%Ta!]%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta'y%Ta(`%Ta(q%Ta!k%Ta!Y%Ta'v%Ta#`%Tav%Ta!_%Ta%i%Ta!g%Ta~P#/XOa#cq!]#cq'y#cq'v#cq!Y#cq!k#cqv#cq!_#cq%i#cq!g#cq~P!:aO![3dO!]'XX!k'XX~P%[O!].oO!k(ja~O!].oO!k(ja~P!:aO!Y3gO~O$O!na!^!na~PKaO$O!ja!]!ja!^!ja~P#B]O$O!ra!^!ra~P!<wO$O!ta!^!ta~P!?_Og'[X!]'[X~P!+xO!].zOg(oa~OSfO!_3{O$d3|O~O!^4QO~Ov4RO~P#/XOa$mq!]$mq'y$mq'v$mq!Y$mq!k$mqv$mq!_$mq%i$mq!g$mq~P!:aO!Y4TO~P!&oO!S4UO~O!Q)|O'x)}O(y%POn'ha(x'ha!]'ha#`'ha~Og'ha$O'ha~P%,XO!Q)|O'x)}On'ja(x'ja(y'ja!]'ja#`'ja~Og'ja$O'ja~P%,zO(q$YO~P#/XO!YfX!Y$zX!]fX!]$zX!g%RX#`fX~P!/wO(S<xO~P!1jO!S%gO![4XO!_%hO(S%fO!]'dX!k'dX~O!]/kO!k(}a~O!]/kO!g#vO!k(}a~O!]/kO!g#vO(q'nO!k(}a~Og$|i!]$|i#`$|i$O$|i~P!0{O![4aO!Y'fX!]'fX~P!3iO!]/sO!Y)Oa~O!]/sO!Y)Oa~P#/XOP]XR]X[]Xj]Xr]X!Q]X!S]X!Y]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(`]X(q]X(x]X(y]X~Oj%YX!g%YX~P%0kOj4fO!g#vO~Oh%VO!g#vO!l%dO~Oh%VOr4kO!l%dO(q'nO~Or4pO!g#vO(q'nO~Os!nO!S4qO(UTO(XUO(d!mO~O(x$}On%ai!Q%ai'x%ai(y%ai!]%ai#`%ai~Og%ai$O%ai~P%4[O(y%POn%ci!Q%ci'x%ci(x%ci!]%ci#`%ci~Og%ci$O%ci~P%4}Og(^i!](^i~P!0{O#`4wOg(^i!](^i~P!0{O!k4zO~Oa$oq!]$oq'y$oq'v$oq!Y$oq!k$oqv$oq!_$oq%i$oq!g$oq~P!:aO!Y5QO~O!]5RO!_)PX~P#/XOa$zX!_$zX%^]X'y$zX!]$zX~P!/wO%^5UOaoXnoX!QoX!_oX'xoX'yoX(xoX(yoX!]oX~Op5VO(S#nO~O%^5UO~Ob5]O%j5^O(S+nO(UTO(XUO!]'sX!^'sX~O!]0}O!^)Wa~O[5bO~O`5cO~Oa%mO'y%mO~P#/XO!]5kO#`5mO!^)TX~O!^5nO~Or5tOs!nO!S*fO!b!yO!c!vO!d!vO!|;wO#T!pO#U!pO#V!pO#W!pO#X!pO#[5sO#]!zO(T!lO(UTO(XUO(d!mO(n!sO~O!^5rO~P%:YOn5yO!_1gO%i5xO~Oh%VOn5yO!_1gO%i5xO~Ob6QO(S#nO(UTO(XUO!]'rX!^'rX~O!]1rO!^)Ua~O(UTO(XUO(d6SO~O`6WO~Oj6ZO&Z6[O~PM|O!k6]O~P%[Oa6_O~Oa6_O~P%[Ob2YO!^6dO&i2XO~P`O!g6fO~O!g6hOh(ii!](ii!^(ii!g(ii!l(iir(ii(q(ii~O!]#hi!^#hi~P#B]O#`6iO!]#hi!^#hi~O!]!ai!^!ai~P#B]Oa%mO#`6rO'y%mO~Oa%mO!g#vO#`6rO'y%mO~O!](sq!k(sqa(sq'y(sq~P!:aO!](hO!k(rq~O!S%gO!_%hO#j6yO(S%fO~O!_'^O%i6|O~On7QO!_'^O%i6|O~O#k'haP'haR'ha['haa'haj'har'ha!S'ha!l'ha!p'ha#R'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#t'ha#u'ha#v'ha#x'ha#z'ha#{'ha'y'ha(`'ha(q'ha!k'ha!Y'ha'v'hav'ha!_'ha%i'ha!g'ha~P%,XO#k'jaP'jaR'ja['jaa'jaj'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja'y'ja(`'ja(q'ja!k'ja!Y'ja'v'jav'ja!_'ja%i'ja!g'ja~P%,zO#k$|iP$|iR$|i[$|ia$|ij$|ir$|i!S$|i!]$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i'y$|i(`$|i(q$|i!k$|i!Y$|i'v$|i#`$|iv$|i!_$|i%i$|i!g$|i~P#/XO#k%aiP%aiR%ai[%aia%aij%air%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai'y%ai(`%ai(q%ai!k%ai!Y%ai'v%aiv%ai!_%ai%i%ai!g%ai~P%4[O#k%ciP%ciR%ci[%cia%cij%cir%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci'y%ci(`%ci(q%ci!k%ci!Y%ci'v%civ%ci!_%ci%i%ci!g%ci~P%4}O!]'Xa!k'Xa~P!:aO!].oO!k(ji~O$O#ci!]#ci!^#ci~P#B]OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(`VO[#mij#mir#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(q#mi(x#mi(y#mi!]#mi!^#mi~O#n#mi~P%MXO#n<PO~P%MXOP$[OR#zOr<]O!Q#yO!S#{O!l#xO!p$[O#n<PO#o<QO#p<QO#q<QO(`VO[#mij#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(q#mi(x#mi(y#mi!]#mi!^#mi~O#r#mi~P& aO#r<RO~P& aOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O(`VO#x#mi#z#mi#{#mi$O#mi(q#mi(x#mi(y#mi!]#mi!^#mi~O#v#mi~P&#iOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O#v<TO(`VO(y#}O#z#mi#{#mi$O#mi(q#mi(x#mi!]#mi!^#mi~O#x<VO~P&%jO#x#mi~P&%jO#v<TO~P&#iOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O#v<TO#x<VO(`VO(x#|O(y#}O#{#mi$O#mi(q#mi!]#mi!^#mi~O#z#mi~P&'yO#z<XO~P&'yOa#|y!]#|y'y#|y'v#|y!Y#|y!k#|yv#|y!_#|y%i#|y!g#|y~P!:aO[#mij#mir#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(q#mi!]#mi!^#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n<PO#o<QO#p<QO#q<QO(`VO(x#mi(y#mi~P&*uOn=}O!Q)|O'x)}O(x$}O(y%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(`#mi~P&*uO#S$dOP(_XR(_X[(_Xj(_Xn(_Xr(_X!Q(_X!S(_X!l(_X!p(_X#R(_X#n(_X#o(_X#p(_X#q(_X#r(_X#s(_X#t(_X#u(_X#v(_X#x(_X#z(_X#{(_X$O(_X'x(_X(`(_X(q(_X(x(_X(y(_X!](_X!^(_X~O$O$Pi!]$Pi!^$Pi~P#B]O$O!ri!^!ri~P$*bOg'[a!]'[a~P!0{O!^7dO~O!]'ca!^'ca~P#B]O!Y7eO~P#/XO!g#vO(q'nO!]'da!k'da~O!]/kO!k(}i~O!]/kO!g#vO!k(}i~Og$|q!]$|q#`$|q$O$|q~P!0{O!Y'fa!]'fa~P#/XO!g7lO~O!]/sO!Y)Oi~P#/XO!]/sO!Y)Oi~O!Y7oO~Oh%VOr7tO!l%dO(q'nO~Oj7vO!g#vO~Or7yO!g#vO(q'nO~O!Q)|O'x)}O(y%POn'ia(x'ia!]'ia#`'ia~Og'ia$O'ia~P&3vO!Q)|O'x)}On'ka(x'ka(y'ka!]'ka#`'ka~Og'ka$O'ka~P&4iO!Y7{O~Og%Oq!]%Oq#`%Oq$O%Oq~P!0{Og(^q!](^q~P!0{O#`7|Og(^q!](^q~P!0{Oa$oy!]$oy'y$oy'v$oy!Y$oy!k$oyv$oy!_$oy%i$oy!g$oy~P!:aO!g6hO~O!]5RO!_)Pa~O!_'^OP$TaR$Ta[$Taj$Tar$Ta!Q$Ta!S$Ta!]$Ta!l$Ta!p$Ta#R$Ta#n$Ta#o$Ta#p$Ta#q$Ta#r$Ta#s$Ta#t$Ta#u$Ta#v$Ta#x$Ta#z$Ta#{$Ta(`$Ta(q$Ta(x$Ta(y$Ta~O%i6|O~P&7ZO%^8QOa%[i!_%[i'y%[i!]%[i~Oa#cy!]#cy'y#cy'v#cy!Y#cy!k#cyv#cy!_#cy%i#cy!g#cy~P!:aO[8SO~Ob8UO(S+nO(UTO(XUO~O!]0}O!^)Wi~O`8YO~O(d(zO!]'oX!^'oX~O!]5kO!^)Ta~O!^8cO~P%:YO(n!sO~P$${O#[8dO~O!_1gO~O!_1gO%i8fO~On8iO!_1gO%i8fO~O[8nO!]'ra!^'ra~O!]1rO!^)Ui~O!k8rO~O!k8sO~O!k8vO~O!k8vO~P%[Oa8xO~O!g8yO~O!k8zO~O!](vi!^(vi~P#B]Oa%mO#`9SO'y%mO~O!](sy!k(sya(sy'y(sy~P!:aO!](hO!k(ry~O%i9VO~P&7ZO!_'^O%i9VO~O#k$|qP$|qR$|q[$|qa$|qj$|qr$|q!S$|q!]$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q'y$|q(`$|q(q$|q!k$|q!Y$|q'v$|q#`$|qv$|q!_$|q%i$|q!g$|q~P#/XO#k'iaP'iaR'ia['iaa'iaj'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia'y'ia(`'ia(q'ia!k'ia!Y'ia'v'iav'ia!_'ia%i'ia!g'ia~P&3vO#k'kaP'kaR'ka['kaa'kaj'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka'y'ka(`'ka(q'ka!k'ka!Y'ka'v'kav'ka!_'ka%i'ka!g'ka~P&4iO#k%OqP%OqR%Oq[%Oqa%Oqj%Oqr%Oq!S%Oq!]%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq'y%Oq(`%Oq(q%Oq!k%Oq!Y%Oq'v%Oq#`%Oqv%Oq!_%Oq%i%Oq!g%Oq~P#/XO!]'Xi!k'Xi~P!:aO$O#cq!]#cq!^#cq~P#B]O(x$}OP%aaR%aa[%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa$O%aa(`%aa(q%aa!]%aa!^%aa~On%aa!Q%aa'x%aa(y%aa~P&HnO(y%POP%caR%ca[%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca$O%ca(`%ca(q%ca!]%ca!^%ca~On%ca!Q%ca'x%ca(x%ca~P&JuOn=}O!Q)|O'x)}O(y%PO~P&HnOn=}O!Q)|O'x)}O(x$}O~P&JuOR0cO!Q0cO!S0dO#S$dOP}a[}aj}an}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a$O}a'x}a(`}a(q}a(x}a(y}a!]}a!^}a~O!Q)|O'x)}OP$saR$sa[$saj$san$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa$O$sa(`$sa(q$sa(x$sa(y$sa!]$sa!^$sa~O!Q)|O'x)}OP$uaR$ua[$uaj$uan$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua$O$ua(`$ua(q$ua(x$ua(y$ua!]$ua!^$ua~On=}O!Q)|O'x)}O(x$}O(y%PO~OP%TaR%Ta[%Taj%Tar%Ta!S%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta$O%Ta(`%Ta(q%Ta!]%Ta!^%Ta~P'%zO$O$mq!]$mq!^$mq~P#B]O$O$oq!]$oq!^$oq~P#B]O!^9dO~O$O9eO~P!0{O!g#vO!]'di!k'di~O!g#vO(q'nO!]'di!k'di~O!]/kO!k(}q~O!Y'fi!]'fi~P#/XO!]/sO!Y)Oq~Or9lO!g#vO(q'nO~O[9nO!Y9mO~P#/XO!Y9mO~Oj9tO!g#vO~Og(^y!](^y~P!0{O!]'ma!_'ma~P#/XOa%[q!_%[q'y%[q!]%[q~P#/XO[9yO~O!]0}O!^)Wq~O#`9}O!]'oa!^'oa~O!]5kO!^)Ti~P#B]O!S:PO~O!_1gO%i:SO~O(UTO(XUO(d:XO~O!]1rO!^)Uq~O!k:[O~O!k:]O~O!k:^O~O!k:^O~P%[O#`:aO!]#hy!^#hy~O!]#hy!^#hy~P#B]O%i:fO~P&7ZO!_'^O%i:fO~O$O#|y!]#|y!^#|y~P#B]OP$|iR$|i[$|ij$|ir$|i!S$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i$O$|i(`$|i(q$|i!]$|i!^$|i~P'%zO!Q)|O'x)}O(y%POP'haR'ha['haj'han'har'ha!S'ha!l'ha!p'ha#R'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#t'ha#u'ha#v'ha#x'ha#z'ha#{'ha$O'ha(`'ha(q'ha(x'ha!]'ha!^'ha~O!Q)|O'x)}OP'jaR'ja['jaj'jan'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja$O'ja(`'ja(q'ja(x'ja(y'ja!]'ja!^'ja~O(x$}OP%aiR%ai[%aij%ain%air%ai!Q%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai$O%ai'x%ai(`%ai(q%ai(y%ai!]%ai!^%ai~O(y%POP%ciR%ci[%cij%cin%cir%ci!Q%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci$O%ci'x%ci(`%ci(q%ci(x%ci!]%ci!^%ci~O$O$oy!]$oy!^$oy~P#B]O$O#cy!]#cy!^#cy~P#B]O!g#vO!]'dq!k'dq~O!]/kO!k(}y~O!Y'fq!]'fq~P#/XOr:pO!g#vO(q'nO~O[:tO!Y:sO~P#/XO!Y:sO~Og(^!R!](^!R~P!0{Oa%[y!_%[y'y%[y!]%[y~P#/XO!]0}O!^)Wy~O!]5kO!^)Tq~O(S:zO~O!_1gO%i:}O~O!k;QO~O%i;VO~P&7ZOP$|qR$|q[$|qj$|qr$|q!S$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q$O$|q(`$|q(q$|q!]$|q!^$|q~P'%zO!Q)|O'x)}O(y%POP'iaR'ia['iaj'ian'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia$O'ia(`'ia(q'ia(x'ia!]'ia!^'ia~O!Q)|O'x)}OP'kaR'ka['kaj'kan'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka$O'ka(`'ka(q'ka(x'ka(y'ka!]'ka!^'ka~OP%OqR%Oq[%Oqj%Oqr%Oq!S%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq$O%Oq(`%Oq(q%Oq!]%Oq!^%Oq~P'%zOg%e!Z!]%e!Z#`%e!Z$O%e!Z~P!0{O!Y;ZO~P#/XOr;[O!g#vO(q'nO~O[;^O!Y;ZO~P#/XO!]'oq!^'oq~P#B]O!]#h!Z!^#h!Z~P#B]O#k%e!ZP%e!ZR%e!Z[%e!Za%e!Zj%e!Zr%e!Z!S%e!Z!]%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z'y%e!Z(`%e!Z(q%e!Z!k%e!Z!Y%e!Z'v%e!Z#`%e!Zv%e!Z!_%e!Z%i%e!Z!g%e!Z~P#/XOr;fO!g#vO(q'nO~O!Y;gO~P#/XOr;nO!g#vO(q'nO~O!Y;oO~P#/XOP%e!ZR%e!Z[%e!Zj%e!Zr%e!Z!S%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z$O%e!Z(`%e!Z(q%e!Z!]%e!Z!^%e!Z~P'%zOr;rO!g#vO(q'nO~Ov(eX~P1qO!Q%qO~P!)PO(T!lO~P!)PO!YfX!]fX#`fX~P%0kOP]XR]X[]Xj]Xr]X!Q]X!S]X!]]X!]fX!l]X!p]X#R]X#S]X#`]X#`fX#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(`]X(q]X(x]X(y]X~O!gfX!k]X!kfX(qfX~P'JsOP;vOQ;vOSfOd=rOe!iOpkOr;vOskOtkOzkO|;vO!O;vO!SWO!WkO!XkO!_XO!i;yO!lZO!o;vO!p;vO!q;vO!s;zO!u;}O!x!hO$W!kO$n=pO(S)ZO(UTO(XUO(`VO(n[O~O!]<ZO!^$qa~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<fO!S${O!_$|O!i=wO!l$xO#j<lO$W%_O$t<hO$v<jO$y%`O(S(tO(UTO(XUO(`$uO(x$}O(y%PO~Ol)bO~P( iOr!eX(q!eX~P# }Or(iX(q(iX~P#!pO!^]X!^fX~P'JsO!YfX!Y$zX!]fX!]$zX#`fX~P!/wO#k<OO~O!g#vO#k<OO~O#`<`O~Oj<SO~O#`<pO!](vX!^(vX~O#`<`O!](tX!^(tX~O#k<qO~Og<sO~P!0{O#k<yO~O#k<zO~O!g#vO#k<{O~O!g#vO#k<qO~O$O<|O~P#B]O#k<}O~O#k=OO~O#k=TO~O#k=UO~O#k=VO~O#k=WO~O$O=XO~P!0{O$O=YO~P!0{Ok#S#T#U#W#X#[#i#j#u$n$t$v$y%]%^%h%i%j%q%s%v%w%y%{~'}T#o!X'{(T#ps#n#qr!Q'|$]'|(S$_(d~",goto:"$8g)[PPPPPP)]PP)`P)qP+R/WPPPP6bPP6xPP<pPPP@dP@zP@zPPP@zPCSP@zP@zP@zPCWPC]PCzPHtPPPHxPPPPHxK{PPPLRLsPHxPHxPP! RHxPPPHxPHxP!#YHxP!&p!'u!(OP!(r!(v!(r!,TPPPPPPP!,t!'uPP!-U!.vP!2SHxHx!2X!5e!:R!:R!>QPPP!>YHxPPPPPPPPP!AiP!BvPPHx!DXPHxPHxHxHxHxHxPHx!EkP!HuP!K{P!LP!LZ!L_!L_P!HrP!Lc!LcP# iP# mHxPHx# s#$xCW@zP@zP@z@zP#&V@z@z#(i@z#+a@z#-m@z@z#.]#0q#0q#0v#1P#0q#1[PP#0qP@z#1t@z#5s@z@z6bPPP#9xPPP#:c#:cP#:cP#:y#:cPP#;PP#:vP#:v#;d#:v#<O#<U#<X)`#<[)`P#<c#<c#<cP)`P)`P)`P)`PP)`P#<i#<lP#<l)`P#<pP#<sP)`P)`P)`P)`P)`P)`)`PP#<y#=P#=[#=b#=h#=n#=t#>S#>Y#>d#>j#>t#>z#?[#?b#@S#@f#@l#@r#AQ#Ag#C[#Cj#Cq#E]#Ek#G]#Gk#Gq#Gw#G}#HX#H_#He#Ho#IR#IXPPPPPPPPPPP#I_PPPPPPP#JS#MZ#Ns#Nz$ SPPP$&nP$&w$)p$0Z$0^$0a$1`$1c$1j$1rP$1x$1{P$2i$2m$3e$4s$4x$5`PP$5e$5k$5o$5r$5v$5z$6v$7_$7v$7z$7}$8Q$8W$8Z$8_$8cR!|RoqOXst!Z#d%l&p&r&s&u,n,s2S2VY!vQ'^-`1g5qQ%svQ%{yQ&S|Q&h!VS'U!e-WQ'd!iS'j!r!yU*h$|*X*lQ+l%|Q+y&UQ,_&bQ-^']Q-h'eQ-p'kQ0U*nQ1q,`R<m;z%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y,k,n,s-d-l-z.Q.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3d4q5y6Z6[6_6r8i8x9SS#q];w!r)]$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sU*{%[<e<fQ+q&OQ,a&eQ,h&mQ0r+dQ0w+fQ1S+rQ1y,fQ3W.bQ5V0vQ5]0}Q6Q1rQ7O3[Q8U5^R9Y7Q'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=s!S!nQ!r!v!y!z$|'U']'^'j'k'l*h*l*n*o-W-^-`-p0U0X1g5q5s%[$ti#v$b$c$d$x${%O%Q%]%^%b)w*P*R*T*W*^*d*t*u+c+f+},Q.a.z/_/h/r/s/u0Y0[0g0h0i1^1a1i3Z4U4V4a4f4w5R5U5x6|7l7v7|8Q8f9V9e9n9t:S:f:t:};V;^<^<_<a<b<c<d<g<h<i<j<k<l<t<u<v<w<y<z<}=O=P=Q=R=S=T=U=X=Y=p=x=y=|=}Q&V|Q'S!eS'Y%h-ZQ+q&OQ,a&eQ0f+OQ1S+rQ1X+xQ1x,eQ1y,fQ5]0}Q5f1ZQ6Q1rQ6T1tQ6U1wQ8U5^Q8X5cQ8q6WQ9|8YQ:Y8nR<o*XrnOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VR,c&i&z^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=r=s[#]WZ#W#Z'V(R!b%im#h#i#l$x%d%g([(f(g(h*W*[*_+W+X+Z,j-Q.O.U.V.W.Y/h/k2[3S3T4X6h6yQ%vxQ%zyS&P|&UQ&]!TQ'a!hQ'c!iQ(o#sS+k%{%|Q+o&OQ,Y&`Q,^&bS-g'd'eQ.d(pQ0{+lQ1R+rQ1T+sQ1W+wQ1l,ZS1p,_,`Q2t-hQ5[0}Q5`1QQ5e1YQ6P1qQ8T5^Q8W5bQ9x8SR:w9y!U$zi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y!^%xy!i!u%z%{%|'T'c'd'e'i's*g+k+l-T-g-h-o/{0O0{2m2t2{4i4j4m7s9pQ+e%vQ,O&YQ,R&ZQ,]&bQ.c(oQ1k,YU1o,^,_,`Q3].dQ5z1lS6O1p1qQ8m6P#f=t#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}o=u<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=YW%Ti%V*v=pS&Y!Q&gQ&Z!RQ&[!SQ+S%cR+|&W%]%Si#v$b$c$d$x${%O%Q%]%^%b)w*P*R*T*W*^*d*t*u+c+f+},Q.a.z/_/h/r/s/u0Y0[0g0h0i1^1a1i3Z4U4V4a4f4w5R5U5x6|7l7v7|8Q8f9V9e9n9t:S:f:t:};V;^<^<_<a<b<c<d<g<h<i<j<k<l<t<u<v<w<y<z<}=O=P=Q=R=S=T=U=X=Y=p=x=y=|=}T)x$u)yV*{%[<e<fW'Y!e%h*X-ZS({#y#zQ+`%qQ+v&RS.](k(lQ1b,SQ4x0cR8^5k'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=s$i$^c#Y#e%p%r%t(Q(W(r(w)P)Q)R)S)T)U)V)W)X)Y)[)^)`)e)o+a+u-U-s-x-}.P.n.q.u.w.x.y/]0j2c2f2v2}3c3h3i3j3k3l3m3n3o3p3q3r3s3t3w3x4P5O5Y6k6q6v7V7W7a7b8`8|9Q9[9b9c:c:y;R;x=gT#TV#U'RkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sQ'W!eR2i-W!W!nQ!e!r!v!y!z$|'U']'^'j'k'l*X*h*l*n*o-W-^-`-p0U0X1g5q5sR1d,UnqOXst!Z#d%l&p&r&s&u,n,s2S2VQ&w!^Q't!xS(q#u<OQ+i%yQ,W&]Q,X&_Q-e'bQ-r'mS.m(v<qS0k+U<{Q0y+jQ1f,VQ2Z,uQ2],vQ2e-RQ2r-fQ2u-jS5P0l=VQ5W0zS5Z0|=WQ6j2gQ6n2sQ6s2zQ8R5XQ8}6lQ9O6oQ9R6tR:`8z$d$]c#Y#e%r%t(Q(W(r(w)P)Q)R)S)T)U)V)W)X)Y)[)^)`)e)o+a+u-U-s-x-}.P.n.q.u.x.y/]0j2c2f2v2}3c3h3i3j3k3l3m3n3o3p3q3r3s3t3w3x4P5O5Y6k6q6v7V7W7a7b8`8|9Q9[9b9c:c:y;R;x=gS(m#p'gQ(}#zS+_%p.wS.^(l(nR3U._'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sS#q];wQ&r!XQ&s!YQ&u![Q&v!]R2R,qQ'_!hQ+b%vQ-c'aS.`(o+eQ2p-bW3Y.c.d0q0sQ6m2qW6z3V3X3]5TU9U6{6}7PU:e9W9X9ZS;T:d:gQ;b;UR;j;cU!wQ'^-`T5o1g5q!Q_OXZ`st!V!Z#d#h%d%l&g&i&p&r&s&u(h,n,s.V2S2V]!pQ!r'^-`1g5qT#q];w%^{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SS({#y#zS.](k(l!s=^$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sU$fd)],hS(n#p'gU*s%R(u3vU0e*z.i7]Q5T0rQ6{3WQ9X7OR:g9Ym!tQ!r!v!y!z'^'j'k'l-`-p1g5q5sQ'r!uS(d#g1|S-n'i'uQ/n*ZQ/{*gQ2|-qQ4]/oQ4i/}Q4j0OQ4o0WQ7h4WS7s4k4mS7w4p4rQ9g7iQ9k7oQ9p7tQ9u7yS:o9l9mS;Y:p:sS;e;Z;[S;m;f;gS;q;n;oR;t;rQ#wbQ'q!uS(c#g1|S(e#m+TQ+V%eQ+g%wQ+m%}U-m'i'r'uQ.R(dQ/m*ZQ/|*gQ0P*iQ0x+hQ1m,[S2y-n-qQ3R.ZS4[/n/oQ4e/yS4h/{0WQ4l0QQ5|1nQ6u2|Q7g4WQ7k4]U7r4i4o4rQ7u4nQ8k5}S9f7h7iQ9j7oQ9r7wQ9s7xQ:V8lQ:m9gS:n9k9mQ:v9uQ;P:WS;X:o:sS;d;Y;ZS;l;e;gS;p;m;oQ;s;qQ;u;tQ=a=[Q=l=eR=m=fV!wQ'^-`%^aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SS#wz!j!r=Z$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sR=a=r%^bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SQ%ej!^%wy!i!u%z%{%|'T'c'd'e'i's*g+k+l-T-g-h-o/{0O0{2m2t2{4i4j4m7s9pS%}z!jQ+h%xQ,[&bW1n,],^,_,`U5}1o1p1qS8l6O6PQ:W8m!r=[$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sQ=e=qR=f=r%QeOPXYstuvw!Z!`!g!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&p&r&s&u&y'R'`'p(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SY#bWZ#W#Z(R!b%im#h#i#l$x%d%g([(f(g(h*W*[*_+W+X+Z,j-Q.O.U.V.W.Y/h/k2[3S3T4X6h6yQ,i&m!p=]$Z$n)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sR=`'VU'Z!e%h*XR2k-Z%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y,k,n,s-d-l-z.Q.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3d4q5y6Z6[6_6r8i8x9S!r)]$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sQ,h&mQ0r+dQ3W.bQ7O3[R9Y7Q!b$Tc#Y%p(Q(W(r(w)X)Y)^)e+u-s-x-}.P.n.q/]0j2v2}3c3s5O5Y6q6v7V9Q:c;x!P<U)[)o-U.w2c2f3h3q3r3w4P6k7W7a7b8`8|9[9b9c:y;R=g!f$Vc#Y%p(Q(W(r(w)U)V)X)Y)^)e+u-s-x-}.P.n.q/]0j2v2}3c3s5O5Y6q6v7V9Q:c;x!T<W)[)o-U.w2c2f3h3n3o3q3r3w4P6k7W7a7b8`8|9[9b9c:y;R=g!^$Zc#Y%p(Q(W(r(w)^)e+u-s-x-}.P.n.q/]0j2v2}3c3s5O5Y6q6v7V9Q:c;xQ4V/fz=s)[)o-U.w2c2f3h3w4P6k7W7a7b8`8|9[9b9c:y;R=gQ=x=zR=y={'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sS$oh$pR3|/P'XgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/P/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sT$kf$qQ$ifS)h$l)lR)t$qT$jf$qT)j$l)l'XhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/P/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sT$oh$pQ$rhR)s$p%^jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9S!s=q$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=s#glOPXZst!Z!`!o#S#d#o#{$n%l&i&l&m&p&r&s&u&y'R'`(|)q*f+Y+d,k,n,s-d.b/Q/i0V0d1j1z1{1}2P2S2V2X3[3{4q5y6Z6[6_7Q8i8x!U%Ri$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y#f(u#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}Q+P%`Q/^)|o3v<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=Y!U$yi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=yQ*`$zU*i$|*X*lQ+Q%aQ0Q*j#f=c#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}n=d<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=YQ=h=tQ=i=uQ=j=vR=k=w!U%Ri$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y#f(u#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}o3v<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=YnoOXst!Z#d%l&p&r&s&u,n,s2S2VS*c${*WQ,|&|Q,}'OR4`/s%[%Si#v$b$c$d$x${%O%Q%]%^%b)w*P*R*T*W*^*d*t*u+c+f+},Q.a.z/_/h/r/s/u0Y0[0g0h0i1^1a1i3Z4U4V4a4f4w5R5U5x6|7l7v7|8Q8f9V9e9n9t:S:f:t:};V;^<^<_<a<b<c<d<g<h<i<j<k<l<t<u<v<w<y<z<}=O=P=Q=R=S=T=U=X=Y=p=x=y=|=}Q,P&ZQ1`,RQ5i1_R8]5jV*k$|*X*lU*k$|*X*lT5p1g5qS/y*f/iQ4n0VT7x4q:PQ+g%wQ0P*iQ0x+hQ1m,[Q5|1nQ8k5}Q:V8lR;P:W!U%Oi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=yx*P$v)c*Q*r+R/q0^0_3y4^4{4|4}7f7z9v:l=b=n=oS0Y*q0Z#f<a#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}n<b<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=Y!d<t(s)a*Y*b.e.h.l/Y/f/v0p1]3`4S4_4c5h7R7U7m7p7}8P9i9q9w:q:u;W;];h=z={`<u3u7X7[7`9]:h:k;kS=P.g3aT=Q7Z9`!U%Qi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y|*R$v)c*S*q+R/b/q0^0_3y4^4s4{4|4}7f7z9v:l=b=n=oS0[*r0]#f<c#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}n<d<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=Y!h<v(s)a*Y*b.f.g.l/Y/f/v0p1]3^3`4S4_4c5h7R7S7U7m7p7}8P9i9q9w:q:u;W;];h=z={d<w3u7Y7Z7`9]9^:h:i:k;kS=R.h3bT=S7[9arnOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VQ&d!UR,k&mrnOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VR&d!UQ,T&[R1[+|snOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VQ1h,YS5w1k1lU8e5u5v5zS:R8g8hS:{:Q:TQ;_:|R;i;`Q&k!VR,d&gR6T1tR:Y8nS&P|&UR1T+sQ&p!WR,n&qR,t&vT2T,s2VR,x&wQ,w&wR2^,xQ'w!{R-t'wSsOtQ#dXT%os#dQ#OTR'y#OQ#RUR'{#RQ)y$uR/Z)yQ#UVR(O#UQ#XWU(U#X(V-{Q(V#YR-{(WQ-X'WR2j-XQ.p(wS3e.p3fR3f.qQ-`'^R2n-`Y!rQ'^-`1g5qR'h!rQ.{)cR3z.{U#_W%g*WU(]#_(^-|Q(^#`R-|(XQ-['ZR2l-[t`OXst!V!Z#d%l&g&i&p&r&s&u,n,s2S2VS#hZ%dU#r`#h.VR.V(hQ(i#jQ.S(eW.[(i.S3P6wQ3P.TR6w3QQ)l$lR/R)lQ$phR)r$pQ$`cU)_$`-w<[Q-w;xR<[)oQ/l*ZW4Y/l4Z7j9hU4Z/m/n/oS7j4[4]R9h7k$e*O$v(s)a)c*Y*b*q*r*|*}+R.g.h.j.k.l/Y/b/d/f/q/v0^0_0p1]3^3_3`3u3y4S4^4_4c4s4u4{4|4}5h7R7S7T7U7Z7[7^7_7`7f7m7p7z7}8P9]9^9_9i9q9v9w:h:i:j:k:l:q:u;W;];h;k=b=n=o=z={Q/t*bU4b/t4d7nQ4d/vR7n4cS*l$|*XR0S*lx*Q$v)c*q*r+R/q0^0_3y4^4{4|4}7f7z9v:l=b=n=o!d.e(s)a*Y*b.g.h.l/Y/f/v0p1]3`4S4_4c5h7R7U7m7p7}8P9i9q9w:q:u;W;];h=z={U/c*Q.e7Xa7X3u7Z7[7`9]:h:k;kQ0Z*qQ3a.gU4t0Z3a9`R9`7Z|*S$v)c*q*r+R/b/q0^0_3y4^4s4{4|4}7f7z9v:l=b=n=o!h.f(s)a*Y*b.g.h.l/Y/f/v0p1]3^3`4S4_4c5h7R7S7U7m7p7}8P9i9q9w:q:u;W;];h=z={U/e*S.f7Ye7Y3u7Z7[7`9]9^:h:i:k;kQ0]*rQ3b.hU4v0]3b9aR9a7[Q*w%UR0a*wQ5S0pR8O5SQ+[%jR0o+[Q5l1bS8_5l:OR:O8`Q,V&]R1e,VQ5q1gR8b5qQ1s,aS6R1s8oR8o6TQ1O+oW5_1O5a8V9zQ5a1RQ8V5`R9z8WQ+t&PR1U+tQ2V,sR6c2VYrOXst#dQ&t!ZQ+^%lQ,m&pQ,o&rQ,p&sQ,r&uQ2Q,nS2T,s2VR6b2SQ%npQ&x!_Q&{!aQ&}!bQ'P!cQ'o!uQ+]%kQ+i%yQ+{&VQ,c&kQ,z&zW-k'i'q'r'uQ-r'mQ0R*kQ0y+jS1v,d,gQ2_,yQ2`,|Q2a,}Q2u-jW2w-m-n-q-sQ5W0zQ5d1XQ5g1]Q5{1mQ6V1xQ6a2RU6p2v2y2|Q6s2zQ8R5XQ8Z5fQ8[5hQ8a5pQ8j5|Q8p6US9P6q6uQ9R6tQ9{8XQ:U8kQ:Z8qQ:b9QQ:x9|Q;O:VQ;S:cR;a;PQ%yyQ'b!iQ'm!uU+j%z%{%|Q-R'TU-f'c'd'eS-j'i'sQ/z*gS0z+k+lQ2g-TS2s-g-hQ2z-oS4g/{0OQ5X0{Q6l2mQ6o2tQ6t2{U7q4i4j4mQ9o7sR:r9pS$wi=pR*x%VU%Ui%V=pR0`*vQ$viS(s#v+fS)a$b$cQ)c$dQ*Y$xS*b${*WQ*q%OQ*r%QQ*|%]Q*}%^Q+R%bQ.g<aQ.h<cQ.j<gQ.k<iQ.l<kQ/Y)wQ/b*PQ/d*RQ/f*TQ/q*^S/v*d/hQ0^*tQ0_*ul0p+c,Q.a1a1i3Z5x6|8f9V:S:f:};VQ1]+}Q3^<tQ3_<vQ3`<yS3u<^<_Q3y.zS4S/_4UQ4^/rQ4_/sQ4c/uQ4s0YQ4u0[Q4{0gQ4|0hQ4}0iQ5h1^Q7R<}Q7S=PQ7T=RQ7U=TQ7Z<bQ7[<dQ7^<hQ7_<jQ7`<lQ7f4VQ7m4aQ7p4fQ7z4wQ7}5RQ8P5UQ9]<zQ9^<uQ9_<wQ9i7lQ9q7vQ9v7|Q9w8QQ:h=OQ:i=QQ:j=SQ:k=UQ:l9eQ:q9nQ:u9tQ;W=XQ;]:tQ;h;^Q;k=YQ=b=pQ=n=xQ=o=yQ=z=|R={=}Q*z%[Q.i<eR7]<fnpOXst!Z#d%l&p&r&s&u,n,s2S2VQ!fPS#fZ#oQ&z!`W'f!o*f0V4qQ'}#SQ)O#{Q)p$nS,g&i&lQ,l&mQ,y&yS-O'R/iQ-b'`Q.s(|Q/V)qQ0m+YQ0s+dQ2O,kQ2q-dQ3X.bQ4O/QQ4y0dQ5v1jQ6X1zQ6Y1{Q6^1}Q6`2PQ6e2XQ7P3[Q7c3{Q8h5yQ8t6ZQ8u6[Q8w6_Q9Z7QQ:T8iR:_8x#[cOPXZst!Z!`!o#d#o#{%l&i&l&m&p&r&s&u&y'R'`(|*f+Y+d,k,n,s-d.b/i0V0d1j1z1{1}2P2S2V2X3[4q5y6Z6[6_7Q8i8xQ#YWQ#eYQ%puQ%rvS%tw!gS(Q#W(TQ(W#ZQ(r#uQ(w#xQ)P$OQ)Q$PQ)R$QQ)S$RQ)T$SQ)U$TQ)V$UQ)W$VQ)X$WQ)Y$XQ)[$ZQ)^$_Q)`$aQ)e$eW)o$n)q/Q3{Q+a%sQ+u&QS-U'V2hQ-s'pS-x(R-zQ-}(ZQ.P(bQ.n(vQ.q(xQ.u;vQ.w;yQ.x;zQ.y;}Q/]){Q0j+UQ2c-PQ2f-SQ2v-lQ2}.QQ3c.oQ3h<OQ3i<PQ3j<QQ3k<RQ3l<SQ3m<TQ3n<UQ3o<VQ3p<WQ3q<XQ3r<YQ3s.vQ3t<]Q3w<`Q3x<mQ4P<ZQ5O0lQ5Y0|Q6k<pQ6q2xQ6v3OQ7V3dQ7W<qQ7a<sQ7b<{Q8`5mQ8|6iQ9Q6rQ9[<|Q9b=VQ9c=WQ:c9SQ:y9}Q;R:aQ;x#SR=g=sR#[WR'X!el!tQ!r!v!y!z'^'j'k'l-`-p1g5q5sS'T!e-WU*g$|*X*lS-T'U']S0O*h*nQ0W*oQ2m-^Q4m0UR4r0XR(y#xQ!fQT-_'^-`]!qQ!r'^-`1g5qQ#p]R'g;wR)d$dY!uQ'^-`1g5qQ'i!rS's!v!yS'u!z5sS-o'j'kQ-q'lR2{-pT#kZ%dS#jZ%dS%jm,jU(e#h#i#lS.T(f(gQ.X(hQ0n+ZQ3Q.UU3R.V.W.YS6x3S3TR9T6yd#^W#W#Z%g(R([*W+W.O/hr#gZm#h#i#l%d(f(g(h+Z.U.V.W.Y3S3T6yS*Z$x*_Q/o*[Q1|,jQ2d-QQ4W/kQ6g2[Q7i4XQ8{6hT=_'V+XV#aW%g*WU#`W%g*WS(S#W([U(X#Z+W/hS-V'V+XT-y(R.OV'[!e%h*XQ$lfR)v$qT)k$l)lR3}/PT*]$x*_T*e${*WQ0q+cQ1_,QQ3V.aQ5j1aQ5u1iQ6}3ZQ8g5xQ9W6|Q:Q8fQ:d9VQ:|:SQ;U:fQ;`:}R;c;VnqOXst!Z#d%l&p&r&s&u,n,s2S2VQ&j!VR,c&gtmOXst!U!V!Z#d%l&g&p&r&s&u,n,s2S2VR,j&mT%km,jR1c,SR,b&eQ&T|R+z&UR+p&OT&n!W&qT&o!W&qT2U,s2V",nodeNames:"⚠ ArithOp ArithOp ?. JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList in out const TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewTarget new NewExpression ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression TypeArgList CompareOp < declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression InstantiationExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast < ArrowFunction TypeParamList SequenceExpression InstantiationExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate asserts is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:379,context:eu,nodeProps:[["isolate",-8,5,6,14,37,39,51,53,55,""],["group",-26,9,17,19,68,207,211,215,216,218,221,224,234,236,242,244,246,248,251,257,263,265,267,269,271,273,274,"Statement",-34,13,14,32,35,36,42,51,54,55,57,62,70,72,76,80,82,84,85,110,111,120,121,136,139,141,142,143,144,145,147,148,167,169,171,"Expression",-23,31,33,37,41,43,45,173,175,177,178,180,181,182,184,185,186,188,189,190,201,203,205,206,"Type",-3,88,103,109,"ClassItem"],["openedBy",23,"<",38,"InterpolationStart",56,"[",60,"{",73,"(",160,"JSXStartCloseTag"],["closedBy",-2,24,168,">",40,"InterpolationEnd",50,"]",61,"}",74,")",165,"JSXEndTag"]],propSources:[su],skippedNodes:[0,5,6,277],repeatNodeCount:37,tokenData:"$Fq07[R!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$,r!c!}Er!}#O$-|#O#P$/W#P#Q$4o#Q#R$5y#R#SEr#S#T$7W#T#o$8b#o#p$<r#p#q$=h#q#r$>x#r#s$@U#s$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$I|Er$I|$I}$Dk$I}$JO$Dk$JO$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr(n%d_$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$i&j(Y!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(Y!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$i&j(VpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(VpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z07[+rq$i&j(Vp(Y!b'{0/lOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z07[.ST(W#S$i&j'|0/lO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c07[.n_$i&j(Vp(Y!b'|0/lOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)3p/x`$i&j!p),Q(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW1V`#v(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW2d_#v(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At3l_(U':f$i&j(Y!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$i&j(Y!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$i&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$d`$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$d``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$d`$i&j(Y!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(Y!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$d`(Y!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k%9[:hh$i&j(Vp(Y!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__WS$i&j(Vp(Y!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]WS$i&j(Y!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXWS$i&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSWSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWWS(Y!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]WS$i&j(VpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWWS(VpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYWS(Vp(Y!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S%9[C}i$i&j(n%1l(Vp(Y!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr%9[EoP;=`<%lCr07[FRk$i&j(Vp(Y!b$]#t(S,2j(d$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$i&j(Vp(Y!b$]#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv07[JPP;=`<%lEr(KWJ_`$i&j(Vp(Y!b#p(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWKl_$i&j$Q(Ch(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,#xLva(y+JY$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWNW`$i&j#z(Ch(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At! c_(X';W$i&j(VpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$i&j(VpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$i&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$d`$i&j(VpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(VpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$d`(VpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b/5|!'t_!l/.^$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&U!)O_!k!Lf$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z-!n!*[b$i&j(Vp(Y!b(T%&f#q(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW!+o`$i&j(Vp(Y!b#n(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;x!,|`$i&j(Vp(Y!br+4YOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,$U!.Z_!]+Jf$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!/ec$i&j(Vp(Y!b!Q.2^OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!0ya$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!2Z_![!L^$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!3eg$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!5Vg$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!6wc$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!8_c$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!9uf$i&j(Vp(Y!b#o(ChOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z?O!;fb$i&j(Vp(Y!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z>^!<w`$i&j(Y!b!X7`OY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n<z!>Q^$i&j!X7`OY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!?Td$i&j!X7`O!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&c7`!@hX!X7`OY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@c7`!AYW!X7`#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!AT7`!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!Ar7`!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!Ar7`!BnP;=`<%l!Ar7`!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@c7`!CTP;=`<%l!@c<z!C][$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DWX$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DvP;=`<%l!CW<z!EOX$i&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!EnP;=`<%l!=y>^!Ezl$i&j(Y!b!X7`OY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}8r!GyZ(Y!b!X7`OY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr8r!Hse(Y!b!X7`OY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}8r!JZX(Y!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU8r!JyP;=`<%l!JU8r!KPP;=`<%l!Gr>^!KZ^$i&j(Y!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS>^!LYP;=`<%l!KS>^!L`P;=`<%l!<n=l!Ll`$i&j(Vp!X7`OY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc=l!Mwl$i&j(Vp!X7`OY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r8Q# vZ(Vp!X7`OY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# o8Q#!pe(Vp!X7`OY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)r8Q#$WX(VpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$R8Q#$vP;=`<%l#$R8Q#$|P;=`<%l# o=l#%W^$i&j(VpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P=l#&VP;=`<%l#%P=l#&]P;=`<%l!Lc?O#&kn$i&j(Vp(Y!b!X7`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z9d#(r](Vp(Y!b!X7`OY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i9d#)th(Vp(Y!b!X7`OY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g9d#+gZ(Vp(Y!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`9d#,]P;=`<%l#+`9d#,cP;=`<%l#(i?O#,o`$i&j(Vp(Y!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f?O#-tP;=`<%l#,f?O#-zP;=`<%l!;Z07[#.[b$i&j(Vp(Y!b'}0/l!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z07[#/o_$i&j(Vp(Y!bT0/lOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d06j#0w]$i&j(Y!bT0/lOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n05W#1wX$i&jT0/lOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p0/l#2iST0/lOY#2dZ;'S#2d;'S;=`#2u<%lO#2d0/l#2xP;=`<%l#2d05W#3OP;=`<%l#1p01O#3YW(Y!bT0/lOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R01O#3uP;=`<%l#3R06j#3{P;=`<%l#0n05x#4X]$i&j(VpT0/lOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O00^#5XW(VpT0/lOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q00^#5tP;=`<%l#5Q05x#5zP;=`<%l#4O01p#6WY(Vp(Y!bT0/lOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}01p#6yP;=`<%l#5}07[#7PP;=`<%l#/d)3h#7ab$i&j$Q(Ch(Vp(Y!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;ZAt#8vb$Z#t$i&j(Vp(Y!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z'Ad#:Zp$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#<jk$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#>j_$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#?rd$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#A]f$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Bzc$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Dbe$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#E|g$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Gpi$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x#Il_!g$b$i&j$O)Lv(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_al$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f#LS^h#)`#R-<U(Vp(Y!b$n7`OY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$k&j(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El#M}Z#r(Ch(Vp(Y!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g(El#NyX$Q(Ch(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El$ oX#s(Ch(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g*)x$!ga#`*!Y$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(K[$#w_#k(Cl$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x$%Vag!*r#s(Ch$f#|$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$&g_#s(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$'qa#r(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$)R`#r(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(Kd$*`a(q(Ct$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!a%Z!a!b$+e!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$+p`$i&j#{(Ch(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`$,}_!|$Ip$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f$.X_!S0,v$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/]Z$i&jO!^$0O!^!_$0f!_#i$0O#i#j$0k#j#l$0O#l#m$2^#m#o$0O#o#p$0f#p;'S$0O;'S;=`$4i<%lO$0O(n$0VT_#S$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0kO_#S(n$0p[$i&jO!Q&c!Q![$1f![!^&c!_!c&c!c!i$1f!i#T&c#T#Z$1f#Z#o&c#o#p$3|#p;'S&c;'S;=`&w<%lO&c(n$1kZ$i&jO!Q&c!Q![$2^![!^&c!_!c&c!c!i$2^!i#T&c#T#Z$2^#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2cZ$i&jO!Q&c!Q![$3U![!^&c!_!c&c!c!i$3U!i#T&c#T#Z$3U#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3ZZ$i&jO!Q&c!Q![$0O![!^&c!_!c&c!c!i$0O!i#T&c#T#Z$0O#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$4PR!Q![$4Y!c!i$4Y#T#Z$4Y#S$4]S!Q![$4Y!c!i$4Y#T#Z$4Y#q#r$0f(n$4lP;=`<%l$0O#1[$4z_!Y#)l$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$6U`#x(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;p$7c_$i&j(Vp(Y!b(`+4QOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$8qk$i&j(Vp(Y!b(S,2j$_#t(d$I[OY%ZYZ&cZr%Zrs&}st%Ztu$8buw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$8b![!^%Z!^!_*g!_!c%Z!c!}$8b!}#O%Z#O#P&c#P#R%Z#R#S$8b#S#T%Z#T#o$8b#o#p*g#p$g%Z$g;'S$8b;'S;=`$<l<%lO$8b+d$:qk$i&j(Vp(Y!b$_#tOY%ZYZ&cZr%Zrs&}st%Ztu$:fuw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$:f![!^%Z!^!_*g!_!c%Z!c!}$:f!}#O%Z#O#P&c#P#R%Z#R#S$:f#S#T%Z#T#o$:f#o#p*g#p$g%Z$g;'S$:f;'S;=`$<f<%lO$:f+d$<iP;=`<%l$:f07[$<oP;=`<%l$8b#Jf$<{X!_#Hb(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g,#x$=sa(x+JY$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+e#q;'S%Z;'S;=`+a<%lO%Z)>v$?V_!^(CdvBr$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z?O$@a_!q7`$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$Aq|$i&j(Vp(Y!b'{0/l$]#t(S,2j(d$I[OX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr07[$D|k$i&j(Vp(Y!b'|0/l$]#t(S,2j(d$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[au,ru,nu,iu,2,3,4,5,6,7,8,9,10,11,12,13,14,tu,new na("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOx~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!U~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(b~~",141,339),new na("j~RQYZXz{^~^O(P~~aP!P!Qd~iO(Q~~",25,322)],topRules:{Script:[0,7],SingleExpression:[1,275],SingleClassItem:[2,276]},dialects:{jsx:0,ts:15098},dynamicPrecedences:{80:1,82:1,94:1,169:1,199:1},specialized:[{term:326,get:e=>ou[e]||-1},{term:342,get:e=>lu[e]||-1},{term:95,get:e=>Ou[e]||-1}],tokenPrec:15124}),Js=[Ae("function ${name}(${params}) {\n	${}\n}",{label:"function",detail:"definition",type:"keyword"}),Ae("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n	${}\n}",{label:"for",detail:"loop",type:"keyword"}),Ae("for (let ${name} of ${collection}) {\n	${}\n}",{label:"for",detail:"of loop",type:"keyword"}),Ae("do {\n	${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),Ae("while (${}) {\n	${}\n}",{label:"while",detail:"loop",type:"keyword"}),Ae(`try {
	\${}
} catch (\${error}) {
	\${}
}`,{label:"try",detail:"/ catch block",type:"keyword"}),Ae("if (${}) {\n	${}\n}",{label:"if",detail:"block",type:"keyword"}),Ae(`if (\${}) {
	\${}
} else {
	\${}
}`,{label:"if",detail:"/ else block",type:"keyword"}),Ae(`class \${name} {
	constructor(\${params}) {
		\${}
	}
}`,{label:"class",detail:"definition",type:"keyword"}),Ae('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),Ae('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],uu=Js.concat([Ae("interface ${name} {\n	${}\n}",{label:"interface",detail:"definition",type:"keyword"}),Ae("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),Ae("enum ${name} {\n	${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),Ci=new ys,eo=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function Ct(e){return(t,a)=>{let r=t.node.getChild("VariableDefinition");return r&&a(r,e),!0}}const fu=["FunctionDeclaration"],du={FunctionDeclaration:Ct("function"),ClassDeclaration:Ct("class"),ClassExpression:()=>!0,EnumDeclaration:Ct("constant"),TypeAliasDeclaration:Ct("type"),NamespaceDeclaration:Ct("namespace"),VariableDefinition(e,t){e.matchContext(fu)||t(e,"variable")},TypeDefinition(e,t){t(e,"type")},__proto__:null};function to(e,t){let a=Ci.get(t);if(a)return a;let r=[],n=!0;function i(s,o){let c=e.sliceString(s.from,s.to);r.push({label:c,type:o})}return t.cursor(Br.IncludeAnonymous).iterate(s=>{if(n)n=!1;else if(s.name){let o=du[s.name];if(o&&o(s,i)||eo.has(s.name))return!1}else if(s.to-s.from>8192){for(let o of to(e,s.node))r.push(o);return!1}}),Ci.set(t,r),r}const Ei=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,ao=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName","JSXText","JSXAttributeValue","JSXOpenTag","JSXCloseTag","JSXSelfClosingTag",".","?."];function hu(e){let t=It(e.state).resolveInner(e.pos,-1);if(ao.indexOf(t.name)>-1)return null;let a=t.name=="VariableName"||t.to-t.from<20&&Ei.test(e.state.sliceDoc(t.from,t.to));if(!a&&!e.explicit)return null;let r=[];for(let n=t;n;n=n.parent)eo.has(n.name)&&(r=r.concat(to(e.state.doc,n)));return{options:r,from:a?t.from:e.pos,validFor:Ei}}const ze=Lr.define({name:"javascript",parser:cu.configure({props:[Fr.add({IfStatement:Nt({except:/^\s*({|else\b)/}),TryStatement:Nt({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:V0,SwitchBody:e=>{let t=e.textAfter,a=/^\s*\}/.test(t),r=/^\s*(case|default)\b/.test(t);return e.baseIndent+(a?0:r?1:2)*e.unit},Block:U0({closing:"}"}),ArrowFunction:e=>e.baseIndent+e.unit,"TemplateString BlockComment":()=>null,"Statement Property":Nt({except:/^{/}),JSXElement(e){let t=/^\s*<\//.test(e.textAfter);return e.lineIndent(e.node.from)+(t?0:e.unit)},JSXEscape(e){let t=/\s*\}/.test(e.textAfter);return e.lineIndent(e.node.from)+(t?0:e.unit)},"JSXOpenTag JSXSelfClosingTag"(e){return e.column(e.node.from)+e.unit}}),Yr.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":Ps,BlockComment(e){return{from:e.from+2,to:e.to-2}}})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),ro={test:e=>/^JSX/.test(e.name),facet:z0({commentTokens:{block:{open:"{/*",close:"*/}"}}})},no=ze.configure({dialect:"ts"},"typescript"),io=ze.configure({dialect:"jsx",props:[$s.add(e=>e.isTop?[ro]:void 0)]}),so=ze.configure({dialect:"jsx ts",props:[$s.add(e=>e.isTop?[ro]:void 0)]},"typescript");let oo=e=>({label:e,type:"keyword"});const lo="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(oo),pu=lo.concat(["declare","implements","private","protected","public"].map(oo));function xu(e={}){let t=e.jsx?e.typescript?so:io:e.typescript?no:ze,a=e.typescript?uu.concat(pu):Js.concat(lo);return new Ir(t,[ze.data.of({autocomplete:M0(ao,W0(a))}),ze.data.of({autocomplete:hu}),e.jsx?vu:[]])}function mu(e){for(;;){if(e.name=="JSXOpenTag"||e.name=="JSXSelfClosingTag"||e.name=="JSXFragmentTag")return e;if(e.name=="JSXEscape"||!e.parent)return null;e=e.parent}}function Ai(e,t,a=e.length){for(let r=t==null?void 0:t.firstChild;r;r=r.nextSibling)if(r.name=="JSXIdentifier"||r.name=="JSXBuiltin"||r.name=="JSXNamespacedName"||r.name=="JSXMemberExpression")return e.sliceString(r.from,Math.min(r.to,a));return""}const gu=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),vu=Dt.inputHandler.of((e,t,a,r,n)=>{if((gu?e.composing:e.compositionStarted)||e.state.readOnly||t!=a||r!=">"&&r!="/"||!ze.isActiveAt(e.state,t,-1))return!1;let i=n(),{state:s}=i,o=s.changeByRange(c=>{var O;let{head:d}=c,g=It(s).resolveInner(d-1,-1),l;if(g.name=="JSXStartTag"&&(g=g.parent),!(s.doc.sliceString(d-1,d)!=r||g.name=="JSXAttributeValue"&&g.to>d)){if(r==">"&&g.name=="JSXFragmentTag")return{range:c,changes:{from:d,insert:"</>"}};if(r=="/"&&g.name=="JSXStartCloseTag"){let u=g.parent,f=u.parent;if(f&&u.from==d-2&&((l=Ai(s.doc,f.firstChild,d))||((O=f.firstChild)===null||O===void 0?void 0:O.name)=="JSXFragmentTag")){let h=`${l}>`;return{range:ws.cursor(d+h.length,-1),changes:{from:d,insert:h}}}}else if(r==">"){let u=mu(g);if(u&&u.name=="JSXOpenTag"&&!/^\/?>|^<\//.test(s.doc.sliceString(d,d+2))&&(l=Ai(s.doc,u,d)))return{range:c,changes:{from:d,insert:`</${l}>`}}}}return{range:c}});return o.changes.empty?!1:(e.dispatch([i,s.update(o,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}),Et=["_blank","_self","_top","_parent"],sr=["ascii","utf-8","utf-16","latin1","latin1"],or=["get","post","put","delete"],lr=["application/x-www-form-urlencoded","multipart/form-data","text/plain"],Be=["true","false"],W={},Qu={a:{attrs:{href:null,ping:null,type:null,media:null,target:Et,hreflang:null}},abbr:W,address:W,area:{attrs:{alt:null,coords:null,href:null,target:null,ping:null,media:null,hreflang:null,type:null,shape:["default","rect","circle","poly"]}},article:W,aside:W,audio:{attrs:{src:null,mediagroup:null,crossorigin:["anonymous","use-credentials"],preload:["none","metadata","auto"],autoplay:["autoplay"],loop:["loop"],controls:["controls"]}},b:W,base:{attrs:{href:null,target:Et}},bdi:W,bdo:W,blockquote:{attrs:{cite:null}},body:W,br:W,button:{attrs:{form:null,formaction:null,name:null,value:null,autofocus:["autofocus"],disabled:["autofocus"],formenctype:lr,formmethod:or,formnovalidate:["novalidate"],formtarget:Et,type:["submit","reset","button"]}},canvas:{attrs:{width:null,height:null}},caption:W,center:W,cite:W,code:W,col:{attrs:{span:null}},colgroup:{attrs:{span:null}},command:{attrs:{type:["command","checkbox","radio"],label:null,icon:null,radiogroup:null,command:null,title:null,disabled:["disabled"],checked:["checked"]}},data:{attrs:{value:null}},datagrid:{attrs:{disabled:["disabled"],multiple:["multiple"]}},datalist:{attrs:{data:null}},dd:W,del:{attrs:{cite:null,datetime:null}},details:{attrs:{open:["open"]}},dfn:W,div:W,dl:W,dt:W,em:W,embed:{attrs:{src:null,type:null,width:null,height:null}},eventsource:{attrs:{src:null}},fieldset:{attrs:{disabled:["disabled"],form:null,name:null}},figcaption:W,figure:W,footer:W,form:{attrs:{action:null,name:null,"accept-charset":sr,autocomplete:["on","off"],enctype:lr,method:or,novalidate:["novalidate"],target:Et}},h1:W,h2:W,h3:W,h4:W,h5:W,h6:W,head:{children:["title","base","link","style","meta","script","noscript","command"]},header:W,hgroup:W,hr:W,html:{attrs:{manifest:null}},i:W,iframe:{attrs:{src:null,srcdoc:null,name:null,width:null,height:null,sandbox:["allow-top-navigation","allow-same-origin","allow-forms","allow-scripts"],seamless:["seamless"]}},img:{attrs:{alt:null,src:null,ismap:null,usemap:null,width:null,height:null,crossorigin:["anonymous","use-credentials"]}},input:{attrs:{alt:null,dirname:null,form:null,formaction:null,height:null,list:null,max:null,maxlength:null,min:null,name:null,pattern:null,placeholder:null,size:null,src:null,step:null,value:null,width:null,accept:["audio/*","video/*","image/*"],autocomplete:["on","off"],autofocus:["autofocus"],checked:["checked"],disabled:["disabled"],formenctype:lr,formmethod:or,formnovalidate:["novalidate"],formtarget:Et,multiple:["multiple"],readonly:["readonly"],required:["required"],type:["hidden","text","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]}},ins:{attrs:{cite:null,datetime:null}},kbd:W,keygen:{attrs:{challenge:null,form:null,name:null,autofocus:["autofocus"],disabled:["disabled"],keytype:["RSA"]}},label:{attrs:{for:null,form:null}},legend:W,li:{attrs:{value:null}},link:{attrs:{href:null,type:null,hreflang:null,media:null,sizes:["all","16x16","16x16 32x32","16x16 32x32 64x64"]}},map:{attrs:{name:null}},mark:W,menu:{attrs:{label:null,type:["list","context","toolbar"]}},meta:{attrs:{content:null,charset:sr,name:["viewport","application-name","author","description","generator","keywords"],"http-equiv":["content-language","content-type","default-style","refresh"]}},meter:{attrs:{value:null,min:null,low:null,high:null,max:null,optimum:null}},nav:W,noscript:W,object:{attrs:{data:null,type:null,name:null,usemap:null,form:null,width:null,height:null,typemustmatch:["typemustmatch"]}},ol:{attrs:{reversed:["reversed"],start:null,type:["1","a","A","i","I"]},children:["li","script","template","ul","ol"]},optgroup:{attrs:{disabled:["disabled"],label:null}},option:{attrs:{disabled:["disabled"],label:null,selected:["selected"],value:null}},output:{attrs:{for:null,form:null,name:null}},p:W,param:{attrs:{name:null,value:null}},pre:W,progress:{attrs:{value:null,max:null}},q:{attrs:{cite:null}},rp:W,rt:W,ruby:W,samp:W,script:{attrs:{type:["text/javascript"],src:null,async:["async"],defer:["defer"],charset:sr}},section:W,select:{attrs:{form:null,name:null,size:null,autofocus:["autofocus"],disabled:["disabled"],multiple:["multiple"]}},slot:{attrs:{name:null}},small:W,source:{attrs:{src:null,type:null,media:null}},span:W,strong:W,style:{attrs:{type:["text/css"],media:null,scoped:null}},sub:W,summary:W,sup:W,table:W,tbody:W,td:{attrs:{colspan:null,rowspan:null,headers:null}},template:W,textarea:{attrs:{dirname:null,form:null,maxlength:null,name:null,placeholder:null,rows:null,cols:null,autofocus:["autofocus"],disabled:["disabled"],readonly:["readonly"],required:["required"],wrap:["soft","hard"]}},tfoot:W,th:{attrs:{colspan:null,rowspan:null,headers:null,scope:["row","col","rowgroup","colgroup"]}},thead:W,time:{attrs:{datetime:null}},title:W,tr:W,track:{attrs:{src:null,label:null,default:null,kind:["subtitles","captions","descriptions","chapters","metadata"],srclang:null}},ul:{children:["li","script","template","ul","ol"]},var:W,video:{attrs:{src:null,poster:null,width:null,height:null,crossorigin:["anonymous","use-credentials"],preload:["auto","metadata","none"],autoplay:["autoplay"],mediagroup:["movie"],muted:["muted"],controls:["controls"]}},wbr:W},Oo={accesskey:null,class:null,contenteditable:Be,contextmenu:null,dir:["ltr","rtl","auto"],draggable:["true","false","auto"],dropzone:["copy","move","link","string:","file:"],hidden:["hidden"],id:null,inert:["inert"],itemid:null,itemprop:null,itemref:null,itemscope:["itemscope"],itemtype:null,lang:["ar","bn","de","en-GB","en-US","es","fr","hi","id","ja","pa","pt","ru","tr","zh"],spellcheck:Be,autocorrect:Be,autocapitalize:Be,style:null,tabindex:null,title:null,translate:["yes","no"],rel:["stylesheet","alternate","author","bookmark","help","license","next","nofollow","noreferrer","prefetch","prev","search","tag"],role:"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer".split(" "),"aria-activedescendant":null,"aria-atomic":Be,"aria-autocomplete":["inline","list","both","none"],"aria-busy":Be,"aria-checked":["true","false","mixed","undefined"],"aria-controls":null,"aria-describedby":null,"aria-disabled":Be,"aria-dropeffect":null,"aria-expanded":["true","false","undefined"],"aria-flowto":null,"aria-grabbed":["true","false","undefined"],"aria-haspopup":Be,"aria-hidden":Be,"aria-invalid":["true","false","grammar","spelling"],"aria-label":null,"aria-labelledby":null,"aria-level":null,"aria-live":["off","polite","assertive"],"aria-multiline":Be,"aria-multiselectable":Be,"aria-owns":null,"aria-posinset":null,"aria-pressed":["true","false","mixed","undefined"],"aria-readonly":Be,"aria-relevant":null,"aria-required":Be,"aria-selected":["true","false","undefined"],"aria-setsize":null,"aria-sort":["ascending","descending","none","other"],"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null,"aria-valuetext":null},co="beforeunload copy cut dragstart dragover dragleave dragenter dragend drag paste focus blur change click load mousedown mouseenter mouseleave mouseup keydown keyup resize scroll unload".split(" ").map(e=>"on"+e);for(let e of co)Oo[e]=null;class la{constructor(t,a){this.tags=Object.assign(Object.assign({},Qu),t),this.globalAttrs=Object.assign(Object.assign({},Oo),a),this.allTags=Object.keys(this.tags),this.globalAttrNames=Object.keys(this.globalAttrs)}}la.default=new la;function Pt(e,t,a=e.length){if(!t)return"";let r=t.firstChild,n=r&&r.getChild("TagName");return n?e.sliceString(n.from,Math.min(n.to,a)):""}function yt(e,t=!1){for(;e;e=e.parent)if(e.name=="Element")if(t)t=!1;else return e;return null}function uo(e,t,a){let r=a.tags[Pt(e,yt(t))];return(r==null?void 0:r.children)||a.allTags}function Kr(e,t){let a=[];for(let r=yt(t);r&&!r.type.isTop;r=yt(r.parent)){let n=Pt(e,r);if(n&&r.lastChild.name=="CloseTag")break;n&&a.indexOf(n)<0&&(t.name=="EndTag"||t.from>=r.firstChild.to)&&a.push(n)}return a}const fo=/^[:\-\.\w\u00b7-\uffff]*$/;function Ti(e,t,a,r,n){let i=/\s*>/.test(e.sliceDoc(n,n+5))?"":">",s=yt(a,!0);return{from:r,to:n,options:uo(e.doc,s,t).map(o=>({label:o,type:"type"})).concat(Kr(e.doc,a).map((o,c)=>({label:"/"+o,apply:"/"+o+i,type:"type",boost:99-c}))),validFor:/^\/?[:\-\.\w\u00b7-\uffff]*$/}}function Zi(e,t,a,r){let n=/\s*>/.test(e.sliceDoc(r,r+5))?"":">";return{from:a,to:r,options:Kr(e.doc,t).map((i,s)=>({label:i,apply:i+n,type:"type",boost:99-s})),validFor:fo}}function Su(e,t,a,r){let n=[],i=0;for(let s of uo(e.doc,a,t))n.push({label:"<"+s,type:"type"});for(let s of Kr(e.doc,a))n.push({label:"</"+s+">",type:"type",boost:99-i++});return{from:r,to:r,options:n,validFor:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}function bu(e,t,a,r,n){let i=yt(a),s=i?t.tags[Pt(e.doc,i)]:null,o=s&&s.attrs?Object.keys(s.attrs):[],c=s&&s.globalAttrs===!1?o:o.length?o.concat(t.globalAttrNames):t.globalAttrNames;return{from:r,to:n,options:c.map(O=>({label:O,type:"property"})),validFor:fo}}function Pu(e,t,a,r,n){var i;let s=(i=a.parent)===null||i===void 0?void 0:i.getChild("AttributeName"),o=[],c;if(s){let O=e.sliceDoc(s.from,s.to),d=t.globalAttrs[O];if(!d){let g=yt(a),l=g?t.tags[Pt(e.doc,g)]:null;d=(l==null?void 0:l.attrs)&&l.attrs[O]}if(d){let g=e.sliceDoc(r,n).toLowerCase(),l='"',u='"';/^['"]/.test(g)?(c=g[0]=='"'?/^[^"]*$/:/^[^']*$/,l="",u=e.sliceDoc(n,n+1)==g[0]?"":g[0],g=g.slice(1),r++):c=/^[^\s<>='"]*$/;for(let f of d)o.push({label:f,apply:l+f+u,type:"constant"})}}return{from:r,to:n,options:o,validFor:c}}function yu(e,t){let{state:a,pos:r}=t,n=It(a).resolveInner(r,-1),i=n.resolve(r);for(let s=r,o;i==n&&(o=n.childBefore(s));){let c=o.lastChild;if(!c||!c.type.isError||c.from<c.to)break;i=n=o,s=c.from}return n.name=="TagName"?n.parent&&/CloseTag$/.test(n.parent.name)?Zi(a,n,n.from,r):Ti(a,e,n,n.from,r):n.name=="StartTag"?Ti(a,e,n,r,r):n.name=="StartCloseTag"||n.name=="IncompleteCloseTag"?Zi(a,n,r,r):n.name=="OpenTag"||n.name=="SelfClosingTag"||n.name=="AttributeName"?bu(a,e,n,n.name=="AttributeName"?n.from:r,r):n.name=="Is"||n.name=="AttributeValue"||n.name=="UnquotedAttributeValue"?Pu(a,e,n,n.name=="Is"?r:n.from,r):t.explicit&&(i.name=="Element"||i.name=="Text"||i.name=="Document")?Su(a,e,n,r):null}function $u(e){let{extraTags:t,extraGlobalAttributes:a}=e,r=a||t?new la(t,a):la.default;return n=>yu(r,n)}const wu=ze.parser.configure({top:"SingleExpression"}),ho=[{tag:"script",attrs:e=>e.type=="text/typescript"||e.lang=="ts",parser:no.parser},{tag:"script",attrs:e=>e.type=="text/babel"||e.type=="text/jsx",parser:io.parser},{tag:"script",attrs:e=>e.type=="text/typescript-jsx",parser:so.parser},{tag:"script",attrs(e){return/^(importmap|speculationrules|application\/(.+\+)?json)$/i.test(e.type)},parser:wu},{tag:"script",attrs(e){return!e.type||/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(e.type)},parser:ze.parser},{tag:"style",attrs(e){return(!e.lang||e.lang=="css")&&(!e.type||/^(text\/)?(x-)?(stylesheet|css)$/i.test(e.type))},parser:oa.parser}],po=[{name:"style",parser:oa.parser.configure({top:"Styles"})}].concat(co.map(e=>({name:e,parser:ze.parser}))),xo=Lr.define({name:"html",parser:nc.configure({props:[Fr.add({Element(e){let t=/^(\s*)(<\/)?/.exec(e.textAfter);return e.node.to<=e.pos+t[0].length?e.continue():e.lineIndent(e.node.from)+(t[2]?0:e.unit)},"OpenTag CloseTag SelfClosingTag"(e){return e.column(e.node.from)+e.unit},Document(e){if(e.pos+/\s*/.exec(e.textAfter)[0].length<e.node.to)return e.continue();let t=null,a;for(let r=e.node;;){let n=r.lastChild;if(!n||n.name!="Element"||n.to!=r.to)break;t=r=n}return t&&!((a=t.lastChild)&&(a.name=="CloseTag"||a.name=="SelfClosingTag"))?e.lineIndent(t.from)+e.unit:null}}),Yr.add({Element(e){let t=e.firstChild,a=e.lastChild;return!t||t.name!="OpenTag"?null:{from:t.to,to:a.name=="CloseTag"?a.from:e.to}}}),N0.add({"OpenTag CloseTag":e=>e.getChild("TagName")})]}),languageData:{commentTokens:{block:{open:"<!--",close:"-->"}},indentOnInput:/^\s*<\/\w+\W$/,wordChars:"-._"}}),Kt=xo.configure({wrap:zs(ho,po)});function _u(e={}){let t="",a;e.matchClosingTags===!1&&(t="noMatch"),e.selfClosingTags===!0&&(t=(t?t+" ":"")+"selfClosing"),(e.nestedLanguages&&e.nestedLanguages.length||e.nestedAttributes&&e.nestedAttributes.length)&&(a=zs((e.nestedLanguages||[]).concat(ho),(e.nestedAttributes||[]).concat(po)));let r=a?xo.configure({wrap:a,dialect:t}):t?Kt.configure({dialect:t}):Kt;return new Ir(r,[Kt.data.of({autocomplete:$u(e)}),e.autoCloseTags!==!1?ku:[],xu().support,Zc().support])}const Xi=new Set("area base br col command embed frame hr img input keygen link meta param source track wbr menuitem".split(" ")),ku=Dt.inputHandler.of((e,t,a,r,n)=>{if(e.composing||e.state.readOnly||t!=a||r!=">"&&r!="/"||!Kt.isActiveAt(e.state,t,-1))return!1;let i=n(),{state:s}=i,o=s.changeByRange(c=>{var O,d,g;let l=s.doc.sliceString(c.from-1,c.to)==r,{head:u}=c,f=It(s).resolveInner(u,-1),h;if(l&&r==">"&&f.name=="EndTag"){let v=f.parent;if(((d=(O=v.parent)===null||O===void 0?void 0:O.lastChild)===null||d===void 0?void 0:d.name)!="CloseTag"&&(h=Pt(s.doc,v.parent,u))&&!Xi.has(h)){let Q=u+(s.doc.sliceString(u,u+1)===">"?1:0),x=`</${h}>`;return{range:c,changes:{from:u,to:Q,insert:x}}}}else if(l&&r=="/"&&f.name=="IncompleteCloseTag"){let v=f.parent;if(f.from==u-2&&((g=v.lastChild)===null||g===void 0?void 0:g.name)!="CloseTag"&&(h=Pt(s.doc,v,u))&&!Xi.has(h)){let Q=u+(s.doc.sliceString(u,u+1)===">"?1:0),x=`${h}>`;return{range:ws.cursor(u+x.length,-1),changes:{from:u,to:Q,insert:x}}}}return{range:c}});return o.changes.empty?!1:(e.dispatch([i,s.update(o,{userEvent:"input.complete",scrollIntoView:!0})]),!0)}),Cu="#e5c07b",Ri="#e06c75",Eu="#56b6c2",Au="#ffffff",Jt="#abb2bf",br="#7d8799",Tu="#61afef",Zu="#98c379",Di="#d19a66",Xu="#c678dd",Ru="#21252b",Bi="#2c313a",qi="#282c34",Or="#353a42",Du="#3E4451",Li="#528bff",Bu=Dt.theme({"&":{color:Jt,backgroundColor:qi},".cm-content":{caretColor:Li},".cm-cursor, .cm-dropCursor":{borderLeftColor:Li},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Du},".cm-panels":{backgroundColor:Ru,color:Jt},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:"1px solid #457dff"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"#6199ff2f"},".cm-activeLine":{backgroundColor:"#6699ff0b"},".cm-selectionMatch":{backgroundColor:"#aafe661a"},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bad0f847"},".cm-gutters":{backgroundColor:qi,color:br,border:"none"},".cm-activeLineGutter":{backgroundColor:Bi},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Or},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Or,borderBottomColor:Or},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Bi,color:Jt}}},{dark:!0}),qu=G0.define([{tag:_.keyword,color:Xu},{tag:[_.name,_.deleted,_.character,_.propertyName,_.macroName],color:Ri},{tag:[_.function(_.variableName),_.labelName],color:Tu},{tag:[_.color,_.constant(_.name),_.standard(_.name)],color:Di},{tag:[_.definition(_.name),_.separator],color:Jt},{tag:[_.typeName,_.className,_.number,_.changed,_.annotation,_.modifier,_.self,_.namespace],color:Cu},{tag:[_.operator,_.operatorKeyword,_.url,_.escape,_.regexp,_.link,_.special(_.string)],color:Eu},{tag:[_.meta,_.comment],color:br},{tag:_.strong,fontWeight:"bold"},{tag:_.emphasis,fontStyle:"italic"},{tag:_.strikethrough,textDecoration:"line-through"},{tag:_.link,color:br,textDecoration:"underline"},{tag:_.heading,fontWeight:"bold",color:Ri},{tag:[_.atom,_.bool,_.special(_.variableName)],color:Di},{tag:[_.processingInstruction,_.string,_.inserted],color:Zu},{tag:_.invalid,color:Au}]),Lu=[Bu,j0(qu)],Fu={key:0,class:"fixed inset-0 z-50 flex items-center justify-center html-modal-container"},Yu={key:0,class:"p-2 w-full h-1/2"},Iu=["srcDoc"],Vu=["srcDoc"],Uu=it({__name:"HtmlDialog",props:{visible:{type:Boolean},html:{},editable:{type:Boolean}},emits:["update:visible","update:html"],setup(e,{emit:t}){const a=e,r=t,n=Wr(),i=Nr(),s=Se(null),o=Se(a.html||""),{isMobile:c}=Ts(),O=Se(null),d=Ce(()=>document.documentElement.classList.contains("dark"));let g=null;yn(()=>{if(a.visible&&(a.html&&a.html!==o.value&&(o.value=a.html),g)){const x=g.state.doc.toString();o.value!==x&&g.dispatch({changes:{from:0,to:g.state.doc.length,insert:o.value||""}})}}),yn(()=>{a.visible&&u()});const l=()=>{if(!O.value||g)return;const x=[H0,_u(),Dt.updateListener.of(p=>{if(p.docChanged){const S=p.state.doc.toString();o.value=S,r("update:html",S)}})];d.value&&x.push(Lu);const m=K0.create({doc:o.value||"",extensions:x});g=new Dt({state:m,parent:O.value})},u=()=>{s.value&&(s.value.srcdoc=o.value)};function f(){return r("update:visible",!1),r("update:html",o.value),n.updateHtmlDialog(!1),!1}const h=()=>fe(this,null,function*(){try{(yield Q(o.value))?i.success("内容已复制到剪贴板"):i.info("复制失败，请手动复制文本框中的内容")}catch(x){i.error("复制失败")}}),v=()=>fe(this,null,function*(){try{const m=(yield dO({htmlContent:o.value})).data.shareCode;(yield Q(m))?i.success("分享链接已复制到剪贴板"):(o.value=m,i.info("复制失败，分享链接已显示在文本框中，请手动复制"))}catch(x){i.error("分享失败")}}),Q=x=>fe(this,null,function*(){try{return yield navigator.clipboard.writeText(x),!0}catch(m){return!1}});return ua(()=>{a.visible&&$n(l)}),Rr(()=>{g&&(g.destroy(),g=null)}),St(()=>a.visible,x=>{x?$n(()=>{!g&&O.value&&l(),u()}):r("update:html",o.value)}),(x,m)=>(be(),et(Xr,{to:"body"},[a.visible?(be(),Ve("div",Fu,[ce("div",{class:"fixed inset-0 bg-black bg-opacity-50",onClick:Xt(f,["stop"])}),Ye(Pe(bs),{class:"absolute top-3 right-3 cursor-pointer z-30",size:"18",onClick:Xt(f,["stop","prevent"])}),ce("div",{class:Zt(["relative bg-white dark:bg-gray-900 w-full h-full p-4 z-10",[Pe(c)?"flex-col":"flex"]]),onClick:m[0]||(m[0]=Xt(()=>{},["stop"]))},[Pe(c)?(be(),Ve("div",Yu,[ce("iframe",{ref_key:"htmlPreviewRef",ref:s,srcDoc:o.value,class:"box-border w-full h-full border rounded-md",frameborder:"0",sandbox:"allow-same-origin allow-scripts allow-popups allow-forms"},null,8,Iu)])):qe("",!0),a.editable!==!1?(be(),Ve("div",{key:1,class:Zt(["p-2 flex flex-col",[Pe(c)?"w-full h-1/2":"w-1/4"]])},[ce("div",{ref_key:"editorContainerRef",ref:O,class:"w-full h-full border rounded-md overflow-hidden dark:border-gray-700 code-editor-container"},null,512),ce("div",{class:"mt-2 flex justify-end"},[ce("button",{onClick:f,class:"px-4 py-2 shadow-sm ring-1 ring-inset bg-white ring-gray-300 hover:bg-gray-50 text-gray-900 rounded-md mr-4 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:ring-gray-700 dark:hover:ring-gray-600"}," 取消 "),ce("button",{onClick:h,class:"px-4 py-2 shadow-sm bg-primary-600 hover:bg-primary-500 text-white dark rounded-md mr-4"}," 复制 "),ce("button",{onClick:v,class:"px-4 py-2 shadow-sm bg-primary-600 hover:bg-primary-500 text-white dark rounded-md"}," 分享 ")])],2)):qe("",!0),Pe(c)?qe("",!0):(be(),Ve("div",{key:2,class:Zt([[a.editable===!1?"w-full":"w-3/4"],"p-2"])},[ce("iframe",{ref_key:"htmlPreviewRef",ref:s,srcDoc:o.value,class:"box-border w-full h-full border rounded-md",frameborder:"0",sandbox:"allow-same-origin allow-scripts allow-popups allow-forms"},null,8,Vu)],2))],2)])):qe("",!0)]))}});const mo=(e,t)=>{const a=e.__vccOpts||e;for(const[r,n]of t)a[r]=n;return a},Mu=mo(Uu,[["__scopeId","data-v-07906350"]]);function hp(e){return ge({url:"/official/getQRSceneStr",data:e})}function pp(e){return st({url:"/official/getQRCode",data:e})}function xp(e){return ge({url:"/official/loginBySceneStr",data:e})}function Wu(e){return ge({url:"/official/loginByCode",data:e})}function mp(e){return ge({url:"/official/getJsapiTicket",data:e})}function gp(){return ge({url:"/official/getQRSceneStrByBind"})}function vp(e){return ge({url:"/official/bindWxBySceneStr",data:e})}function zu(e){return ge({url:"/official/getRedirectUrl",data:e})}function Qp(e){return ge({url:"/auth/verifyIdentity",data:e})}function Sp(e){return ge({url:"/auth/verifyPhoneIdentity",data:e})}const Nu=nt(),Gu=Ce(()=>Nu.isLogin);function ju(){return fe(this,null,function*(){if(Gu.value)return;const e=v0(),t=nt(),r=new URLSearchParams(window.location.search).getAll("code"),n=r.length>0?r[r.length-1]:null;if(n)try{const i=yield Wu({code:n});i.success&&(t.setToken(i.data),yield t.getUserInfo(),t.setLoginDialog(!1),e.replace("/"))}catch(i){}else try{const i=window.location.href,s=yield zu({url:i});s.success&&window.location.replace(s.data)}catch(i){}return{success:!1}})}function Hu(){ju()}var go={exports:{}};(function(e,t){(function(a,r){e.exports=r()})(K,function(){return function(a){var r={};function n(i){if(r[i])return r[i].exports;var s=r[i]={i,l:!1,exports:{}};return a[i].call(s.exports,s,s.exports,n),s.l=!0,s.exports}return n.m=a,n.c=r,n.d=function(i,s,o){n.o(i,s)||Object.defineProperty(i,s,{enumerable:!0,get:o})},n.r=function(i){typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})},n.t=function(i,s){if(1&s&&(i=n(i)),8&s||4&s&&typeof i=="object"&&i&&i.__esModule)return i;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:i}),2&s&&typeof i!="string")for(var c in i)n.d(o,c,function(O){return i[O]}.bind(null,c));return o},n.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(s,"a",s),s},n.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},n.p="",n(n.s=0)}([function(a,r,n){var i,s,o=n(1)(),c=n(3),O=n(4),d=n(6),g=function(){var l=new O;return i=l.getResult(),s=new d,this};g.prototype={getSoftwareVersion:function(){return"0.1.11"},getBrowserData:function(){return i},getFingerprint:function(){var l="|",u=i.ua,f=this.getScreenPrint(),h=this.getPlugins(),v=this.getFonts(),Q=this.isLocalStorage(),x=this.isSessionStorage(),m=this.getTimeZone(),p=this.getLanguage(),S=this.getSystemLanguage(),P=this.isCookie(),b=this.getCanvasPrint();return c(u+l+f+l+h+l+v+l+Q+l+x+l+m+l+p+l+S+l+P+l+b,256)},getCustomFingerprint:function(){for(var l="|",u="",f=0;f<arguments.length;f++)u+=arguments[f]+l;return c(u,256)},getUserAgent:function(){return i.ua},getUserAgentLowerCase:function(){return i.ua.toLowerCase()},getBrowser:function(){return i.browser.name},getBrowserVersion:function(){return i.browser.version},getBrowserMajorVersion:function(){return i.browser.major},isIE:function(){return/IE/i.test(i.browser.name)},isChrome:function(){return/Chrome/i.test(i.browser.name)},isFirefox:function(){return/Firefox/i.test(i.browser.name)},isSafari:function(){return/Safari/i.test(i.browser.name)},isMobileSafari:function(){return/Mobile\sSafari/i.test(i.browser.name)},isOpera:function(){return/Opera/i.test(i.browser.name)},getEngine:function(){return i.engine.name},getEngineVersion:function(){return i.engine.version},getOS:function(){return i.os.name},getOSVersion:function(){return i.os.version},isWindows:function(){return/Windows/i.test(i.os.name)},isMac:function(){return/Mac/i.test(i.os.name)},isLinux:function(){return/Linux/i.test(i.os.name)},isUbuntu:function(){return/Ubuntu/i.test(i.os.name)},isSolaris:function(){return/Solaris/i.test(i.os.name)},getDevice:function(){return i.device.model},getDeviceType:function(){return i.device.type},getDeviceVendor:function(){return i.device.vendor},getCPU:function(){return i.cpu.architecture},isMobile:function(){var l=i.ua||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(l)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(l.substr(0,4))},isMobileMajor:function(){return this.isMobileAndroid()||this.isMobileBlackBerry()||this.isMobileIOS()||this.isMobileOpera()||this.isMobileWindows()},isMobileAndroid:function(){return!!i.ua.match(/Android/i)},isMobileOpera:function(){return!!i.ua.match(/Opera Mini/i)},isMobileWindows:function(){return!!i.ua.match(/IEMobile/i)},isMobileBlackBerry:function(){return!!i.ua.match(/BlackBerry/i)},isMobileIOS:function(){return!!i.ua.match(/iPhone|iPad|iPod/i)},isIphone:function(){return!!i.ua.match(/iPhone/i)},isIpad:function(){return!!i.ua.match(/iPad/i)},isIpod:function(){return!!i.ua.match(/iPod/i)},getScreenPrint:function(){return"Current Resolution: "+this.getCurrentResolution()+", Available Resolution: "+this.getAvailableResolution()+", Color Depth: "+this.getColorDepth()+", Device XDPI: "+this.getDeviceXDPI()+", Device YDPI: "+this.getDeviceYDPI()},getColorDepth:function(){return screen.colorDepth},getCurrentResolution:function(){return screen.width+"x"+screen.height},getAvailableResolution:function(){return screen.availWidth+"x"+screen.availHeight},getDeviceXDPI:function(){return screen.deviceXDPI},getDeviceYDPI:function(){return screen.deviceYDPI},getPlugins:function(){for(var l="",u=0;u<navigator.plugins.length;u++)u==navigator.plugins.length-1?l+=navigator.plugins[u].name:l+=navigator.plugins[u].name+", ";return l},isJava:function(){return navigator.javaEnabled()},getJavaVersion:function(){throw new Error("Please use client.java.js or client.js if you need this functionality!")},isFlash:function(){return!!navigator.plugins["Shockwave Flash"]},getFlashVersion:function(){throw new Error("Please use client.flash.js or client.js if you need this functionality!")},isSilverlight:function(){return!!navigator.plugins["Silverlight Plug-In"]},getSilverlightVersion:function(){return this.isSilverlight()?navigator.plugins["Silverlight Plug-In"].description:""},isMimeTypes:function(){return!(!navigator.mimeTypes||!navigator.mimeTypes.length)},getMimeTypes:function(){var l="";if(navigator.mimeTypes)for(var u=0;u<navigator.mimeTypes.length;u++)u==navigator.mimeTypes.length-1?l+=navigator.mimeTypes[u].description:l+=navigator.mimeTypes[u].description+", ";return l},isFont:function(l){return s.detect(l)},getFonts:function(){for(var l=["Abadi MT Condensed Light","Adobe Fangsong Std","Adobe Hebrew","Adobe Ming Std","Agency FB","Aharoni","Andalus","Angsana New","AngsanaUPC","Aparajita","Arab","Arabic Transparent","Arabic Typesetting","Arial Baltic","Arial Black","Arial CE","Arial CYR","Arial Greek","Arial TUR","Arial","Batang","BatangChe","Bauhaus 93","Bell MT","Bitstream Vera Serif","Bodoni MT","Bookman Old Style","Braggadocio","Broadway","Browallia New","BrowalliaUPC","Calibri Light","Calibri","Californian FB","Cambria Math","Cambria","Candara","Castellar","Casual","Centaur","Century Gothic","Chalkduster","Colonna MT","Comic Sans MS","Consolas","Constantia","Copperplate Gothic Light","Corbel","Cordia New","CordiaUPC","Courier New Baltic","Courier New CE","Courier New CYR","Courier New Greek","Courier New TUR","Courier New","DFKai-SB","DaunPenh","David","DejaVu LGC Sans Mono","Desdemona","DilleniaUPC","DokChampa","Dotum","DotumChe","Ebrima","Engravers MT","Eras Bold ITC","Estrangelo Edessa","EucrosiaUPC","Euphemia","Eurostile","FangSong","Forte","FrankRuehl","Franklin Gothic Heavy","Franklin Gothic Medium","FreesiaUPC","French Script MT","Gabriola","Gautami","Georgia","Gigi","Gisha","Goudy Old Style","Gulim","GulimChe","GungSeo","Gungsuh","GungsuhChe","Haettenschweiler","Harrington","Hei S","HeiT","Heisei Kaku Gothic","Hiragino Sans GB","Impact","Informal Roman","IrisUPC","Iskoola Pota","JasmineUPC","KacstOne","KaiTi","Kalinga","Kartika","Khmer UI","Kino MT","KodchiangUPC","Kokila","Kozuka Gothic Pr6N","Lao UI","Latha","Leelawadee","Levenim MT","LilyUPC","Lohit Gujarati","Loma","Lucida Bright","Lucida Console","Lucida Fax","Lucida Sans Unicode","MS Gothic","MS Mincho","MS PGothic","MS PMincho","MS Reference Sans Serif","MS UI Gothic","MV Boli","Magneto","Malgun Gothic","Mangal","Marlett","Matura MT Script Capitals","Meiryo UI","Meiryo","Menlo","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Sans Serif","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU-ExtB","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","Miriam Fixed","Miriam","Mongolian Baiti","MoolBoran","NSimSun","Narkisim","News Gothic MT","Niagara Solid","Nyala","PMingLiU","PMingLiU-ExtB","Palace Script MT","Palatino Linotype","Papyrus","Perpetua","Plantagenet Cherokee","Playbill","Prelude Bold","Prelude Condensed Bold","Prelude Condensed Medium","Prelude Medium","PreludeCompressedWGL Black","PreludeCompressedWGL Bold","PreludeCompressedWGL Light","PreludeCompressedWGL Medium","PreludeCondensedWGL Black","PreludeCondensedWGL Bold","PreludeCondensedWGL Light","PreludeCondensedWGL Medium","PreludeWGL Black","PreludeWGL Bold","PreludeWGL Light","PreludeWGL Medium","Raavi","Rachana","Rockwell","Rod","Sakkal Majalla","Sawasdee","Script MT Bold","Segoe Print","Segoe Script","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Segoe UI","Shonar Bangla","Showcard Gothic","Shruti","SimHei","SimSun","SimSun-ExtB","Simplified Arabic Fixed","Simplified Arabic","Snap ITC","Sylfaen","Symbol","Tahoma","Times New Roman Baltic","Times New Roman CE","Times New Roman CYR","Times New Roman Greek","Times New Roman TUR","Times New Roman","TlwgMono","Traditional Arabic","Trebuchet MS","Tunga","Tw Cen MT Condensed Extra Bold","Ubuntu","Umpush","Univers","Utopia","Utsaah","Vani","Verdana","Vijaya","Vladimir Script","Vrinda","Webdings","Wide Latin","Wingdings"],u="",f=0;f<l.length;f++)s.detect(l[f])&&(u+=f==l.length-1?l[f]:l[f]+", ");return u},isLocalStorage:function(){try{return!!o.localStorage}catch(l){return!0}},isSessionStorage:function(){try{return!!o.sessionStorage}catch(l){return!0}},isCookie:function(){return navigator.cookieEnabled},getTimeZone:function(){var l,u;return l=new Date,(u=String(-l.getTimezoneOffset()/60))<0?"-"+("0"+(u*=-1)).slice(-2):"+"+("0"+u).slice(-2)},getLanguage:function(){return navigator.language},getSystemLanguage:function(){return navigator.systemLanguage||window.navigator.language},isCanvas:function(){var l=document.createElement("canvas");try{return!(!l.getContext||!l.getContext("2d"))}catch(u){return!1}},getCanvasPrint:function(){var l,u=document.createElement("canvas");try{l=u.getContext("2d")}catch(h){return""}var f="ClientJS,org <canvas> 1.0";return l.textBaseline="top",l.font="14px 'Arial'",l.textBaseline="alphabetic",l.fillStyle="#f60",l.fillRect(125,1,62,20),l.fillStyle="#069",l.fillText(f,2,15),l.fillStyle="rgba(102, 204, 0, 0.7)",l.fillText(f,4,17),u.toDataURL()}},r.ClientJS=g},function(a,r,n){var i=n(2);a.exports=function(){return typeof K=="object"&&K&&K.Math===Math&&K.Array===Array?K:i}},function(a,r,n){typeof self!="undefined"?a.exports=self:typeof window!="undefined"?a.exports=window:a.exports=Function("return this")()},function(a,r,n){a.exports=function(i,s){var o,c,O,d,g,l,u,f;for(o=3&i.length,c=i.length-o,O=s,g=3432918353,l=461845907,f=0;f<c;)u=255&i.charCodeAt(f)|(255&i.charCodeAt(++f))<<8|(255&i.charCodeAt(++f))<<16|(255&i.charCodeAt(++f))<<24,++f,O=27492+(65535&(d=5*(65535&(O=(O^=u=(65535&(u=(u=(65535&u)*g+(((u>>>16)*g&65535)<<16)&4294967295)<<15|u>>>17))*l+(((u>>>16)*l&65535)<<16)&4294967295)<<13|O>>>19))+((5*(O>>>16)&65535)<<16)&4294967295))+((58964+(d>>>16)&65535)<<16);switch(u=0,o){case 3:u^=(255&i.charCodeAt(f+2))<<16;case 2:u^=(255&i.charCodeAt(f+1))<<8;case 1:O^=u=(65535&(u=(u=(65535&(u^=255&i.charCodeAt(f)))*g+(((u>>>16)*g&65535)<<16)&4294967295)<<15|u>>>17))*l+(((u>>>16)*l&65535)<<16)&4294967295}return O^=i.length,O=2246822507*(65535&(O^=O>>>16))+((2246822507*(O>>>16)&65535)<<16)&4294967295,O=3266489909*(65535&(O^=O>>>13))+((3266489909*(O>>>16)&65535)<<16)&4294967295,(O^=O>>>16)>>>0}},function(a,r,n){var i;(function(s,o){var c="function",O="undefined",d="object",g="string",l="model",u="name",f="type",h="vendor",v="version",Q="architecture",x="console",m="mobile",p="tablet",S="smarttv",P="wearable",b="embedded",k="Amazon",D="Apple",w="ASUS",C="BlackBerry",Z="Firefox",R="Google",U="Huawei",M="LG",G="Microsoft",ne="Motorola",Y="Opera",F="Samsung",j="Sony",T="Xiaomi",X="Zebra",q="Facebook",B=function(ee){var te={};for(var ae in ee)te[ee[ae].toUpperCase()]=ee[ae];return te},oe=function(ee,te){return typeof ee===g&&J(te).indexOf(J(ee))!==-1},J=function(ee){return ee.toLowerCase()},Qe=function(ee,te){if(typeof ee===g)return ee=ee.replace(/^\s\s*/,"").replace(/\s\s*$/,""),typeof te===O?ee:ee.substring(0,255)},H=function(ee,te){for(var ae,$,y,E,I,z,N=0;N<te.length&&!I;){var A=te[N],L=te[N+1];for(ae=$=0;ae<A.length&&!I;)if(I=A[ae++].exec(ee))for(y=0;y<L.length;y++)z=I[++$],typeof(E=L[y])===d&&E.length>0?E.length==2?typeof E[1]==c?this[E[0]]=E[1].call(this,z):this[E[0]]=E[1]:E.length==3?typeof E[1]!==c||E[1].exec&&E[1].test?this[E[0]]=z?z.replace(E[1],E[2]):o:this[E[0]]=z?E[1].call(this,z,E[2]):o:E.length==4&&(this[E[0]]=z?E[3].call(this,z.replace(E[1],E[2])):o):this[E]=z||o;N+=2}},Ze=function(ee,te){for(var ae in te)if(typeof te[ae]===d&&te[ae].length>0){for(var $=0;$<te[ae].length;$++)if(oe(te[ae][$],ee))return ae==="?"?o:ae}else if(oe(te[ae],ee))return ae==="?"?o:ae;return ee},Xe={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Me={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[v,[u,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[v,[u,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[u,v],[/opios[\/ ]+([\w\.]+)/i],[v,[u,"Opera Mini"]],[/\bopr\/([\w\.]+)/i],[v,[u,Y]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[u,v],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[v,[u,"UCBrowser"]],[/\bqbcore\/([\w\.]+)/i],[v,[u,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[v,[u,"WeChat"]],[/konqueror\/([\w\.]+)/i],[v,[u,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[v,[u,"IE"]],[/yabrowser\/([\w\.]+)/i],[v,[u,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[u,/(.+)/,"$1 Secure Browser"],v],[/\bfocus\/([\w\.]+)/i],[v,[u,"Firefox Focus"]],[/\bopt\/([\w\.]+)/i],[v,[u,"Opera Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[v,[u,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[v,[u,"Dolphin"]],[/coast\/([\w\.]+)/i],[v,[u,"Opera Coast"]],[/miuibrowser\/([\w\.]+)/i],[v,[u,"MIUI Browser"]],[/fxios\/([-\w\.]+)/i],[v,[u,Z]],[/\bqihu|(qi?ho?o?|360)browser/i],[[u,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[u,/(.+)/,"$1 Browser"],v],[/(comodo_dragon)\/([\w\.]+)/i],[[u,/_/g," "],v],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[u,v],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i],[u],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[u,q],v],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[u,v],[/\bgsa\/([\w\.]+) .*safari\//i],[v,[u,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[v,[u,"Chrome Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[u,"Chrome WebView"],v],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[v,[u,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[u,v],[/version\/([\w\.]+) .*mobile\/\w+ (safari)/i],[v,[u,"Mobile Safari"]],[/version\/([\w\.]+) .*(mobile ?safari|safari)/i],[v,u],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[u,[v,Ze,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[u,v],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[u,"Netscape"],v],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[v,[u,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[u,v]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[Q,"amd64"]],[/(ia32(?=;))/i],[[Q,J]],[/((?:i[346]|x)86)[;\)]/i],[[Q,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[Q,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[Q,"armhf"]],[/windows (ce|mobile); ppc;/i],[[Q,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[Q,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[Q,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[Q,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[l,[h,F],[f,p]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[l,[h,F],[f,m]],[/\((ip(?:hone|od)[\w ]*);/i],[l,[h,D],[f,m]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[l,[h,D],[f,p]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[l,[h,U],[f,p]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}-[atu]?[ln][01259x][012359][an]?)\b(?!.+d\/s)/i],[l,[h,U],[f,m]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[l,/_/g," "],[h,T],[f,m]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[l,/_/g," "],[h,T],[f,p]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[l,[h,"OPPO"],[f,m]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[l,[h,"Vivo"],[f,m]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[l,[h,"Realme"],[f,m]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[l,[h,ne],[f,m]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[l,[h,ne],[f,p]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[l,[h,M],[f,p]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[l,[h,M],[f,m]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[l,[h,"Lenovo"],[f,p]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[l,/_/g," "],[h,"Nokia"],[f,m]],[/(pixel c)\b/i],[l,[h,R],[f,p]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[l,[h,R],[f,m]],[/droid.+ ([c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[l,[h,j],[f,m]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[l,"Xperia Tablet"],[h,j],[f,p]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[l,[h,"OnePlus"],[f,m]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[l,[h,k],[f,p]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[l,/(.+)/g,"Fire Phone $1"],[h,k],[f,m]],[/(playbook);[-\w\),; ]+(rim)/i],[l,h,[f,p]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[l,[h,C],[f,m]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[l,[h,w],[f,p]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[l,[h,w],[f,m]],[/(nexus 9)/i],[l,[h,"HTC"],[f,p]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony)[-_ ]?([-\w]*)/i],[h,[l,/_/g," "],[f,m]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[l,[h,"Acer"],[f,p]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[l,[h,"Meizu"],[f,m]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[l,[h,"Sharp"],[f,m]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,l,[f,m]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,l,[f,p]],[/(surface duo)/i],[l,[h,G],[f,p]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[l,[h,"Fairphone"],[f,m]],[/(u304aa)/i],[l,[h,"AT&T"],[f,m]],[/\bsie-(\w*)/i],[l,[h,"Siemens"],[f,m]],[/\b(rct\w+) b/i],[l,[h,"RCA"],[f,p]],[/\b(venue[\d ]{2,7}) b/i],[l,[h,"Dell"],[f,p]],[/\b(q(?:mv|ta)\w+) b/i],[l,[h,"Verizon"],[f,p]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[l,[h,"Barnes & Noble"],[f,p]],[/\b(tm\d{3}\w+) b/i],[l,[h,"NuVision"],[f,p]],[/\b(k88) b/i],[l,[h,"ZTE"],[f,p]],[/\b(nx\d{3}j) b/i],[l,[h,"ZTE"],[f,m]],[/\b(gen\d{3}) b.+49h/i],[l,[h,"Swiss"],[f,m]],[/\b(zur\d{3}) b/i],[l,[h,"Swiss"],[f,p]],[/\b((zeki)?tb.*\b) b/i],[l,[h,"Zeki"],[f,p]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],l,[f,p]],[/\b(ns-?\w{0,9}) b/i],[l,[h,"Insignia"],[f,p]],[/\b((nxa|next)-?\w{0,9}) b/i],[l,[h,"NextBook"],[f,p]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],l,[f,m]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],l,[f,m]],[/\b(ph-1) /i],[l,[h,"Essential"],[f,m]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[l,[h,"Envizen"],[f,p]],[/\b(trio[-\w\. ]+) b/i],[l,[h,"MachSpeed"],[f,p]],[/\btu_(1491) b/i],[l,[h,"Rotor"],[f,p]],[/(shield[\w ]+) b/i],[l,[h,"Nvidia"],[f,p]],[/(sprint) (\w+)/i],[h,l,[f,m]],[/(kin\.[onetw]{3})/i],[[l,/\./g," "],[h,G],[f,m]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[l,[h,X],[f,p]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[l,[h,X],[f,m]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,l,[f,x]],[/droid.+; (shield) bui/i],[l,[h,"Nvidia"],[f,x]],[/(playstation [345portablevi]+)/i],[l,[h,j],[f,x]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[l,[h,G],[f,x]],[/smart-tv.+(samsung)/i],[h,[f,S]],[/hbbtv.+maple;(\d+)/i],[[l,/^/,"SmartTV"],[h,F],[f,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,M],[f,S]],[/(apple) ?tv/i],[h,[l,"Apple TV"],[f,S]],[/crkey/i],[[l,"Chromecast"],[h,R],[f,S]],[/droid.+aft(\w)( bui|\))/i],[l,[h,k],[f,S]],[/\(dtv[\);].+(aquos)/i],[l,[h,"Sharp"],[f,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[h,Qe],[l,Qe],[f,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,S]],[/((pebble))app/i],[h,l,[f,P]],[/droid.+; (glass) \d/i],[l,[h,R],[f,P]],[/droid.+; (wt63?0{2,3})\)/i],[l,[h,X],[f,P]],[/(quest( 2)?)/i],[l,[h,q],[f,P]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[f,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[l,[f,m]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[l,[f,p]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,p]],[/(phone|mobile(?:[;\/]| safari)|pda(?=.+windows ce))/i],[[f,m]],[/(android[-\w\. ]{0,9});.+buil/i],[l,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[v,[u,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[v,[u,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[u,v],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[v,u]],os:[[/microsoft (windows) (vista|xp)/i],[u,v],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[u,[v,Ze,Xe]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[u,"Windows"],[v,Ze,Xe]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[v,/_/g,"."],[u,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[u,"Mac OS"],[v,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[v,u],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[u,v],[/\(bb(10);/i],[v,[u,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[v,[u,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[v,[u,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[v,[u,"webOS"]],[/crkey\/([\d\.]+)/i],[v,[u,"Chromecast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[u,"Chromium OS"],v],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[u,v],[/(sunos) ?([\w\.\d]*)/i],[[u,"Solaris"],v],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[u,v]]},ve=function(ee,te){if(typeof ee===d&&(te=ee,ee=o),!(this instanceof ve))return new ve(ee,te).getResult();var ae=ee||(typeof s!==O&&s.navigator&&s.navigator.userAgent?s.navigator.userAgent:""),$=te?function(y,E){var I={};for(var z in y)E[z]&&E[z].length%2==0?I[z]=E[z].concat(y[z]):I[z]=y[z];return I}(Me,te):Me;return this.getBrowser=function(){var y,E={};return E.name=o,E.version=o,H.call(E,ae,$.browser),E.major=typeof(y=E.version)===g?y.replace(/[^\d\.]/g,"").split(".")[0]:o,E},this.getCPU=function(){var y={};return y.architecture=o,H.call(y,ae,$.cpu),y},this.getDevice=function(){var y={};return y.vendor=o,y.model=o,y.type=o,H.call(y,ae,$.device),y},this.getEngine=function(){var y={};return y.name=o,y.version=o,H.call(y,ae,$.engine),y},this.getOS=function(){var y={};return y.name=o,y.version=o,H.call(y,ae,$.os),y},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return ae},this.setUA=function(y){return ae=typeof y===g&&y.length>255?Qe(y,255):y,this},this.setUA(ae),this};ve.VERSION="0.7.30",ve.BROWSER=B([u,v,"major"]),ve.CPU=B([Q]),ve.DEVICE=B([l,h,f,x,m,S,p,P,b]),ve.ENGINE=ve.OS=B([u,v]),typeof r!==O?(typeof a!==O&&a.exports&&(r=a.exports=ve),r.UAParser=ve):n(5)?(i=function(){return ve}.call(r,n,r,a))===o||(a.exports=i):typeof s!==O&&(s.UAParser=ve);var me=typeof s!==O&&(s.jQuery||s.Zepto);if(me&&!me.ua){var pe=new ve;me.ua=pe.getResult(),me.ua.get=function(){return pe.getUA()},me.ua.set=function(ee){pe.setUA(ee);var te=pe.getResult();for(var ae in te)me.ua[ae]=te[ae]}}})(typeof window=="object"?window:this)},function(a,r){(function(n){a.exports=n}).call(this,{})},function(a,r){a.exports=function(){var n=["monospace","sans-serif","serif"],i=document.getElementsByTagName("body")[0],s=document.createElement("span");s.style.fontSize="72px",s.innerHTML="mmmmmmmmmmlli";var o={},c={};for(var O in n)s.style.fontFamily=n[O],i.appendChild(s),o[n[O]]=s.offsetWidth,c[n[O]]=s.offsetHeight,i.removeChild(s);this.detect=function(d){var g=!1;for(var l in n){s.style.fontFamily=d+","+n[l],i.appendChild(s);var u=s.offsetWidth!=o[n[l]]||s.offsetHeight!=c[n[l]];i.removeChild(s),g=g||u}return g}}}])})})(go);var Ku=go.exports;const Ju=it({__name:"App",setup(e){const t=new Ku.ClientJS,a=nt(),r=Wr(),n=Se("");r.updateFingerprint(t.getFingerprint());const i=Ce(()=>{var h;return((h=a.globalConfig)==null?void 0:h.clientFaviconPath)||uO}),s=Ce(()=>{var h;return Number((h=a.globalConfig)==null?void 0:h.isAutoOpenNotice)===1});Ce(()=>a.isLogin);const o=Ce(()=>{var h;return Number((h=a.globalConfig)==null?void 0:h.wechatSilentLoginStatus)===1}),c=Ce(()=>{var h;return Number((h=a.globalConfig)==null?void 0:h.showWatermark)===1}),O=Ce(()=>{var h;return Number((h=a.globalConfig)==null?void 0:h.clearCacheEnabled)===1});function d(h=!1){if(!O.value&&!h||a.isLogin)return;const Q={};Q.theme=localStorage.getItem("theme")||"light",["appLanguage","agreedToUserAgreement"].forEach(m=>{const p=localStorage.getItem(m);p&&(Q[m]=p);const S=sessionStorage.getItem(m);S&&(Q[`ss_${m}`]=S)}),localStorage.clear(),sessionStorage.clear(),Object.keys(Q).forEach(m=>{const p=Q[m];p!==null&&(m.startsWith("ss_")?sessionStorage.setItem(m.substring(3),p):localStorage.setItem(m,p))}),window.indexedDB.databases&&window.indexedDB.databases().then(m=>{m.forEach(p=>{p.name&&window.indexedDB.deleteDatabase(p.name)})}).catch(()=>{}),"caches"in window&&caches.keys().then(m=>{m.forEach(p=>{caches.delete(p)})})}function g(){return fe(this,null,function*(){var Q;const h=(Q=a.globalConfig)==null?void 0:Q.baiduCode;if(!h)return;const v=document.createElement("script");v.innerHTML=h.replace(/<script[\s\S]*?>([\s\S]*?)<\/script>/gi,"$1"),document.head.appendChild(v)})}function l(){var h;document.title=((h=a.globalConfig)==null?void 0:h.siteName)||"AI"}function u(){const h=rt.get("showNotice");(!h||Date.now()>Number(h))&&s.value&&r.updateSettingsDialog(!0,Tl.NOTICE)}function f(){const h=navigator.userAgent.toLowerCase();return!(h.indexOf("wxwork")!==-1)&&h.indexOf("micromessenger")!==-1}return ua(()=>fe(this,null,function*(){l(),g(),o.value&&f()&&(yield Hu()),d();const h=document.createElement("link");h.rel="shortcut icon",h.href=i.value,h.type="image/png",document.querySelectorAll('link[rel="shortcut icon"], link[rel="icon"]').forEach(Q=>{var x;return(x=Q.parentNode)==null?void 0:x.removeChild(Q)}),document.head.appendChild(h),yield u()})),(h,v)=>{const Q=wn("router-view"),x=wn("GlobalImageViewer");return be(),Ve(ca,null,[c.value?(be(),et(fO,{key:0})):qe("",!0),Ye(Q),Ye(Mu,{visible:Pe(r).htmlDialog,html:n.value},null,8,["visible","html"]),Ye(x)],64)}}});/*!
  * shared v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Oa=typeof window!="undefined",ut=(e,t=!1)=>t?Symbol.for(e):Symbol(e),ef=(e,t,a)=>tf({l:e,k:t,s:a}),tf=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),ye=e=>typeof e=="number"&&isFinite(e),af=e=>Jr(e)==="[object Date]",$t=e=>Jr(e)==="[object RegExp]",da=e=>re(e)&&Object.keys(e).length===0,we=Object.assign,rf=Object.create,de=(e=null)=>rf(e);let Fi;const pt=()=>Fi||(Fi=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:de());function Yi(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const nf=Object.prototype.hasOwnProperty;function Ue(e,t){return nf.call(e,t)}const $e=Array.isArray,xe=e=>typeof e=="function",V=e=>typeof e=="string",le=e=>typeof e=="boolean",Oe=e=>e!==null&&typeof e=="object",sf=e=>Oe(e)&&xe(e.then)&&xe(e.catch),vo=Object.prototype.toString,Jr=e=>vo.call(e),re=e=>Jr(e)==="[object Object]",of=e=>e==null?"":$e(e)||re(e)&&e.toString===vo?JSON.stringify(e,null,2):String(e);function en(e,t=""){return e.reduce((a,r,n)=>n===0?a+r:a+t+r,"")}function lf(e,t){}const Ut=e=>!Oe(e)||$e(e);function ea(e,t){if(Ut(e)||Ut(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:r,des:n}=a.pop();Object.keys(r).forEach(i=>{i!=="__proto__"&&(Oe(r[i])&&!Oe(n[i])&&(n[i]=Array.isArray(r[i])?[]:de()),Ut(n[i])||Ut(r[i])?n[i]=r[i]:a.push({src:r[i],des:n[i]}))})}}/*!
  * message-compiler v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Of(e,t,a){return{line:e,column:t,offset:a}}function Pr(e,t,a){const r={start:e,end:t};return a!=null&&(r.source=a),r}const ue={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},cf=17;function ha(e,t,a={}){const{domain:r,messages:n,args:i}=a,s=e,o=new SyntaxError(String(s));return o.code=e,t&&(o.location=t),o.domain=r,o}function uf(e){throw e}const Ke=" ",ff="\r",Ee=`
`,df=String.fromCharCode(8232),hf=String.fromCharCode(8233);function pf(e){const t=e;let a=0,r=1,n=1,i=0;const s=b=>t[b]===ff&&t[b+1]===Ee,o=b=>t[b]===Ee,c=b=>t[b]===hf,O=b=>t[b]===df,d=b=>s(b)||o(b)||c(b)||O(b),g=()=>a,l=()=>r,u=()=>n,f=()=>i,h=b=>s(b)||c(b)||O(b)?Ee:t[b],v=()=>h(a),Q=()=>h(a+i);function x(){return i=0,d(a)&&(r++,n=0),s(a)&&a++,a++,n++,t[a]}function m(){return s(a+i)&&i++,i++,t[a+i]}function p(){a=0,r=1,n=1,i=0}function S(b=0){i=b}function P(){const b=a+i;for(;b!==a;)x();i=0}return{index:g,line:l,column:u,peekOffset:f,charAt:h,currentChar:v,currentPeek:Q,next:x,peek:m,reset:p,resetPeek:S,skipToPeek:P}}const Ot=void 0,xf=".",Ii="'",mf="tokenizer";function gf(e,t={}){const a=t.location!==!1,r=pf(e),n=()=>r.index(),i=()=>Of(r.line(),r.column(),r.index()),s=i(),o=n(),c={currentType:13,offset:o,startLoc:s,endLoc:s,lastType:13,lastOffset:o,lastStartLoc:s,lastEndLoc:s,braceNest:0,inLinked:!1,text:""},O=()=>c,{onError:d}=t;function g($,y,E,...I){const z=O();if(y.column+=E,y.offset+=E,d){const N=a?Pr(z.startLoc,y):null,A=ha($,N,{domain:mf,args:I});d(A)}}function l($,y,E){$.endLoc=i(),$.currentType=y;const I={type:y};return a&&(I.loc=Pr($.startLoc,$.endLoc)),E!=null&&(I.value=E),I}const u=$=>l($,13);function f($,y){return $.currentChar()===y?($.next(),y):(g(ue.EXPECTED_TOKEN,i(),0,y),"")}function h($){let y="";for(;$.currentPeek()===Ke||$.currentPeek()===Ee;)y+=$.currentPeek(),$.peek();return y}function v($){const y=h($);return $.skipToPeek(),y}function Q($){if($===Ot)return!1;const y=$.charCodeAt(0);return y>=97&&y<=122||y>=65&&y<=90||y===95}function x($){if($===Ot)return!1;const y=$.charCodeAt(0);return y>=48&&y<=57}function m($,y){const{currentType:E}=y;if(E!==2)return!1;h($);const I=Q($.currentPeek());return $.resetPeek(),I}function p($,y){const{currentType:E}=y;if(E!==2)return!1;h($);const I=$.currentPeek()==="-"?$.peek():$.currentPeek(),z=x(I);return $.resetPeek(),z}function S($,y){const{currentType:E}=y;if(E!==2)return!1;h($);const I=$.currentPeek()===Ii;return $.resetPeek(),I}function P($,y){const{currentType:E}=y;if(E!==7)return!1;h($);const I=$.currentPeek()===".";return $.resetPeek(),I}function b($,y){const{currentType:E}=y;if(E!==8)return!1;h($);const I=Q($.currentPeek());return $.resetPeek(),I}function k($,y){const{currentType:E}=y;if(!(E===7||E===11))return!1;h($);const I=$.currentPeek()===":";return $.resetPeek(),I}function D($,y){const{currentType:E}=y;if(E!==9)return!1;const I=()=>{const N=$.currentPeek();return N==="{"?Q($.peek()):N==="@"||N==="|"||N===":"||N==="."||N===Ke||!N?!1:N===Ee?($.peek(),I()):C($,!1)},z=I();return $.resetPeek(),z}function w($){h($);const y=$.currentPeek()==="|";return $.resetPeek(),y}function C($,y=!0){const E=(z=!1,N="")=>{const A=$.currentPeek();return A==="{"||A==="@"||!A?z:A==="|"?!(N===Ke||N===Ee):A===Ke?($.peek(),E(!0,Ke)):A===Ee?($.peek(),E(!0,Ee)):!0},I=E();return y&&$.resetPeek(),I}function Z($,y){const E=$.currentChar();return E===Ot?Ot:y(E)?($.next(),E):null}function R($){const y=$.charCodeAt(0);return y>=97&&y<=122||y>=65&&y<=90||y>=48&&y<=57||y===95||y===36}function U($){return Z($,R)}function M($){const y=$.charCodeAt(0);return y>=97&&y<=122||y>=65&&y<=90||y>=48&&y<=57||y===95||y===36||y===45}function G($){return Z($,M)}function ne($){const y=$.charCodeAt(0);return y>=48&&y<=57}function Y($){return Z($,ne)}function F($){const y=$.charCodeAt(0);return y>=48&&y<=57||y>=65&&y<=70||y>=97&&y<=102}function j($){return Z($,F)}function T($){let y="",E="";for(;y=Y($);)E+=y;return E}function X($){let y="";for(;;){const E=$.currentChar();if(E==="{"||E==="}"||E==="@"||E==="|"||!E)break;if(E===Ke||E===Ee)if(C($))y+=E,$.next();else{if(w($))break;y+=E,$.next()}else y+=E,$.next()}return y}function q($){v($);let y="",E="";for(;y=G($);)E+=y;return $.currentChar()===Ot&&g(ue.UNTERMINATED_CLOSING_BRACE,i(),0),E}function B($){v($);let y="";return $.currentChar()==="-"?($.next(),y+=`-${T($)}`):y+=T($),$.currentChar()===Ot&&g(ue.UNTERMINATED_CLOSING_BRACE,i(),0),y}function oe($){return $!==Ii&&$!==Ee}function J($){v($),f($,"'");let y="",E="";for(;y=Z($,oe);)y==="\\"?E+=Qe($):E+=y;const I=$.currentChar();return I===Ee||I===Ot?(g(ue.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,i(),0),I===Ee&&($.next(),f($,"'")),E):(f($,"'"),E)}function Qe($){const y=$.currentChar();switch(y){case"\\":case"'":return $.next(),`\\${y}`;case"u":return H($,y,4);case"U":return H($,y,6);default:return g(ue.UNKNOWN_ESCAPE_SEQUENCE,i(),0,y),""}}function H($,y,E){f($,y);let I="";for(let z=0;z<E;z++){const N=j($);if(!N){g(ue.INVALID_UNICODE_ESCAPE_SEQUENCE,i(),0,`\\${y}${I}${$.currentChar()}`);break}I+=N}return`\\${y}${I}`}function Ze($){return $!=="{"&&$!=="}"&&$!==Ke&&$!==Ee}function Xe($){v($);let y="",E="";for(;y=Z($,Ze);)E+=y;return E}function Me($){let y="",E="";for(;y=U($);)E+=y;return E}function ve($){const y=E=>{const I=$.currentChar();return I==="{"||I==="@"||I==="|"||I==="("||I===")"||!I||I===Ke?E:(E+=I,$.next(),y(E))};return y("")}function me($){v($);const y=f($,"|");return v($),y}function pe($,y){let E=null;switch($.currentChar()){case"{":return y.braceNest>=1&&g(ue.NOT_ALLOW_NEST_PLACEHOLDER,i(),0),$.next(),E=l(y,2,"{"),v($),y.braceNest++,E;case"}":return y.braceNest>0&&y.currentType===2&&g(ue.EMPTY_PLACEHOLDER,i(),0),$.next(),E=l(y,3,"}"),y.braceNest--,y.braceNest>0&&v($),y.inLinked&&y.braceNest===0&&(y.inLinked=!1),E;case"@":return y.braceNest>0&&g(ue.UNTERMINATED_CLOSING_BRACE,i(),0),E=ee($,y)||u(y),y.braceNest=0,E;default:{let z=!0,N=!0,A=!0;if(w($))return y.braceNest>0&&g(ue.UNTERMINATED_CLOSING_BRACE,i(),0),E=l(y,1,me($)),y.braceNest=0,y.inLinked=!1,E;if(y.braceNest>0&&(y.currentType===4||y.currentType===5||y.currentType===6))return g(ue.UNTERMINATED_CLOSING_BRACE,i(),0),y.braceNest=0,te($,y);if(z=m($,y))return E=l(y,4,q($)),v($),E;if(N=p($,y))return E=l(y,5,B($)),v($),E;if(A=S($,y))return E=l(y,6,J($)),v($),E;if(!z&&!N&&!A)return E=l(y,12,Xe($)),g(ue.INVALID_TOKEN_IN_PLACEHOLDER,i(),0,E.value),v($),E;break}}return E}function ee($,y){const{currentType:E}=y;let I=null;const z=$.currentChar();switch((E===7||E===8||E===11||E===9)&&(z===Ee||z===Ke)&&g(ue.INVALID_LINKED_FORMAT,i(),0),z){case"@":return $.next(),I=l(y,7,"@"),y.inLinked=!0,I;case".":return v($),$.next(),l(y,8,".");case":":return v($),$.next(),l(y,9,":");default:return w($)?(I=l(y,1,me($)),y.braceNest=0,y.inLinked=!1,I):P($,y)||k($,y)?(v($),ee($,y)):b($,y)?(v($),l(y,11,Me($))):D($,y)?(v($),z==="{"?pe($,y)||I:l(y,10,ve($))):(E===7&&g(ue.INVALID_LINKED_FORMAT,i(),0),y.braceNest=0,y.inLinked=!1,te($,y))}}function te($,y){let E={type:13};if(y.braceNest>0)return pe($,y)||u(y);if(y.inLinked)return ee($,y)||u(y);switch($.currentChar()){case"{":return pe($,y)||u(y);case"}":return g(ue.UNBALANCED_CLOSING_BRACE,i(),0),$.next(),l(y,3,"}");case"@":return ee($,y)||u(y);default:{if(w($))return E=l(y,1,me($)),y.braceNest=0,y.inLinked=!1,E;if(C($))return l(y,0,X($));break}}return E}function ae(){const{currentType:$,offset:y,startLoc:E,endLoc:I}=c;return c.lastType=$,c.lastOffset=y,c.lastStartLoc=E,c.lastEndLoc=I,c.offset=n(),c.startLoc=i(),r.currentChar()===Ot?l(c,13):te(r,c)}return{nextToken:ae,currentOffset:n,currentPosition:i,context:O}}const vf="parser",Qf=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Sf(e,t,a){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||a,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function bf(e={}){const t=e.location!==!1,{onError:a}=e;function r(Q,x,m,p,...S){const P=Q.currentPosition();if(P.offset+=p,P.column+=p,a){const b=t?Pr(m,P):null,k=ha(x,b,{domain:vf,args:S});a(k)}}function n(Q,x,m){const p={type:Q};return t&&(p.start=x,p.end=x,p.loc={start:m,end:m}),p}function i(Q,x,m,p){t&&(Q.end=x,Q.loc&&(Q.loc.end=m))}function s(Q,x){const m=Q.context(),p=n(3,m.offset,m.startLoc);return p.value=x,i(p,Q.currentOffset(),Q.currentPosition()),p}function o(Q,x){const m=Q.context(),{lastOffset:p,lastStartLoc:S}=m,P=n(5,p,S);return P.index=parseInt(x,10),Q.nextToken(),i(P,Q.currentOffset(),Q.currentPosition()),P}function c(Q,x){const m=Q.context(),{lastOffset:p,lastStartLoc:S}=m,P=n(4,p,S);return P.key=x,Q.nextToken(),i(P,Q.currentOffset(),Q.currentPosition()),P}function O(Q,x){const m=Q.context(),{lastOffset:p,lastStartLoc:S}=m,P=n(9,p,S);return P.value=x.replace(Qf,Sf),Q.nextToken(),i(P,Q.currentOffset(),Q.currentPosition()),P}function d(Q){const x=Q.nextToken(),m=Q.context(),{lastOffset:p,lastStartLoc:S}=m,P=n(8,p,S);return x.type!==11?(r(Q,ue.UNEXPECTED_EMPTY_LINKED_MODIFIER,m.lastStartLoc,0),P.value="",i(P,p,S),{nextConsumeToken:x,node:P}):(x.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,We(x)),P.value=x.value||"",i(P,Q.currentOffset(),Q.currentPosition()),{node:P})}function g(Q,x){const m=Q.context(),p=n(7,m.offset,m.startLoc);return p.value=x,i(p,Q.currentOffset(),Q.currentPosition()),p}function l(Q){const x=Q.context(),m=n(6,x.offset,x.startLoc);let p=Q.nextToken();if(p.type===8){const S=d(Q);m.modifier=S.node,p=S.nextConsumeToken||Q.nextToken()}switch(p.type!==9&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(p)),p=Q.nextToken(),p.type===2&&(p=Q.nextToken()),p.type){case 10:p.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(p)),m.key=g(Q,p.value||"");break;case 4:p.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(p)),m.key=c(Q,p.value||"");break;case 5:p.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(p)),m.key=o(Q,p.value||"");break;case 6:p.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(p)),m.key=O(Q,p.value||"");break;default:{r(Q,ue.UNEXPECTED_EMPTY_LINKED_KEY,x.lastStartLoc,0);const S=Q.context(),P=n(7,S.offset,S.startLoc);return P.value="",i(P,S.offset,S.startLoc),m.key=P,i(m,S.offset,S.startLoc),{nextConsumeToken:p,node:m}}}return i(m,Q.currentOffset(),Q.currentPosition()),{node:m}}function u(Q){const x=Q.context(),m=x.currentType===1?Q.currentOffset():x.offset,p=x.currentType===1?x.endLoc:x.startLoc,S=n(2,m,p);S.items=[];let P=null;do{const D=P||Q.nextToken();switch(P=null,D.type){case 0:D.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(D)),S.items.push(s(Q,D.value||""));break;case 5:D.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(D)),S.items.push(o(Q,D.value||""));break;case 4:D.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(D)),S.items.push(c(Q,D.value||""));break;case 6:D.value==null&&r(Q,ue.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,We(D)),S.items.push(O(Q,D.value||""));break;case 7:{const w=l(Q);S.items.push(w.node),P=w.nextConsumeToken||null;break}}}while(x.currentType!==13&&x.currentType!==1);const b=x.currentType===1?x.lastOffset:Q.currentOffset(),k=x.currentType===1?x.lastEndLoc:Q.currentPosition();return i(S,b,k),S}function f(Q,x,m,p){const S=Q.context();let P=p.items.length===0;const b=n(1,x,m);b.cases=[],b.cases.push(p);do{const k=u(Q);P||(P=k.items.length===0),b.cases.push(k)}while(S.currentType!==13);return P&&r(Q,ue.MUST_HAVE_MESSAGES_IN_PLURAL,m,0),i(b,Q.currentOffset(),Q.currentPosition()),b}function h(Q){const x=Q.context(),{offset:m,startLoc:p}=x,S=u(Q);return x.currentType===13?S:f(Q,m,p,S)}function v(Q){const x=gf(Q,we({},e)),m=x.context(),p=n(0,m.offset,m.startLoc);return t&&p.loc&&(p.loc.source=Q),p.body=h(x),e.onCacheKey&&(p.cacheKey=e.onCacheKey(Q)),m.currentType!==13&&r(x,ue.UNEXPECTED_LEXICAL_ANALYSIS,m.lastStartLoc,0,Q[m.offset]||""),i(p,x.currentOffset(),x.currentPosition()),p}return{parse:v}}function We(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Pf(e,t={}){const a={ast:e,helpers:new Set};return{context:()=>a,helper:i=>(a.helpers.add(i),i)}}function Vi(e,t){for(let a=0;a<e.length;a++)tn(e[a],t)}function tn(e,t){switch(e.type){case 1:Vi(e.cases,t),t.helper("plural");break;case 2:Vi(e.items,t);break;case 6:{tn(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function yf(e,t={}){const a=Pf(e);a.helper("normalize"),e.body&&tn(e.body,a);const r=a.context();e.helpers=Array.from(r.helpers)}function $f(e){const t=e.body;return t.type===2?Ui(t):t.cases.forEach(a=>Ui(a)),e}function Ui(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let a=0;a<e.items.length;a++){const r=e.items[a];if(!(r.type===3||r.type===9)||r.value==null)break;t.push(r.value)}if(t.length===e.items.length){e.static=en(t);for(let a=0;a<e.items.length;a++){const r=e.items[a];(r.type===3||r.type===9)&&delete r.value}}}}function vt(e){switch(e.t=e.type,e.type){case 0:{const t=e;vt(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,a=t.cases;for(let r=0;r<a.length;r++)vt(a[r]);t.c=a,delete t.cases;break}case 2:{const t=e,a=t.items;for(let r=0;r<a.length;r++)vt(a[r]);t.i=a,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;vt(t.key),t.k=t.key,delete t.key,t.modifier&&(vt(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function wf(e,t){const{sourceMap:a,filename:r,breakLineCode:n,needIndent:i}=t,s=t.location!==!1,o={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:n,needIndent:i,indentLevel:0};s&&e.loc&&(o.source=e.loc.source);const c=()=>o;function O(v,Q){o.code+=v}function d(v,Q=!0){const x=Q?n:"";O(i?x+"  ".repeat(v):x)}function g(v=!0){const Q=++o.indentLevel;v&&d(Q)}function l(v=!0){const Q=--o.indentLevel;v&&d(Q)}function u(){d(o.indentLevel)}return{context:c,push:O,indent:g,deindent:l,newline:u,helper:v=>`_${v}`,needIndent:()=>o.needIndent}}function _f(e,t){const{helper:a}=e;e.push(`${a("linked")}(`),wt(e,t.key),t.modifier?(e.push(", "),wt(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function kf(e,t){const{helper:a,needIndent:r}=e;e.push(`${a("normalize")}([`),e.indent(r());const n=t.items.length;for(let i=0;i<n&&(wt(e,t.items[i]),i!==n-1);i++)e.push(", ");e.deindent(r()),e.push("])")}function Cf(e,t){const{helper:a,needIndent:r}=e;if(t.cases.length>1){e.push(`${a("plural")}([`),e.indent(r());const n=t.cases.length;for(let i=0;i<n&&(wt(e,t.cases[i]),i!==n-1);i++)e.push(", ");e.deindent(r()),e.push("])")}}function Ef(e,t){t.body?wt(e,t.body):e.push("null")}function wt(e,t){const{helper:a}=e;switch(t.type){case 0:Ef(e,t);break;case 1:Cf(e,t);break;case 2:kf(e,t);break;case 6:_f(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${a("interpolate")}(${a("list")}(${t.index}))`,t);break;case 4:e.push(`${a("interpolate")}(${a("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const Af=(e,t={})=>{const a=V(t.mode)?t.mode:"normal",r=V(t.filename)?t.filename:"message.intl",n=!!t.sourceMap,i=t.breakLineCode!=null?t.breakLineCode:a==="arrow"?";":`
`,s=t.needIndent?t.needIndent:a!=="arrow",o=e.helpers||[],c=wf(e,{mode:a,filename:r,sourceMap:n,breakLineCode:i,needIndent:s});c.push(a==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(s),o.length>0&&(c.push(`const { ${en(o.map(g=>`${g}: _${g}`),", ")} } = ctx`),c.newline()),c.push("return "),wt(c,e),c.deindent(s),c.push("}"),delete e.helpers;const{code:O,map:d}=c.context();return{ast:e,code:O,map:d?d.toJSON():void 0}};function Tf(e,t={}){const a=we({},t),r=!!a.jit,n=!!a.minify,i=a.optimize==null?!0:a.optimize,o=bf(a).parse(e);return r?(i&&$f(o),n&&vt(o),{ast:o,code:""}):(yf(o,a),Af(o,a))}/*!
  * core-base v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Zf(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(pt().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(pt().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function Ne(e){return Oe(e)&&an(e)===0&&(Ue(e,"b")||Ue(e,"body"))}const Qo=["b","body"];function Xf(e){return ft(e,Qo)}const So=["c","cases"];function Rf(e){return ft(e,So,[])}const bo=["s","static"];function Df(e){return ft(e,bo)}const Po=["i","items"];function Bf(e){return ft(e,Po,[])}const yo=["t","type"];function an(e){return ft(e,yo)}const $o=["v","value"];function Mt(e,t){const a=ft(e,$o);if(a!=null)return a;throw qt(t)}const wo=["m","modifier"];function qf(e){return ft(e,wo)}const _o=["k","key"];function Lf(e){const t=ft(e,_o);if(t)return t;throw qt(6)}function ft(e,t,a){for(let r=0;r<t.length;r++){const n=t[r];if(Ue(e,n)&&e[n]!=null)return e[n]}return a}const ko=[...Qo,...So,...bo,...Po,..._o,...wo,...$o,...yo];function qt(e){return new Error(`unhandled node type: ${e}`)}function cr(e){return a=>Ff(a,e)}function Ff(e,t){const a=Xf(t);if(a==null)throw qt(0);if(an(a)===1){const i=Rf(a);return e.plural(i.reduce((s,o)=>[...s,Mi(e,o)],[]))}else return Mi(e,a)}function Mi(e,t){const a=Df(t);if(a!=null)return e.type==="text"?a:e.normalize([a]);{const r=Bf(t).reduce((n,i)=>[...n,yr(e,i)],[]);return e.normalize(r)}}function yr(e,t){const a=an(t);switch(a){case 3:return Mt(t,a);case 9:return Mt(t,a);case 4:{const r=t;if(Ue(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(Ue(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw qt(a)}case 5:{const r=t;if(Ue(r,"i")&&ye(r.i))return e.interpolate(e.list(r.i));if(Ue(r,"index")&&ye(r.index))return e.interpolate(e.list(r.index));throw qt(a)}case 6:{const r=t,n=qf(r),i=Lf(r);return e.linked(yr(e,i),n?yr(e,n):void 0,e.type)}case 7:return Mt(t,a);case 8:return Mt(t,a);default:throw new Error(`unhandled node on format message part: ${a}`)}}const Yf=e=>e;let Wt=de();function If(e,t={}){let a=!1;const r=t.onError||uf;return t.onError=n=>{a=!0,r(n)},_t(ke({},Tf(e,t)),{detectError:a})}function Vf(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&V(e)){le(t.warnHtmlMessage)&&t.warnHtmlMessage;const r=(t.onCacheKey||Yf)(e),n=Wt[r];if(n)return n;const{ast:i,detectError:s}=If(e,_t(ke({},t),{location:!1,jit:!0})),o=cr(i);return s?o:Wt[r]=o}else{const a=e.cacheKey;if(a){const r=Wt[a];return r||(Wt[a]=cr(e))}else return cr(e)}}let Lt=null;function Uf(e){Lt=e}function Mf(e,t,a){Lt&&Lt.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:a})}const Wf=zf("function:translate");function zf(e){return t=>Lt&&Lt.emit(e,t)}const tt={INVALID_ARGUMENT:cf,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},Nf=24;function at(e){return ha(e,null,void 0)}function rn(e,t){return t.locale!=null?Wi(t.locale):Wi(e.locale)}let ur;function Wi(e){if(V(e))return e;if(xe(e)){if(e.resolvedOnce&&ur!=null)return ur;if(e.constructor.name==="Function"){const t=e();if(sf(t))throw at(tt.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ur=t}else throw at(tt.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw at(tt.NOT_SUPPORT_LOCALE_TYPE)}function Gf(e,t,a){return[...new Set([a,...$e(t)?t:Oe(t)?Object.keys(t):V(t)?[t]:[a]])]}function Co(e,t,a){const r=V(a)?a:Ft,n=e;n.__localeChainCache||(n.__localeChainCache=new Map);let i=n.__localeChainCache.get(r);if(!i){i=[];let s=[a];for(;$e(s);)s=zi(i,s,t);const o=$e(t)||!re(t)?t:t.default?t.default:null;s=V(o)?[o]:o,$e(s)&&zi(i,s,!1),n.__localeChainCache.set(r,i)}return i}function zi(e,t,a){let r=!0;for(let n=0;n<t.length&&le(r);n++){const i=t[n];V(i)&&(r=jf(e,t[n],a))}return r}function jf(e,t,a){let r;const n=t.split("-");do{const i=n.join("-");r=Hf(e,i,a),n.splice(-1,1)}while(n.length&&r===!0);return r}function Hf(e,t,a){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const n=t.replace(/!/g,"");e.push(n),($e(a)||re(a))&&a[n]&&(r=a[n])}return r}const dt=[];dt[0]={w:[0],i:[3,0],"[":[4],o:[7]};dt[1]={w:[1],".":[2],"[":[4],o:[7]};dt[2]={w:[2],i:[3,0],0:[3,0]};dt[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};dt[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};dt[5]={"'":[4,0],o:8,l:[5,0]};dt[6]={'"':[4,0],o:8,l:[6,0]};const Kf=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Jf(e){return Kf.test(e)}function ed(e){const t=e.charCodeAt(0),a=e.charCodeAt(e.length-1);return t===a&&(t===34||t===39)?e.slice(1,-1):e}function td(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function ad(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Jf(t)?ed(t):"*"+t}function rd(e){const t=[];let a=-1,r=0,n=0,i,s,o,c,O,d,g;const l=[];l[0]=()=>{s===void 0?s=o:s+=o},l[1]=()=>{s!==void 0&&(t.push(s),s=void 0)},l[2]=()=>{l[0](),n++},l[3]=()=>{if(n>0)n--,r=4,l[0]();else{if(n=0,s===void 0||(s=ad(s),s===!1))return!1;l[1]()}};function u(){const f=e[a+1];if(r===5&&f==="'"||r===6&&f==='"')return a++,o="\\"+f,l[0](),!0}for(;r!==null;)if(a++,i=e[a],!(i==="\\"&&u())){if(c=td(i),g=dt[r],O=g[c]||g.l||8,O===8||(r=O[0],O[1]!==void 0&&(d=l[O[1]],d&&(o=i,d()===!1))))return;if(r===7)return t}}const Ni=new Map;function nd(e,t){return Oe(e)?e[t]:null}function id(e,t){if(!Oe(e))return null;let a=Ni.get(t);if(a||(a=rd(t),a&&Ni.set(t,a)),!a)return null;const r=a.length;let n=e,i=0;for(;i<r;){const s=a[i];if(ko.includes(s)&&Ne(n))return null;const o=n[s];if(o===void 0||xe(n))return null;n=o,i++}return n}const sd="11.1.3",pa=-1,Ft="en-US",Gi="",ji=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function od(){return{upper:(e,t)=>t==="text"&&V(e)?e.toUpperCase():t==="vnode"&&Oe(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&V(e)?e.toLowerCase():t==="vnode"&&Oe(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&V(e)?ji(e):t==="vnode"&&Oe(e)&&"__v_isVNode"in e?ji(e.children):e}}let Eo;function ld(e){Eo=e}let Ao;function Od(e){Ao=e}let To;function cd(e){To=e}let Zo=null;const ud=e=>{Zo=e},fd=()=>Zo;let Xo=null;const Hi=e=>{Xo=e},dd=()=>Xo;let Ki=0;function hd(e={}){const t=xe(e.onWarn)?e.onWarn:lf,a=V(e.version)?e.version:sd,r=V(e.locale)||xe(e.locale)?e.locale:Ft,n=xe(r)?Ft:r,i=$e(e.fallbackLocale)||re(e.fallbackLocale)||V(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:n,s=re(e.messages)?e.messages:fr(n),o=re(e.datetimeFormats)?e.datetimeFormats:fr(n),c=re(e.numberFormats)?e.numberFormats:fr(n),O=we(de(),e.modifiers,od()),d=e.pluralRules||de(),g=xe(e.missing)?e.missing:null,l=le(e.missingWarn)||$t(e.missingWarn)?e.missingWarn:!0,u=le(e.fallbackWarn)||$t(e.fallbackWarn)?e.fallbackWarn:!0,f=!!e.fallbackFormat,h=!!e.unresolving,v=xe(e.postTranslation)?e.postTranslation:null,Q=re(e.processor)?e.processor:null,x=le(e.warnHtmlMessage)?e.warnHtmlMessage:!0,m=!!e.escapeParameter,p=xe(e.messageCompiler)?e.messageCompiler:Eo,S=xe(e.messageResolver)?e.messageResolver:Ao||nd,P=xe(e.localeFallbacker)?e.localeFallbacker:To||Gf,b=Oe(e.fallbackContext)?e.fallbackContext:void 0,k=e,D=Oe(k.__datetimeFormatters)?k.__datetimeFormatters:new Map,w=Oe(k.__numberFormatters)?k.__numberFormatters:new Map,C=Oe(k.__meta)?k.__meta:{};Ki++;const Z={version:a,cid:Ki,locale:r,fallbackLocale:i,messages:s,modifiers:O,pluralRules:d,missing:g,missingWarn:l,fallbackWarn:u,fallbackFormat:f,unresolving:h,postTranslation:v,processor:Q,warnHtmlMessage:x,escapeParameter:m,messageCompiler:p,messageResolver:S,localeFallbacker:P,fallbackContext:b,onWarn:t,__meta:C};return Z.datetimeFormats=o,Z.numberFormats=c,Z.__datetimeFormatters=D,Z.__numberFormatters=w,__INTLIFY_PROD_DEVTOOLS__&&Mf(Z,a,C),Z}const fr=e=>({[e]:de()});function nn(e,t,a,r,n){const{missing:i,onWarn:s}=e;if(i!==null){const o=i(e,a,t,n);return V(o)?o:t}else return t}function At(e,t,a){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}function pd(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function xd(e,t){const a=t.indexOf(e);if(a===-1)return!1;for(let r=a+1;r<t.length;r++)if(pd(e,t[r]))return!0;return!1}function Ji(e,...t){const{datetimeFormats:a,unresolving:r,fallbackLocale:n,onWarn:i,localeFallbacker:s}=e,{__datetimeFormatters:o}=e,[c,O,d,g]=$r(...t),l=le(d.missingWarn)?d.missingWarn:e.missingWarn;le(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const u=!!d.part,f=rn(e,d),h=s(e,n,f);if(!V(c)||c==="")return new Intl.DateTimeFormat(f,g).format(O);let v={},Q,x=null;const m="datetime format";for(let P=0;P<h.length&&(Q=h[P],v=a[Q]||{},x=v[c],!re(x));P++)nn(e,c,Q,l,m);if(!re(x)||!V(Q))return r?pa:c;let p=`${Q}__${c}`;da(g)||(p=`${p}__${JSON.stringify(g)}`);let S=o.get(p);return S||(S=new Intl.DateTimeFormat(Q,we({},x,g)),o.set(p,S)),u?S.formatToParts(O):S.format(O)}const Ro=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function $r(...e){const[t,a,r,n]=e,i=de();let s=de(),o;if(V(t)){const c=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!c)throw at(tt.INVALID_ISO_DATE_ARGUMENT);const O=c[3]?c[3].trim().startsWith("T")?`${c[1].trim()}${c[3].trim()}`:`${c[1].trim()}T${c[3].trim()}`:c[1].trim();o=new Date(O);try{o.toISOString()}catch(d){throw at(tt.INVALID_ISO_DATE_ARGUMENT)}}else if(af(t)){if(isNaN(t.getTime()))throw at(tt.INVALID_DATE_ARGUMENT);o=t}else if(ye(t))o=t;else throw at(tt.INVALID_ARGUMENT);return V(a)?i.key=a:re(a)&&Object.keys(a).forEach(c=>{Ro.includes(c)?s[c]=a[c]:i[c]=a[c]}),V(r)?i.locale=r:re(r)&&(s=r),re(n)&&(s=n),[i.key||"",o,i,s]}function es(e,t,a){const r=e;for(const n in a){const i=`${t}__${n}`;r.__datetimeFormatters.has(i)&&r.__datetimeFormatters.delete(i)}}function ts(e,...t){const{numberFormats:a,unresolving:r,fallbackLocale:n,onWarn:i,localeFallbacker:s}=e,{__numberFormatters:o}=e,[c,O,d,g]=wr(...t),l=le(d.missingWarn)?d.missingWarn:e.missingWarn;le(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const u=!!d.part,f=rn(e,d),h=s(e,n,f);if(!V(c)||c==="")return new Intl.NumberFormat(f,g).format(O);let v={},Q,x=null;const m="number format";for(let P=0;P<h.length&&(Q=h[P],v=a[Q]||{},x=v[c],!re(x));P++)nn(e,c,Q,l,m);if(!re(x)||!V(Q))return r?pa:c;let p=`${Q}__${c}`;da(g)||(p=`${p}__${JSON.stringify(g)}`);let S=o.get(p);return S||(S=new Intl.NumberFormat(Q,we({},x,g)),o.set(p,S)),u?S.formatToParts(O):S.format(O)}const Do=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function wr(...e){const[t,a,r,n]=e,i=de();let s=de();if(!ye(t))throw at(tt.INVALID_ARGUMENT);const o=t;return V(a)?i.key=a:re(a)&&Object.keys(a).forEach(c=>{Do.includes(c)?s[c]=a[c]:i[c]=a[c]}),V(r)?i.locale=r:re(r)&&(s=r),re(n)&&(s=n),[i.key||"",o,i,s]}function as(e,t,a){const r=e;for(const n in a){const i=`${t}__${n}`;r.__numberFormatters.has(i)&&r.__numberFormatters.delete(i)}}const md=e=>e,gd=e=>"",vd="text",Qd=e=>e.length===0?"":en(e),Sd=of;function rs(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function bd(e){const t=ye(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(ye(e.named.count)||ye(e.named.n))?ye(e.named.count)?e.named.count:ye(e.named.n)?e.named.n:t:t}function Pd(e,t){t.count||(t.count=e),t.n||(t.n=e)}function yd(e={}){const t=e.locale,a=bd(e),r=Oe(e.pluralRules)&&V(t)&&xe(e.pluralRules[t])?e.pluralRules[t]:rs,n=Oe(e.pluralRules)&&V(t)&&xe(e.pluralRules[t])?rs:void 0,i=Q=>Q[r(a,Q.length,n)],s=e.list||[],o=Q=>s[Q],c=e.named||de();ye(e.pluralIndex)&&Pd(a,c);const O=Q=>c[Q];function d(Q,x){const m=xe(e.messages)?e.messages(Q,!!x):Oe(e.messages)?e.messages[Q]:!1;return m||(e.parent?e.parent.message(Q):gd)}const g=Q=>e.modifiers?e.modifiers[Q]:md,l=re(e.processor)&&xe(e.processor.normalize)?e.processor.normalize:Qd,u=re(e.processor)&&xe(e.processor.interpolate)?e.processor.interpolate:Sd,f=re(e.processor)&&V(e.processor.type)?e.processor.type:vd,v={list:o,named:O,plural:i,linked:(Q,...x)=>{const[m,p]=x;let S="text",P="";x.length===1?Oe(m)?(P=m.modifier||P,S=m.type||S):V(m)&&(P=m||P):x.length===2&&(V(m)&&(P=m||P),V(p)&&(S=p||S));const b=d(Q,!0)(v),k=S==="vnode"&&$e(b)&&P?b[0]:b;return P?g(P)(k,S):k},message:d,type:f,interpolate:u,normalize:l,values:we(de(),s,c)};return v}const ns=()=>"",Fe=e=>xe(e);function is(e,...t){const{fallbackFormat:a,postTranslation:r,unresolving:n,messageCompiler:i,fallbackLocale:s,messages:o}=e,[c,O]=_r(...t),d=le(O.missingWarn)?O.missingWarn:e.missingWarn,g=le(O.fallbackWarn)?O.fallbackWarn:e.fallbackWarn,l=le(O.escapeParameter)?O.escapeParameter:e.escapeParameter,u=!!O.resolvedMessage,f=V(O.default)||le(O.default)?le(O.default)?i?c:()=>c:O.default:a?i?c:()=>c:null,h=a||f!=null&&(V(f)||xe(f)),v=rn(e,O);l&&$d(O);let[Q,x,m]=u?[c,v,o[v]||de()]:Bo(e,c,v,s,g,d),p=Q,S=c;if(!u&&!(V(p)||Ne(p)||Fe(p))&&h&&(p=f,S=p),!u&&(!(V(p)||Ne(p)||Fe(p))||!V(x)))return n?pa:c;let P=!1;const b=()=>{P=!0},k=Fe(p)?p:qo(e,c,x,p,S,b);if(P)return p;const D=kd(e,x,m,O),w=yd(D),C=wd(e,k,w),Z=r?r(C,c):C;if(__INTLIFY_PROD_DEVTOOLS__){const R={timestamp:Date.now(),key:V(c)?c:Fe(p)?p.key:"",locale:x||(Fe(p)?p.locale:""),format:V(p)?p:Fe(p)?p.source:"",message:Z};R.meta=we({},e.__meta,fd()||{}),Wf(R)}return Z}function $d(e){$e(e.list)?e.list=e.list.map(t=>V(t)?Yi(t):t):Oe(e.named)&&Object.keys(e.named).forEach(t=>{V(e.named[t])&&(e.named[t]=Yi(e.named[t]))})}function Bo(e,t,a,r,n,i){const{messages:s,onWarn:o,messageResolver:c,localeFallbacker:O}=e,d=O(e,r,a);let g=de(),l,u=null;const f="translate";for(let h=0;h<d.length&&(l=d[h],g=s[l]||de(),(u=c(g,t))===null&&(u=g[t]),!(V(u)||Ne(u)||Fe(u)));h++)if(!xd(l,d)){const v=nn(e,t,l,i,f);v!==t&&(u=v)}return[u,l,g]}function qo(e,t,a,r,n,i){const{messageCompiler:s,warnHtmlMessage:o}=e;if(Fe(r)){const O=r;return O.locale=O.locale||a,O.key=O.key||t,O}if(s==null){const O=()=>r;return O.locale=a,O.key=t,O}const c=s(r,_d(e,a,n,r,o,i));return c.locale=a,c.key=t,c.source=r,c}function wd(e,t,a){return t(a)}function _r(...e){const[t,a,r]=e,n=de();if(!V(t)&&!ye(t)&&!Fe(t)&&!Ne(t))throw at(tt.INVALID_ARGUMENT);const i=ye(t)?String(t):(Fe(t),t);return ye(a)?n.plural=a:V(a)?n.default=a:re(a)&&!da(a)?n.named=a:$e(a)&&(n.list=a),ye(r)?n.plural=r:V(r)?n.default=r:re(r)&&we(n,r),[i,n]}function _d(e,t,a,r,n,i){return{locale:t,key:a,warnHtmlMessage:n,onError:s=>{throw i&&i(s),s},onCacheKey:s=>ef(t,a,s)}}function kd(e,t,a,r){const{modifiers:n,pluralRules:i,messageResolver:s,fallbackLocale:o,fallbackWarn:c,missingWarn:O,fallbackContext:d}=e,l={locale:t,modifiers:n,pluralRules:i,messages:(u,f)=>{let h=s(a,u);if(h==null&&(d||f)){const[,,v]=Bo(d||e,u,t,o,c,O);h=s(v,u)}if(V(h)||Ne(h)){let v=!1;const x=qo(e,u,t,h,u,()=>{v=!0});return v?ns:x}else return Fe(h)?h:ns}};return e.processor&&(l.processor=e.processor),r.list&&(l.list=r.list),r.named&&(l.named=r.named),ye(r.plural)&&(l.pluralIndex=r.plural),l}Zf();/*!
  * vue-i18n v11.1.3
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Cd="11.1.3";function Ed(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(pt().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(pt().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(pt().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(pt().__INTLIFY_PROD_DEVTOOLS__=!1)}const Te={UNEXPECTED_RETURN_TYPE:Nf,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34};function Le(e,...t){return ha(e,null,void 0)}const kr=ut("__translateVNode"),Cr=ut("__datetimeParts"),Er=ut("__numberParts"),Lo=ut("__setPluralRules"),Fo=ut("__injectWithOption"),Ar=ut("__dispose");function Yt(e){if(!Oe(e)||Ne(e))return e;for(const t in e)if(Ue(e,t))if(!t.includes("."))Oe(e[t])&&Yt(e[t]);else{const a=t.split("."),r=a.length-1;let n=e,i=!1;for(let s=0;s<r;s++){if(a[s]==="__proto__")throw new Error(`unsafe key: ${a[s]}`);if(a[s]in n||(n[a[s]]=de()),!Oe(n[a[s]])){i=!0;break}n=n[a[s]]}if(i||(Ne(n)?ko.includes(a[r])||delete e[t]:(n[a[r]]=e[t],delete e[t])),!Ne(n)){const s=n[a[r]];Oe(s)&&Yt(s)}}return e}function sn(e,t){const{messages:a,__i18n:r,messageResolver:n,flatJson:i}=t,s=re(a)?a:$e(r)?de():{[e]:de()};if($e(r)&&r.forEach(o=>{if("locale"in o&&"resource"in o){const{locale:c,resource:O}=o;c?(s[c]=s[c]||de(),ea(O,s[c])):ea(O,s)}else V(o)&&ea(JSON.parse(o),s)}),n==null&&i)for(const o in s)Ue(s,o)&&Yt(s[o]);return s}function Yo(e){return e.type}function Io(e,t,a){let r=Oe(t.messages)?t.messages:de();"__i18nGlobal"in a&&(r=sn(e.locale.value,{messages:r,__i18n:a.__i18nGlobal}));const n=Object.keys(r);n.length&&n.forEach(i=>{e.mergeLocaleMessage(i,r[i])});{if(Oe(t.datetimeFormats)){const i=Object.keys(t.datetimeFormats);i.length&&i.forEach(s=>{e.mergeDateTimeFormat(s,t.datetimeFormats[s])})}if(Oe(t.numberFormats)){const i=Object.keys(t.numberFormats);i.length&&i.forEach(s=>{e.mergeNumberFormat(s,t.numberFormats[s])})}}}function ss(e){return Ye(P0,null,e,0)}const os="__INTLIFY_META__",ls=()=>[],Ad=()=>!1;let Os=0;function cs(e){return(t,a,r,n)=>e(a,r,Rt()||void 0,n)}const Td=()=>{const e=Rt();let t=null;return e&&(t=Yo(e)[os])?{[os]:t}:null};function on(e={}){const{__root:t,__injectWithOption:a}=e,r=t===void 0,n=e.flatJson,i=Oa?Se:b0;let s=le(e.inheritLocale)?e.inheritLocale:!0;const o=i(t&&s?t.locale.value:V(e.locale)?e.locale:Ft),c=i(t&&s?t.fallbackLocale.value:V(e.fallbackLocale)||$e(e.fallbackLocale)||re(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:o.value),O=i(sn(o.value,e)),d=i(re(e.datetimeFormats)?e.datetimeFormats:{[o.value]:{}}),g=i(re(e.numberFormats)?e.numberFormats:{[o.value]:{}});let l=t?t.missingWarn:le(e.missingWarn)||$t(e.missingWarn)?e.missingWarn:!0,u=t?t.fallbackWarn:le(e.fallbackWarn)||$t(e.fallbackWarn)?e.fallbackWarn:!0,f=t?t.fallbackRoot:le(e.fallbackRoot)?e.fallbackRoot:!0,h=!!e.fallbackFormat,v=xe(e.missing)?e.missing:null,Q=xe(e.missing)?cs(e.missing):null,x=xe(e.postTranslation)?e.postTranslation:null,m=t?t.warnHtmlMessage:le(e.warnHtmlMessage)?e.warnHtmlMessage:!0,p=!!e.escapeParameter;const S=t?t.modifiers:re(e.modifiers)?e.modifiers:{};let P=e.pluralRules||t&&t.pluralRules,b;b=(()=>{r&&Hi(null);const A={version:Cd,locale:o.value,fallbackLocale:c.value,messages:O.value,modifiers:S,pluralRules:P,missing:Q===null?void 0:Q,missingWarn:l,fallbackWarn:u,fallbackFormat:h,unresolving:!0,postTranslation:x===null?void 0:x,warnHtmlMessage:m,escapeParameter:p,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};A.datetimeFormats=d.value,A.numberFormats=g.value,A.__datetimeFormatters=re(b)?b.__datetimeFormatters:void 0,A.__numberFormatters=re(b)?b.__numberFormatters:void 0;const L=hd(A);return r&&Hi(L),L})(),At(b,o.value,c.value);function D(){return[o.value,c.value,O.value,d.value,g.value]}const w=Ce({get:()=>o.value,set:A=>{b.locale=A,o.value=A}}),C=Ce({get:()=>c.value,set:A=>{b.fallbackLocale=A,c.value=A,At(b,o.value,A)}}),Z=Ce(()=>O.value),R=Ce(()=>d.value),U=Ce(()=>g.value);function M(){return xe(x)?x:null}function G(A){x=A,b.postTranslation=A}function ne(){return v}function Y(A){A!==null&&(Q=cs(A)),v=A,b.missing=Q}const F=(A,L,se,he,Ge,ht)=>{D();let je;try{__INTLIFY_PROD_DEVTOOLS__,r||(b.fallbackContext=t?dd():void 0),je=A(b)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(b.fallbackContext=void 0)}if(se!=="translate exists"&&ye(je)&&je===pa||se==="translate exists"&&!je){const[ot,gt]=L();return t&&f?he(t):Ge(ot)}else{if(ht(je))return je;throw Le(Te.UNEXPECTED_RETURN_TYPE)}};function j(...A){return F(L=>Reflect.apply(is,null,[L,...A]),()=>_r(...A),"translate",L=>Reflect.apply(L.t,L,[...A]),L=>L,L=>V(L))}function T(...A){const[L,se,he]=A;if(he&&!Oe(he))throw Le(Te.INVALID_ARGUMENT);return j(L,se,we({resolvedMessage:!0},he||{}))}function X(...A){return F(L=>Reflect.apply(Ji,null,[L,...A]),()=>$r(...A),"datetime format",L=>Reflect.apply(L.d,L,[...A]),()=>Gi,L=>V(L))}function q(...A){return F(L=>Reflect.apply(ts,null,[L,...A]),()=>wr(...A),"number format",L=>Reflect.apply(L.n,L,[...A]),()=>Gi,L=>V(L))}function B(A){return A.map(L=>V(L)||ye(L)||le(L)?ss(String(L)):L)}const J={normalize:B,interpolate:A=>A,type:"vnode"};function Qe(...A){return F(L=>{let se;const he=L;try{he.processor=J,se=Reflect.apply(is,null,[he,...A])}finally{he.processor=null}return se},()=>_r(...A),"translate",L=>L[kr](...A),L=>[ss(L)],L=>$e(L))}function H(...A){return F(L=>Reflect.apply(ts,null,[L,...A]),()=>wr(...A),"number format",L=>L[Er](...A),ls,L=>V(L)||$e(L))}function Ze(...A){return F(L=>Reflect.apply(Ji,null,[L,...A]),()=>$r(...A),"datetime format",L=>L[Cr](...A),ls,L=>V(L)||$e(L))}function Xe(A){P=A,b.pluralRules=P}function Me(A,L){return F(()=>{if(!A)return!1;const se=V(L)?L:o.value,he=pe(se),Ge=b.messageResolver(he,A);return Ne(Ge)||Fe(Ge)||V(Ge)},()=>[A],"translate exists",se=>Reflect.apply(se.te,se,[A,L]),Ad,se=>le(se))}function ve(A){let L=null;const se=Co(b,c.value,o.value);for(let he=0;he<se.length;he++){const Ge=O.value[se[he]]||{},ht=b.messageResolver(Ge,A);if(ht!=null){L=ht;break}}return L}function me(A){const L=ve(A);return L!=null?L:t?t.tm(A)||{}:{}}function pe(A){return O.value[A]||{}}function ee(A,L){if(n){const se={[A]:L};for(const he in se)Ue(se,he)&&Yt(se[he]);L=se[A]}O.value[A]=L,b.messages=O.value}function te(A,L){O.value[A]=O.value[A]||{};const se={[A]:L};if(n)for(const he in se)Ue(se,he)&&Yt(se[he]);L=se[A],ea(L,O.value[A]),b.messages=O.value}function ae(A){return d.value[A]||{}}function $(A,L){d.value[A]=L,b.datetimeFormats=d.value,es(b,A,L)}function y(A,L){d.value[A]=we(d.value[A]||{},L),b.datetimeFormats=d.value,es(b,A,L)}function E(A){return g.value[A]||{}}function I(A,L){g.value[A]=L,b.numberFormats=g.value,as(b,A,L)}function z(A,L){g.value[A]=we(g.value[A]||{},L),b.numberFormats=g.value,as(b,A,L)}Os++,t&&Oa&&(St(t.locale,A=>{s&&(o.value=A,b.locale=A,At(b,o.value,c.value))}),St(t.fallbackLocale,A=>{s&&(c.value=A,b.fallbackLocale=A,At(b,o.value,c.value))}));const N={id:Os,locale:w,fallbackLocale:C,get inheritLocale(){return s},set inheritLocale(A){s=A,A&&t&&(o.value=t.locale.value,c.value=t.fallbackLocale.value,At(b,o.value,c.value))},get availableLocales(){return Object.keys(O.value).sort()},messages:Z,get modifiers(){return S},get pluralRules(){return P||{}},get isGlobal(){return r},get missingWarn(){return l},set missingWarn(A){l=A,b.missingWarn=l},get fallbackWarn(){return u},set fallbackWarn(A){u=A,b.fallbackWarn=u},get fallbackRoot(){return f},set fallbackRoot(A){f=A},get fallbackFormat(){return h},set fallbackFormat(A){h=A,b.fallbackFormat=h},get warnHtmlMessage(){return m},set warnHtmlMessage(A){m=A,b.warnHtmlMessage=A},get escapeParameter(){return p},set escapeParameter(A){p=A,b.escapeParameter=A},t:j,getLocaleMessage:pe,setLocaleMessage:ee,mergeLocaleMessage:te,getPostTranslationHandler:M,setPostTranslationHandler:G,getMissingHandler:ne,setMissingHandler:Y,[Lo]:Xe};return N.datetimeFormats=R,N.numberFormats=U,N.rt=T,N.te=Me,N.tm=me,N.d=X,N.n=q,N.getDateTimeFormat=ae,N.setDateTimeFormat=$,N.mergeDateTimeFormat=y,N.getNumberFormat=E,N.setNumberFormat=I,N.mergeNumberFormat=z,N[Fo]=a,N[kr]=Qe,N[Cr]=Ze,N[Er]=H,N}function Zd(e){const t=V(e.locale)?e.locale:Ft,a=V(e.fallbackLocale)||$e(e.fallbackLocale)||re(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=xe(e.missing)?e.missing:void 0,n=le(e.silentTranslationWarn)||$t(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,i=le(e.silentFallbackWarn)||$t(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,s=le(e.fallbackRoot)?e.fallbackRoot:!0,o=!!e.formatFallbackMessages,c=re(e.modifiers)?e.modifiers:{},O=e.pluralizationRules,d=xe(e.postTranslation)?e.postTranslation:void 0,g=V(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,l=!!e.escapeParameterHtml,u=le(e.sync)?e.sync:!0;let f=e.messages;if(re(e.sharedMessages)){const S=e.sharedMessages;f=Object.keys(S).reduce((b,k)=>{const D=b[k]||(b[k]={});return we(D,S[k]),b},f||{})}const{__i18n:h,__root:v,__injectWithOption:Q}=e,x=e.datetimeFormats,m=e.numberFormats,p=e.flatJson;return{locale:t,fallbackLocale:a,messages:f,flatJson:p,datetimeFormats:x,numberFormats:m,missing:r,missingWarn:n,fallbackWarn:i,fallbackRoot:s,fallbackFormat:o,modifiers:c,pluralRules:O,postTranslation:d,warnHtmlMessage:g,escapeParameter:l,messageResolver:e.messageResolver,inheritLocale:u,__i18n:h,__root:v,__injectWithOption:Q}}function Tr(e={}){const t=on(Zd(e)),{__extender:a}=e,r={id:t.id,get locale(){return t.locale.value},set locale(n){t.locale.value=n},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(n){t.fallbackLocale.value=n},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(n){t.setMissingHandler(n)},get silentTranslationWarn(){return le(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(n){t.missingWarn=le(n)?!n:n},get silentFallbackWarn(){return le(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(n){t.fallbackWarn=le(n)?!n:n},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(n){t.fallbackFormat=n},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(n){t.setPostTranslationHandler(n)},get sync(){return t.inheritLocale},set sync(n){t.inheritLocale=n},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(n){t.warnHtmlMessage=n!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(n){t.escapeParameter=n},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...n){return Reflect.apply(t.t,t,[...n])},rt(...n){return Reflect.apply(t.rt,t,[...n])},te(n,i){return t.te(n,i)},tm(n){return t.tm(n)},getLocaleMessage(n){return t.getLocaleMessage(n)},setLocaleMessage(n,i){t.setLocaleMessage(n,i)},mergeLocaleMessage(n,i){t.mergeLocaleMessage(n,i)},d(...n){return Reflect.apply(t.d,t,[...n])},getDateTimeFormat(n){return t.getDateTimeFormat(n)},setDateTimeFormat(n,i){t.setDateTimeFormat(n,i)},mergeDateTimeFormat(n,i){t.mergeDateTimeFormat(n,i)},n(...n){return Reflect.apply(t.n,t,[...n])},getNumberFormat(n){return t.getNumberFormat(n)},setNumberFormat(n,i){t.setNumberFormat(n,i)},mergeNumberFormat(n,i){t.mergeNumberFormat(n,i)}};return r.__extender=a,r}function Xd(e,t,a){return{beforeCreate(){const r=Rt();if(!r)throw Le(Te.UNEXPECTED_ERROR);const n=this.$options;if(n.i18n){const i=n.i18n;if(n.__i18n&&(i.__i18n=n.__i18n),i.__root=t,this===this.$root)this.$i18n=us(e,i);else{i.__injectWithOption=!0,i.__extender=a.__vueI18nExtend,this.$i18n=Tr(i);const s=this.$i18n;s.__extender&&(s.__disposer=s.__extender(this.$i18n))}}else if(n.__i18n)if(this===this.$root)this.$i18n=us(e,n);else{this.$i18n=Tr({__i18n:n.__i18n,__injectWithOption:!0,__extender:a.__vueI18nExtend,__root:t});const i=this.$i18n;i.__extender&&(i.__disposer=i.__extender(this.$i18n))}else this.$i18n=e;n.__i18nGlobal&&Io(t,n,n),this.$t=(...i)=>this.$i18n.t(...i),this.$rt=(...i)=>this.$i18n.rt(...i),this.$te=(i,s)=>this.$i18n.te(i,s),this.$d=(...i)=>this.$i18n.d(...i),this.$n=(...i)=>this.$i18n.n(...i),this.$tm=i=>this.$i18n.tm(i),a.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const r=Rt();if(!r)throw Le(Te.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),a.__deleteInstance(r),delete this.$i18n}}}function us(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Lo](t.pluralizationRules||e.pluralizationRules);const a=sn(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach(r=>e.mergeLocaleMessage(r,a[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const ln={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function Rd({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,n)=>[...r,...n.type===ca?n.children:[n]],[]):t.reduce((a,r)=>{const n=e[r];return n&&(a[r]=n()),a},de())}function Vo(){return ca}const Dd=it({name:"i18n-t",props:we({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>ye(e)||!isNaN(e)}},ln),setup(e,t){const{slots:a,attrs:r}=t,n=e.i18n||On({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(a).filter(g=>g!=="_"),s=de();e.locale&&(s.locale=e.locale),e.plural!==void 0&&(s.plural=V(e.plural)?+e.plural:e.plural);const o=Rd(t,i),c=n[kr](e.keypath,o,s),O=we(de(),r),d=V(e.tag)||Oe(e.tag)?e.tag:Vo();return Qs(d,O,c)}}}),fs=Dd;function Bd(e){return $e(e)&&!V(e[0])}function Uo(e,t,a,r){const{slots:n,attrs:i}=t;return()=>{const s={part:!0};let o=de();e.locale&&(s.locale=e.locale),V(e.format)?s.key=e.format:Oe(e.format)&&(V(e.format.key)&&(s.key=e.format.key),o=Object.keys(e.format).reduce((l,u)=>a.includes(u)?we(de(),l,{[u]:e.format[u]}):l,de()));const c=r(e.value,s,o);let O=[s.key];$e(c)?O=c.map((l,u)=>{const f=n[l.type],h=f?f({[l.type]:l.value,index:u,parts:c}):[l.value];return Bd(h)&&(h[0].key=`${l.type}-${u}`),h}):V(c)&&(O=[c]);const d=we(de(),i),g=V(e.tag)||Oe(e.tag)?e.tag:Vo();return Qs(g,d,O)}}const qd=it({name:"i18n-n",props:we({value:{type:Number,required:!0},format:{type:[String,Object]}},ln),setup(e,t){const a=e.i18n||On({useScope:e.scope,__useComponent:!0});return Uo(e,t,Do,(...r)=>a[Er](...r))}}),ds=qd;function Ld(e,t){const a=e;if(e.mode==="composition")return a.__getInstance(t)||e.global;{const r=a.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function Fd(e){const t=s=>{const{instance:o,value:c}=s;if(!o||!o.$)throw Le(Te.UNEXPECTED_ERROR);const O=Ld(e,o.$),d=hs(c);return[Reflect.apply(O.t,O,[...ps(d)]),O]};return{created:(s,o)=>{const[c,O]=t(o);Oa&&e.global===O&&(s.__i18nWatcher=St(O.locale,()=>{o.instance&&o.instance.$forceUpdate()})),s.__composer=O,s.textContent=c},unmounted:s=>{Oa&&s.__i18nWatcher&&(s.__i18nWatcher(),s.__i18nWatcher=void 0,delete s.__i18nWatcher),s.__composer&&(s.__composer=void 0,delete s.__composer)},beforeUpdate:(s,{value:o})=>{if(s.__composer){const c=s.__composer,O=hs(o);s.textContent=Reflect.apply(c.t,c,[...ps(O)])}},getSSRProps:s=>{const[o]=t(s);return{textContent:o}}}}function hs(e){if(V(e))return{path:e};if(re(e)){if(!("path"in e))throw Le(Te.REQUIRED_VALUE,"path");return e}else throw Le(Te.INVALID_VALUE)}function ps(e){const{path:t,locale:a,args:r,choice:n,plural:i}=e,s={},o=r||{};return V(a)&&(s.locale=a),ye(n)&&(s.plural=n),ye(i)&&(s.plural=i),[t,o,s]}function Yd(e,t,...a){const r=re(a[0])?a[0]:{};(le(r.globalInstall)?r.globalInstall:!0)&&([fs.name,"I18nT"].forEach(i=>e.component(i,fs)),[ds.name,"I18nN"].forEach(i=>e.component(i,ds)),[ms.name,"I18nD"].forEach(i=>e.component(i,ms))),e.directive("t",Fd(t))}const Id=ut("global-vue-i18n");function Vd(e={}){const t=__VUE_I18N_LEGACY_API__&&le(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,a=le(e.globalInjection)?e.globalInjection:!0,r=new Map,[n,i]=Ud(e,t),s=ut("");function o(l){return r.get(l)||null}function c(l,u){r.set(l,u)}function O(l){r.delete(l)}const d={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},install(l,...u){return fe(this,null,function*(){if(l.__VUE_I18N_SYMBOL__=s,l.provide(l.__VUE_I18N_SYMBOL__,d),re(u[0])){const v=u[0];d.__composerExtend=v.__composerExtend,d.__vueI18nExtend=v.__vueI18nExtend}let f=null;!t&&a&&(f=Kd(l,d.global)),__VUE_I18N_FULL_INSTALL__&&Yd(l,d,...u),__VUE_I18N_LEGACY_API__&&t&&l.mixin(Xd(i,i.__composer,d));const h=l.unmount;l.unmount=()=>{f&&f(),d.dispose(),h()}})},get global(){return i},dispose(){n.stop()},__instances:r,__getInstance:o,__setInstance:c,__deleteInstance:O};return d}function On(e={}){const t=Rt();if(t==null)throw Le(Te.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Le(Te.NOT_INSTALLED);const a=Md(t),r=zd(a),n=Yo(t),i=Wd(e,n);if(i==="global")return Io(r,e,n),r;if(i==="parent"){let c=Nd(a,t,e.__useComponent);return c==null&&(c=r),c}const s=a;let o=s.__getInstance(t);if(o==null){const c=we({},e);"__i18n"in n&&(c.__i18n=n.__i18n),r&&(c.__root=r),o=on(c),s.__composerExtend&&(o[Ar]=s.__composerExtend(o)),jd(s,t,o),s.__setInstance(t,o)}return o}function Ud(e,t){const a=Q0(),r=__VUE_I18N_LEGACY_API__&&t?a.run(()=>Tr(e)):a.run(()=>on(e));if(r==null)throw Le(Te.UNEXPECTED_ERROR);return[a,r]}function Md(e){const t=S0(e.isCE?Id:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Le(e.isCE?Te.NOT_INSTALLED_WITH_PROVIDE:Te.UNEXPECTED_ERROR);return t}function Wd(e,t){return da(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function zd(e){return e.mode==="composition"?e.global:e.global.__composer}function Nd(e,t,a=!1){let r=null;const n=t.root;let i=Gd(t,a);for(;i!=null;){const s=e;if(e.mode==="composition")r=s.__getInstance(i);else if(__VUE_I18N_LEGACY_API__){const o=s.__getInstance(i);o!=null&&(r=o.__composer,a&&r&&!r[Fo]&&(r=null))}if(r!=null||n===i)break;i=i.parent}return r}function Gd(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function jd(e,t,a){ua(()=>{},t),Rr(()=>{const r=a;e.__deleteInstance(t);const n=r[Ar];n&&(n(),delete r[Ar])},t)}const Hd=["locale","fallbackLocale","availableLocales"],xs=["t","rt","d","n","tm","te"];function Kd(e,t){const a=Object.create(null);return Hd.forEach(n=>{const i=Object.getOwnPropertyDescriptor(t,n);if(!i)throw Le(Te.UNEXPECTED_ERROR);const s=Ss(i.value)?{get(){return i.value.value},set(o){i.value.value=o}}:{get(){return i.get&&i.get()}};Object.defineProperty(a,n,s)}),e.config.globalProperties.$i18n=a,xs.forEach(n=>{const i=Object.getOwnPropertyDescriptor(t,n);if(!i||!i.value)throw Le(Te.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${n}`,i)}),()=>{delete e.config.globalProperties.$i18n,xs.forEach(n=>{delete e.config.globalProperties[`$${n}`]})}}const Jd=it({name:"i18n-d",props:we({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ln),setup(e,t){const a=e.i18n||On({useScope:e.scope,__useComponent:!0});return Uo(e,t,Ro,(...r)=>a[Cr](...r))}}),ms=Jd;Ed();ld(Vf);Od(id);cd(Co);if(__INTLIFY_PROD_DEVTOOLS__){const e=pt();e.__INTLIFY__=!0,Uf(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}const eh="en",th={add:"Add",addSuccess:"Add Success",edit:"Edit",editSuccess:"Edit Success",delete:"Delete",deleteSuccess:"Delete Success",save:"Save",saveSuccess:"Save Success",reset:"Reset",action:"Action",export:"Export",exportSuccess:"Export Success",import:"Import",importSuccess:"Import Success",clear:"Clear",clearSuccess:"Clear Success",yes:"Yes",no:"No",update:"Update",download:"Download",noData:"No Data",wrong:"Something went wrong, please try again later.",success:"Success",failed:"Failed",verify:"Verify",unauthorizedTips:"Unauthorized, please verify first.",confirm:"Confirm",cancel:"Cancel"},ah={newChatButton:"New Chat",placeholder:"Ask me anything...(Shift + Enter = line break)",placeholderMobile:"Ask me anything...",copy:"Copy",copied:"Copied",copyCode:"Copy Code",clearChat:"Clear Chat",clearChatConfirm:"Are you sure to clear this chat?",exportImage:"Export Image",exportImageConfirm:"Are you sure to export this chat to png?",exportSuccess:"Export Success",exportFailed:"Export Failed",usingContext:"Context Mode",turnOnContext:"In the current mode, sending messages will carry previous chat records.",turnOffContext:"In the current mode, sending messages will not carry previous chat records.",deleteMessage:"Delete Message",deleteMessageConfirm:"Are you sure to delete this message?",deleteHistoryConfirm:"Are you sure to clear this history?",clearHistoryConfirm:"Are you sure to clear chat history?",preview:"Preview",showRawText:"Show as raw text",size:"Size:",generatedContentDisclaimer:"AI can make mistakes. Consider checking important information. All rights reserved ©",square1:"Square [1:1]",illustration:"Illustration [4:3]",wallpaper:"Wallpaper [16:9]",media:"Media [3:4]",poster:"Poster [9:16]",square:"Square",landscape:"Landscape",portrait:"Portrait",chatDialogue:"Chat Dialogue",startNewConversationPrompt:"Click the button below to start a new conversation",newConversation:"New Conversation",networkModeEnabledContextInvalid:"Network mode enabled, context invalidated!",networkModeDisabled:"Network mode disabled!",pointsMall:"Points Mall",toggleTheme:"Toggle Theme",signInReward:"Sign-In Reward",networkMode:"Network Mode",searchHistoryConversations:"Search History Conversations",announcement:"Announcement",clear:"Clear",remaining:"",ordinaryPoints:"Ordinary Points",advancedPoints:"Advanced Points",drawingPoints:"Drawing Points",points:"Points",clearConversation:"Clear Conversation",clearAllNonFavoriteConversations:"Clear all non-favorite conversations?",more:"More",collapse:"Collapse",myApps:"My Apps",appSquare:"App Square",favorites:"Favorites",todayConversations:"Today",Conversations:"",historyConversations:"History",favoriteConversations:"Favorite",unfavorite:"Unfavorite",rename:"Rename",deleteConversation:"Delete",me:"You",onlineSearch:"Online Search",mindMap:"Mind Map",fileAnalysis:"File Analysis",delete:"Delete",regenerate:"Regenerate",pause:"Pause",loading:"Loading...",readAloud:"Read Aloud",vipCenter:"VIP Center",U1:"🔍 U1",U2:"🔍 U2",U3:"🔍 U3",U4:"🔍 U4",V1:"🪄 V1",V2:"🪄 V2",V3:"🪄 V3",V4:"🪄 V4",panLeft:"⬅️ Pan Left",panRight:"➡️ Pan Right",panUp:"⬆️ Pan Up",panDown:"⬇️ Pan Down",zoomIn15x:"↔️ Zoom 1.5x",zoomIn2x:"↔️ Zoom 2x",minorTransform:"Vary(Subtle)",strongTransform:"Vary(Strong)",enlargeImage:"Enlarge image {order}",transformImage:"Transform image {order}",expandDrawing:"Expand drawing",advancedTransform:"Advanced transform",translateImage:"Translate image",enlargeImagePrefix:"Enlarge image ",enlargeImageSuffix:"",transformImagePrefix:"Transform image ",transformImageSuffix:"",imageToImage:"Image to Image",faceConsistency:"Face Consistency",styleConsistency:"Style Consistency",selectAppOrTopic:"Select an application or topic for quick conversation"},rh={sampleTemplate:"Sample Template",exploreInfinitePossibilities:"Explore infinite possibilities, create a smart future with AI",searchAppNameQuickFind:"Search app names, quick find applications...",allCategories:"All Categories",noModelConfigured:"No specific application model has been configured by the administrator, please contact them to set it up~"},nh={setting:"Setting",general:"General",advanced:"Advanced",config:"Config",avatarLink:"Avatar Link",name:"Name",description:"Description",role:"Role",resetUserInfo:"Reset UserInfo",chatHistory:"ChatHistory",theme:"Theme",language:"Language",api:"API",reverseProxy:"Reverse Proxy",timeout:"Timeout",socks:"Socks",httpsProxy:"HTTPS Proxy",balance:"API Balance",sign:"Signature"},ih={siderButton:"Prompt Store",local:"Local",online:"Online",title:"Title",description:"Description",clearStoreConfirm:"Whether to clear the data?",importPlaceholder:"Please paste the JSON data here",addRepeatTitleTips:"Title duplicate, please re-enter",addRepeatContentTips:"Content duplicate: {msg}, please re-enter",editRepeatTitleTips:"Title conflict, please revise",editRepeatContentTips:"Content conflict {msg} , please re-modify",importError:"Key value mismatch",importRepeatTitle:"Title repeatedly skipped: {msg}",importRepeatContent:"Content is repeatedly skipped: {msg}",onlineImportWarning:"Note: Please check the JSON file source!",downloadError:"Please check the network status and JSON file validity"},sh={use:"Use",download:"Download",delete:"Delete",zoom:"Zoom:",U1:"U1",U2:"U2",U3:"U3",U4:"U4",regenerateOnce:"Regenerate",transform:"Transform:",V1:"V1",V2:"V2",V3:"V3",V4:"V4",pan:"Pan:",panLeft:"⬅️",panRight:"➡️",panUp:"⬆️",panDown:"⬇️",transformZoom:"Zoom Transform",zoom1_5x:"Zoom 1.5x",zoom2x:"Zoom 2x",minorTransform:"Minor Transform",strongTransform:"Strong Transform",regionalRedraw:"Regional Redraw",regionalRedraw1:"Regional Redraw (Select the Area to Change)",submitTask:"Submit Task",selectSuiteForZoom:"Action: Select a suite to zoom",selectSuiteForTransform:"Action: Select a suite for transformation",regeneratingImage:"Action: Regenerating the image",drawingInProgress:"Action: Rapid drawing in progress...",tryDifferentPrompt:"Execute: Try a different prompt!",statusWaiting:"Waiting",statusDrawing:"Drawing",statusSuccess:"Success",statusFailure:"Failure",statusTimeout:"Timeout",downloadImageTitle:"Download Image",downloadImageContent:"Download the current image",downloadButtonText:"Download",cancelButtonText:"Cancel",deleteRecordTitle:"Delete Record",deleteRecordContent:"Delete the current drawing record?",deleteButtonText:"Delete",submitZoomDrawingSuccess:"Zoom drawing task submitted successfully, please wait for it to finish!",submitRedrawSuccess:"Redraw task submitted successfully, please wait for it to finish!",submitTransformDrawingSuccess:"Transform drawing task submitted successfully, please wait for it to finish!",submitEnlargeDrawingSuccess:"Enlarge drawing task submitted successfully, please wait for it to finish!",submitAdvancedTransformDrawingSuccess:"Advanced transform drawing task submitted successfully, please wait for it to finish!",submitRegionalRedrawSuccess:"Regional redraw task submitted successfully, please wait for it to finish!",drawingRecordDeleted:"Drawing record has been deleted!",queueing:"Queueing...",drawing:"Drawing...",storing:"Storing image...",drawingFailed:"Drawing Failed",pointsRefunded:"Points Refunded!",submitDrawingTaskSuccess:"Drawing task submitted successfully, please wait for it to finish!",defaultStyle:"Default Style",expressiveStyle:"Expressive Style",cuteStyle:"Cute Style",scenicStyle:"Scenic Style",standardQuality:"Standard",generalQuality:"General",highDefinitionQuality:"High Definition",ultraHighDefinitionQuality:"Ultra High Definition",enterDescription:"Please enter descriptive words!",optimizationFailed:"Optimization failed!",professionalDrawing:"Professional Drawing",parameterExplanation:"Parameter Explanation: Generate image size ratio",imageSize:"Image Size",modelSelection:"Model Selection",tooltipMJ:"MJ: General-purpose realistic model",tooltipNIJI:"NIJI: Anime style, suitable for 2D models",version:"Version",style:"Style",parameters:"Parameters",parametersTooltip:"Use parameters wisely to achieve more ideal results!",quality:"Quality",chaos:"Chaos",chaosDescription:"Value range: 0-100, --chaos or --c",chaosExplanation:"Chaos level, can be understood as the space for AI to think outside the box",chaosAdvice:"The smaller the value, the more reliable, with the default of 0 being the most precise",stylization:"Stylization",stylizationDescription:"Stylization: --stylize or --s, range 1-1000",parameterExplanation1:"Parameter explanation: The higher the number, the richer and more artistic the visual presentation",setting:"Setting",carryParameters:"Carry Parameters",autoCarryParameters:"Whether to automatically carry parameters",carryOn:"On: Carries the parameters we have configured",carryOff:"Off: Uses the parameters we customize in the command",imageToImage:"Image to Image",clickOrDrag:"Click or drag an image here to use as input",supportFormats:"Supports PNG and JPG formats",remainingPoints:"Remaining Points",refresh:"Refresh",accountInfo:"Account Information",points:"Points",paintingSingleUse:"Painting:",imageGenerationSingleUse:"Generation:",enlargementSingleUse:"Enlargement:",submitDrawingTask:"Enter keywords, submit drawing task",optimize:"Optimize",enterDrawingKeywords:"Enter drawing keywords. For example: A colorful cat, cute, cartoon",unnecessaryElements:"Unnecessary Elements",exclusionPrompt:"Example: Generate a room image, but exclude the bed, you can fill in 'bed'!",workingContents:"Working Contents",currentTasks:"Current tasks in progress",goToAIDrawingSquare:"Click to go to the AI Drawing Square",tasksInProgress:"tasks are currently in progress. Please wait patiently for the drawing to complete. You can visit other pages and return later to see the results!",myDrawings:"My Drawings",aiDrawingSquare:"AI Drawing Square",sizeAdjustment:"Size Adjustment",keywordSearchPlaceholder:"Prompt Keyword Search"},oh={membershipMarket:"Membership Market",sizeAdjustment:"Size Adjustment",memberPackage:"Limited Time Member Package",permanentAddOnCard:"Permanent Add-On Card",baseModelQuota:"Base Model Quota",advancedModelQuota:"Advanced Model Quota",MJDrawingQuota:"MJ Drawing Quota",packageValidity:"Package Validity",days:"days",permanent:"Permanent",points:"Points",welcomeTipMobile:"Explore freely, welcome to our online store!",welcomeTipDesktop:"Explore freely, welcome to our online store, thank you for choosing us, let's start a delightful shopping journey together!",paymentNotEnabled:"Payment has not been enabled by the admin!",purchaseSuccess:"Purchase successful, enjoy your product!",paymentNotComplete:"You have not completed the payment yet!",wechat:"WeChat",alipay:"Alipay",wechatPay:"WeChat Pay",alipayPay:"Alipay Pay",paymentSuccess:"Congratulations, your payment was successful. Enjoy your purchase!",paymentTimeout:"Payment timeout, please place your order again!",productPayment:"Product Payment",amountDue:"Amount Due:",packageName:"Package Name:",packageDescription:"Package Description:",siteAdminEnabledRedirect:"The site administrator has enabled redirect payment",clickToPay:"Click to Proceed to Payment",completePaymentWithin:"Please complete the payment within",timeToCompletePayment:"!",open:"Open",scanToPay:"Scan to Pay"},lh={title:"Mind Map",yourNeeds:"Your Needs?",inputPlaceholder:"Please enter a brief description of the content you want to generate, AI will produce a complete markdown content and its mind map for you!",generateMindMapButton:"Generate Mind Map",contentRequirements:"Content Requirements",tryDemoButton:"Try a Demo",usageCredits:"Base credits per use: 1",exportHTML:"Export HTML",exportPNG:"Export PNG",exportSVG:"Export SVG"},Oh={defaultSignature:"I am an AI robot based on deep learning and natural language processing technologies, aimed at providing users with efficient, accurate, and personalized intelligent services.",syncComplete:"Data synchronization completed",personalCenter:"Personal Center",logOut:"Log Out",myUsageRecord:"My Usage Record on This Site",basicModelCredits:"Basic Model Credits:",advancedModelCredits:"Advanced Model Credits:",basicModelUsage:"Basic Model Usage:",advancedModelUsage:"Advanced Model Usage:",drawingUsageCredits:"Drawing Usage Credits:",bindWeChat:"Bind WeChat:",clickToBindWeChat:"Click to Bind WeChat",weChatBound:"WeChat Bound",syncVisitorData:"Click to Sync Visitor Data",points:"Points",membershipExpiration:"Membership Expiration Date:",editInfoDescription:"Edit personal information, view more details",myDetails:"My Details",myWallet:"My Wallet",basicInfo:"Basic Information",userBasicSettings:"User Basic Settings",avatarPlaceholder:"Please enter your avatar URL",usernamePlaceholder:"Edit your username",signaturePlaceholder:"Edit your signature",passwordManagement:"Password Management",inviteBenefits:"Invite for Benefits",clickToLogin:"Log In",notLoggedIn:"Not Logged In",avatar:"Avatar",username:"Username",email:"Email",inviteeStatus:"Invitee Status",inviteTime:"Invite Time",rewardStatus:"Reward Status",certified:"Certified",notActivated:"Not Activated",rewardReceived:"Reward Received",waitingConfirmation:"Waiting for Confirmation",linkGeneratedSuccess:"Invitation link generated successfully",generateLinkFirst:"Please generate your exclusive invitation link first!",linkCopiedSuccess:"Exclusive invitation link copied successfully!",copyNotSupported:"Automatic copying is not supported on this device, please copy manually!",inviteForBenefits:"Invite Users, Earn Benefits!",myInviteCode:"My Invitation Code",generateInviteCode:"Generate Exclusive Invite Code",copyInviteLink:"Copy Exclusive Invite Link",inviteOneUser:"Inviting a user grants",basicModelCredits1:"basic model credits+",advancedModelCredits1:"advanced model credits+",mjDrawingCredits:"MJ drawing credits",receiveInvitation:"Invited users receive",creditsEnd:"credits",invitationRecord:"Invitation Record",passwordMinLength:"The minimum password length is 6 characters",passwordMaxLength:"The maximum password length is 30 characters",enterPassword:"Please enter a password",reenterPassword:"Please re-enter your password",passwordsNotMatch:"The passwords do not match",passwordUpdateSuccess:"Password updated successfully, please log in again!",changeYourPassword:"Change Your Password",oldPassword:"Old Password",newPassword:"New Password",confirmPassword:"Confirm Password",reloginAfterPasswordChange:"You will need to log in again after updating your password!",updateYourPassword:"Update Your Password",passwordRequirements:"Password Requirements",newPasswordInstructions:"To create a new password, you must meet all the following requirements:",minimumCharacters:"At least 6 characters",maximumCharacters:"No more than 30 characters",requireNumber:"Must contain at least one number",orderNumber:"Order Number",rechargeType:"Recharge Type",basicModelQuota:"Basic Model Quota",advancedModelQuota:"Advanced Model Quota",mjDrawingQuota:"MJ Drawing Quota",validity:"Validity",rechargeTime:"Recharge Time",enterCardSecret:"Please enter the card secret first!",cardRedeemSuccess:"Card redeemed successfully, enjoy your use!",userWalletBalance:"User Wallet Balance",basicModelBalance:"Basic Model Balance",creditUsageNote:"Each conversation consumes different credits depending on the model!",advancedModelBalance:"Advanced Model Balance",modelConsumptionNote:"Each conversation consumes different credits depending on the model!",mjDrawingBalance:"MJ Drawing Balance",drawingConsumptionNote:"Different credits are consumed based on drawing actions!",cardRecharge:"Card Recharge",enterCardDetails:"Please paste or enter your card details!",pleaseEnterCardDetails:"Please enter card details",exchange:"Exchange",buyCardSecret:"Buy Card Secret",rechargeRecords:"Recharge Records",packagePurchase:"Package Purchase",buyPackage:"Buy Package"},ch={signInReward:"Sign-in Reward",themeSwitch:"Theme Switch",personalCenter:"Personal Center",loginAccount:"Log In Account"},uh={doNotRemind24h:"Do not remind again for 24 hours"},fh={enterUsername:"Please enter your username",usernameLength:"Username must be between 2 and 30 characters",enterPassword:"Please enter your password",passwordLength:"Password must be between 6 and 30 characters",enterEmail:"Please enter your email address",emailValid:"Please enter a valid email address",enterCaptcha:"Please enter the captcha",emailPhone:"Email / Phone",email:"Email",phone:"Phone",registrationSuccess:"Account registration successful, start your experience!",loginSuccess:"Account login successful, start your experience!",registerTitle:"Register",enterContact:"Please provide your ",enterCode:"Please enter the verification code",sendVerificationCode:"Send Verification Code",optionalInvitationCode:"Invitation Code [Optional]",registerAccount:"Register Account",alreadyHaveAccount:"Already have an account?",goToLogin:"Go to Login",password:"Password",enterYourPassword:"Please enter your password",rememberAccount:"Remember account",forgotPassword:"Forgot password?",loginAccount:"Log In Account",noAccount:"Don't have an account?",register:"Register",orUse:"or use",scanLogin:"Scan to Log In",wechatLogin:"WeChat Login",wechatScanFailed:"Failed WeChat QR code login? Use",useWechatScan:"Use WeChat to Scan and Log In"},dh={orderAmount:"Order Amount",productType:"Product Type",status:"Status",commissionRate:"Commission Rate",commission:"Commission",orderTime:"Order Time",purchasePackage:"Purchase Package",accounted:"Accounted",generateInviteCodeSuccess:"Invitation code generated successfully",withdrawalTime:"Withdrawal Time",withdrawalAmount:"Withdrawal Amount",withdrawalChannel:"Withdrawal Channel",withdrawalStatus:"Withdrawal Status",withdrawalRemarks:"Withdrawal Remarks",auditor:"Auditor",alipay:"Alipay",wechat:"WeChat",paid:"Paid",rejected:"Rejected",inReview:"In Review",avatar:"Avatar",username:"Username",email:"Email",inviteeStatus:"Invitee Status",registered:"Registered",pendingActivation:"Pending Activation",registrationTime:"Registration Time",lastLogin:"Last Login",requestInviteCodeFirst:"Please request your invitation code first",linkCopiedSuccess:"Share link copied successfully",title:"Referral Program",description:"Join us and share in success! Welcome to our distribution page, become our partner and create a bright future together!",defaultSalesOutletName:"Rookie Referral Officer",myReferrals:"My Referrals",currencyUnit:"Yuan",remainingAmount:"Remaining Withdrawable Amount",withdrawingAmount:"Amount in Withdrawal",withdrawNow:"Withdraw Now",minimumWithdrawalPrefix:"Minimum",minimumWithdrawalSuffix:"Yuan Withdrawable",purchaseOrderCount:"Purchase Order Count",promotionLinkVisits:"Promotion Link Visits",registeredUsers:"Registered Users",referralEarnings:"Referral Earnings",referralEarningsDescription:"Commission amount returned after referred users register and buy products",percentage:"Percentage",applyForAdvancedAgent:"Apply to Become an Advanced Agent",contactAdminForAdvancedAgent:"Contact the site owner to apply for an advanced agent to enjoy high commissions",joinAsPartner:"Join Us as a Partner",partnerDescription:"Join us as a partner to co-operate the community, win-win cooperation!",winTogether:"Win Together, Advance Together",referralLink:"Referral Link:",apply:"Apply",referralRecordsTab:"Referral Records",withdrawalRecordsTab:"Withdrawal Records",registeredUsersTab:"Registered Users",inviteFriends:"Invite friends, gift meal cards, and enjoy recharge commissions!",inviteLink:"Invite Link",copy:"Copy",inviteBenefits1:"Both parties enjoy a certain amount of permanent card rewards when inviting friends.",inviteBenefits2Prefix:"Earn a ",inviteBenefits2Suffix:"% commission on your friend's recharge amount.",enterWithdrawalAmount:"Please enter your withdrawal amount!",selectWithdrawalChannel:"Please select your withdrawal channel!",enterContactInfo:"Please provide your contact information and remark!",optionalRemark:"If there are any special circumstances, please remark!",withdrawalSuccess:"Withdrawal application successful, please wait for approval!",withdrawalApplicationForm:"Withdrawal Application Form",contactInformation:"Contact Information",withdrawalRemark:"Withdrawal Remark",enterWithdrawalRemark:"Please enter your withdrawal remarks",applyWithdrawal:"Apply for Withdrawal"},hh={purchaseSuccess:"Purchase successful, enjoy your item!",paymentNotSuccessful:"You have not completed the payment yet!",orderConfirmationTitle:"Order Confirmation",orderConfirmationContent:"Welcome to purchase, are you sure you want to buy ",thinkAgain:"Let me think again",confirmPurchase:"Confirm Purchase",paymentNotEnabled:"Payment has not been enabled by the administrator!",selectProducts:"Select Products",basicModelQuota:"Basic Model Quota",advancedModelQuota:"Advanced Model Quota",drawingQuota:"Drawing Quota",buyPackage:"Buy Package"},ph={1:"Registration Bonus",2:"Invitation Bonus",3:"Referring Others Bonus",4:"Purchase via Card Code",5:"Admin Bonus",6:"QR Code Purchase",7:"MJ Drawing Failure Refund",8:"Sign-in Reward"},xh={0:"Not Paid",1:"Paid",2:"Payment Failed",3:"Payment Timeout"},mh={logoutSuccess:"Successfully logged out!"},gh={language:eh,common:th,chat:ah,app:rh,setting:nh,store:ih,draw:sh,pay:oh,mindmap:lh,usercenter:Oh,siderBar:ch,notice:uh,login:fh,share:dh,goods:hh,rechargeTypes:ph,orderStatus:xh,messages:mh},vh="中文",Qh={add:"添加",addSuccess:"添加成功",edit:"编辑",editSuccess:"编辑成功",delete:"删除",deleteSuccess:"删除成功",update:"修改",saveSuccess:"保存成功",updateUserSuccess:"修改用户信息成功",reset:"重置",action:"操作",export:"导出",exportSuccess:"导出成功",import:"导入",importSuccess:"导入成功",clear:"清空",clearSuccess:"清空成功",yes:"是",no:"否",download:"下载",noData:"暂无数据",wrong:"好像出错了，请稍后再试。",success:"操作成功",failed:"操作失败",verify:"验证",unauthorizedTips:"未经授权，请先进行验证。",confirm:"确认",cancel:"取消"},Sh={newChatButton:"新建聊天",placeholder:"来说点什么吧...（Shift + Enter = 换行）",placeholderMobile:"来说点什么...",copy:"复制",copied:"复制成功",copyCode:"复制",clearChat:"清空会话",clearChatConfirm:"是否清空会话?",exportImage:"保存会话到图片",exportImageConfirm:"是否将会话保存为图片?",exportSuccess:"保存成功",exportFailed:"保存失败",deleteMessage:"删除消息",deleteMessageConfirm:"删除此条对话?",deleteHistoryConfirm:"确定删除此记录?",deleteSuccess:"删除成功",clearHistoryConfirm:"确定清空聊天记录?",preview:"预览",showRawText:"显示原文",size:"尺寸：",generatedContentDisclaimer:"AI 生成内容仅供参考，不代表本平台立场。版权所有 ©",square1:"方形（1:1）",illustration:"配图（4:3）",wallpaper:"壁纸（16:9）",media:"媒体（3:4）",poster:"海报（9:16）",square:"方形",landscape:"宽屏",portrait:"垂直",chatDialogue:"对话聊天",startNewConversationPrompt:"点击下方按钮，开始一个新的对话吧",newConversation:"新对话",networkModeEnabledContextInvalid:"已开启联网模式、上下文状态失效！",networkModeDisabled:"已关闭联网模式！",pointsMall:"积分商城",toggleTheme:"切换主题",signInReward:"签到奖励",networkMode:"联网模式",searchHistoryConversations:"搜索历史对话",announcement:"网站公告",clear:"清空对话",remaining:"剩余：",ordinaryPoints:"普通积分",advancedPoints:"高级积分",drawingPoints:"绘画积分",points:"积分",clearConversation:"清空对话",clearAllNonFavoriteConversations:"清空所有非收藏的对话？",more:"更多",collapse:"折叠",myApps:"我的应用",appSquare:"应用广场",favorites:"收藏",todayConversations:"今日对话",historyConversations:"历史对话",favoriteConversations:"收藏对话",unfavorite:"取消收藏",rename:"重命名",deleteConversation:"删除对话",me:"我",onlineSearch:"联网搜索",mindMap:"思维导图",fileAnalysis:"文件",delete:"删除",regenerate:"重新生成",pause:"暂停",loading:"加载中...",readAloud:"朗读",vipCenter:"会员中心",U1:"🔍 放大左上",U2:"🔍 放大右上",U3:"🔍 放大左下",U4:"🔍 放大右下",V1:"🪄 变换左上",V2:"🪄 变换右上",V3:"🪄 变换左下",V4:"🪄 变换右下",panLeft:"⬅️ 向左平移",panRight:"➡️ 向右平移",panUp:"⬆️ 向上平移",panDown:"⬇️ 向下平移",zoomIn15x:"↔️ 扩图1.5倍",zoomIn2x:"↔️ 扩图2倍",minorTransform:"🖌️ 微变换",strongTransform:"🖌️ 强变换",enlargeImagePrefix:"放大第",enlargeImageSuffix:"张图片",transformImagePrefix:"变换第",transformImageSuffix:"张图片",expandDrawing:"扩图绘制",advancedTransform:"高级变换",translateImage:"平移图片",imageToImage:"以图生图",faceConsistency:"人脸一致",styleConsistency:"风格一致",selectAppOrTopic:"选择应用或话题快速对话"},bh={sampleTemplate:"示例模板",exploreInfinitePossibilities:"探索无限可能，与 AI 一同开创智慧未来",searchAppNameQuickFind:"搜索应用名称、快速查找应用...",allCategories:"全部分类",noModelConfigured:"管理员未配置特定应用模型、请联系管理员配置~"},Ph={setting:"设置",general:"总览",advanced:"高级",personalInfo:"个人信息",avatarLink:"头像链接",name:"用户名称",sign:"用户签名",role:"角色设定",resetUserInfo:"重置用户信息",chatHistory:"聊天记录",theme:"主题",language:"语言",api:"API",reverseProxy:"反向代理",timeout:"超时",socks:"Socks",httpsProxy:"HTTPS Proxy",balance:"API余额"},yh={siderButton:"提示词商店",local:"本地",online:"在线",title:"标题",description:"描述",clearStoreConfirm:"是否清空数据？",importPlaceholder:"请粘贴 JSON 数据到此处",addRepeatTitleTips:"标题重复，请重新输入",addRepeatContentTips:"内容重复：{msg}，请重新输入",editRepeatTitleTips:"标题冲突，请重新修改",editRepeatContentTips:"内容冲突{msg} ，请重新修改",importError:"键值不匹配",importRepeatTitle:"标题重复跳过：{msg}",importRepeatContent:"内容重复跳过：{msg}",onlineImportWarning:"注意：请检查 JSON 文件来源！",downloadError:"请检查网络状态与 JSON 文件有效性"},$h={use:"使用",download:"下载",delete:"删除",zoom:"放大：",U1:"左上",U2:"右上",U3:"左下",U4:"右下",regenerateOnce:"重新生成一次",transform:"变换：",V1:"左上",V2:"右上",V3:"左下",V4:"右下",pan:"平移：",panLeft:"向左",panRight:"向右",panUp:"向上",panDown:"向下",transformZoom:"扩图变换：",zoom1_5x:"扩图1.5倍",zoom2x:"扩图2倍",minorTransform:"微变换",strongTransform:"强变换",regionalRedraw:"区域重绘",regionalRedraw1:"区域重绘（框选要改变的区域）",submitTask:"提交任务",selectSuiteForZoom:"操作：选中套图进行放大",selectSuiteForTransform:"操作：选中套图进行变换",regeneratingImage:"操作：正在对图片重新生成一次",drawingInProgress:"操作：正在火速绘制中...",tryDifferentPrompt:"执行：换个提示词重新试试吧！",statusWaiting:"等待中",statusDrawing:"绘制中",statusSuccess:"成功",statusFailure:"失败",statusTimeout:"超时",downloadImageTitle:"下载图片",downloadImageContent:"下载当前图片",downloadButtonText:"下载",cancelButtonText:"取消",deleteRecordTitle:"删除记录",deleteRecordContent:"删除当前绘制记录？",deleteButtonText:"删除",submitZoomDrawingSuccess:"提交放大绘制任务成功、请等待绘制结束！",submitRedrawSuccess:"提交重新绘制任务成功、请等待绘制结束！",submitTransformDrawingSuccess:"提交变换绘制任务成功、请等待绘制结束！",submitEnlargeDrawingSuccess:"提交扩图任务成功、请等待绘制结束！",submitAdvancedTransformDrawingSuccess:"提交高级变换绘制任务成功、请等待绘制结束！",submitRegionalRedrawSuccess:"提交区域重绘任务成功、请等待绘制结束！",drawingRecordDeleted:"绘制记录已删除！",queueing:"排队中...",drawing:"正在绘制...",storing:"图片存储中...",drawingFailed:"绘制失败",pointsRefunded:"积分已退还！",submitDrawingTaskSuccess:"提交绘制任务成功、请等待绘制结束！",defaultStyle:"默认风格",expressiveStyle:"表现力风格",cuteStyle:"可爱风格",scenicStyle:"景观风格",standardQuality:"普通",generalQuality:"一般",highDefinitionQuality:"高清",ultraHighDefinitionQuality:"超高清",enterDescription:"请输入描述词！",optimizationFailed:"优化失败了！",professionalDrawing:"专业绘图",parameterExplanation:"参数释义：生成图片尺寸比例",imageSize:"图片尺寸",modelSelection:"模型选择",tooltipMJ:"MJ: 偏真实通用模型",tooltipNIJI:"NIJI: 偏动漫风格、适用于二次元模型",version:"版本",style:"风格",parameters:"参数",parametersTooltip:"合理使用参数绘制更为理想的结果！",quality:"品质",chaos:"混乱",chaosDescription:"取值范围：0-100、 --chaos 或 --c",chaosExplanation:"混乱级别，可以理解为让AI天马行空的空间",chaosAdvice:"值越小越可靠、默认0最为精准",stylization:"风格化",stylizationDescription:"风格化：--stylize 或 --s，范围 1-1000",parameterExplanation1:"参数释义：数值越高，画面表现也会更具丰富性和艺术性",setting:"设定",carryParameters:"携带参数",autoCarryParameters:"是否自动携带参数",carryOn:"打开：携带上述我们配置的参数",carryOff:"关闭：使用指令中的我们自定义的参数",imageToImage:"以图生图",clickOrDrag:"点击或拖拽一个图片到这里作为输入",supportFormats:"支持PNG和JPG格式",remainingPoints:"剩余积分",refresh:"刷新",accountInfo:"账户信息",points:"积分",paintingSingleUse:"绘画单次消耗：",imageGenerationSingleUse:"图生图单次消耗：",enlargementSingleUse:"放大单次消耗：",submitDrawingTask:"输入关键词，提交绘制任务",optimize:"优化",enterDrawingKeywords:"输入绘图关键词。例如：一只五颜六色的猫，可爱，卡通",unnecessaryElements:"不需要的元素",exclusionPrompt:"例：生成房间图片、但是不要床、你可以填bed！",workingContents:"工作中的内容",currentTasks:"当前系统进行中任务",goToAIDrawingSquare:"点击前往 AI 绘画广场",tasksInProgress:"个任务正在进行中、请耐心等候绘制完成、您可以前往其他页面稍后回来查看结果！",myDrawings:"我的绘图",aiDrawingSquare:"AI绘画广场",sizeAdjustment:"尺寸调整",keywordSearchPlaceholder:"prompt关键词搜索"},wh={membershipMarket:"会员商场",sizeAdjustment:"尺寸调整",memberPackage:"会员限时套餐",permanentAddOnCard:"叠加永久次卡",baseModelQuota:"普通积分",advancedModelQuota:"高级积分",MJDrawingQuota:"绘画积分",packageValidity:"套餐有效期",days:"天",permanent:"永久",points:"积分",welcomeTipMobile:"尽情探索，欢迎光临我们的在线商店！",welcomeTipDesktop:"尽情探索，欢迎光临我们的在线商店、感谢您选择我们、让我们一同开启愉悦的购物之旅！",paymentNotEnabled:"管理员还未开启支付！",purchaseSuccess:"购买成功、祝您使用愉快!",paymentNotComplete:"您还没有支付成功哟！",wechat:"微信",alipay:"支付宝",wechatPay:"微信支付",alipayPay:"支付宝支付",paymentSuccess:"恭喜你支付成功、祝您使用愉快！",paymentTimeout:"支付超时，请重新下单!",productPayment:"商品支付",amountDue:"需要支付：",packageName:"套餐名称：",packageDescription:"套餐描述：",siteAdminEnabledRedirect:"当前站长开通了跳转支付",clickToPay:"点击前往支付",completePaymentWithin:"请在",timeToCompletePayment:"时间内完成支付！",open:"打开",scanToPay:"扫码支付"},_h={title:"思维导图",yourNeeds:"您的需求？",inputPlaceholder:"请输入您想要生成内容的简单描述、AI将为您输出一份完整的markdown内容及其思维导图!",generateMindMapButton:"智能生成生成思维导图",contentRequirements:"内容需求",tryDemoButton:"试试示例",usageCredits:"每次使用消耗基础积分： 1",exportHTML:"导出HTML",exportPNG:"导出PNG",exportSVG:"导出SVG"},kh={defaultSignature:"我是一台基于深度学习和自然语言处理技术的 AI 机器人，旨在为用户提供高效、精准、个性化的智能服务。",syncComplete:"已同步数据完成",personalCenter:"个人中心",logOut:"退出登录",myUsageRecord:"我在本站的使用记录",basicModelCredits:"基础模型积分:",advancedModelCredits:"高级模型积分:",basicModelUsage:"基础模型使用:",advancedModelUsage:"高级模型使用:",drawingUsageCredits:"绘画使用积分:",bindWeChat:"绑定微信:",clickToBindWeChat:"点击绑定微信",weChatBound:"已绑定微信",syncVisitorData:"点击同步访客数据",points:"积分",membershipExpiration:"会员过期时间：",editInfoDescription:"编辑个人信息、查看更多详情",myDetails:"我的详情",myWallet:"我的钱包",basicInfo:"基础信息",userBasicSettings:"用户基础设置",avatarPlaceholder:"请填写头像地址",usernamePlaceholder:"请编辑您的用户名",signaturePlaceholder:"请编辑您的签名",passwordManagement:"密码管理",inviteBenefits:"邀请得福利",clickToLogin:"点击登入",notLoggedIn:"未登录",avatar:"头像",username:"用户名称",email:"用户邮箱",inviteeStatus:"受邀人状态",inviteTime:"邀请时间",rewardStatus:"获得奖励状态",certified:"已认证",notActivated:"未激活",rewardReceived:"已领取邀请奖励",waitingConfirmation:"等待受邀人确认",linkGeneratedSuccess:"生成邀请链接成功",generateLinkFirst:"请先生成您的专属邀请链接！",linkCopiedSuccess:"复制专属邀请链接成功！",copyNotSupported:"当前设置不支持自动复制、手动复制吧！",inviteForBenefits:"邀用户、得福利!",myInviteCode:"我的邀请码",generateInviteCode:"生成专属邀请码",copyInviteLink:"复制专属邀请链接",inviteOneUser:"邀请一位用户赠送",basicModelCredits1:"积分基础模型额度+",advancedModelCredits1:"积分高级模型额度+",mjDrawingCredits:"MJ绘画积分额度",receiveInvitation:"收到邀请用户获得",creditsEnd:"积分",invitationRecord:"邀请记录",passwordMinLength:"密码最短长度为6位数",passwordMaxLength:"密码最长长度为30位数",enterPassword:"请输入密码",reenterPassword:"请再次输入密码",passwordsNotMatch:"两次密码输入不一致",passwordUpdateSuccess:"密码更新成功、请重新登录系统！",changeYourPassword:"变更您的密码",oldPassword:"旧密码",newPassword:"新密码",confirmPassword:"确认密码",reloginAfterPasswordChange:"更新密码完成后将重新登录！",updateYourPassword:"更新您的密码",passwordRequirements:"密码要求",newPasswordInstructions:"要创建一个新的密码，你必须满足以下所有要求。",minimumCharacters:"最少6个字符",maximumCharacters:"最多30个字符",requireNumber:"至少带有一个数字",orderNumber:"订单编号",rechargeType:"充值类型",basicModelQuota:"普通积分",advancedModelQuota:"高级积分",mjDrawingQuota:"绘画积分",validity:"有效期",rechargeTime:"充值时间",enterCardSecret:"请先填写卡密！",cardRedeemSuccess:"卡密兑换成功、祝您使用愉快！",userWalletBalance:"用户钱包余额",basicModelBalance:"基础模型余额",creditUsageNote:"每次对话根据模型消费不同积分！",advancedModelBalance:"高级模型余额",modelConsumptionNote:"每次对话根据模型消费不同积分！",mjDrawingBalance:"MJ绘画余额",drawingConsumptionNote:"根据画图动作消耗不同的积分！",cardRecharge:"卡密充值",enterCardDetails:"请粘贴或填写您的卡密信息！",pleaseEnterCardDetails:"请输入卡密信息",exchange:"兑换",buyCardSecret:"购买卡密",rechargeRecords:"充值记录",packagePurchase:"套餐购买",buyPackage:"购买套餐"},Ch={signInReward:"签到奖励",themeSwitch:"主题切换",personalCenter:"个人中心",loginAccount:"登录账户"},Eh={doNotRemind24h:"我已知晓"},Ah={enterUsername:"请输入用户名",usernameLength:"用户名长度应为 2 到 30 个字符",enterPassword:"请输入密码",passwordLength:"密码长度应为 6 到 30 个字符",enterEmail:"请输入邮箱地址",enterPhone:"请输入手机号码",enterEmailOrPhone:"请输入邮箱地址或手机号码",emailValid:"请输入正确的邮箱地址",enterCaptcha:"请填写图形验证码",emailPhone:"邮箱 / 手机号",email:"邮箱",phone:"手机号",registrationSuccess:"账户注册成功、开始体验吧！",loginSuccess:"账户登录成功、开始体验吧！",registerTitle:"注册",enterContact:"请填写您的",enterCode:"请填写验证码",sendVerificationCode:"发送验证码",optionalInvitationCode:"邀请码[非必填]",registerAccount:"注册账户",alreadyHaveAccount:"已经有帐号？",goToLogin:"去登录",password:"密码",enterYourPassword:"请输入您的账户密码",rememberAccount:"记住帐号",forgotPassword:"忘记密码?",loginAccount:"登录账户",noAccount:"还没有帐号？",register:"去注册",orUse:"或使用",scanLogin:"扫码登录",wechatLogin:"微信登录",wechatScanFailed:"不使用微信扫码登录？试试",useWechatScan:"使用微信扫码登录"},Th={orderAmount:"订单金额",productType:"商品类型",status:"状态",commissionRate:"佣金比例",commission:"佣金",orderTime:"订购时间",purchasePackage:"购买套餐",accounted:"已入账",generateInviteCodeSuccess:"生成邀请码成功",withdrawalTime:"提现时间",withdrawalAmount:"提现金额",withdrawalChannel:"提现渠道",withdrawalStatus:"提现状态",withdrawalRemarks:"提现备注",auditor:"审核人",alipay:"支付宝",wechat:"微信",paid:"已打款",rejected:"被拒绝",inReview:"审核中",avatar:"头像",username:"用户名",email:"邮箱",inviteeStatus:"受邀人状态",registered:"已注册",pendingActivation:"待激活",registrationTime:"注册时间",lastLogin:"最后登录",requestInviteCodeFirst:"请先申请你的邀请码",linkCopiedSuccess:"复制推荐链接成功",title:"推介计划",description:"加入我们，共享成功！欢迎来到我们的分销页面，成为我们的合作伙伴，一同开创美好未来！",defaultSalesOutletName:"新秀推荐官",myReferrals:"我的推介",currencyUnit:"元",remainingAmount:"剩余可提金额",withdrawingAmount:"提现中金额",withdrawNow:"立即提现",minimumWithdrawalPrefix:"最低",minimumWithdrawalSuffix:"元可提现",purchaseOrderCount:"购买订单数量",promotionLinkVisits:"推广链接访问次数",registeredUsers:"注册用户",referralEarnings:"推介收益",referralEarningsDescription:"推介的用户注册购买产品后返佣金额",percentage:"百分比",applyForAdvancedAgent:"申请成为高级代理",contactAdminForAdvancedAgent:"联系站长申请高级代理可享超高返佣",joinAsPartner:"加入我们成为合伙人",partnerDescription:"加入我们成为合伙人共同运营社区、合作双赢！",winTogether:"合作共赢，携手共进",referralLink:"推荐链接：",apply:"申请",referralRecordsTab:"推介记录",withdrawalRecordsTab:"提现记录",registeredUsersTab:"注册用户",inviteFriends:"邀好友、赠套餐卡密、享充值返佣！",inviteLink:"邀请链接",copy:"复制",inviteBenefits1:"邀请好友双方都可享受一定额度的永久次卡奖励",inviteBenefits2Prefix:"邀请好友充值，您可获得充值金额的",inviteBenefits2Suffix:"%返佣",enterWithdrawalAmount:"请填写你的提款金额！",selectWithdrawalChannel:"请选择你的提款渠道！",enterContactInfo:"请填写您的联系方式并备注！",optionalRemark:"如有特殊情况、请备注说明！",withdrawalSuccess:"申请提现成功、请耐心等待审核！",withdrawalApplicationForm:"提款申请表",contactInformation:"联系方式",withdrawalRemark:"提款备注",enterWithdrawalRemark:"请填写你的提款备注！",applyWithdrawal:"申 请 提 现"},Zh={purchaseSuccess:"购买成功、祝您使用愉快!",paymentNotSuccessful:"您还没有支付成功哟！",orderConfirmationTitle:"订单确认",orderConfirmationContent:"欢迎选购、确定购买",thinkAgain:"我再想想",confirmPurchase:"确认购买",paymentNotEnabled:"管理员还未开启支付！",selectProducts:"选购套餐",basicModelQuota:"基础积分",advancedModelQuota:"高级积分",drawingQuota:"绘画积分",buyPackage:"购买套餐"},Xh={1:"注册赠送",2:"受邀请赠送",3:"邀请他人赠送",4:"购买卡密充值",5:"管理员赠送",6:"扫码购买充值",7:"MJ绘画失败退款",8:"签到奖励"},Rh={0:"未支付",1:"已支付",2:"支付失败",3:"支付超时"},Dh={logoutSuccess:"登出账户成功！"},Bh={language:vh,common:Qh,chat:Sh,app:bh,setting:Ph,store:yh,draw:$h,pay:wh,mindmap:_h,usercenter:kh,siderBar:Ch,notice:Eh,login:Ah,share:Th,goods:Zh,rechargeTypes:Xh,orderStatus:Rh,messages:Dh},qh="zh-CN",Mo=Vd({legacy:!1,globalInjection:!0,locale:qh,fallbackLocale:"en-US",allowComposition:!0,messages:{"en-US":gh,"zh-CN":Bh}});function bp(e){return Mo.global.t(e)}function Lh(e){e.use(Mo)}const Fh={class:"absolute top-4 left-1/2 transform -translate-x-1/2 z-10 flex items-center space-x-2 bg-black bg-opacity-50 rounded-lg px-4 py-2"},Yh=["disabled"],Ih={class:"text-white text-sm min-w-[60px] text-center"},Vh=["disabled"],Uh=["src"],Mh={key:0,class:"absolute inset-0 flex items-center justify-center"},Wh={key:1,class:"absolute inset-0 flex items-center justify-center"},dr=.1,hr=5,zh=it({__name:"index",props:{visible:{type:Boolean},imageUrl:{},fileName:{default:"image"}},emits:["update:visible","close"],setup(e,{emit:t}){const a=e,r=t,n=Se(),i=Se(),s=Se(!0),o=Se(!1),c=Se(1),O=Se(0),d=Se(0),g=Se(0),l=Se(!1),u=Se({x:0,y:0}),f=Se({x:0,y:0}),h=Se({width:0,height:0}),v=Ce(()=>({transform:`translate(${d.value}px, ${g.value}px) scale(${c.value}) rotate(${O.value}deg)`,transformOrigin:"center center"}));function Q(){s.value=!1,o.value=!1,n.value&&(h.value={width:n.value.naturalWidth,height:n.value.naturalHeight},m())}function x(){s.value=!1,o.value=!0}function m(){if(!n.value||!i.value)return;const Y=i.value.getBoundingClientRect(),F=h.value.width,j=h.value.height,T=Y.width*.9/F,X=Y.height*.9/j,q=Math.min(T,X,1);c.value=q}function p(){const Y=Math.min(c.value*1.2,hr);c.value=Y}function S(){const Y=Math.max(c.value/1.2,dr);c.value=Y}function P(){O.value=(O.value+90)%360}function b(){O.value=(O.value-90+360)%360}function k(){c.value=1,O.value=0,d.value=0,g.value=0,m()}function D(){return fe(this,null,function*(){if(a.imageUrl)try{try{const Y=yield fetch(a.imageUrl,{mode:"cors",credentials:"omit"});if(!Y.ok)throw new Error(`HTTP error! status: ${Y.status}`);const F=yield Y.blob(),j=window.URL.createObjectURL(F),T=document.createElement("a");T.href=j,T.download=`${a.fileName}.${w(a.imageUrl)}`,document.body.appendChild(T),T.click(),document.body.removeChild(T),window.URL.revokeObjectURL(j);return}catch(Y){const F=new Image;F.crossOrigin="anonymous",yield new Promise((j,T)=>{F.onload=()=>{try{const X=document.createElement("canvas"),q=X.getContext("2d");X.width=F.naturalWidth,X.height=F.naturalHeight,q==null||q.drawImage(F,0,0),X.toBlob(B=>{if(B){const oe=window.URL.createObjectURL(B),J=document.createElement("a");J.href=oe,J.download=`${a.fileName}.${w(a.imageUrl)}`,document.body.appendChild(J),J.click(),document.body.removeChild(J),window.URL.revokeObjectURL(oe),j(!0)}else T(new Error("无法生成图片blob"))},"image/png")}catch(X){T(X)}},F.onerror=()=>{T(new Error("图片加载失败"))},F.src=a.imageUrl})}}catch(Y){try{const F=document.createElement("a");F.href=a.imageUrl,F.download=`${a.fileName}.${w(a.imageUrl)}`,F.target="_blank",document.body.appendChild(F),F.click(),document.body.removeChild(F)}catch(F){}}})}function w(Y){const F=Y.match(/\.([^.]+)$/);return F?F[1]:"png"}function C(Y){Y.preventDefault();const F=Y.deltaY>0?-1:1,j=1.1,T=F>0?Math.min(c.value*j,hr):Math.max(c.value/j,dr);c.value=T}function Z(Y){Y.button===0&&(l.value=!0,u.value={x:Y.clientX,y:Y.clientY},f.value={x:d.value,y:g.value})}function R(Y){if(!l.value)return;const F=Y.clientX-u.value.x,j=Y.clientY-u.value.y;d.value=f.value.x+F,g.value=f.value.y+j}function U(){l.value=!1}function M(Y){Y.target===Y.currentTarget&&G()}function G(){r("update:visible",!1),r("close")}function ne(Y){if(!a.visible)return;const{key:F,ctrlKey:j,metaKey:T}=Y,X=j||T;switch(F){case"Escape":G();break;case"=":case"+":X&&(Y.preventDefault(),p());break;case"-":X&&(Y.preventDefault(),S());break;case"0":X&&(Y.preventDefault(),k());break;case"ArrowLeft":X&&(Y.preventDefault(),b());break;case"ArrowRight":X&&(Y.preventDefault(),P());break;case"s":X&&(Y.preventDefault(),D());break}}return St(()=>a.visible,Y=>{Y&&(s.value=!0,o.value=!1,k())}),St(()=>a.imageUrl,()=>{a.visible&&(s.value=!0,o.value=!1,k())}),ua(()=>{document.addEventListener("keydown",ne)}),Rr(()=>{document.removeEventListener("keydown",ne)}),(Y,F)=>(be(),et(Xr,{to:"body"},[Y.visible?(be(),Ve("div",{key:0,class:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm",onClick:M,onWheel:Xt(C,["prevent"])},[ce("div",Fh,[ce("button",{class:"toolbar-btn",onClick:S,disabled:c.value<=dr,title:"缩小 (Ctrl + -)"},[Ye(Pe(X0),{size:"20"})],8,Yh),ce("span",Ih,gs(Math.round(c.value*100))+"% ",1),ce("button",{class:"toolbar-btn",onClick:p,disabled:c.value>=hr,title:"放大 (Ctrl + +)"},[Ye(Pe(R0),{size:"20"})],8,Vh),F[3]||(F[3]=ce("div",{class:"w-px h-6 bg-gray-400"},null,-1)),ce("button",{class:"toolbar-btn",onClick:b,title:"逆时针旋转 (Ctrl + ←)"},F[1]||(F[1]=[ce("span",{class:"text-lg"},"↺",-1)])),ce("button",{class:"toolbar-btn",onClick:P,title:"顺时针旋转 (Ctrl + →)"},F[2]||(F[2]=[ce("span",{class:"text-lg"},"↻",-1)])),F[4]||(F[4]=ce("div",{class:"w-px h-6 bg-gray-400"},null,-1)),ce("button",{class:"toolbar-btn",onClick:k,title:"重置 (Ctrl + 0)"},[Ye(Pe(D0),{size:"20"})]),ce("button",{class:"toolbar-btn",onClick:D,title:"保存 (Ctrl + S)"},[Ye(Pe(B0),{size:"20"})])]),ce("button",{class:"absolute top-4 right-4 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-200",onClick:G,title:"关闭 (ESC)"},[Ye(Pe(bs),{size:"24"})]),ce("div",{ref_key:"imageContainer",ref:i,class:Zt(["relative w-full h-full flex items-center justify-center overflow-hidden cursor-grab",{"cursor-grabbing":l.value}]),onMousedown:Z,onMousemove:R,onMouseup:U,onMouseleave:U},[ce("img",{ref_key:"imageRef",ref:n,src:Y.imageUrl,class:"max-w-none max-h-none transition-transform duration-300 ease-out select-none",style:vs(v.value),alt:"预览图片",onLoad:Q,onError:x,onDragstart:F[0]||(F[0]=Xt(()=>{},["prevent"]))},null,44,Uh),s.value?(be(),Ve("div",Mh,F[5]||(F[5]=[ce("div",{class:"text-white text-lg"},"加载中...",-1)]))):qe("",!0),o.value?(be(),Ve("div",Wh,F[6]||(F[6]=[ce("div",{class:"text-white text-lg"},"图片加载失败",-1)]))):qe("",!0)],34),F[7]||(F[7]=ce("div",{class:"absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full"}," 拖拽移动 • 滚轮缩放 • ESC 关闭 ",-1))],32)):qe("",!0)]))}});const Wo=mo(zh,[["__scopeId","data-v-77d28dd8"]]),cn=Se(!1),un=Se(""),fn=Se("image");function zo(e){un.value=e.imageUrl,fn.value=e.fileName||"image",cn.value=!0}function No(){cn.value=!1,un.value="",fn.value="image"}function Go(){return{isVisible:cn,currentImageUrl:un,currentFileName:fn,openImageViewer:zo,closeImageViewer:No}}const Nh={install(e){e.component("ImageViewer",Wo),e.config.globalProperties.$imageViewer={open:zo,close:No},e.provide("imageViewer",Go())}},Gh=it({__name:"GlobalImageViewer",setup(e){const{isVisible:t,currentImageUrl:a,currentFileName:r,closeImageViewer:n}=Go();function i(){n()}return(s,o)=>(be(),et(Wo,{visible:Pe(t),"onUpdate:visible":o[0]||(o[0]=c=>Ss(t)?t.value=c:null),"image-url":Pe(a),"file-name":Pe(r),onClose:i},null,8,["visible","image-url","file-name"]))}});function jh(e){e.use(Nh),e.component("GlobalImageViewer",Gh)}console.log=()=>{},console.warn=()=>{},console.error=()=>{};function Hh(){const e=window.matchMedia("(prefers-color-scheme: dark)"),a=localStorage.getItem("theme")||(e.matches?"dark":"light");localStorage.setItem("theme",a),document.documentElement.classList.toggle("dark",a==="dark"),document.documentElement.dataset.theme=a}const Kh=lO();function Jh(){return fe(this,null,function*(){const e=y0(Ju);Zl(e),Lh(e),jh(e),e.use(cO),Hh();const{init:t}=J0();t();const a=Nr(),r=`${window.location.protocol}//${window.location.hostname}${window.location.port?`:${window.location.port}`:""}`;yield Kh.getGlobalConfig(r);const n=(yield pr(()=>import("./index-1507b968.js"),["./index-1507b968.js","./vue-vendor-d751b0f5.js"],import.meta.url)).default;e.use(n);const{MotionPlugin:i}=yield pr(()=>import("./index-ac5ef21b.js"),["./index-ac5ef21b.js","./utils-vendor-c35799af.js","./vue-vendor-d751b0f5.js"],import.meta.url);e.use(i),e.config.globalProperties.$onAppUnmount=()=>{a&&typeof a.destroy=="function"&&a.destroy()},e.mount("#app")})}Jh();export{lp as A,zo as B,op as C,Tl as D,mo as _,Wr as a,Ts as b,Op as c,hp as d,pp as e,fp as f,st as g,xp as h,cp as i,Qp as j,dp as k,Sp as l,Nr as m,gp as n,vp as o,ge as p,up as q,Mr as r,rt as s,bp as t,nt as u,mp as v,Xs as w,lO as x,sp as y,Yl as z};
