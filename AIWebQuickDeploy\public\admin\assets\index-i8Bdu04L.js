
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,a as t,az as a,a2 as i,e as n,w as s,c as l,T as o,i as r,f as u,g as m,V as c,W as d,a6 as v,_ as f,t as g,aB as p,Z as x}from"./index-BERX8Mlm.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang-AClYjrVV.js";import{u as h}from"./useMenu-CK91fAX9.js";const _={key:0,class:"main-sidebar-container"},y={class:"menu flex flex-col of-hidden transition-all"},w=["title","onClick"],k={class:"w-full inline-flex flex-1 flex-col items-center justify-center gap-[2px]"},j={class:"w-full flex-1 truncate text-center text-sm transition-height transition-opacity transition-width"},M=x(e({name:"MainSidebar",__name:"index",setup(e){const x=t(),M=a(),{switchTo:z}=h();return(e,t)=>{const a=f;return n(),i(p,{name:"main-sidebar"},{default:s((()=>["side"===r(x).settings.menu.menuMode||"mobile"===r(x).mode&&"single"!==r(x).settings.menu.menuMode?(n(),l("div",_,[u(b,{"show-title":!1,class:"sidebar-logo"}),m("div",y,[(n(!0),l(c,null,d(r(M).allMenus,((e,t)=>{var s,u,c,d,f,p,x,b;return n(),l("div",{key:t,class:v(["menu-item relative transition-all",{active:t===r(M).actived}])},[e.children&&0!==e.children.length?(n(),l("div",{key:0,class:v(["group menu-item-container h-full w-full flex cursor-pointer items-center justify-between gap-1 py-4 text-[var(--g-main-sidebar-menu-color)] transition-all hover-bg-[var(--g-main-sidebar-menu-hover-bg)] hover-text-[var(--g-main-sidebar-menu-hover-color)] px-2!",{"text-[var(--g-main-sidebar-menu-active-color)]! bg-[var(--g-main-sidebar-menu-active-bg)]!":t===r(M).actived}]),title:"function"==typeof(null==(s=e.meta)?void 0:s.title)?null==(u=e.meta)?void 0:u.title():null==(c=e.meta)?void 0:c.title,onClick:e=>r(z)(t)},[m("div",k,[(null==(d=e.meta)?void 0:d.icon)?(n(),i(a,{key:0,name:null==(f=e.meta)?void 0:f.icon,size:20,class:"menu-item-container-icon transition-transform group-hover-scale-120",async:""},null,8,["name"])):o("",!0),m("span",j,g("function"==typeof(null==(p=e.meta)?void 0:p.title)?null==(x=e.meta)?void 0:x.title():null==(b=e.meta)?void 0:b.title),1)])],10,w)):o("",!0)],2)})),128))])])):o("",!0)])),_:1})}}}),[["__scopeId","data-v-1a7bf0ae"]]);export{M as default};
