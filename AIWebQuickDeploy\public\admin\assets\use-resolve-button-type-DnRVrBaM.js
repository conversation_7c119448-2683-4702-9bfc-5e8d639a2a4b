
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{r as t,b as e,bg as u,bf as n}from"./index-BERX8Mlm.js";function a(t,e){if(t)return t;let u=null!=e?e:"button";return"string"==typeof u&&"button"===u.toLowerCase()?"button":void 0}function o(o,l){let r=t(a(o.value.type,o.value.as));return e((()=>{r.value=a(o.value.type,o.value.as)})),u((()=>{var t;r.value||n(l)&&n(l)instanceof HTMLButtonElement&&(null==(t=n(l))||!t.hasAttribute("type"))&&(r.value="button")})),r}export{o as s};
