
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,$ as t,r as l,b as u,Q as i,c as s,e as d,f as r,w as o,j as n,h as c,_ as f,g as p,Y as m,k as b}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";const y=a({__name:"baiduSensitive",setup(a){const b=t({baiduTextStatus:"",baiduTextApiKey:"",baiduTextSecretKey:""}),y=l({baiduTextStatus:[{required:!0,trigger:"blur",message:"请选择是否启用百度文本审核"}],baiduTextSecretKey:[{required:!0,trigger:"blur",message:"请填写百度文本审核SecretKey"}],baiduTextApiKey:[{required:!0,trigger:"blur",message:"请填写百度文本审核APIKey"}]}),_=l();async function g(){const e=await x.queryConfig({keys:["baiduTextStatus","baiduTextSecretKey","baiduTextApiKey"]});Object.assign(b,e.data)}function K(){var e;null==(e=_.value)||e.validate((async e=>{if(e){try{await x.setConfig({settings:(a=b,Object.keys(a).map((e=>({configKey:e,configVal:a[e]}))))}),m.success("变更配置信息成功")}catch(t){}g()}else m.error("请填写完整信息");var a}))}return u((()=>{g()})),(a,t)=>{const l=f,u=n,m=e,x=i("el-switch"),g=i("el-tooltip"),v=i("el-form-item"),T=i("el-col"),S=i("el-row"),V=i("el-input"),h=i("el-form"),A=i("el-card");return d(),s("div",null,[r(m,null,{title:o((()=>t[3]||(t[3]=[p("div",{class:"flex items-center gap-4"},"百度文本审核参数设置",-1)]))),content:o((()=>t[4]||(t[4]=[p("div",{class:"text-sm/6"},[p("div",null,[c(" 当前百度云免费5万条，可查看"),p("a",{href:"https://console.bce.baidu.com/ai/#/ai/antiporn/overview/index",target:"_blank"},"使用文档"),c("，如果百度云敏感词与自定义敏感词都配置的情况，会先检测百度云后检测自定义的敏感词。 ")])],-1)]))),default:o((()=>[r(u,{outline:"",onClick:K},{default:o((()=>[r(l,{name:"i-ri:file-text-line"}),t[5]||(t[5]=c(" 保存设置 "))])),_:1})])),_:1}),r(A,{style:{margin:"20px"}},{default:o((()=>[r(h,{ref_key:"formRef",ref:_,rules:y.value,model:b,"label-width":"150px"},{default:o((()=>[r(S,null,{default:o((()=>[r(T,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[r(v,{label:"开启此敏感词设置",prop:"baiduTextStatus"},{default:o((()=>[r(g,{content:"开启将打开敏感词检测、如果同时开启其他敏感词将会通过菜单顺序仅同时开启一个！",placement:"top","show-after":500},{default:o((()=>[r(x,{modelValue:b.baiduTextStatus,"onUpdate:modelValue":t[0]||(t[0]=e=>b.baiduTextStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),r(S,null,{default:o((()=>[r(T,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[r(v,{label:"文本审核ApiKey",prop:"baiduTextApiKey"},{default:o((()=>[r(V,{modelValue:b.baiduTextApiKey,"onUpdate:modelValue":t[1]||(t[1]=e=>b.baiduTextApiKey=e),placeholder:"请填写百度文本审核ApiKey",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),r(S,null,{default:o((()=>[r(T,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[r(v,{label:"文本审核SecretKey",prop:"baiduTextSecretKey"},{default:o((()=>[r(V,{modelValue:b.baiduTextSecretKey,"onUpdate:modelValue":t[2]||(t[2]=e=>b.baiduTextSecretKey=e),placeholder:"请填写百度文本审核SecretKey",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof b&&b(y);export{y as default};
