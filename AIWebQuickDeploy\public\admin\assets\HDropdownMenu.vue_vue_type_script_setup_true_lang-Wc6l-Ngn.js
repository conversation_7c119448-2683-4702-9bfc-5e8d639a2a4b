
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,P as s,Q as t,a2 as a,e as o,w as r,aj as d,c as l,V as i,W as n,i as b,t as p,R as u}from"./index-BERX8Mlm.js";const c=["disabled","onClick"],h=e({__name:"HDropdownMenu",props:{items:{}},setup(e){const h=e,g=s((()=>h.items.map((e=>e.filter((e=>!e.hide)))).filter((e=>e.length))));return(e,s)=>{const h=t("VMenu");return o(),a(h,u({"show-triggers":["hover"],"auto-hide":!1,"popper-triggers":["hover","click"],delay:200},e.$attrs),{popper:r((()=>[(o(!0),l(i,null,n(b(g),((e,s)=>(o(),l("div",{key:s,class:"b-b-stone-2 b-b-solid p-1 last-b-b-size-0 dark-b-b-stone-7"},[(o(!0),l(i,null,n(e,((e,s)=>(o(),l("button",{key:s,disabled:e.disabled,class:"w-full flex cursor-pointer items-center gap-2 border-size-0 rounded-md bg-inherit px-2 py-1.5 text-sm text-dark disabled-cursor-not-allowed dark-text-white disabled-opacity-50 hover-not-disabled-bg-stone-1 dark-hover-not-disabled-bg-stone-9",onClick:e.handle},p(e.label),9,c)))),128))])))),128))])),default:r((()=>[d(e.$slots,"default")])),_:3},16)}}});export{h as _};
