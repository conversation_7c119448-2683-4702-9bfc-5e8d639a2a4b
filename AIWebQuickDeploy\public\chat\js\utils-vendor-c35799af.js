var In=Object.defineProperty,_n=Object.defineProperties;var Un=Object.getOwnPropertyDescriptors;var Et=Object.getOwnPropertySymbols;var Fn=Object.prototype.hasOwnProperty,Dn=Object.prototype.propertyIsEnumerable;var te=(e,t)=>{if(t=Symbol[e])return t;throw Error("Symbol."+e+" is not defined")};var bt=(e,t,n)=>t in e?In(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,H=(e,t)=>{for(var n in t||(t={}))Fn.call(t,n)&&bt(e,n,t[n]);if(Et)for(var n of Et(t))Dn.call(t,n)&&bt(e,n,t[n]);return e},ve=(e,t)=>_n(e,Un(t));var q=(e,t,n)=>new Promise((r,o)=>{var s=u=>{try{a(n.next(u))}catch(c){o(c)}},i=u=>{try{a(n.throw(u))}catch(c){o(c)}},a=u=>u.done?r(u.value):Promise.resolve(u.value).then(s,i);a((n=n.apply(e,t)).next())}),z=function(e,t){this[0]=e,this[1]=t},je=(e,t,n)=>{var r=(i,a,u,c)=>{try{var l=n[i](a),p=(a=l.value)instanceof z,m=l.done;Promise.resolve(p?a[0]:a).then(h=>p?r(i==="return"?i:"next",a[1]?{done:h.done,value:h.value}:h,u,c):u({value:h,done:m})).catch(h=>r("throw",h,u,c))}catch(h){c(h)}},o=i=>s[i]=a=>new Promise((u,c)=>r(i,a,u,c)),s={};return n=n.apply(e,t),s[Symbol.asyncIterator]=()=>s,o("next"),o("throw"),o("return"),s},He=e=>{var t=e[te("asyncIterator")],n=!1,r,o={};return t==null?(t=e[te("iterator")](),r=s=>o[s]=i=>t[s](i)):(t=t.call(e),r=s=>o[s]=i=>{if(n){if(n=!1,s==="throw")throw i;return i}return n=!0,{done:!1,value:new z(new Promise(a=>{var u=t[s](i);if(!(u instanceof Object))throw TypeError("Object expected");a(u)}),1)}}),o[te("iterator")]=()=>o,r("next"),"throw"in t?r("throw"):o.throw=s=>{throw s},"return"in t&&r("return"),o},St=(e,t,n)=>(t=e[te("asyncIterator")])?t.call(e):(e=e[te("iterator")](),t={},n=(r,o)=>(o=e[r])&&(t[r]=s=>new Promise((i,a,u)=>(s=o.call(e,s),u=s.done,Promise.resolve(s.value).then(c=>i({value:c,done:u}),a)))),n("next"),n("return"),t);import{o as kn,g as xn,a as vn,b as at,h as xt,i as jn,t as Hn,r as qn,c as zn,d as $n,w as vt,e as jt,n as Vn,s as J,f as Jn,j as K,k as x,u as Kn}from"./vue-vendor-d751b0f5.js";var Ps=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function Os(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Ls(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}),n}function Ht(e){return xn()?(vn(e),!0):!1}const qe=new WeakMap,Wn=(...e)=>{var t;const n=e[0],r=(t=at())==null?void 0:t.proxy;if(r==null&&!xt())throw new Error("injectLocal must be called in setup");return r&&qe.has(r)&&n in qe.get(r)?qe.get(r)[n]:jn(...e)},Gn=typeof window!="undefined"&&typeof document!="undefined";typeof WorkerGlobalScope!="undefined"&&globalThis instanceof WorkerGlobalScope;const Yn=e=>e!=null,Qn=Object.prototype.toString,Xn=e=>Qn.call(e)==="[object Object]",de=()=>{};function Ms(...e){if(e.length!==1)return Hn(...e);const t=e[0];return typeof t=="function"?qn(zn(()=>({get:t,set:de}))):$n(t)}function Zn(e,t){var n;if(typeof e=="number")return e+t;const r=((n=e.match(/^-?\d+\.?\d*/))==null?void 0:n[0])||"",o=e.slice(r.length),s=Number.parseFloat(r)+t;return Number.isNaN(s)?e:s+o}function oe(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function he(e){return Array.isArray(e)?e:[e]}function qt(e){return e||at()}function er(e,t=!0,n){qt(n)?jt(e,n):t?e():Vn(e)}function Is(e,t){qt(t)&&kn(e,t)}function tr(e,t,n){return vt(e,t,ve(H({},n),{immediate:!0}))}const Te=Gn?window:void 0;function pe(e){var t;const n=x(e);return(t=n==null?void 0:n.$el)!=null?t:n}function nr(...e){const t=[],n=()=>{t.forEach(a=>a()),t.length=0},r=(a,u,c,l)=>(a.addEventListener(u,c,l),()=>a.removeEventListener(u,c,l)),o=K(()=>{const a=he(x(e[0])).filter(u=>u!=null);return a.every(u=>typeof u!="string")?a:void 0}),s=tr(()=>{var a,u;return[(u=(a=o.value)==null?void 0:a.map(c=>pe(c)))!=null?u:[Te].filter(c=>c!=null),he(x(o.value?e[1]:e[0])),he(Kn(o.value?e[2]:e[1])),x(o.value?e[3]:e[2])]},([a,u,c,l])=>{if(n(),!(a!=null&&a.length)||!(u!=null&&u.length)||!(c!=null&&c.length))return;const p=Xn(l)?H({},l):l;t.push(...a.flatMap(m=>u.flatMap(h=>c.map(d=>r(m,h,d,p)))))},{flush:"post"}),i=()=>{s(),n()};return Ht(n),i}function rr(){const e=J(!1),t=at();return t&&jt(()=>{e.value=!0},t),e}function zt(e){const t=rr();return K(()=>(t.value,!!e()))}const or=Symbol("vueuse-ssr-width");function $t(){const e=xt()?Wn(or,null):null;return typeof e=="number"?e:void 0}function ne(e,t={}){const{window:n=Te,ssrWidth:r=$t()}=t,o=zt(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),s=J(typeof r=="number"),i=J(),a=J(!1),u=c=>{a.value=c.matches};return Jn(()=>{if(s.value){s.value=!o.value;const c=x(e).split(",");a.value=c.some(l=>{const p=l.includes("not all"),m=l.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),h=l.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let d=!!(m||h);return m&&d&&(d=r>=oe(m[1])),h&&d&&(d=r<=oe(h[1])),p?!d:d});return}o.value&&(i.value=n.matchMedia(x(e)),a.value=i.value.matches)}),nr(i,"change",u,{passive:!0}),K(()=>a.value)}const _s={sm:640,md:768,lg:1024,xl:1280,"2xl":1536};function Us(e,t={}){function n(h,d){let g=x(e[x(h)]);return d!=null&&(g=Zn(g,d)),typeof g=="number"&&(g=`${g}px`),g}const{window:r=Te,strategy:o="min-width",ssrWidth:s=$t()}=t,i=typeof s=="number",a=i?J(!1):{value:!0};i&&er(()=>a.value=!!r);function u(h,d){return!a.value&&i?h==="min"?s>=oe(d):s<=oe(d):r?r.matchMedia(`(${h}-width: ${d})`).matches:!1}const c=h=>ne(()=>`(min-width: ${n(h)})`,t),l=h=>ne(()=>`(max-width: ${n(h)})`,t),p=Object.keys(e).reduce((h,d)=>(Object.defineProperty(h,d,{get:()=>o==="min-width"?c(d):l(d),enumerable:!0,configurable:!0}),h),{});function m(){const h=Object.keys(e).map(d=>[d,p[d],oe(n(d))]).sort((d,g)=>d[2]-g[2]);return K(()=>h.filter(([,d])=>d.value).map(([d])=>d))}return Object.assign(p,{greaterOrEqual:c,smallerOrEqual:l,greater(h){return ne(()=>`(min-width: ${n(h,.1)})`,t)},smaller(h){return ne(()=>`(max-width: ${n(h,-.1)})`,t)},between(h,d){return ne(()=>`(min-width: ${n(h)}) and (max-width: ${n(d,-.1)})`,t)},isGreater(h){return u("min",n(h,.1))},isGreaterOrEqual(h){return u("min",n(h))},isSmaller(h){return u("max",n(h,-.1))},isSmallerOrEqual(h){return u("max",n(h))},isInBetween(h,d){return u("min",n(h))&&u("max",n(d,-.1))},current:m,active(){const h=m();return K(()=>h.value.length===0?"":h.value.at(o==="min-width"?-1:0))}})}function Fs(e,t,n={}){const{root:r,rootMargin:o="0px",threshold:s=0,window:i=Te,immediate:a=!0}=n,u=zt(()=>i&&"IntersectionObserver"in i),c=K(()=>{const d=x(e);return he(d).map(pe).filter(Yn)});let l=de;const p=J(a),m=u.value?vt(()=>[c.value,pe(r),p.value],([d,g])=>{if(l(),!p.value||!d.length)return;const w=new IntersectionObserver(t,{root:pe(g),rootMargin:o,threshold:s});d.forEach(y=>y&&w.observe(y)),l=()=>{w.disconnect(),l=de}},{immediate:a,flush:"post"}):de,h=()=>{l(),m(),p.value=!1};return Ht(h),{isSupported:u,isActive:p,pause(){l(),p.value=!1},resume(){p.value=!0},stop:h}}function Vt(e,t){return function(){return e.apply(t,arguments)}}const{toString:sr}=Object.prototype,{getPrototypeOf:ut}=Object,{iterator:Ae,toStringTag:Jt}=Symbol,Ne=(e=>t=>{const n=sr.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),_=e=>(e=e.toLowerCase(),t=>Ne(t)===e),Be=e=>t=>typeof t===e,{isArray:X}=Array,ie=Be("undefined");function ir(e){return e!==null&&!ie(e)&&e.constructor!==null&&!ie(e.constructor)&&O(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Kt=_("ArrayBuffer");function ar(e){let t;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Kt(e.buffer),t}const ur=Be("string"),O=Be("function"),Wt=Be("number"),Pe=e=>e!==null&&typeof e=="object",cr=e=>e===!0||e===!1,ge=e=>{if(Ne(e)!=="object")return!1;const t=ut(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Jt in e)&&!(Ae in e)},lr=_("Date"),fr=_("File"),dr=_("Blob"),hr=_("FileList"),pr=e=>Pe(e)&&O(e.pipe),gr=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||O(e.append)&&((t=Ne(e))==="formdata"||t==="object"&&O(e.toString)&&e.toString()==="[object FormData]"))},mr=_("URLSearchParams"),[wr,yr,Er,br]=["ReadableStream","Request","Response","Headers"].map(_),Sr=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ue(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e=="undefined")return;let r,o;if(typeof e!="object"&&(e=[e]),X(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let a;for(r=0;r<i;r++)a=s[r],t.call(null,e[a],a,e)}}function Gt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const $=(()=>typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global)(),Yt=e=>!ie(e)&&e!==$;function Qe(){const{caseless:e}=Yt(this)&&this||{},t={},n=(r,o)=>{const s=e&&Gt(t,o)||o;ge(t[s])&&ge(r)?t[s]=Qe(t[s],r):ge(r)?t[s]=Qe({},r):X(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&ue(arguments[r],n);return t}const Rr=(e,t,n,{allOwnKeys:r}={})=>(ue(t,(o,s)=>{n&&O(o)?e[s]=Vt(o,n):e[s]=o},{allOwnKeys:r}),e),Cr=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Tr=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Ar=(e,t,n,r)=>{let o,s,i;const a={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&ut(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Nr=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Br=e=>{if(!e)return null;if(X(e))return e;let t=e.length;if(!Wt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Pr=(e=>t=>e&&t instanceof e)(typeof Uint8Array!="undefined"&&ut(Uint8Array)),Or=(e,t)=>{const r=(e&&e[Ae]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},Lr=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Mr=_("HTMLFormElement"),Ir=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),Rt=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),_r=_("RegExp"),Qt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ue(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},Ur=e=>{Qt(e,(t,n)=>{if(O(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(O(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Fr=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return X(e)?r(e):r(String(e).split(t)),n},Dr=()=>{},kr=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function xr(e){return!!(e&&O(e.append)&&e[Jt]==="FormData"&&e[Ae])}const vr=e=>{const t=new Array(10),n=(r,o)=>{if(Pe(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=X(r)?[]:{};return ue(r,(i,a)=>{const u=n(i,o+1);!ie(u)&&(s[a]=u)}),t[o]=void 0,s}}return r};return n(e,0)},jr=_("AsyncFunction"),Hr=e=>e&&(Pe(e)||O(e))&&O(e.then)&&O(e.catch),Xt=((e,t)=>e?setImmediate:t?((n,r)=>($.addEventListener("message",({source:o,data:s})=>{o===$&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),$.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",O($.postMessage)),qr=typeof queueMicrotask!="undefined"?queueMicrotask.bind($):typeof process!="undefined"&&process.nextTick||Xt,zr=e=>e!=null&&O(e[Ae]),f={isArray:X,isArrayBuffer:Kt,isBuffer:ir,isFormData:gr,isArrayBufferView:ar,isString:ur,isNumber:Wt,isBoolean:cr,isObject:Pe,isPlainObject:ge,isReadableStream:wr,isRequest:yr,isResponse:Er,isHeaders:br,isUndefined:ie,isDate:lr,isFile:fr,isBlob:dr,isRegExp:_r,isFunction:O,isStream:pr,isURLSearchParams:mr,isTypedArray:Pr,isFileList:hr,forEach:ue,merge:Qe,extend:Rr,trim:Sr,stripBOM:Cr,inherits:Tr,toFlatObject:Ar,kindOf:Ne,kindOfTest:_,endsWith:Nr,toArray:Br,forEachEntry:Or,matchAll:Lr,isHTMLForm:Mr,hasOwnProperty:Rt,hasOwnProp:Rt,reduceDescriptors:Qt,freezeMethods:Ur,toObjectSet:Fr,toCamelCase:Ir,noop:Dr,toFiniteNumber:kr,findKey:Gt,global:$,isContextDefined:Yt,isSpecCompliantForm:xr,toJSONObject:vr,isAsyncFn:jr,isThenable:Hr,setImmediate:Xt,asap:qr,isIterable:zr};function R(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}f.inherits(R,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const Zt=R.prototype,en={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{en[e]={value:e}});Object.defineProperties(R,en);Object.defineProperty(Zt,"isAxiosError",{value:!0});R.from=(e,t,n,r,o,s)=>{const i=Object.create(Zt);return f.toFlatObject(e,i,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),R.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const $r=null;function Xe(e){return f.isPlainObject(e)||f.isArray(e)}function tn(e){return f.endsWith(e,"[]")?e.slice(0,-2):e}function Ct(e,t,n){return e?e.concat(t).map(function(o,s){return o=tn(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function Vr(e){return f.isArray(e)&&!e.some(Xe)}const Jr=f.toFlatObject(f,{},null,function(t){return/^is[A-Z]/.test(t)});function Oe(e,t,n){if(!f.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=f.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,w){return!f.isUndefined(w[g])});const r=n.metaTokens,o=n.visitor||l,s=n.dots,i=n.indexes,u=(n.Blob||typeof Blob!="undefined"&&Blob)&&f.isSpecCompliantForm(t);if(!f.isFunction(o))throw new TypeError("visitor must be a function");function c(d){if(d===null)return"";if(f.isDate(d))return d.toISOString();if(!u&&f.isBlob(d))throw new R("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(d)||f.isTypedArray(d)?u&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function l(d,g,w){let y=d;if(d&&!w&&typeof d=="object"){if(f.endsWith(g,"{}"))g=r?g:g.slice(0,-2),d=JSON.stringify(d);else if(f.isArray(d)&&Vr(d)||(f.isFileList(d)||f.endsWith(g,"[]"))&&(y=f.toArray(d)))return g=tn(g),y.forEach(function(E,S){!(f.isUndefined(E)||E===null)&&t.append(i===!0?Ct([g],S,s):i===null?g:g+"[]",c(E))}),!1}return Xe(d)?!0:(t.append(Ct(w,g,s),c(d)),!1)}const p=[],m=Object.assign(Jr,{defaultVisitor:l,convertValue:c,isVisitable:Xe});function h(d,g){if(!f.isUndefined(d)){if(p.indexOf(d)!==-1)throw Error("Circular reference detected in "+g.join("."));p.push(d),f.forEach(d,function(y,b){(!(f.isUndefined(y)||y===null)&&o.call(t,y,f.isString(b)?b.trim():b,g,m))===!0&&h(y,g?g.concat(b):[b])}),p.pop()}}if(!f.isObject(e))throw new TypeError("data must be an object");return h(e),t}function Tt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function ct(e,t){this._pairs=[],e&&Oe(e,this,t)}const nn=ct.prototype;nn.append=function(t,n){this._pairs.push([t,n])};nn.toString=function(t){const n=t?function(r){return t.call(this,r,Tt)}:Tt;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function Kr(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rn(e,t,n){if(!t)return e;const r=n&&n.encode||Kr;f.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=f.isURLSearchParams(t)?t.toString():new ct(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Wr{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){f.forEach(this.handlers,function(r){r!==null&&t(r)})}}const At=Wr,on={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gr=typeof URLSearchParams!="undefined"?URLSearchParams:ct,Yr=typeof FormData!="undefined"?FormData:null,Qr=typeof Blob!="undefined"?Blob:null,Xr={isBrowser:!0,classes:{URLSearchParams:Gr,FormData:Yr,Blob:Qr},protocols:["http","https","file","blob","url","data"]},lt=typeof window!="undefined"&&typeof document!="undefined",Ze=typeof navigator=="object"&&navigator||void 0,Zr=lt&&(!Ze||["ReactNative","NativeScript","NS"].indexOf(Ze.product)<0),eo=(()=>typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),to=lt&&window.location.href||"http://localhost",no=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:lt,hasStandardBrowserEnv:Zr,hasStandardBrowserWebWorkerEnv:eo,navigator:Ze,origin:to},Symbol.toStringTag,{value:"Module"})),P=H(H({},no),Xr);function ro(e,t){return Oe(e,new P.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return P.isNode&&f.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function oo(e){return f.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function so(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function sn(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),u=s>=n.length;return i=!i&&f.isArray(o)?o.length:i,u?(f.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!a):((!o[i]||!f.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&f.isArray(o[i])&&(o[i]=so(o[i])),!a)}if(f.isFormData(e)&&f.isFunction(e.entries)){const n={};return f.forEachEntry(e,(r,o)=>{t(oo(r),o,n,0)}),n}return null}function io(e,t,n){if(f.isString(e))try{return(t||JSON.parse)(e),f.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const ft={transitional:on,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=f.isObject(t);if(s&&f.isHTMLForm(t)&&(t=new FormData(t)),f.isFormData(t))return o?JSON.stringify(sn(t)):t;if(f.isArrayBuffer(t)||f.isBuffer(t)||f.isStream(t)||f.isFile(t)||f.isBlob(t)||f.isReadableStream(t))return t;if(f.isArrayBufferView(t))return t.buffer;if(f.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return ro(t,this.formSerializer).toString();if((a=f.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return Oe(a?{"files[]":t}:t,u&&new u,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),io(t)):t}],transformResponse:[function(t){const n=this.transitional||ft.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(f.isResponse(t)||f.isReadableStream(t))return t;if(t&&f.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?R.from(a,R.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:P.classes.FormData,Blob:P.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],e=>{ft.headers[e]={}});const dt=ft,ao=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),uo=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&ao[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Nt=Symbol("internals");function re(e){return e&&String(e).trim().toLowerCase()}function me(e){return e===!1||e==null?e:f.isArray(e)?e.map(me):String(e)}function co(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const lo=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ze(e,t,n,r,o){if(f.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!f.isString(t)){if(f.isString(r))return t.indexOf(r)!==-1;if(f.isRegExp(r))return r.test(t)}}function fo(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function ho(e,t){const n=f.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}class Le{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(a,u,c){const l=re(u);if(!l)throw new Error("header name must be a non-empty string");const p=f.findKey(o,l);(!p||o[p]===void 0||c===!0||c===void 0&&o[p]!==!1)&&(o[p||u]=me(a))}const i=(a,u)=>f.forEach(a,(c,l)=>s(c,l,u));if(f.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(f.isString(t)&&(t=t.trim())&&!lo(t))i(uo(t),n);else if(f.isObject(t)&&f.isIterable(t)){let a={},u,c;for(const l of t){if(!f.isArray(l))throw TypeError("Object iterator must return a key-value pair");a[c=l[0]]=(u=a[c])?f.isArray(u)?[...u,l[1]]:[u,l[1]]:l[1]}i(a,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=re(t),t){const r=f.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return co(o);if(f.isFunction(n))return n.call(this,o,r);if(f.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=re(t),t){const r=f.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ze(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=re(i),i){const a=f.findKey(r,i);a&&(!n||ze(r,r[a],a,n))&&(delete r[a],o=!0)}}return f.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||ze(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return f.forEach(this,(o,s)=>{const i=f.findKey(r,s);if(i){n[i]=me(o),delete n[s];return}const a=t?fo(s):String(s).trim();a!==s&&delete n[s],n[a]=me(o),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return f.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&f.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Nt]=this[Nt]={accessors:{}}).accessors,o=this.prototype;function s(i){const a=re(i);r[a]||(ho(o,i),r[a]=!0)}return f.isArray(t)?t.forEach(s):s(t),this}}Le.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(Le.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});f.freezeMethods(Le);const I=Le;function $e(e,t){const n=this||dt,r=t||n,o=I.from(r.headers);let s=r.data;return f.forEach(e,function(a){s=a.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function an(e){return!!(e&&e.__CANCEL__)}function Z(e,t,n){R.call(this,e==null?"canceled":e,R.ERR_CANCELED,t,n),this.name="CanceledError"}f.inherits(Z,R,{__CANCEL__:!0});function un(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new R("Request failed with status code "+n.status,[R.ERR_BAD_REQUEST,R.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function po(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function go(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),l=r[s];i||(i=c),n[o]=u,r[o]=c;let p=s,m=0;for(;p!==o;)m+=n[p++],p=p%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const h=l&&c-l;return h?Math.round(m*1e3/h):void 0}}function mo(e,t){let n=0,r=1e3/t,o,s;const i=(c,l=Date.now())=>{n=l,o=null,s&&(clearTimeout(s),s=null),e.apply(null,c)};return[(...c)=>{const l=Date.now(),p=l-n;p>=r?i(c,l):(o=c,s||(s=setTimeout(()=>{s=null,i(o)},r-p)))},()=>o&&i(o)]}const Ee=(e,t,n=3)=>{let r=0;const o=go(50,250);return mo(s=>{const i=s.loaded,a=s.lengthComputable?s.total:void 0,u=i-r,c=o(u),l=i<=a;r=i;const p={loaded:i,total:a,progress:a?i/a:void 0,bytes:u,rate:c||void 0,estimated:c&&a&&l?(a-i)/c:void 0,event:s,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(p)},n)},Bt=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Pt=e=>(...t)=>f.asap(()=>e(...t)),wo=P.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,P.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(P.origin),P.navigator&&/(msie|trident)/i.test(P.navigator.userAgent)):()=>!0,yo=P.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];f.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),f.isString(r)&&i.push("path="+r),f.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Eo(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function bo(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function cn(e,t,n){let r=!Eo(t);return e&&(r||n==!1)?bo(e,t):t}const Ot=e=>e instanceof I?H({},e):e;function V(e,t){t=t||{};const n={};function r(c,l,p,m){return f.isPlainObject(c)&&f.isPlainObject(l)?f.merge.call({caseless:m},c,l):f.isPlainObject(l)?f.merge({},l):f.isArray(l)?l.slice():l}function o(c,l,p,m){if(f.isUndefined(l)){if(!f.isUndefined(c))return r(void 0,c,p,m)}else return r(c,l,p,m)}function s(c,l){if(!f.isUndefined(l))return r(void 0,l)}function i(c,l){if(f.isUndefined(l)){if(!f.isUndefined(c))return r(void 0,c)}else return r(void 0,l)}function a(c,l,p){if(p in t)return r(c,l);if(p in e)return r(void 0,c)}const u={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(c,l,p)=>o(Ot(c),Ot(l),p,!0)};return f.forEach(Object.keys(Object.assign({},e,t)),function(l){const p=u[l]||o,m=p(e[l],t[l],l);f.isUndefined(m)&&p!==a||(n[l]=m)}),n}const ln=e=>{const t=V({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:a}=t;t.headers=i=I.from(i),t.url=rn(cn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(f.isFormData(n)){if(P.hasStandardBrowserEnv||P.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((u=i.getContentType())!==!1){const[c,...l]=u?u.split(";").map(p=>p.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...l].join("; "))}}if(P.hasStandardBrowserEnv&&(r&&f.isFunction(r)&&(r=r(t)),r||r!==!1&&wo(t.url))){const c=o&&s&&yo.read(s);c&&i.set(o,c)}return t},So=typeof XMLHttpRequest!="undefined",Ro=So&&function(e){return new Promise(function(n,r){const o=ln(e);let s=o.data;const i=I.from(o.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:c}=o,l,p,m,h,d;function g(){h&&h(),d&&d(),o.cancelToken&&o.cancelToken.unsubscribe(l),o.signal&&o.signal.removeEventListener("abort",l)}let w=new XMLHttpRequest;w.open(o.method.toUpperCase(),o.url,!0),w.timeout=o.timeout;function y(){if(!w)return;const E=I.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),C={data:!a||a==="text"||a==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:E,config:e,request:w};un(function(A){n(A),g()},function(A){r(A),g()},C),w=null}"onloadend"in w?w.onloadend=y:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(y)},w.onabort=function(){w&&(r(new R("Request aborted",R.ECONNABORTED,e,w)),w=null)},w.onerror=function(){r(new R("Network Error",R.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let S=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const C=o.transitional||on;o.timeoutErrorMessage&&(S=o.timeoutErrorMessage),r(new R(S,C.clarifyTimeoutError?R.ETIMEDOUT:R.ECONNABORTED,e,w)),w=null},s===void 0&&i.setContentType(null),"setRequestHeader"in w&&f.forEach(i.toJSON(),function(S,C){w.setRequestHeader(C,S)}),f.isUndefined(o.withCredentials)||(w.withCredentials=!!o.withCredentials),a&&a!=="json"&&(w.responseType=o.responseType),c&&([m,d]=Ee(c,!0),w.addEventListener("progress",m)),u&&w.upload&&([p,h]=Ee(u),w.upload.addEventListener("progress",p),w.upload.addEventListener("loadend",h)),(o.cancelToken||o.signal)&&(l=E=>{w&&(r(!E||E.type?new Z(null,e,w):E),w.abort(),w=null)},o.cancelToken&&o.cancelToken.subscribe(l),o.signal&&(o.signal.aborted?l():o.signal.addEventListener("abort",l)));const b=po(o.url);if(b&&P.protocols.indexOf(b)===-1){r(new R("Unsupported protocol "+b+":",R.ERR_BAD_REQUEST,e));return}w.send(s||null)})},Co=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(c){if(!o){o=!0,a();const l=c instanceof Error?c:this.reason;r.abort(l instanceof R?l:new Z(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,s(new R(`timeout ${t} of ms exceeded`,R.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(s):c.removeEventListener("abort",s)}),e=null)};e.forEach(c=>c.addEventListener("abort",s));const{signal:u}=r;return u.unsubscribe=()=>f.asap(a),u}},To=Co,Ao=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},No=function(e,t){return je(this,null,function*(){try{for(var n=St(Bo(e)),r,o,s;r=!(o=yield new z(n.next())).done;r=!1){const i=o.value;yield*He(Ao(i,t))}}catch(o){s=[o]}finally{try{r&&(o=n.return)&&(yield new z(o.call(n)))}finally{if(s)throw s[0]}}})},Bo=function(e){return je(this,null,function*(){if(e[Symbol.asyncIterator]){yield*He(e);return}const t=e.getReader();try{for(;;){const{done:n,value:r}=yield new z(t.read());if(n)break;yield r}}finally{yield new z(t.cancel())}})},Lt=(e,t,n,r)=>{const o=No(e,t);let s=0,i,a=c=>{i||(i=!0,r&&r(c))};return new ReadableStream({pull(c){return q(this,null,function*(){try{const{done:l,value:p}=yield o.next();if(l){a(),c.close();return}let m=p.byteLength;if(n){let h=s+=m;n(h)}c.enqueue(new Uint8Array(p))}catch(l){throw a(l),l}})},cancel(c){return a(c),o.return()}},{highWaterMark:2})},Me=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",fn=Me&&typeof ReadableStream=="function",Po=Me&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):e=>q(void 0,null,function*(){return new Uint8Array(yield new Response(e).arrayBuffer())})),dn=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},Oo=fn&&dn(()=>{let e=!1;const t=new Request(P.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Mt=64*1024,et=fn&&dn(()=>f.isReadableStream(new Response("").body)),be={stream:et&&(e=>e.body)};Me&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!be[t]&&(be[t]=f.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new R(`Response type '${t}' is not supported`,R.ERR_NOT_SUPPORT,r)})})})(new Response);const Lo=e=>q(void 0,null,function*(){if(e==null)return 0;if(f.isBlob(e))return e.size;if(f.isSpecCompliantForm(e))return(yield new Request(P.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(f.isArrayBufferView(e)||f.isArrayBuffer(e))return e.byteLength;if(f.isURLSearchParams(e)&&(e=e+""),f.isString(e))return(yield Po(e)).byteLength}),Mo=(e,t)=>q(void 0,null,function*(){const n=f.toFiniteNumber(e.getContentLength());return n==null?Lo(t):n}),Io=Me&&(e=>q(void 0,null,function*(){let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:u,responseType:c,headers:l,withCredentials:p="same-origin",fetchOptions:m}=ln(e);c=c?(c+"").toLowerCase():"text";let h=To([o,s&&s.toAbortSignal()],i),d;const g=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let w;try{if(u&&Oo&&n!=="get"&&n!=="head"&&(w=yield Mo(l,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),T;if(f.isFormData(r)&&(T=C.headers.get("content-type"))&&l.setContentType(T),C.body){const[A,B]=Bt(w,Ee(Pt(u)));r=Lt(C.body,Mt,A,B)}}f.isString(p)||(p=p?"include":"omit");const y="credentials"in Request.prototype;d=new Request(t,ve(H({},m),{signal:h,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:y?p:void 0}));let b=yield fetch(d);const E=et&&(c==="stream"||c==="response");if(et&&(a||E&&g)){const C={};["status","statusText","headers"].forEach(M=>{C[M]=b[M]});const T=f.toFiniteNumber(b.headers.get("content-length")),[A,B]=a&&Bt(T,Ee(Pt(a),!0))||[];b=new Response(Lt(b.body,Mt,A,()=>{B&&B(),g&&g()}),C)}c=c||"text";let S=yield be[f.findKey(be,c)||"text"](b,e);return!E&&g&&g(),yield new Promise((C,T)=>{un(C,T,{data:S,headers:I.from(b.headers),status:b.status,statusText:b.statusText,config:e,request:d})})}catch(y){throw g&&g(),y&&y.name==="TypeError"&&/Load failed|fetch/i.test(y.message)?Object.assign(new R("Network Error",R.ERR_NETWORK,e,d),{cause:y.cause||y}):R.from(y,y&&y.code,e,d)}})),tt={http:$r,xhr:Ro,fetch:Io};f.forEach(tt,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const It=e=>`- ${e}`,_o=e=>f.isFunction(e)||e===null||e===!1,hn={getAdapter:e=>{e=f.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!_o(n)&&(r=tt[(i=String(n)).toLowerCase()],r===void 0))throw new R(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(It).join(`
`):" "+It(s[0]):"as no adapter specified";throw new R("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:tt};function Ve(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Z(null,e)}function _t(e){return Ve(e),e.headers=I.from(e.headers),e.data=$e.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),hn.getAdapter(e.adapter||dt.adapter)(e).then(function(r){return Ve(e),r.data=$e.call(e,e.transformResponse,r),r.headers=I.from(r.headers),r},function(r){return an(r)||(Ve(e),r&&r.response&&(r.response.data=$e.call(e,e.transformResponse,r.response),r.response.headers=I.from(r.response.headers))),Promise.reject(r)})}const pn="1.9.0",Ie={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ie[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ut={};Ie.transitional=function(t,n,r){function o(s,i){return"[Axios v"+pn+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,a)=>{if(t===!1)throw new R(o(i," has been removed"+(n?" in "+n:"")),R.ERR_DEPRECATED);return n&&!Ut[i]&&(Ut[i]=!0),t?t(s,i,a):!0}};Ie.spelling=function(t){return(n,r)=>!0};function Uo(e,t,n){if(typeof e!="object")throw new R("options must be an object",R.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const a=e[s],u=a===void 0||i(a,s,e);if(u!==!0)throw new R("option "+s+" must be "+u,R.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new R("Unknown option "+s,R.ERR_BAD_OPTION)}}const we={assertOptions:Uo,validators:Ie},F=we.validators;class Se{constructor(t){this.defaults=t||{},this.interceptors={request:new At,response:new At}}request(t,n){return q(this,null,function*(){try{return yield this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch(i){}}throw r}})}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=V(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&we.assertOptions(r,{silentJSONParsing:F.transitional(F.boolean),forcedJSONParsing:F.transitional(F.boolean),clarifyTimeoutError:F.transitional(F.boolean)},!1),o!=null&&(f.isFunction(o)?n.paramsSerializer={serialize:o}:we.assertOptions(o,{encode:F.function,serialize:F.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),we.assertOptions(n,{baseUrl:F.spelling("baseURL"),withXsrfToken:F.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&f.merge(s.common,s[n.method]);s&&f.forEach(["delete","get","head","post","put","patch","common"],d=>{delete s[d]}),n.headers=I.concat(i,s);const a=[];let u=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(u=u&&g.synchronous,a.unshift(g.fulfilled,g.rejected))});const c=[];this.interceptors.response.forEach(function(g){c.push(g.fulfilled,g.rejected)});let l,p=0,m;if(!u){const d=[_t.bind(this),void 0];for(d.unshift.apply(d,a),d.push.apply(d,c),m=d.length,l=Promise.resolve(n);p<m;)l=l.then(d[p++],d[p++]);return l}m=a.length;let h=n;for(p=0;p<m;){const d=a[p++],g=a[p++];try{h=d(h)}catch(w){g.call(this,w);break}}try{l=_t.call(this,h)}catch(d){return Promise.reject(d)}for(p=0,m=c.length;p<m;)l=l.then(c[p++],c[p++]);return l}getUri(t){t=V(this.defaults,t);const n=cn(t.baseURL,t.url,t.allowAbsoluteUrls);return rn(n,t.params,t.paramsSerializer)}}f.forEach(["delete","get","head","options"],function(t){Se.prototype[t]=function(n,r){return this.request(V(r||{},{method:t,url:n,data:(r||{}).data}))}});f.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,a){return this.request(V(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}Se.prototype[t]=n(),Se.prototype[t+"Form"]=n(!0)});const ye=Se;class ht{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(a=>{r.subscribe(a),s=a}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,a){r.reason||(r.reason=new Z(s,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new ht(function(o){t=o}),cancel:t}}}const Fo=ht;function Do(e){return function(n){return e.apply(null,n)}}function ko(e){return f.isObject(e)&&e.isAxiosError===!0}const nt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(nt).forEach(([e,t])=>{nt[t]=e});const xo=nt;function gn(e){const t=new ye(e),n=Vt(ye.prototype.request,t);return f.extend(n,ye.prototype,t,{allOwnKeys:!0}),f.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return gn(V(e,o))},n}const N=gn(dt);N.Axios=ye;N.CanceledError=Z;N.CancelToken=Fo;N.isCancel=an;N.VERSION=pn;N.toFormData=Oe;N.AxiosError=R;N.Cancel=N.CanceledError;N.all=function(t){return Promise.all(t)};N.spread=Do;N.isAxiosError=ko;N.mergeConfig=V;N.AxiosHeaders=I;N.formToJSON=e=>sn(f.isHTMLForm(e)?new FormData(e):e);N.getAdapter=hn.getAdapter;N.HttpStatusCode=xo;N.default=N;const Ds=N;var _e={},vo=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},mn={},L={};let pt;const jo=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];L.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17};L.getSymbolTotalCodewords=function(t){return jo[t]};L.getBCHDigit=function(e){let t=0;for(;e!==0;)t++,e>>>=1;return t};L.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');pt=t};L.isKanjiModeEnabled=function(){return typeof pt!="undefined"};L.toSJIS=function(t){return pt(t)};var Ue={};(function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+n)}}e.isValid=function(r){return r&&typeof r.bit!="undefined"&&r.bit>=0&&r.bit<4},e.from=function(r,o){if(e.isValid(r))return r;try{return t(r)}catch(s){return o}}})(Ue);function wn(){this.buffer=[],this.length=0}wn.prototype={get:function(e){const t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)===1},put:function(e,t){for(let n=0;n<t;n++)this.putBit((e>>>t-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var Ho=wn;function ce(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}ce.prototype.set=function(e,t,n,r){const o=e*this.size+t;this.data[o]=n,r&&(this.reservedBit[o]=!0)};ce.prototype.get=function(e,t){return this.data[e*this.size+t]};ce.prototype.xor=function(e,t,n){this.data[e*this.size+t]^=n};ce.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var qo=ce,yn={};(function(e){const t=L.getSymbolSize;e.getRowColCoords=function(r){if(r===1)return[];const o=Math.floor(r/7)+2,s=t(r),i=s===145?26:Math.ceil((s-13)/(2*o-2))*2,a=[s-7];for(let u=1;u<o-1;u++)a[u]=a[u-1]-i;return a.push(6),a.reverse()},e.getPositions=function(r){const o=[],s=e.getRowColCoords(r),i=s.length;for(let a=0;a<i;a++)for(let u=0;u<i;u++)a===0&&u===0||a===0&&u===i-1||a===i-1&&u===0||o.push([s[a],s[u]]);return o}})(yn);var En={};const zo=L.getSymbolSize,Ft=7;En.getPositions=function(t){const n=zo(t);return[[0,0],[n-Ft,0],[0,n-Ft]]};var bn={};(function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(o){return o!=null&&o!==""&&!isNaN(o)&&o>=0&&o<=7},e.from=function(o){return e.isValid(o)?parseInt(o,10):void 0},e.getPenaltyN1=function(o){const s=o.size;let i=0,a=0,u=0,c=null,l=null;for(let p=0;p<s;p++){a=u=0,c=l=null;for(let m=0;m<s;m++){let h=o.get(p,m);h===c?a++:(a>=5&&(i+=t.N1+(a-5)),c=h,a=1),h=o.get(m,p),h===l?u++:(u>=5&&(i+=t.N1+(u-5)),l=h,u=1)}a>=5&&(i+=t.N1+(a-5)),u>=5&&(i+=t.N1+(u-5))}return i},e.getPenaltyN2=function(o){const s=o.size;let i=0;for(let a=0;a<s-1;a++)for(let u=0;u<s-1;u++){const c=o.get(a,u)+o.get(a,u+1)+o.get(a+1,u)+o.get(a+1,u+1);(c===4||c===0)&&i++}return i*t.N2},e.getPenaltyN3=function(o){const s=o.size;let i=0,a=0,u=0;for(let c=0;c<s;c++){a=u=0;for(let l=0;l<s;l++)a=a<<1&2047|o.get(c,l),l>=10&&(a===1488||a===93)&&i++,u=u<<1&2047|o.get(l,c),l>=10&&(u===1488||u===93)&&i++}return i*t.N3},e.getPenaltyN4=function(o){let s=0;const i=o.data.length;for(let u=0;u<i;u++)s+=o.data[u];return Math.abs(Math.ceil(s*100/i/5)-10)*t.N4};function n(r,o,s){switch(r){case e.Patterns.PATTERN000:return(o+s)%2===0;case e.Patterns.PATTERN001:return o%2===0;case e.Patterns.PATTERN010:return s%3===0;case e.Patterns.PATTERN011:return(o+s)%3===0;case e.Patterns.PATTERN100:return(Math.floor(o/2)+Math.floor(s/3))%2===0;case e.Patterns.PATTERN101:return o*s%2+o*s%3===0;case e.Patterns.PATTERN110:return(o*s%2+o*s%3)%2===0;case e.Patterns.PATTERN111:return(o*s%3+(o+s)%2)%2===0;default:throw new Error("bad maskPattern:"+r)}}e.applyMask=function(o,s){const i=s.size;for(let a=0;a<i;a++)for(let u=0;u<i;u++)s.isReserved(u,a)||s.xor(u,a,n(o,u,a))},e.getBestMask=function(o,s){const i=Object.keys(e.Patterns).length;let a=0,u=1/0;for(let c=0;c<i;c++){s(c),e.applyMask(c,o);const l=e.getPenaltyN1(o)+e.getPenaltyN2(o)+e.getPenaltyN3(o)+e.getPenaltyN4(o);e.applyMask(c,o),l<u&&(u=l,a=c)}return a}})(bn);var Fe={};const v=Ue,le=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],fe=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];Fe.getBlocksCount=function(t,n){switch(n){case v.L:return le[(t-1)*4+0];case v.M:return le[(t-1)*4+1];case v.Q:return le[(t-1)*4+2];case v.H:return le[(t-1)*4+3];default:return}};Fe.getTotalCodewordsCount=function(t,n){switch(n){case v.L:return fe[(t-1)*4+0];case v.M:return fe[(t-1)*4+1];case v.Q:return fe[(t-1)*4+2];case v.H:return fe[(t-1)*4+3];default:return}};var Sn={},De={};const se=new Uint8Array(512),Re=new Uint8Array(256);(function(){let t=1;for(let n=0;n<255;n++)se[n]=t,Re[t]=n,t<<=1,t&256&&(t^=285);for(let n=255;n<512;n++)se[n]=se[n-255]})();De.log=function(t){if(t<1)throw new Error("log("+t+")");return Re[t]};De.exp=function(t){return se[t]};De.mul=function(t,n){return t===0||n===0?0:se[Re[t]+Re[n]]};(function(e){const t=De;e.mul=function(r,o){const s=new Uint8Array(r.length+o.length-1);for(let i=0;i<r.length;i++)for(let a=0;a<o.length;a++)s[i+a]^=t.mul(r[i],o[a]);return s},e.mod=function(r,o){let s=new Uint8Array(r);for(;s.length-o.length>=0;){const i=s[0];for(let u=0;u<o.length;u++)s[u]^=t.mul(o[u],i);let a=0;for(;a<s.length&&s[a]===0;)a++;s=s.slice(a)}return s},e.generateECPolynomial=function(r){let o=new Uint8Array([1]);for(let s=0;s<r;s++)o=e.mul(o,new Uint8Array([1,t.exp(s)]));return o}})(Sn);const Rn=Sn;function gt(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}gt.prototype.initialize=function(t){this.degree=t,this.genPoly=Rn.generateECPolynomial(this.degree)};gt.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const n=new Uint8Array(t.length+this.degree);n.set(t);const r=Rn.mod(n,this.genPoly),o=this.degree-r.length;if(o>0){const s=new Uint8Array(this.degree);return s.set(r,o),s}return r};var $o=gt,Cn={},j={},mt={};mt.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40};var D={};const Tn="[0-9]+",Vo="[A-Z $%*+\\-./:]+";let ae="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";ae=ae.replace(/u/g,"\\u");const Jo="(?:(?![A-Z0-9 $%*+\\-./:]|"+ae+`)(?:.|[\r
]))+`;D.KANJI=new RegExp(ae,"g");D.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");D.BYTE=new RegExp(Jo,"g");D.NUMERIC=new RegExp(Tn,"g");D.ALPHANUMERIC=new RegExp(Vo,"g");const Ko=new RegExp("^"+ae+"$"),Wo=new RegExp("^"+Tn+"$"),Go=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");D.testKanji=function(t){return Ko.test(t)};D.testNumeric=function(t){return Wo.test(t)};D.testAlphanumeric=function(t){return Go.test(t)};(function(e){const t=mt,n=D;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(s,i){if(!s.ccBits)throw new Error("Invalid mode: "+s);if(!t.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?s.ccBits[0]:i<27?s.ccBits[1]:s.ccBits[2]},e.getBestModeForData=function(s){return n.testNumeric(s)?e.NUMERIC:n.testAlphanumeric(s)?e.ALPHANUMERIC:n.testKanji(s)?e.KANJI:e.BYTE},e.toString=function(s){if(s&&s.id)return s.id;throw new Error("Invalid mode")},e.isValid=function(s){return s&&s.bit&&s.ccBits};function r(o){if(typeof o!="string")throw new Error("Param is not a string");switch(o.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+o)}}e.from=function(s,i){if(e.isValid(s))return s;try{return r(s)}catch(a){return i}}})(j);(function(e){const t=L,n=Fe,r=Ue,o=j,s=mt,i=7973,a=t.getBCHDigit(i);function u(m,h,d){for(let g=1;g<=40;g++)if(h<=e.getCapacity(g,d,m))return g}function c(m,h){return o.getCharCountIndicator(m,h)+4}function l(m,h){let d=0;return m.forEach(function(g){const w=c(g.mode,h);d+=w+g.getBitsLength()}),d}function p(m,h){for(let d=1;d<=40;d++)if(l(m,d)<=e.getCapacity(d,h,o.MIXED))return d}e.from=function(h,d){return s.isValid(h)?parseInt(h,10):d},e.getCapacity=function(h,d,g){if(!s.isValid(h))throw new Error("Invalid QR Code version");typeof g=="undefined"&&(g=o.BYTE);const w=t.getSymbolTotalCodewords(h),y=n.getTotalCodewordsCount(h,d),b=(w-y)*8;if(g===o.MIXED)return b;const E=b-c(g,h);switch(g){case o.NUMERIC:return Math.floor(E/10*3);case o.ALPHANUMERIC:return Math.floor(E/11*2);case o.KANJI:return Math.floor(E/13);case o.BYTE:default:return Math.floor(E/8)}},e.getBestVersionForData=function(h,d){let g;const w=r.from(d,r.M);if(Array.isArray(h)){if(h.length>1)return p(h,w);if(h.length===0)return 1;g=h[0]}else g=h;return u(g.mode,g.getLength(),w)},e.getEncodedBits=function(h){if(!s.isValid(h)||h<7)throw new Error("Invalid QR Code version");let d=h<<12;for(;t.getBCHDigit(d)-a>=0;)d^=i<<t.getBCHDigit(d)-a;return h<<12|d}})(Cn);var An={};const rt=L,Nn=1335,Yo=21522,Dt=rt.getBCHDigit(Nn);An.getEncodedBits=function(t,n){const r=t.bit<<3|n;let o=r<<10;for(;rt.getBCHDigit(o)-Dt>=0;)o^=Nn<<rt.getBCHDigit(o)-Dt;return(r<<10|o)^Yo};var Bn={};const Qo=j;function W(e){this.mode=Qo.NUMERIC,this.data=e.toString()}W.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)};W.prototype.getLength=function(){return this.data.length};W.prototype.getBitsLength=function(){return W.getBitsLength(this.data.length)};W.prototype.write=function(t){let n,r,o;for(n=0;n+3<=this.data.length;n+=3)r=this.data.substr(n,3),o=parseInt(r,10),t.put(o,10);const s=this.data.length-n;s>0&&(r=this.data.substr(n),o=parseInt(r,10),t.put(o,s*3+1))};var Xo=W;const Zo=j,Je=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function G(e){this.mode=Zo.ALPHANUMERIC,this.data=e}G.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)};G.prototype.getLength=function(){return this.data.length};G.prototype.getBitsLength=function(){return G.getBitsLength(this.data.length)};G.prototype.write=function(t){let n;for(n=0;n+2<=this.data.length;n+=2){let r=Je.indexOf(this.data[n])*45;r+=Je.indexOf(this.data[n+1]),t.put(r,11)}this.data.length%2&&t.put(Je.indexOf(this.data[n]),6)};var es=G;const ts=j;function Y(e){this.mode=ts.BYTE,typeof e=="string"?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}Y.getBitsLength=function(t){return t*8};Y.prototype.getLength=function(){return this.data.length};Y.prototype.getBitsLength=function(){return Y.getBitsLength(this.data.length)};Y.prototype.write=function(e){for(let t=0,n=this.data.length;t<n;t++)e.put(this.data[t],8)};var ns=Y;const rs=j,os=L;function Q(e){this.mode=rs.KANJI,this.data=e}Q.getBitsLength=function(t){return t*13};Q.prototype.getLength=function(){return this.data.length};Q.prototype.getBitsLength=function(){return Q.getBitsLength(this.data.length)};Q.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let n=os.toSJIS(this.data[t]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);n=(n>>>8&255)*192+(n&255),e.put(n,13)}};var ss=Q,Pn={exports:{}};(function(e){var t={single_source_shortest_paths:function(n,r,o){var s={},i={};i[r]=0;var a=t.PriorityQueue.make();a.push(r,0);for(var u,c,l,p,m,h,d,g,w;!a.empty();){u=a.pop(),c=u.value,p=u.cost,m=n[c]||{};for(l in m)m.hasOwnProperty(l)&&(h=m[l],d=p+h,g=i[l],w=typeof i[l]=="undefined",(w||g>d)&&(i[l]=d,a.push(l,d),s[l]=c))}if(typeof o!="undefined"&&typeof i[o]=="undefined"){var y=["Could not find a path from ",r," to ",o,"."].join("");throw new Error(y)}return s},extract_shortest_path_from_predecessor_list:function(n,r){for(var o=[],s=r;s;)o.push(s),n[s],s=n[s];return o.reverse(),o},find_path:function(n,r,o){var s=t.single_source_shortest_paths(n,r,o);return t.extract_shortest_path_from_predecessor_list(s,o)},PriorityQueue:{make:function(n){var r=t.PriorityQueue,o={},s;n=n||{};for(s in r)r.hasOwnProperty(s)&&(o[s]=r[s]);return o.queue=[],o.sorter=n.sorter||r.default_sorter,o},default_sorter:function(n,r){return n.cost-r.cost},push:function(n,r){var o={value:n,cost:r};this.queue.push(o),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t})(Pn);var is=Pn.exports;(function(e){const t=j,n=Xo,r=es,o=ns,s=ss,i=D,a=L,u=is;function c(y){return unescape(encodeURIComponent(y)).length}function l(y,b,E){const S=[];let C;for(;(C=y.exec(E))!==null;)S.push({data:C[0],index:C.index,mode:b,length:C[0].length});return S}function p(y){const b=l(i.NUMERIC,t.NUMERIC,y),E=l(i.ALPHANUMERIC,t.ALPHANUMERIC,y);let S,C;return a.isKanjiModeEnabled()?(S=l(i.BYTE,t.BYTE,y),C=l(i.KANJI,t.KANJI,y)):(S=l(i.BYTE_KANJI,t.BYTE,y),C=[]),b.concat(E,S,C).sort(function(A,B){return A.index-B.index}).map(function(A){return{data:A.data,mode:A.mode,length:A.length}})}function m(y,b){switch(b){case t.NUMERIC:return n.getBitsLength(y);case t.ALPHANUMERIC:return r.getBitsLength(y);case t.KANJI:return s.getBitsLength(y);case t.BYTE:return o.getBitsLength(y)}}function h(y){return y.reduce(function(b,E){const S=b.length-1>=0?b[b.length-1]:null;return S&&S.mode===E.mode?(b[b.length-1].data+=E.data,b):(b.push(E),b)},[])}function d(y){const b=[];for(let E=0;E<y.length;E++){const S=y[E];switch(S.mode){case t.NUMERIC:b.push([S,{data:S.data,mode:t.ALPHANUMERIC,length:S.length},{data:S.data,mode:t.BYTE,length:S.length}]);break;case t.ALPHANUMERIC:b.push([S,{data:S.data,mode:t.BYTE,length:S.length}]);break;case t.KANJI:b.push([S,{data:S.data,mode:t.BYTE,length:c(S.data)}]);break;case t.BYTE:b.push([{data:S.data,mode:t.BYTE,length:c(S.data)}])}}return b}function g(y,b){const E={},S={start:{}};let C=["start"];for(let T=0;T<y.length;T++){const A=y[T],B=[];for(let M=0;M<A.length;M++){const U=A[M],ee=""+T+M;B.push(ee),E[ee]={node:U,lastCount:0},S[ee]={};for(let xe=0;xe<C.length;xe++){const k=C[xe];E[k]&&E[k].node.mode===U.mode?(S[k][ee]=m(E[k].lastCount+U.length,U.mode)-m(E[k].lastCount,U.mode),E[k].lastCount+=U.length):(E[k]&&(E[k].lastCount=U.length),S[k][ee]=m(U.length,U.mode)+4+t.getCharCountIndicator(U.mode,b))}}C=B}for(let T=0;T<C.length;T++)S[C[T]].end=0;return{map:S,table:E}}function w(y,b){let E;const S=t.getBestModeForData(y);if(E=t.from(b,S),E!==t.BYTE&&E.bit<S.bit)throw new Error('"'+y+'" cannot be encoded with mode '+t.toString(E)+`.
 Suggested mode is: `+t.toString(S));switch(E===t.KANJI&&!a.isKanjiModeEnabled()&&(E=t.BYTE),E){case t.NUMERIC:return new n(y);case t.ALPHANUMERIC:return new r(y);case t.KANJI:return new s(y);case t.BYTE:return new o(y)}}e.fromArray=function(b){return b.reduce(function(E,S){return typeof S=="string"?E.push(w(S,null)):S.data&&E.push(w(S.data,S.mode)),E},[])},e.fromString=function(b,E){const S=p(b,a.isKanjiModeEnabled()),C=d(S),T=g(C,E),A=u.find_path(T.map,"start","end"),B=[];for(let M=1;M<A.length-1;M++)B.push(T.table[A[M]].node);return e.fromArray(h(B))},e.rawSplit=function(b){return e.fromArray(p(b,a.isKanjiModeEnabled()))}})(Bn);const ke=L,Ke=Ue,as=Ho,us=qo,cs=yn,ls=En,ot=bn,st=Fe,fs=$o,Ce=Cn,ds=An,hs=j,We=Bn;function ps(e,t){const n=e.size,r=ls.getPositions(t);for(let o=0;o<r.length;o++){const s=r[o][0],i=r[o][1];for(let a=-1;a<=7;a++)if(!(s+a<=-1||n<=s+a))for(let u=-1;u<=7;u++)i+u<=-1||n<=i+u||(a>=0&&a<=6&&(u===0||u===6)||u>=0&&u<=6&&(a===0||a===6)||a>=2&&a<=4&&u>=2&&u<=4?e.set(s+a,i+u,!0,!0):e.set(s+a,i+u,!1,!0))}}function gs(e){const t=e.size;for(let n=8;n<t-8;n++){const r=n%2===0;e.set(n,6,r,!0),e.set(6,n,r,!0)}}function ms(e,t){const n=cs.getPositions(t);for(let r=0;r<n.length;r++){const o=n[r][0],s=n[r][1];for(let i=-2;i<=2;i++)for(let a=-2;a<=2;a++)i===-2||i===2||a===-2||a===2||i===0&&a===0?e.set(o+i,s+a,!0,!0):e.set(o+i,s+a,!1,!0)}}function ws(e,t){const n=e.size,r=Ce.getEncodedBits(t);let o,s,i;for(let a=0;a<18;a++)o=Math.floor(a/3),s=a%3+n-8-3,i=(r>>a&1)===1,e.set(o,s,i,!0),e.set(s,o,i,!0)}function Ge(e,t,n){const r=e.size,o=ds.getEncodedBits(t,n);let s,i;for(s=0;s<15;s++)i=(o>>s&1)===1,s<6?e.set(s,8,i,!0):s<8?e.set(s+1,8,i,!0):e.set(r-15+s,8,i,!0),s<8?e.set(8,r-s-1,i,!0):s<9?e.set(8,15-s-1+1,i,!0):e.set(8,15-s-1,i,!0);e.set(r-8,8,1,!0)}function ys(e,t){const n=e.size;let r=-1,o=n-1,s=7,i=0;for(let a=n-1;a>0;a-=2)for(a===6&&a--;;){for(let u=0;u<2;u++)if(!e.isReserved(o,a-u)){let c=!1;i<t.length&&(c=(t[i]>>>s&1)===1),e.set(o,a-u,c),s--,s===-1&&(i++,s=7)}if(o+=r,o<0||n<=o){o-=r,r=-r;break}}}function Es(e,t,n){const r=new as;n.forEach(function(u){r.put(u.mode.bit,4),r.put(u.getLength(),hs.getCharCountIndicator(u.mode,e)),u.write(r)});const o=ke.getSymbolTotalCodewords(e),s=st.getTotalCodewordsCount(e,t),i=(o-s)*8;for(r.getLengthInBits()+4<=i&&r.put(0,4);r.getLengthInBits()%8!==0;)r.putBit(0);const a=(i-r.getLengthInBits())/8;for(let u=0;u<a;u++)r.put(u%2?17:236,8);return bs(r,e,t)}function bs(e,t,n){const r=ke.getSymbolTotalCodewords(t),o=st.getTotalCodewordsCount(t,n),s=r-o,i=st.getBlocksCount(t,n),a=r%i,u=i-a,c=Math.floor(r/i),l=Math.floor(s/i),p=l+1,m=c-l,h=new fs(m);let d=0;const g=new Array(i),w=new Array(i);let y=0;const b=new Uint8Array(e.buffer);for(let A=0;A<i;A++){const B=A<u?l:p;g[A]=b.slice(d,d+B),w[A]=h.encode(g[A]),d+=B,y=Math.max(y,B)}const E=new Uint8Array(r);let S=0,C,T;for(C=0;C<y;C++)for(T=0;T<i;T++)C<g[T].length&&(E[S++]=g[T][C]);for(C=0;C<m;C++)for(T=0;T<i;T++)E[S++]=w[T][C];return E}function Ss(e,t,n,r){let o;if(Array.isArray(e))o=We.fromArray(e);else if(typeof e=="string"){let c=t;if(!c){const l=We.rawSplit(e);c=Ce.getBestVersionForData(l,n)}o=We.fromString(e,c||40)}else throw new Error("Invalid data");const s=Ce.getBestVersionForData(o,n);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(!t)t=s;else if(t<s)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+s+`.
`);const i=Es(t,n,o),a=ke.getSymbolSize(t),u=new us(a);return ps(u,t),gs(u),ms(u,t),Ge(u,n,0),t>=7&&ws(u,t),ys(u,i),isNaN(r)&&(r=ot.getBestMask(u,Ge.bind(null,u,n))),ot.applyMask(r,u),Ge(u,n,r),{modules:u,version:t,errorCorrectionLevel:n,maskPattern:r,segments:o}}mn.create=function(t,n){if(typeof t=="undefined"||t==="")throw new Error("No input text");let r=Ke.M,o,s;return typeof n!="undefined"&&(r=Ke.from(n.errorCorrectionLevel,Ke.M),o=Ce.from(n.version),s=ot.from(n.maskPattern),n.toSJISFunc&&ke.setToSJISFunction(n.toSJISFunc)),Ss(t,o,r,s)};var On={},wt={};(function(e){function t(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let r=n.slice().replace("#","").split("");if(r.length<3||r.length===5||r.length>8)throw new Error("Invalid hex color: "+n);(r.length===3||r.length===4)&&(r=Array.prototype.concat.apply([],r.map(function(s){return[s,s]}))),r.length===6&&r.push("F","F");const o=parseInt(r.join(""),16);return{r:o>>24&255,g:o>>16&255,b:o>>8&255,a:o&255,hex:"#"+r.slice(0,6).join("")}}e.getOptions=function(r){r||(r={}),r.color||(r.color={});const o=typeof r.margin=="undefined"||r.margin===null||r.margin<0?4:r.margin,s=r.width&&r.width>=21?r.width:void 0,i=r.scale||4;return{width:s,scale:s?4:i,margin:o,color:{dark:t(r.color.dark||"#000000ff"),light:t(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},e.getScale=function(r,o){return o.width&&o.width>=r+o.margin*2?o.width/(r+o.margin*2):o.scale},e.getImageWidth=function(r,o){const s=e.getScale(r,o);return Math.floor((r+o.margin*2)*s)},e.qrToImageData=function(r,o,s){const i=o.modules.size,a=o.modules.data,u=e.getScale(i,s),c=Math.floor((i+s.margin*2)*u),l=s.margin*u,p=[s.color.light,s.color.dark];for(let m=0;m<c;m++)for(let h=0;h<c;h++){let d=(m*c+h)*4,g=s.color.light;if(m>=l&&h>=l&&m<c-l&&h<c-l){const w=Math.floor((m-l)/u),y=Math.floor((h-l)/u);g=p[a[w*i+y]?1:0]}r[d++]=g.r,r[d++]=g.g,r[d++]=g.b,r[d]=g.a}}})(wt);(function(e){const t=wt;function n(o,s,i){o.clearRect(0,0,s.width,s.height),s.style||(s.style={}),s.height=i,s.width=i,s.style.height=i+"px",s.style.width=i+"px"}function r(){try{return document.createElement("canvas")}catch(o){throw new Error("You need to specify a canvas element")}}e.render=function(s,i,a){let u=a,c=i;typeof u=="undefined"&&(!i||!i.getContext)&&(u=i,i=void 0),i||(c=r()),u=t.getOptions(u);const l=t.getImageWidth(s.modules.size,u),p=c.getContext("2d"),m=p.createImageData(l,l);return t.qrToImageData(m.data,s,u),n(p,c,l),p.putImageData(m,0,0),c},e.renderToDataURL=function(s,i,a){let u=a;typeof u=="undefined"&&(!i||!i.getContext)&&(u=i,i=void 0),u||(u={});const c=e.render(s,i,u),l=u.type||"image/png",p=u.rendererOpts||{};return c.toDataURL(l,p.quality)}})(On);var Ln={};const Rs=wt;function kt(e,t){const n=e.a/255,r=t+'="'+e.hex+'"';return n<1?r+" "+t+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function Ye(e,t,n){let r=e+t;return typeof n!="undefined"&&(r+=" "+n),r}function Cs(e,t,n){let r="",o=0,s=!1,i=0;for(let a=0;a<e.length;a++){const u=Math.floor(a%t),c=Math.floor(a/t);!u&&!s&&(s=!0),e[a]?(i++,a>0&&u>0&&e[a-1]||(r+=s?Ye("M",u+n,.5+c+n):Ye("m",o,0),o=0,s=!1),u+1<t&&e[a+1]||(r+=Ye("h",i),i=0)):o++}return r}Ln.render=function(t,n,r){const o=Rs.getOptions(n),s=t.modules.size,i=t.modules.data,a=s+o.margin*2,u=o.color.light.a?"<path "+kt(o.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",c="<path "+kt(o.color.dark,"stroke")+' d="'+Cs(i,s,o.margin)+'"/>',l='viewBox="0 0 '+a+" "+a+'"',m='<svg xmlns="http://www.w3.org/2000/svg" '+(o.width?'width="'+o.width+'" height="'+o.width+'" ':"")+l+' shape-rendering="crispEdges">'+u+c+`</svg>
`;return typeof r=="function"&&r(null,m),m};const Ts=vo,it=mn,Mn=On,As=Ln;function yt(e,t,n,r,o){const s=[].slice.call(arguments,1),i=s.length,a=typeof s[i-1]=="function";if(!a&&!Ts())throw new Error("Callback required as last argument");if(a){if(i<2)throw new Error("Too few arguments provided");i===2?(o=n,n=t,t=r=void 0):i===3&&(t.getContext&&typeof o=="undefined"?(o=r,r=void 0):(o=r,r=n,n=t,t=void 0))}else{if(i<1)throw new Error("Too few arguments provided");return i===1?(n=t,t=r=void 0):i===2&&!t.getContext&&(r=n,n=t,t=void 0),new Promise(function(u,c){try{const l=it.create(n,r);u(e(l,t,r))}catch(l){c(l)}})}try{const u=it.create(n,r);o(null,e(u,t,r))}catch(u){o(u)}}_e.create=it.create;_e.toCanvas=yt.bind(null,Mn.render);_e.toDataURL=yt.bind(null,Mn.renderToDataURL);_e.toString=yt.bind(null,function(e,t,n){return As.render(e,n)});export{nr as a,Fs as b,Ps as c,pe as d,Ms as e,Gn as f,Os as g,_e as h,Xn as i,Ls as j,Us as k,_s as l,Ds as m,Is as t,ne as u};
