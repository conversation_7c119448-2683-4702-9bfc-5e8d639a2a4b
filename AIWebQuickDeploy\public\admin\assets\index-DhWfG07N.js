
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{P as e,at as t,aV as n,aW as l,a0 as a,aX as i,$ as u,aY as o,aZ as r,a_ as s,a$ as c,b0 as v,b1 as d,b as f,b2 as p,b3 as w,i as m,b4 as h}from"./index-BERX8Mlm.js";const g=s?window:void 0,b=s?window.document:void 0,y=s?window.navigator:void 0;function S(e){var t;const n=o(e);return null!=(t=null==n?void 0:n.$el)?t:n}function F(...t){const l=[],a=()=>{l.forEach((e=>e())),l.length=0},i=e((()=>{const e=c(o(t[0])).filter((e=>null!=e));return e.every((e=>"string"!=typeof e))?e:void 0})),u=v((()=>{var e,n;return[null!=(n=null==(e=i.value)?void 0:e.map((e=>S(e))))?n:[g].filter((e=>null!=e)),c(o(i.value?t[1]:t[0])),c(m(i.value?t[2]:t[1])),o(i.value?t[3]:t[2])]}),(([e,t,n,i])=>{if(a(),!(null==e?void 0:e.length)||!(null==t?void 0:t.length)||!(null==n?void 0:n.length))return;const u=w(i)?{...i}:i;l.push(...e.flatMap((e=>t.flatMap((t=>n.map((n=>((e,t,n,l)=>(e.addEventListener(t,n,l),()=>e.removeEventListener(t,n,l)))(e,t,n,u))))))))}),{flush:"post"});return n(a),()=>{u(),a()}}function x(n){const l=function(){const e=t(!1),n=p();return n&&f((()=>{e.value=!0}),n),e}();return e((()=>(l.value,Boolean(n()))))}function E(e,n={}){const{controls:l=!1,navigator:a=y}=n,i=x((()=>a&&"permissions"in a)),u=t(),o="string"==typeof e?{name:e}:e,r=t(),s=()=>{var e,t;r.value=null!=(t=null==(e=u.value)?void 0:e.state)?t:"prompt"};F(u,"change",s,{passive:!0});const c=d((async()=>{if(i.value){if(!u.value)try{u.value=await a.permissions.query(o)}catch(e){u.value=void 0}finally{s()}return l?h(u.value):void 0}}));return c(),l?{state:r,isSupported:i,query:c}:r}function k(n={}){const{navigator:l=y,read:a=!1,source:i,copiedDuring:u=1500,legacy:s=!1}=n,c=x((()=>l&&"clipboard"in l)),v=E("clipboard-read"),d=E("clipboard-write"),f=e((()=>c.value||s)),p=t(""),w=t(!1),m=r((()=>w.value=!1),u,{immediate:!1});function h(e){return"granted"===e||"prompt"===e}return f.value&&a&&F(["copy","cut"],(async function(){let e=!(c.value&&h(v.value));if(!e)try{p.value=await l.clipboard.readText()}catch(i){e=!0}var t,n,a;e&&(p.value=null!=(a=null==(n=null==(t=null==document?void 0:document.getSelection)?void 0:t.call(document))?void 0:n.toString())?a:"")}),{passive:!0}),{isSupported:f,text:p,copied:w,copy:async function(e=o(i)){if(f.value&&null!=e){let n=!(c.value&&h(d.value));if(!n)try{await l.clipboard.writeText(e)}catch(t){n=!0}n&&function(e){const t=document.createElement("textarea");t.value=null!=e?e:"",t.style.position="absolute",t.style.opacity="0",document.body.appendChild(t),t.select(),document.execCommand("copy"),t.remove()}(e),p.value=e,w.value=!0,m.start()}}}}function z(i,u={width:0,height:0},r={}){const{window:s=g,box:v="content-box"}=r,d=e((()=>{var e,t;return null==(t=null==(e=S(i))?void 0:e.namespaceURI)?void 0:t.includes("svg")})),f=t(u.width),p=t(u.height),{stop:w}=function(t,l,i={}){const{window:u=g,...r}=i;let s;const c=x((()=>u&&"ResizeObserver"in u)),v=()=>{s&&(s.disconnect(),s=void 0)},d=e((()=>{const e=o(t);return Array.isArray(e)?e.map((e=>S(e))):[S(e)]})),f=a(d,(e=>{if(v(),c.value&&u){s=new ResizeObserver(l);for(const t of e)t&&s.observe(t,r)}}),{immediate:!0,flush:"post"}),p=()=>{v(),f()};return n(p),{isSupported:c,stop:p}}(i,(([e])=>{const t="border-box"===v?e.borderBoxSize:"content-box"===v?e.contentBoxSize:e.devicePixelContentBoxSize;if(s&&d.value){const e=S(i);if(e){const t=e.getBoundingClientRect();f.value=t.width,p.value=t.height}}else if(t){const e=c(t);f.value=e.reduce(((e,{inlineSize:t})=>e+t),0),p.value=e.reduce(((e,{blockSize:t})=>e+t),0)}else f.value=e.contentRect.width,p.value=e.contentRect.height}),r);l((()=>{const e=S(i);e&&(f.value="offsetWidth"in e?e.offsetWidth:u.width,p.value="offsetHeight"in e?e.offsetHeight:u.height)}));const m=a((()=>S(i)),(e=>{f.value=e?u.width:0,p.value=e?u.height:0}));return{width:f,height:p,stop:function(){w(),m()}}}const R=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function C(l,a={}){const{document:i=b,autoExit:u=!1}=a,o=e((()=>{var e;return null!=(e=S(l))?e:null==i?void 0:i.documentElement})),r=t(!1),s=e((()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find((e=>i&&e in i||o.value&&e in o.value)))),c=e((()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find((e=>i&&e in i||o.value&&e in o.value)))),v=e((()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find((e=>i&&e in i||o.value&&e in o.value)))),d=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find((e=>i&&e in i)),f=x((()=>o.value&&i&&void 0!==s.value&&void 0!==c.value&&void 0!==v.value)),p=()=>{if(v.value){if(i&&null!=i[v.value])return i[v.value];{const e=o.value;if(null!=(null==e?void 0:e[v.value]))return Boolean(e[v.value])}}return!1};async function w(){if(f.value&&r.value){if(c.value)if(null!=(null==i?void 0:i[c.value]))await i[c.value]();else{const e=o.value;null!=(null==e?void 0:e[c.value])&&await e[c.value]()}r.value=!1}}async function m(){if(!f.value||r.value)return;p()&&await w();const e=o.value;s.value&&null!=(null==e?void 0:e[s.value])&&(await e[s.value](),r.value=!0)}const h=()=>{const e=p();(!e||e&&d&&(null==i?void 0:i[d])===o.value)&&(r.value=e)},g={capture:!1,passive:!0};return F(i,R,h,g),F((()=>S(o)),R,h,g),u&&n(w),{isSupported:f,isFullscreen:r,enter:m,exit:w,toggle:async function(){await(r.value?w():m())}}}const B={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function q(n={}){const{reactive:l=!1,target:a=g,aliasMap:r=B,passive:s=!0,onEventFired:c=i}=n,v=u(new Set),d={toJSON:()=>({}),current:v},f=l?u(d):d,p=new Set,w=new Set;function m(e,t){e in f&&(l?f[e]=t:f[e].value=t)}function h(){v.clear();for(const e of w)m(e,!1)}function b(e,t){var n,l;const a=null==(n=e.key)?void 0:n.toLowerCase(),i=[null==(l=e.code)?void 0:l.toLowerCase(),a].filter(Boolean);a&&(t?v.add(a):v.delete(a));for(const u of i)w.add(u),m(u,t);"meta"!==a||t?"function"==typeof e.getModifierState&&e.getModifierState("Meta")&&t&&[...v,...i].forEach((e=>p.add(e))):(p.forEach((e=>{v.delete(e),m(e,!1)})),p.clear())}F(a,"keydown",(e=>(b(e,!0),c(e))),{passive:s}),F(a,"keyup",(e=>(b(e,!1),c(e))),{passive:s}),F("blur",h,{passive:s}),F("focus",h,{passive:s});const y=new Proxy(f,{get(n,a,i){if("string"!=typeof a)return Reflect.get(n,a,i);if((a=a.toLowerCase())in r&&(a=r[a]),!(a in f))if(/[+_-]/.test(a)){const t=a.split(/[+_-]/g).map((e=>e.trim()));f[a]=e((()=>t.map((e=>o(y[e]))).every(Boolean)))}else f[a]=t(!1);const u=Reflect.get(n,a,i);return l?o(u):u}});return y}export{k as a,q as b,C as c,z as u};
