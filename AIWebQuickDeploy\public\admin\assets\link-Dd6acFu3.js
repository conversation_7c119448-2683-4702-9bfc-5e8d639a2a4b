
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{d as t,af as a,c as s,e as l,f as i,w as n,a2 as o,i as r,g as c,_ as m,t as p,j as u,h as f,aB as x,Z as _}from"./index-BERX8Mlm.js";import{_ as d}from"./HTooltip.vue_vue_type_script_setup_true_lang-T8XkjmIi.js";import{a as k}from"./index-DhWfG07N.js";const v={class:"absolute h-full w-full flex flex-col"},y={class:"flex flex-col items-center"},w={class:"line-clamp-3"},j=_(t({name:"Link<PERSON>iew",__name:"link",setup(t){const _=a(),{copy:j}=k();function h(){window.open(_.meta.link,"_blank")}return(t,a)=>{const k=m,g=d,b=u,C=e;return l(),s("div",v,[i(x,{name:"slide-right",mode:"out-in",appear:""},{default:n((()=>[(l(),o(C,{key:r(_).meta.link,class:"flex flex-1 flex-col justify-center"},{default:n((()=>[c("div",y,[i(k,{name:"i-icon-park-twotone:planet",size:120,class:"text-ui-primary/80"}),a[2]||(a[2]=c("div",{class:"my-2 text-xl text-dark dark-text-white"},"是否访问此链接",-1)),c("div",{class:"my-2 max-w-[300px] cursor-pointer text-center text-[14px] text-stone-5",onClick:a[0]||(a[0]=e=>r(_).meta.link&&r(j)(r(_).meta.link))},[i(g,{text:"复制链接"},{default:n((()=>[c("div",w,p(r(_).meta.link),1)])),_:1})]),i(b,{class:"my-4",onClick:h},{default:n((()=>[i(k,{name:"i-ri:external-link-fill"}),a[1]||(a[1]=f(" 立即访问 "))])),_:1})])])),_:1}))])),_:1})])}}}),[["__scopeId","data-v-ad5909bf"]]);export{j as default};
