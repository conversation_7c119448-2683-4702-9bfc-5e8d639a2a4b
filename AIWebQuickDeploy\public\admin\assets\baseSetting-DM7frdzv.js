
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as r,P as i,Q as o,c as s,e as n,f as d,w as u,j as p,h as m,_ as g,g as c,T as h,t as b,V as y,W as f,i as v,ah as x,a2 as _,Y as U,k as T}from"./index-BERX8Mlm.js";import{a as K}from"./config-BrbFL53_.js";const V={class:"font-bold text-lg mb-4"},k={key:0,class:"text-xs text-gray-400 mt-1"},M={class:"font-bold text-lg mt-8 mb-4"},w={key:0,class:"text-xs text-gray-400 mt-1"},B={class:"font-bold text-lg mt-8 mb-4"},A={class:"font-bold text-lg mt-8 mb-4"},q=l({__name:"baseSetting",setup(l){const T=a({openaiBaseUrl:"",openaiBaseKey:"",deepThinkingModel:"deepseek-reasoner",deepThinkingUrl:"",deepThinkingKey:"",openaiBaseModel:"gpt-4o-mini",isGeneratePromptReference:0,isConvertToBase64:0,openaiVoice:"",systemPreMessage:"",isModelInherited:1,pluginUrl:"",pluginKey:"",openaiTemperature:1,vectorUrl:"",vectorKey:"",vectorModel:"text-embedding-3-small",vectorAnalysisThreshold:1e3,maxUrlTextLength:1e5,toolCallUrl:"",toolCallKey:"",toolCallModel:"",imageAnalysisUrl:"",imageAnalysisKey:"",imageAnalysisModel:""}),q=[{value:"https://api.deepseek.com",label:"【DeepSeek 官方】https://api.deepseek.com"},{value:"https://dashscope.aliyuncs.com/compatible-mode",label:"【阿里云百炼】https://dashscope.aliyuncs.com/compatible-mode"},{value:"https://api.lkeap.cloud.tencent.com",label:"【腾讯云知识引擎】https://api.lkeap.cloud.tencent.com"},{value:"https://api.lightai.io",label:"【LightAI API】https://api.lightai.io"},{value:"",label:"【其他】填写后选择"}],C=t([{label:"Alloy",value:"alloy"},{label:"Echo",value:"echo"},{label:"Fable",value:"fable"},{label:"Onyx",value:"onyx"},{label:"Nova",value:"nova"},{label:"Shimmer",value:"shimmer"}]),P=[{value:"https://open.bigmodel.cn/api/paas/v4/tools",label:"【智谱 web-search-pro】"},{value:"https://api.bochaai.com/v1/web-search",label:"【博查 web-search】"},{value:"https://api.tavily.com/search",label:"【Tavily 1000 次/月（免费）】"}],I=t({openaiBaseUrl:[{required:!0,trigger:"blur",message:"请填写 AI 的请求地址"}],openaiBaseKey:[{required:!0,trigger:"blur",message:"请填写模型全局 Key"}],openaiBaseModel:[{required:!0,trigger:"blur",message:"请填写全局模型，用于后台一些静默性赋能操作"}],isGeneratePromptReference:[{required:!1,trigger:"blur",message:"是否生成提示词参考"}],isModelInherited:[{required:!1,trigger:"blur",message:"是否继承模型"}],pluginUrl:[{required:!1,trigger:"blur",message:"请填写联网插件地址"}],pluginKey:[{required:!1,trigger:"blur",message:"请填写联网插件 Key"}],openaiTemperature:[{required:!1,trigger:"blur",message:"请填写模型 Temperature 设置，默认1"}],deepThinkingUrl:[{required:!1,trigger:"blur",message:"请填写深度思考模型地址"}],deepThinkingKey:[{required:!1,trigger:"blur",message:"请填写深度思考模型 Key"}],deepThinkingModel:[{required:!1,trigger:"blur",message:"请填写深度思考模型名称"}],vectorUrl:[{required:!1,trigger:"blur",message:"请填写向量模型地址"}],vectorKey:[{required:!1,trigger:"blur",message:"请填写向量模型 Key"}],vectorModel:[{required:!1,trigger:"blur",message:"请填写向量模型名称"}],vectorAnalysisThreshold:[{required:!1,trigger:"blur",message:"请填写文件启用向量分析阈值"}],maxUrlTextLength:[{required:!1,trigger:"blur",message:"请填写文件最大字符限制"}],toolCallUrl:[{required:!1,trigger:"blur",message:"请填写工具调用模型地址"}],toolCallKey:[{required:!1,trigger:"blur",message:"请填写工具调用模型 Key"}],toolCallModel:[{required:!1,trigger:"blur",message:"请填写工具调用模型名称"}],imageAnalysisUrl:[{required:!1,trigger:"blur",message:"请填写图片解析模型地址"}],imageAnalysisKey:[{required:!1,trigger:"blur",message:"请填写图片解析模型 Key"}],imageAnalysisModel:[{required:!1,trigger:"blur",message:"请填写图片解析模型名称"}]}),R=t();async function G(){const e=await K.queryConfig({keys:["openaiBaseUrl","openaiBaseKey","openaiBaseModel","systemPreMessage","isGeneratePromptReference","isConvertToBase64","openaiVoice,","isModelInherited","pluginUrl","pluginKey","openaiTemperature","deepThinkingUrl","deepThinkingKey","deepThinkingModel","vectorUrl","vectorKey","vectorModel","vectorAnalysisThreshold","maxUrlTextLength","openaiVoice","toolCallUrl","toolCallKey","toolCallModel","imageAnalysisUrl","imageAnalysisKey","imageAnalysisModel"]}),{openaiBaseUrl:l="",openaiBaseKey:a="",openaiBaseModel:t="gpt-4o-mini",isGeneratePromptReference:r=0,systemPreMessage:i,pluginUrl:o,pluginKey:s,openaiTemperature:n=1,deepThinkingUrl:d,deepThinkingKey:u,deepThinkingModel:p,isModelInherited:m,vectorUrl:g,vectorKey:c,isConvertToBase64:h,openaiVoice:b,vectorModel:y="text-embedding-3-small",vectorAnalysisThreshold:f=1e4,maxUrlTextLength:v=5e5,toolCallUrl:x,toolCallKey:_,toolCallModel:U="",imageAnalysisUrl:V,imageAnalysisKey:k,imageAnalysisModel:M=""}=e.data;Object.assign(T,{openaiBaseUrl:l,openaiBaseKey:a,isGeneratePromptReference:r,isConvertToBase64:h,openaiVoice:b,openaiBaseModel:t,systemPreMessage:i,pluginUrl:o,pluginKey:s,openaiTemperature:n,deepThinkingKey:u,deepThinkingUrl:d,deepThinkingModel:p,isModelInherited:m,vectorUrl:g,vectorKey:c,vectorModel:y,vectorAnalysisThreshold:f,maxUrlTextLength:v,toolCallUrl:x,toolCallKey:_,toolCallModel:U,imageAnalysisUrl:V,imageAnalysisKey:k,imageAnalysisModel:M})}function j(){var e;null==(e=R.value)||e.validate((async e=>{if(e){try{await K.setConfig({settings:(l=T,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),U.success("变更配置信息成功")}catch(a){}G()}else U.error("请填写完整信息");var l}))}r((()=>{G()}));const L=e=>{if(!e)return"";let l=e.trim();return l.endsWith("/")&&(l=l.slice(0,-1)),/\/v\d+(?:beta|alpha)?/.test(l)?l:`${l}/v1`},S=i((()=>L(T.openaiBaseUrl))),O=i((()=>L(T.deepThinkingUrl)));return i((()=>L(T.vectorUrl))),i((()=>L(T.toolCallUrl))),i((()=>L(T.imageAnalysisUrl))),i((()=>L(T.pluginUrl))),(l,a)=>{const t=g,r=p,i=e,U=o("el-divider"),K=o("el-input"),G=o("el-form-item"),L=o("el-col"),W=o("el-row"),$=o("el-option"),D=o("el-select"),E=o("el-switch"),F=o("el-icon"),N=o("el-tooltip"),Q=o("el-input-number"),Y=o("el-form"),z=o("el-card");return n(),s("div",null,[d(i,null,{title:u((()=>a[14]||(a[14]=[c("div",{class:"flex items-center gap-4"},"基础设置",-1)]))),content:u((()=>a[15]||(a[15]=[c("div",{class:"text-sm/6"},[c("div",null," 全局配置用于对话标题生成、生成提问建议、提示词翻译等内置操作。模型不配置 Url 或 Key 时，也会使用全局配置。 "),c("div",null,[c("strong",null,"重要提示："),m("向量模型、工具调用模型、图片解析模型和深度思考模型如果没有单独设置，系统将默认使用全局模型的地址和 Key，请确保全局配置正确填写。 ")]),c("div",null,[m(" API 中转推荐 "),c("a",{href:"https://api.lightai.io",target:"_blank",style:{"margin-right":"5px"}},"https://api.lightai.io"),m("，提供 AI 接口聚合管理服务，一站式接入各种 AI 模型，无强制绑定关系，可按需选择。 ")]),c("div",null,"深度思考模型用于模型的深度思考，需在模型配置中开启深度思考模式。"),c("div",null,[m(" 联网插件已支持多种方式： "),c("a",{href:"https://bigmodel.cn",target:"_blank"},"智谱 web-search-pro"),m("、 "),c("a",{href:"https://open.bochaai.com",target:"_blank"},"博查 web-search"),m("、 "),c("a",{href:"https://app.tavily.com/home",target:"_blank"},"Tavily"),m(" 需自行登录以上网站，获取对应的 Key（多个Key用英文逗号隔开）。 ")])],-1)]))),default:u((()=>[d(r,{text:"",outline:"",onClick:j},{default:u((()=>[d(t,{name:"i-ri:file-text-line"}),a[16]||(a[16]=m(" 保存设置 "))])),_:1})])),_:1}),d(z,{style:{margin:"20px"}},{default:u((()=>[d(Y,{ref_key:"formRef",ref:R,rules:I.value,model:T,"label-width":"220px"},{default:u((()=>[c("h3",V,[a[17]||(a[17]=m("基础配置 ")),d(U)]),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"全局地址",prop:"openaiBaseUrl","label-width":"120px",required:""},{default:u((()=>[d(K,{modelValue:T.openaiBaseUrl,"onUpdate:modelValue":a[0]||(a[0]=e=>T.openaiBaseUrl=e),placeholder:"例如 https://api.openai.com，未显式指定 /v1 等版本时将自动添加 /v1",clearable:""},null,8,["modelValue"]),S.value?(n(),s("div",k," 实际调用地址："+b(S.value),1)):h("",!0)])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"全局 Key",prop:"openaiBaseKey","label-width":"120px",required:""},{default:u((()=>[d(K,{modelValue:T.openaiBaseKey,"onUpdate:modelValue":a[1]||(a[1]=e=>T.openaiBaseKey=e),placeholder:"请填写模型全局 Key 信息，当模型 Key 为空时调用",type:"password","show-password":"",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"全局模型",prop:"openaiBaseModel","label-width":"120px",required:""},{default:u((()=>[d(K,{modelValue:T.openaiBaseModel,"onUpdate:modelValue":a[2]||(a[2]=e=>T.openaiBaseModel=e),placeholder:"全局模型配置，用于后台一些静默赋能操作",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c("h3",M,[a[18]||(a[18]=m("深度思考配置 ")),d(U)]),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"深度思考地址",prop:"deepThinkingUrl","label-width":"120px"},{default:u((()=>[d(D,{modelValue:T.deepThinkingUrl,"onUpdate:modelValue":a[3]||(a[3]=e=>T.deepThinkingUrl=e),placeholder:"选择或输入地址，未指定 /v1 等版本时将自动添加 /v1",clearable:"",filterable:"","allow-create":""},{default:u((()=>[(n(),s(y,null,f(q,(e=>d($,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),O.value?(n(),s("div",w," 实际调用地址："+b(O.value),1)):h("",!0)])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"深度思考 Key",prop:"deepThinkingKey","label-width":"120px"},{default:u((()=>[d(K,{modelValue:T.deepThinkingKey,"onUpdate:modelValue":a[4]||(a[4]=e=>T.deepThinkingKey=e),placeholder:"请填写深度思考模型 Key",type:"password","show-password":"",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"深度思考模型",prop:"deepThinkingModel","label-width":"120px"},{default:u((()=>[d(K,{modelValue:T.deepThinkingModel,"onUpdate:modelValue":a[5]||(a[5]=e=>T.deepThinkingModel=e),placeholder:"请选择深度思考模型",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c("h3",B,[a[19]||(a[19]=m("联网配置 ")),d(U)]),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"联网搜索地址","label-width":"120",prop:"pluginUrl"},{default:u((()=>[d(D,{modelValue:T.pluginUrl,"onUpdate:modelValue":a[6]||(a[6]=e=>T.pluginUrl=e),placeholder:"请选择或输入联网搜索使用的地址",clearable:"",filterable:"","allow-create":""},{default:u((()=>[(n(),s(y,null,f(P,(e=>d($,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"联网搜索 Key","label-width":"120",prop:"pluginKey"},{default:u((()=>[d(K,{modelValue:T.pluginKey,"onUpdate:modelValue":a[7]||(a[7]=e=>T.pluginKey=e),placeholder:"插件 Key",clearable:"",password:"","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),c("h3",A,[a[20]||(a[20]=m("其他配置 ")),d(U)]),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"继承对话模型",prop:"isModelInherited","label-width":"120"},{default:u((()=>[d(E,{modelValue:T.isModelInherited,"onUpdate:modelValue":a[8]||(a[8]=e=>T.isModelInherited=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),d(N,{class:"box-item",effect:"dark",placement:"right"},{content:u((()=>a[21]||(a[21]=[c("div",{style:{width:"250px"}},[c("p",null,"开启后，新建对话模型将继承上一次对话所使用的模型")],-1)]))),default:u((()=>[d(F,{class:"ml-3 cursor-pointer"},{default:u((()=>[d(v(x))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"生成提问建议",prop:"isGeneratePromptReference","label-width":"120"},{default:u((()=>[d(E,{modelValue:T.isGeneratePromptReference,"onUpdate:modelValue":a[9]||(a[9]=e=>T.isGeneratePromptReference=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),d(N,{class:"box-item",effect:"dark",placement:"right"},{content:u((()=>a[22]||(a[22]=[c("div",{style:{width:"250px"}},[c("p",null,"开启后，将使用全局模型在每次对话后，生成提问建议")],-1)]))),default:u((()=>[d(F,{class:"ml-3 cursor-pointer"},{default:u((()=>[d(v(x))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"Base64 识图",prop:"isConvertToBase64","label-width":"120"},{default:u((()=>[d(E,{modelValue:T.isConvertToBase64,"onUpdate:modelValue":a[10]||(a[10]=e=>T.isConvertToBase64=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),d(N,{class:"box-item",effect:"dark",placement:"right"},{content:u((()=>a[23]||(a[23]=[c("div",{style:{width:"250px"}},[c("p",null," 开启后，识图时将使用 base64 格式，对于本地/存储桶 链接 API 端无法访问时建议开启 ")],-1)]))),default:u((()=>[d(F,{class:"ml-3 cursor-pointer"},{default:u((()=>[d(v(x))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"TTS 音色",prop:"openaiVoice","label-width":"120px"},{default:u((()=>[d(D,{modelValue:T.openaiVoice,"onUpdate:modelValue":a[11]||(a[11]=e=>T.openaiVoice=e),placeholder:"选择或输入 openai 语音合成的默认发音人",clearable:"",filterable:"","allow-create":""},{default:u((()=>[(n(!0),s(y,null,f(C.value,(e=>(n(),_($,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"Temperature",prop:"openaiTemperature","label-width":"120px"},{default:u((()=>[d(Q,{modelValue:T.openaiTemperature,"onUpdate:modelValue":a[12]||(a[12]=e=>T.openaiTemperature=e),"controls-position":"right",min:0,max:2,step:.1,placeholder:"模型 Temperature 设置，默认1",clearable:""},null,8,["modelValue"]),d(N,{class:"box-item",effect:"dark",placement:"right"},{content:u((()=>a[24]||(a[24]=[c("div",{style:{width:"250px"}},[c("p",null,"模型 Temperature 设置，一般情况无需调整")],-1)]))),default:u((()=>[d(F,{class:"ml-3 cursor-pointer"},{default:u((()=>[d(v(x))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),d(W,null,{default:u((()=>[d(L,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[d(G,{label:"全局头部预设",prop:"systemPreMessage","label-width":"120px"},{default:u((()=>[d(K,{modelValue:T.systemPreMessage,"onUpdate:modelValue":a[13]||(a[13]=e=>T.systemPreMessage=e),type:"textarea",rows:8,placeholder:"请填写模型全局头部预设信息！",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof T&&T(q);export{q as default};
