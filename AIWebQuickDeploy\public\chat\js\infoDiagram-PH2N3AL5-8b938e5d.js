var g=(e,t,a)=>new Promise((o,s)=>{var d=r=>{try{i(a.next(r))}catch(n){s(n)}},m=r=>{try{i(a.throw(r))}catch(n){s(n)}},i=r=>r.done?o(r.value):Promise.resolve(r.value).then(d,m);i((a=a.apply(e,t)).next())});import{_ as p,l as v,I as c,k as l,K as f}from"./chart-vendor-e1d59b84.js";import{p as u}from"./mermaid-parser.core-a05879fd.js";import"./utils-vendor-c35799af.js";import"./vue-vendor-d751b0f5.js";import"./_baseUniq-5ee25ed9.js";import"./_basePickBy-a1ec2f81.js";import"./clone-92746810.js";var _={parse:p(e=>g(void 0,null,function*(){const t=yield u("info",e);v.debug(t)}),"parse")},x={version:f.version},b=p(()=>x.version,"getVersion"),w={getVersion:b},y=p((e,t,a)=>{v.debug(`rendering info diagram
`+e);const o=c(t);l(o,100,400,!0),o.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${a}`)},"draw"),S={draw:y},A={parser:_,db:w,renderer:S};export{A as diagram};
