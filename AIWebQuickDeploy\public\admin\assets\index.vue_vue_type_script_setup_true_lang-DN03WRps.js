
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,a as s,c as n,T as r,i as a,e as i,f as l,_ as c}from"./index-BERX8Mlm.js";import{c as o}from"./index-DhWfG07N.js";const t=e({name:"Fullscreen",__name:"index",setup(e){const t=s(),{isFullscreen:m,toggle:p}=o();return(e,s)=>{const o=c;return"pc"===a(t).mode?(i(),n("span",{key:0,class:"flex-center cursor-pointer px-2 py-1",onClick:s[0]||(s[0]=(...e)=>a(p)&&a(p)(...e))},[l(o,{name:a(m)?"i-ri:fullscreen-exit-line":"i-ri:fullscreen-line"},null,8,["name"])])):r("",!0)}}});export{t as _};
