{"prettier.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "json", "jsonc", "json5", "yaml", "yml", "markdown"], "cSpell.words": ["AIWEB", "ant<PERSON>", "axios", "Baichuan", "bumpp", "Chatbox", "chatglm", "chatgpt", "chenz<PERSON>yu", "chevereto", "cogvideox", "commitlint", "<PERSON><PERSON><PERSON>", "cref", "dall", "dalle", "<PERSON><PERSON><PERSON>", "deepsearch", "deepseek", "docker<PERSON>b", "Do<PERSON><PERSON>", "duckduck<PERSON>", "<PERSON><PERSON>", "EMAILCODE", "Epay", "errmsg", "esno", "flowith", "GPTAPI", "gpts", "headlessui", "heroicons", "highlightjs", "hljs", "hun<PERSON>", "<PERSON><PERSON>", "iconify", "ISDEV", "Jsapi", "katex", "ka<PERSON><PERSON><PERSON><PERSON>", "langchain", "<PERSON><PERSON>", "linkify", "logprobs", "longcontext", "<PERSON><PERSON><PERSON>", "luma", "mapi", "Markmap", "mdhljs", "micromessenger", "mila", "Mindmap", "MODELSMAPLIST", "MODELTYPELIST", "modelvalue", "<PERSON><PERSON><PERSON>", "newconfig", "niji", "Nmessage", "nodata", "OPENAI", "pinia", "Popconfirm", "PPTCREATE", "projectaddress", "<PERSON><PERSON><PERSON><PERSON>", "qwen", "rushstack", "sdxl", "<PERSON><PERSON>", "seedream", "<PERSON><PERSON>", "sref", "suno", "tailwindcss", "<PERSON><PERSON>", "traptitech", "tsup", "Typecheck", "typeorm", "unplugin", "usercenter", "vastxie", "VITE", "vueuse", "wechat", "<PERSON><PERSON>", "wxpay"], "vue.codeActions.enabled": false}