
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as e,P as u,r as t,a0 as y,b as r,Q as d,c as p,e as n,f as i,w as s,j as o,h as c,_ as P,g as f,V as m,W as D,t as g,Y as _,k as b}from"./index-BERX8Mlm.js";import{a as h}from"./config-BrbFL53_.js";const x=l({__name:"duluPay",setup(l){const b=e({payDuluPayStatus:"",payDuluPayPid:"",payDuluPaySecret:"",payDuluPayNotifyUrl:"",payDuluPayReturnUrl:"",payDuluPayRedirect:"",payDuluPayChannel:[]}),x=u((()=>{const a="1"===b.payDuluPayStatus;return{payDuluPayStatus:[{required:!0,trigger:"change",message:"请选择当前支付开启状态"}],payDuluPaySecret:[{required:a,trigger:"blur",message:"请填写支付秘钥"}],payDuluPayPid:[{required:a,trigger:"blur",message:"请填写商户PID"}],payDuluPayNotifyUrl:[{required:a,trigger:"blur",message:"请填写支付通知地址"}],payDuluPayReturnUrl:[{required:a,trigger:"blur",message:"请填写支付回调地址"}],payDuluPayRedirect:[{required:a,trigger:"change",message:"请选择支付模式"}],payDuluPayChannel:[{required:a,trigger:"change",message:"请选择至少一个支付渠道"}]}})),v=t(),V=[{label:"微信支付",value:"wxpay"},{label:"支付宝支付",value:"alipay"}];async function U(){const a=await h.queryConfig({keys:["payDuluPaySecret","payDuluPayNotifyUrl","payDuluPayReturnUrl","payDuluPayPid","payDuluPayStatus","payDuluPayRedirect","payDuluPayChannel"]}),l=a.data.payDuluPayChannel?JSON.parse(a.data.payDuluPayChannel):[];Object.assign(b,a.data,{payDuluPayChannel:l})}function S(){var a;null==(a=v.value)||a.validate((async a=>{if(a){try{await h.setConfig({settings:(l=b,Object.keys(l).map((a=>({configKey:a,configVal:C(a,l[a])}))))}),_.success("变更配置信息成功")}catch(e){}U()}else _.error("请填写完整信息");var l}))}function C(a,l){return["payDuluPayChannel"].includes(a)?l?l?JSON.stringify(l):void 0:[]:l}return y((()=>b.payDuluPayStatus),(()=>{setTimeout((()=>{var a;null==(a=v.value)||a.validateField(["payDuluPaySecret","payDuluPayPid","payDuluPayNotifyUrl","payDuluPayReturnUrl","payDuluPayRedirect","payDuluPayChannel"])}),0)})),r((()=>{U()})),(l,e)=>{const u=P,t=o,y=a,r=d("el-switch"),_=d("el-form-item"),h=d("el-col"),U=d("el-row"),C=d("el-input"),R=d("el-radio"),w=d("el-radio-group"),k=d("el-tooltip"),q=d("el-checkbox"),N=d("el-checkbox-group"),j=d("el-form"),I=d("el-card");return n(),p("div",null,[i(y,null,{title:s((()=>e[7]||(e[7]=[f("div",{class:"flex items-center gap-4"},"嘟噜支付设置",-1)]))),content:s((()=>e[8]||(e[8]=[f("div",{class:"text-sm/6"},[f("div",null,[f("a",{href:"https://www.dulupay.com/?invite=99AI",target:"_blank"},"嘟噜支付"),c(" 支付渠道，请按文档配置即可。 ")]),f("div",null,"支付通知地址为： https://您的域名/api/pay/notify。")],-1)]))),default:s((()=>[i(t,{outline:"",onClick:S},{default:s((()=>[i(u,{name:"i-ri:file-text-line"}),e[9]||(e[9]=c(" 保存设置 "))])),_:1})])),_:1}),i(I,{style:{margin:"20px"}},{default:s((()=>[i(j,{ref_key:"formRef",ref:v,rules:x.value,model:b,"label-width":"120px"},{default:s((()=>[i(U,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(_,{label:"启用当前支付",prop:"payDuluPayPid"},{default:s((()=>[i(r,{modelValue:b.payDuluPayStatus,"onUpdate:modelValue":e[0]||(e[0]=a=>b.payDuluPayStatus=a),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(U,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(_,{label:"商户PID",prop:"payDuluPayPid"},{default:s((()=>[i(C,{modelValue:b.payDuluPayPid,"onUpdate:modelValue":e[1]||(e[1]=a=>b.payDuluPayPid=a),placeholder:"请填写商户PID",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(U,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(_,{label:"商户秘钥",prop:"payDuluPaySecret"},{default:s((()=>[i(C,{modelValue:b.payDuluPaySecret,"onUpdate:modelValue":e[2]||(e[2]=a=>b.payDuluPaySecret=a),placeholder:"请填写商户秘钥",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(U,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(_,{label:"支付通知地址",prop:"payDuluPaySecret"},{default:s((()=>[i(C,{modelValue:b.payDuluPayNotifyUrl,"onUpdate:modelValue":e[3]||(e[3]=a=>b.payDuluPayNotifyUrl=a),placeholder:"请填写支付通知地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(U,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(_,{label:"支付回调地址",prop:"payDuluPaySecret"},{default:s((()=>[i(C,{modelValue:b.payDuluPayReturnUrl,"onUpdate:modelValue":e[4]||(e[4]=a=>b.payDuluPayReturnUrl=a),placeholder:"请填写支付成功后的回跳地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(U,null,{default:s((()=>[i(h,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[i(_,{label:"支付模式",prop:"payDuluPayRedirect","label-width":"130px"},{default:s((()=>[i(k,{class:"box-item",effect:"dark",content:"说明：扫码模式在购买页面直接显示二维码供用户扫码支付；跳转模式则会引导用户前往新页面完成支付",placement:"right"},{default:s((()=>[i(w,{modelValue:b.payDuluPayRedirect,"onUpdate:modelValue":e[5]||(e[5]=a=>b.payDuluPayRedirect=a)},{default:s((()=>[i(R,{label:"0"},{default:s((()=>e[10]||(e[10]=[c("扫码模式")]))),_:1}),i(R,{label:"1"},{default:s((()=>e[11]||(e[11]=[c("跳转模式")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),i(U,null,{default:s((()=>[i(h,{xs:24,md:24,lg:24,xl:24},{default:s((()=>[i(_,{label:"开启支付渠道",prop:"payDuluPayChannel"},{default:s((()=>[i(N,{modelValue:b.payDuluPayChannel,"onUpdate:modelValue":e[6]||(e[6]=a=>b.payDuluPayChannel=a),size:"small"},{default:s((()=>[(n(),p(m,null,D(V,(a=>i(q,{key:a.value,border:"",label:a.value},{default:s((()=>[c(g(a.label),1)])),_:2},1032,["label"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof b&&b(x);export{x as default};
