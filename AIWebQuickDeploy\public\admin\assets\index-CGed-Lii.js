
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,a as t,c as s,e as _,g as i,a5 as p,f as r,a8 as u,i as a,Z as n}from"./index-BERX8Mlm.js";import{_ as o}from"./leftSide.vue_vue_type_script_setup_true_lang-BTr4xL8t.js";import{_ as l}from"./rightSide.vue_vue_type_script_setup_true_lang-ClZNHSw-.js";import"./index-BTtsYu0a.js";import"./index-CZaxHimt.js";import"./item.vue_vue_type_script_setup_true_lang-aMXYc_KV.js";import"./HDropdownMenu.vue_vue_type_script_setup_true_lang-Wc6l-Ngn.js";import"./index.vue_vue_type_script_setup_true_lang-PUnUpH4H.js";import"./HDropdown-DFGm5c_S.js";import"./HTabList.vue_vue_type_script_setup_true_lang-BEyYCazB.js";import"./use-resolve-button-type-DnRVrBaM.js";import"./index.vue_vue_type_script_setup_true_lang-DN03WRps.js";import"./index-DhWfG07N.js";import"./index.vue_vue_type_script_setup_true_lang-Do4XPH2t.js";import"./HKbd-LjWkyhwy.js";import"./index.vue_vue_type_script_setup_true_lang-DbfRBGyF.js";import"./useMainPage-Dbp8uSF1.js";const m={class:"toolbar-container flex items-center justify-between"},d={class:"h-full flex items-center of-hidden pl-2 pr-16",style:{"mask-image":"linear-gradient(90deg, #000 0%, #000 calc(100% - 50px), transparent)"}},c={class:"h-full flex items-center px-2"},v=n(e({name:"Toolbar",__name:"index",setup(e){const n=t();return(e,t)=>(_(),s("div",m,[i("div",d,[r(o)]),p(i("div",c,[r(l)],512),[[u,["side","single"].includes(a(n).settings.menu.menuMode)]])]))}}),[["__scopeId","data-v-35cc5876"]]);export{v as default};
