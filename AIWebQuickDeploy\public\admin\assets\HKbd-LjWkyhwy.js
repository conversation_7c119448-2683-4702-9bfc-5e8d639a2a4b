
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{Z as t,c as e,e as n,aj as r}from"./index-BERX8Mlm.js";const s={class:"mr-[4px] h-6 min-w-[24px] inline-flex items-center justify-center rounded bg-stone-1 px-1 text-[12px] text-dark font-medium font-sans ring-1 ring-stone-3 ring-inset last:mr-0 dark-bg-dark-9 dark-text-white dark-ring-stone-7"};const a=t({},[["render",function(t,a){return n(),e("kbd",s,[r(t.$slots,"default")])}]]);export{a as default};
