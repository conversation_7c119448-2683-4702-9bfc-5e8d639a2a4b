
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as i,Q as c,c as o,e as f,f as u,w as d,j as n,h as r,_ as p,g as s,Y as m,k as h}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";const w=l({__name:"wechat",setup(l){const h=a({wechatRegisterStatus:"",wechatSilentLoginStatus:"",wechatOfficialName:"",wechatOfficialAppId:"",wechatOfficialToken:"",wechatOfficialAppSecret:"",officialSubscribeText:"",officialBindAccountText:"",officialScanLoginText:"",officialAutoReplyText:""}),w=t({wechatOfficialName:[{required:!1,trigger:"blur",message:"请填写微信公众号名称"}],wechatOfficialAppId:[{required:!1,trigger:"blur",message:"请填写微信公众号开发配置 AppId"}],wechatOfficialToken:[{required:!1,trigger:"blur",message:"请填写微信公众号开发配置 Token"}],wechatOfficialAppSecret:[{required:!1,trigger:"blur",message:"请填写微信公众号开发配置 AppSecret"}]}),g=t();async function _(){const e=await x.queryConfig({keys:["wechatOfficialName","wechatOfficialAppId","wechatOfficialToken","wechatOfficialAppSecret","officialSubscribeText","officialBindAccountText","officialScanLoginText","officialAutoReplyText","wechatRegisterStatus","wechatSilentLoginStatus"]});Object.assign(h,e.data)}function b(){var e;null==(e=g.value)||e.validate((async e=>{if(e){try{await x.setConfig({settings:(l=h,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),m.success("变更配置信息成功")}catch(a){}_()}else m.error("请填写完整信息");var l}))}return i((()=>{_()})),(l,a)=>{const t=p,i=n,m=e,x=c("el-switch"),_=c("el-tooltip"),S=c("el-form-item"),T=c("el-col"),V=c("el-row"),A=c("el-input"),O=c("el-divider"),v=c("el-form"),y=c("el-card");return f(),o("div",null,[u(m,null,{title:d((()=>a[10]||(a[10]=[s("div",{class:"flex items-center gap-4"},"微信登录设置[仔细阅读]",-1)]))),content:d((()=>a[11]||(a[11]=[s("div",{class:"text-sm/6"},[s("div",null," 系统微信登录通过关联公众号实现[请务必注册为服务号、个人公众号没有二维码等此类权限]。 "),s("div",null,[r(" 请前往 "),s("a",{href:"https://mp.weixin.qq.com/",target:"_blank"},"微信公众平台"),r(" ，获取开发者配置信息。 ")]),s("div",null,"如果用户对公众号发送消息，将会从下面设置的自定义回复默认信息。"),s("div",null,[r(" 同时别忘记在微信公众号平台将自己的 ip/域名 加入白名单，配置位置为公众号后台->基本配置：服务复制参考 "),s("a",{href:"https://域名/api/official/notify",target:"_blank"},"https://域名/api/official/notify"),r(" 将域名修改为您的域名。 ")]),s("div",null,"下方Token对应自己后台设置的Token，加密秘钥随机即可。"),s("div",null,"当设置不指定首页并且配置了微信登录即可默认打开静默登录！")],-1)]))),default:d((()=>[u(i,{outline:"",text:"",onClick:b},{default:d((()=>[u(t,{name:"i-ri:file-text-line"}),a[12]||(a[12]=r(" 保存设置 "))])),_:1})])),_:1}),u(y,{style:{margin:"20px"}},{default:d((()=>[u(v,{ref_key:"formRef",ref:g,rules:w.value,model:h,"label-width":"170px"},{default:d((()=>[u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"是否开启微信登录注册",prop:"wechatRegisterStatus"},{default:d((()=>[u(_,{class:"box-item",effect:"dark",content:"如您启用微信注册、则用户端则可以通过微信扫码方式注册或登录！",placement:"right"},{default:d((()=>[u(x,{modelValue:h.wechatRegisterStatus,"onUpdate:modelValue":a[0]||(a[0]=e=>h.wechatRegisterStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"是否开启微信静默",prop:"wechatSilentLoginStatus"},{default:d((()=>[u(_,{class:"box-item",effect:"dark",content:"如您启用静默登录、则用户在微信环境打开则直接自动登录！",placement:"right"},{default:d((()=>[u(x,{modelValue:h.wechatSilentLoginStatus,"onUpdate:modelValue":a[1]||(a[1]=e=>h.wechatSilentLoginStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"公众号名称",prop:"wechatOfficialName"},{default:d((()=>[u(A,{modelValue:h.wechatOfficialName,"onUpdate:modelValue":a[2]||(a[2]=e=>h.wechatOfficialName=e),placeholder:"公众号名称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"AppId",prop:"wechatOfficialAppId"},{default:d((()=>[u(A,{modelValue:h.wechatOfficialAppId,"onUpdate:modelValue":a[3]||(a[3]=e=>h.wechatOfficialAppId=e),placeholder:"公众号开发信息 AppId",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"Token",prop:"wechatOfficialToken"},{default:d((()=>[u(A,{modelValue:h.wechatOfficialToken,"onUpdate:modelValue":a[4]||(a[4]=e=>h.wechatOfficialToken=e),placeholder:"公众号Token配置",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"AppSecret",prop:"wechatOfficialAppSecret"},{default:d((()=>[u(A,{modelValue:h.wechatOfficialAppSecret,"onUpdate:modelValue":a[5]||(a[5]=e=>h.wechatOfficialAppSecret=e),placeholder:"公众号开发信息 AppSecret",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(O),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"订阅公众号欢迎消息",prop:"officialSubscribeText"},{default:d((()=>[u(A,{modelValue:h.officialSubscribeText,"onUpdate:modelValue":a[6]||(a[6]=e=>h.officialSubscribeText=e),type:"textarea",rows:3,placeholder:"订阅你的公众号后对他的欢迎语！",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"绑定账号回复消息",prop:"officialBindAccountText"},{default:d((()=>[u(A,{modelValue:h.officialBindAccountText,"onUpdate:modelValue":a[7]||(a[7]=e=>h.officialBindAccountText=e),type:"textarea",rows:3,placeholder:"非微信登录用户首次绑定微信的欢迎语",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"扫码登录回复消息",prop:"officialScanLoginText"},{default:d((()=>[u(A,{modelValue:h.officialScanLoginText,"onUpdate:modelValue":a[8]||(a[8]=e=>h.officialScanLoginText=e),type:"textarea",rows:3,placeholder:"用户扫码登录成功时自动回复的内容",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(V,null,{default:d((()=>[u(T,{xs:24,md:20,lg:15,xl:12},{default:d((()=>[u(S,{label:"自定义回复的默认信息",prop:"officialAutoReplyText"},{default:d((()=>[u(A,{modelValue:h.officialAutoReplyText,"onUpdate:modelValue":a[9]||(a[9]=e=>h.officialAutoReplyText=e),type:"textarea",rows:3,placeholder:"当用户对公众号发了消息不在自动回复列表时回复的兜底内容",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof h&&h(w);export{w as default};
