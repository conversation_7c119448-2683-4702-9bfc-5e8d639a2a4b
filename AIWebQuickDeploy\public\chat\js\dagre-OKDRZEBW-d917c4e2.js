var U=Object.defineProperty,$=Object.defineProperties;var q=Object.getOwnPropertyDescriptors;var B=Object.getOwnPropertySymbols;var z=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable;var A=(e,t,n)=>t in e?U(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,P=(e,t)=>{for(var n in t||(t={}))z.call(t,n)&&A(e,n,t[n]);if(B)for(var n of B(t))K.call(t,n)&&A(e,n,t[n]);return e},J=(e,t)=>$(e,q(t));var b=(e,t,n)=>new Promise((o,c)=>{var d=a=>{try{u(n.next(a))}catch(w){c(w)}},r=a=>{try{u(n.throw(a))}catch(w){c(w)}},u=a=>a.done?o(a.value):Promise.resolve(a.value).then(d,r);u((n=n.apply(e,t)).next())});import{_ as X,ag as Q,ah as W,ai as Z,aj as I,l as i,d as L,ak as ee,al as ne,a7 as te,ac as se,a8 as G,a6 as ie,am as re,an as oe,ao as ae}from"./chart-vendor-e1d59b84.js";import{G as T}from"./graph-f794edc0.js";import{l as ce}from"./layout-663bb27b.js";import{i as D}from"./_baseUniq-5ee25ed9.js";import{c as de}from"./clone-92746810.js";import{m as M}from"./_basePickBy-a1ec2f81.js";import"./utils-vendor-c35799af.js";import"./vue-vendor-d751b0f5.js";function E(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:le(e),edges:fe(e)};return D(e.graph())||(t.value=de(e.graph())),t}function le(e){return M(e.nodes(),function(t){var n=e.node(t),o=e.parent(t),c={v:t};return D(n)||(c.value=n),D(o)||(c.parent=o),c})}function fe(e){return M(e.edges(),function(t){var n=e.edge(t),o={v:t.v,w:t.w};return D(t.name)||(o.name=t.name),D(n)||(o.value=n),o})}var f=new Map,C=new Map,j=new Map,ue=X(()=>{C.clear(),j.clear(),f.clear()},"clear"),k=X((e,t)=>{const n=C.get(t)||[];return i.trace("In isDescendant",t," ",e," = ",n.includes(e)),n.includes(e)},"isDescendant"),ge=X((e,t)=>{const n=C.get(t)||[];return i.info("Descendants of ",t," is ",n),i.info("Edge is ",e),e.v===t||e.w===t?!1:n?n.includes(e.v)||k(e.v,t)||k(e.w,t)||n.includes(e.w):(i.debug("Tilt, ",t,",not in descendants"),!1)},"edgeInCluster"),F=X((e,t,n,o)=>{i.warn("Copying children of ",e,"root",o,"data",t.node(e),o);const c=t.children(e)||[];e!==o&&c.push(e),i.warn("Copying (nodes) clusterId",e,"nodes",c),c.forEach(d=>{if(t.children(d).length>0)F(d,t,n,o);else{const r=t.node(d);i.info("cp ",d," to ",o," with parent ",e),n.setNode(d,r),o!==t.parent(d)&&(i.warn("Setting parent",d,t.parent(d)),n.setParent(d,t.parent(d))),e!==o&&d!==e?(i.debug("Setting parent",d,e),n.setParent(d,e)):(i.info("In copy ",e,"root",o,"data",t.node(e),o),i.debug("Not Setting parent for node=",d,"cluster!==rootId",e!==o,"node!==clusterId",d!==e));const u=t.edges(d);i.debug("Copying Edges",u),u.forEach(a=>{i.info("Edge",a);const w=t.edge(a.v,a.w,a.name);i.info("Edge data",w,o);try{ge(a,o)?(i.info("Copying as ",a.v,a.w,w,a.name),n.setEdge(a.v,a.w,w,a.name),i.info("newGraph edges ",n.edges(),n.edge(n.edges()[0]))):i.info("Skipping copy of edge ",a.v,"-->",a.w," rootId: ",o," clusterId:",e)}catch(S){i.error(S)}})}i.debug("Removing node",d),t.removeNode(d)})},"copy"),Y=X((e,t)=>{const n=t.children(e);let o=[...n];for(const c of n)j.set(c,e),o=[...o,...Y(c,t)];return o},"extractDescendants"),we=X((e,t,n)=>{const o=e.edges().filter(a=>a.v===t||a.w===t),c=e.edges().filter(a=>a.v===n||a.w===n),d=o.map(a=>({v:a.v===t?n:a.v,w:a.w===t?t:a.w})),r=c.map(a=>({v:a.v,w:a.w}));return d.filter(a=>r.some(w=>a.v===w.v&&a.w===w.w))},"findCommonEdges"),O=X((e,t,n)=>{const o=t.children(e);if(i.trace("Searching children of id ",e,o),o.length<1)return e;let c;for(const d of o){const r=O(d,t,n),u=we(t,n,r);if(r)if(u.length>0)c=r;else return r}return c},"findNonClusterChild"),R=X(e=>!f.has(e)||!f.get(e).externalConnections?e:f.has(e)?f.get(e).id:e,"getAnchorId"),me=X((e,t)=>{if(!e||t>10){i.debug("Opting out, no graph ");return}else i.debug("Opting in, graph ");e.nodes().forEach(function(n){e.children(n).length>0&&(i.warn("Cluster identified",n," Replacement id in edges: ",O(n,e,n)),C.set(n,Y(n,e)),f.set(n,{id:O(n,e,n),clusterData:e.node(n)}))}),e.nodes().forEach(function(n){const o=e.children(n),c=e.edges();o.length>0?(i.debug("Cluster identified",n,C),c.forEach(d=>{const r=k(d.v,n),u=k(d.w,n);r^u&&(i.warn("Edge: ",d," leaves cluster ",n),i.warn("Descendants of XXX ",n,": ",C.get(n)),f.get(n).externalConnections=!0)})):i.debug("Not a cluster ",n,C)});for(let n of f.keys()){const o=f.get(n).id,c=e.parent(o);c!==n&&f.has(c)&&!f.get(c).externalConnections&&(f.get(n).id=c)}e.edges().forEach(function(n){const o=e.edge(n);i.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(n)),i.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(e.edge(n)));let c=n.v,d=n.w;if(i.warn("Fix XXX",f,"ids:",n.v,n.w,"Translating: ",f.get(n.v)," --- ",f.get(n.w)),f.get(n.v)||f.get(n.w)){if(i.warn("Fixing and trying - removing XXX",n.v,n.w,n.name),c=R(n.v),d=R(n.w),e.removeEdge(n.v,n.w,n.name),c!==n.v){const r=e.parent(c);f.get(r).externalConnections=!0,o.fromCluster=n.v}if(d!==n.w){const r=e.parent(d);f.get(r).externalConnections=!0,o.toCluster=n.w}i.warn("Fix Replacing with XXX",c,d,n.name),e.setEdge(c,d,o,n.name)}}),i.warn("Adjusted Graph",E(e)),_(e,0),i.trace(f)},"adjustClustersAndEdges"),_=X((e,t)=>{var c,d;if(i.warn("extractor - ",t,E(e),e.children("D")),t>10){i.error("Bailing out");return}let n=e.nodes(),o=!1;for(const r of n){const u=e.children(r);o=o||u.length>0}if(!o){i.debug("Done, no node has children",e.nodes());return}i.debug("Nodes = ",n,t);for(const r of n)if(i.debug("Extracting node",r,f,f.has(r)&&!f.get(r).externalConnections,!e.parent(r),e.node(r),e.children("D")," Depth ",t),!f.has(r))i.debug("Not a cluster",r,t);else if(!f.get(r).externalConnections&&e.children(r)&&e.children(r).length>0){i.warn("Cluster without external connections, without a parent and with children",r,t);let a=e.graph().rankdir==="TB"?"LR":"TB";(d=(c=f.get(r))==null?void 0:c.clusterData)!=null&&d.dir&&(a=f.get(r).clusterData.dir,i.warn("Fixing dir",f.get(r).clusterData.dir,a));const w=new T({multigraph:!0,compound:!0}).setGraph({rankdir:a,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});i.warn("Old graph before copy",E(e)),F(r,e,w,r),e.setNode(r,{clusterNode:!0,id:r,clusterData:f.get(r).clusterData,label:f.get(r).label,graph:w}),i.warn("New graph after copy node: (",r,")",E(w)),i.debug("Old graph after copy",E(e))}else i.warn("Cluster ** ",r," **not meeting the criteria !externalConnections:",!f.get(r).externalConnections," no parent: ",!e.parent(r)," children ",e.children(r)&&e.children(r).length>0,e.children("D"),t),i.debug(f);n=e.nodes(),i.warn("New list of nodes",n);for(const r of n){const u=e.node(r);i.warn(" Now next level",r,u),u!=null&&u.clusterNode&&_(u.graph,t+1)}},"extractor"),H=X((e,t)=>{if(t.length===0)return[];let n=Object.assign([],t);return t.forEach(o=>{const c=e.children(o),d=H(e,c);n=[...n,...d]}),n},"sorter"),he=X(e=>H(e,e.children()),"sortNodesByHierarchy"),V=X((e,t,n,o,c,d)=>b(void 0,null,function*(){i.warn("Graph in recursive render:XAX",E(t),c);const r=t.graph().rankdir;i.trace("Dir in recursive render - dir:",r);const u=e.insert("g").attr("class","root");t.nodes()?i.info("Recursive render XXX",t.nodes()):i.info("No nodes found for",t),t.edges().length>0&&i.info("Recursive edges",t.edge(t.edges()[0]));const a=u.insert("g").attr("class","clusters"),w=u.insert("g").attr("class","edgePaths"),S=u.insert("g").attr("class","edgeLabels"),g=u.insert("g").attr("class","nodes");yield Promise.all(t.nodes().map(function(l){return b(this,null,function*(){const s=t.node(l);if(c!==void 0){const m=JSON.parse(JSON.stringify(c.clusterData));i.trace(`Setting data for parent cluster XXX
 Node.id = `,l,`
 data=`,m.height,`
Parent cluster`,c.height),t.setNode(c.id,m),t.parent(l)||(i.trace("Setting parent",l,c.id),t.setParent(l,c.id,m))}if(i.info("(Insert) Node XXX"+l+": "+JSON.stringify(t.node(l))),s!=null&&s.clusterNode){i.info("Cluster identified XBX",l,s.width,t.node(l));const{ranksep:m,nodesep:h}=t.graph();s.graph.setGraph(J(P({},s.graph.graph()),{ranksep:m+25,nodesep:h}));const N=yield V(g,s.graph,n,o,t.node(l),d),x=N.elem;ee(s,x),s.diff=N.diff||0,i.info("New compound node after recursive render XAX",l,"width",s.width,"height",s.height),ne(x,s)}else t.children(l).length>0?(i.trace("Cluster - the non recursive path XBX",l,s.id,s,s.width,"Graph:",t),i.trace(O(s.id,t)),f.set(s.id,{id:O(s.id,t),node:s})):(i.trace("Node - the non recursive path XAX",l,g,t.node(l),r),yield te(g,t.node(l),{config:d,dir:r}))})})),yield X(()=>b(void 0,null,function*(){const l=t.edges().map(function(s){return b(this,null,function*(){const m=t.edge(s.v,s.w,s.name);i.info("Edge "+s.v+" -> "+s.w+": "+JSON.stringify(s)),i.info("Edge "+s.v+" -> "+s.w+": ",s," ",JSON.stringify(t.edge(s))),i.info("Fix",f,"ids:",s.v,s.w,"Translating: ",f.get(s.v),f.get(s.w)),yield ae(S,m)})});yield Promise.all(l)}),"processEdges")(),i.info("Graph before layout:",JSON.stringify(E(t))),i.info("############################################# XXX"),i.info("###                Layout                 ### XXX"),i.info("############################################# XXX"),ce(t),i.info("Graph after layout:",JSON.stringify(E(t)));let y=0,{subGraphTitleTotalMargin:p}=se(d);return yield Promise.all(he(t).map(function(l){return b(this,null,function*(){var m;const s=t.node(l);if(i.info("Position XBX => "+l+": ("+s.x,","+s.y,") width: ",s.width," height: ",s.height),s!=null&&s.clusterNode)s.y+=p,i.info("A tainted cluster node XBX1",l,s.id,s.width,s.height,s.x,s.y,t.parent(l)),f.get(s.id).node=s,G(s);else if(t.children(l).length>0){i.info("A pure cluster node XBX1",l,s.id,s.x,s.y,s.width,s.height,t.parent(l)),s.height+=p,t.node(s.parentId);const h=(s==null?void 0:s.padding)/2||0,N=((m=s==null?void 0:s.labelBBox)==null?void 0:m.height)||0,x=N-h||0;i.debug("OffsetY",x,"labelHeight",N,"halfPadding",h),yield ie(a,s),f.get(s.id).node=s}else{const h=t.node(s.parentId);s.y+=p/2,i.info("A regular node XBX1 - using the padding",s.id,"parent",s.parentId,s.width,s.height,s.x,s.y,"offsetY",s.offsetY,"parent",h,h==null?void 0:h.offsetY,s),G(s)}})})),t.edges().forEach(function(l){const s=t.edge(l);i.info("Edge "+l.v+" -> "+l.w+": "+JSON.stringify(s),s),s.points.forEach(x=>x.y+=p/2);const m=t.node(l.v);var h=t.node(l.w);const N=re(w,s,f,n,m,h,o);oe(s,N)}),t.nodes().forEach(function(l){const s=t.node(l);i.info(l,s.type,s.diff),s.isGroup&&(y=s.diff)}),i.warn("Returning from recursive render XAX",u,y),{elem:u,diff:y}}),"recursiveRender"),xe=X((e,t)=>b(void 0,null,function*(){var d,r,u,a,w,S;const n=new T({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:((d=e.config)==null?void 0:d.nodeSpacing)||((u=(r=e.config)==null?void 0:r.flowchart)==null?void 0:u.nodeSpacing)||e.nodeSpacing,ranksep:((a=e.config)==null?void 0:a.rankSpacing)||((S=(w=e.config)==null?void 0:w.flowchart)==null?void 0:S.rankSpacing)||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),o=t.select("g");Q(o,e.markers,e.type,e.diagramId),W(),Z(),I(),ue(),e.nodes.forEach(g=>{n.setNode(g.id,P({},g)),g.parentId&&n.setParent(g.id,g.parentId)}),i.debug("Edges:",e.edges),e.edges.forEach(g=>{if(g.start===g.end){const v=g.start,y=v+"---"+v+"---1",p=v+"---"+v+"---2",l=n.node(v);n.setNode(y,{domId:y,id:y,parentId:l.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),n.setParent(y,l.parentId),n.setNode(p,{domId:p,id:p,parentId:l.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),n.setParent(p,l.parentId);const s=structuredClone(g),m=structuredClone(g),h=structuredClone(g);s.label="",s.arrowTypeEnd="none",s.id=v+"-cyclic-special-1",m.arrowTypeStart="none",m.arrowTypeEnd="none",m.id=v+"-cyclic-special-mid",h.label="",l.isGroup&&(s.fromCluster=v,h.toCluster=v),h.id=v+"-cyclic-special-2",h.arrowTypeStart="none",n.setEdge(v,y,s,v+"-cyclic-special-0"),n.setEdge(y,p,m,v+"-cyclic-special-1"),n.setEdge(p,v,h,v+"-cyc<lic-special-2")}else n.setEdge(g.start,g.end,P({},g),g.id)}),i.warn("Graph at first:",JSON.stringify(E(n))),me(n),i.warn("Graph after XAX:",JSON.stringify(E(n)));const c=L();yield V(o,n,e.type,e.diagramId,void 0,c)}),"render");export{xe as render};
