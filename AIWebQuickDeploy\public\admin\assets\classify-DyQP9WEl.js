
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,r as t,$ as r,P as s,a0 as o,b as u,Q as d,a1 as n,c as i,e as m,f as p,w as c,j as f,h as b,_,g as v,a3 as g,a4 as y,V as h,W as w,i as M,a2 as V,a5 as k,t as C,T as F,ab as j,a9 as x,Y as z,k as N}from"./index-BERX8Mlm.js";import{A as U}from"./app-Cak_t3ob.js";import{u as I}from"./utcFormatTime-BtFjiA-p.js";import{E as q,Q as D}from"./index-gPQwgooA.js";const Y={key:1},A={class:"mr-5 flex justify-end"},P=l({__name:"classify",setup(l){const N=t(),P=t(0),K=t(!1),Q=t(!1),R=r({name:"",status:"",page:1,size:10}),S=t(),T=t(0),E=r({name:"",des:"",coverImg:"",order:100,status:0,isMember:0,hideFromNonMember:0}),O=r({name:[{required:!0,message:"请填写分类名称",trigger:"blur"}],des:[{required:!1,message:"请填写分类描述",trigger:"blur"}],coverImg:[{required:!1,message:"请填写分类封面图片地址",trigger:"blur"}],order:[{required:!1,message:"请填写排序ID",trigger:"blur"}],status:[{required:!0,message:"请选择分类状态",trigger:"change"}]}),W=t([]),$=s((()=>T.value?"更新分类":"新增分类")),B=s((()=>T.value?"确认更新":"确认新增"));async function G(){try{Q.value=!0;const e=await U.queryCats(R),{rows:a,count:l}=e.data;Q.value=!1,P.value=l,W.value=a}catch(e){Q.value=!1}}return o((()=>E.isMember),(e=>{0===e&&(E.hideFromNonMember=0)})),u((()=>{G()})),(l,t)=>{const r=_,s=f,o=a,u=d("el-input"),H=d("el-form-item"),J=d("el-option"),L=d("el-select"),X=d("el-button"),Z=d("el-form"),ee=e,ae=d("el-table-column"),le=d("el-tag"),te=d("el-popconfirm"),re=d("el-table"),se=d("el-pagination"),oe=d("el-row"),ue=d("el-switch"),de=d("el-dialog"),ne=n("loading");return m(),i("div",null,[p(o,null,{title:c((()=>t[15]||(t[15]=[v("div",{class:"flex items-center gap-4"},"应用分类配置",-1)]))),content:c((()=>t[16]||(t[16]=[v("div",{class:"text-sm/6"},[v("div",null,"应用分类可能会被多个用户收藏，一旦创建，不建议删除。")],-1)]))),default:c((()=>[p(s,{outline:"",onClick:t[0]||(t[0]=e=>K.value=!0)},{default:c((()=>[p(r,{name:"ic:baseline-plus"}),t[17]||(t[17]=b(" 新增分类 "))])),_:1})])),_:1}),p(ee,null,{default:c((()=>[p(Z,{ref_key:"formRef",ref:N,inline:!0,model:R},{default:c((()=>[p(H,{label:"分类名称",prop:"name"},{default:c((()=>[p(u,{modelValue:R.name,"onUpdate:modelValue":t[1]||(t[1]=e=>R.name=e),placeholder:"分类名称[模糊搜索]",onKeydown:g(y(G,["prevent"]),["enter"])},null,8,["modelValue","onKeydown"])])),_:1}),p(H,{label:"分类状态",prop:"status"},{default:c((()=>[p(L,{modelValue:R.status,"onUpdate:modelValue":t[2]||(t[2]=e=>R.status=e),placeholder:"请选择分类状态",clearable:"",style:{width:"160px"}},{default:c((()=>[(m(!0),i(h,null,w(M(q),(e=>(m(),V(J,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(H,null,{default:c((()=>[p(X,{type:"primary",onClick:G},{default:c((()=>t[18]||(t[18]=[b(" 查询 ")]))),_:1}),p(X,{onClick:t[3]||(t[3]=e=>{return null==(a=M(N))||a.resetFields(),void G();var a})},{default:c((()=>t[19]||(t[19]=[b(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),p(ee,{style:{width:"100%"}},{default:c((()=>[k((m(),V(re,{border:"",data:M(W),style:{width:"100%"},size:"large"},{default:c((()=>[p(ae,{prop:"name",label:"分类名称"}),p(ae,{prop:"appCount",label:"应用数量"}),p(ae,{prop:"order",label:"排序ID"}),p(ae,{prop:"status",label:"分类状态"},{default:c((e=>[p(le,{type:1===e.row.status?"success":"danger"},{default:c((()=>[b(C(M(D)[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),p(ae,{prop:"isMember",label:"会员专属"},{default:c((e=>[p(le,{type:1===e.row.isMember?"warning":"info"},{default:c((()=>[b(C(1===e.row.isMember?"是":"否"),1)])),_:2},1032,["type"])])),_:1}),p(ae,{prop:"hideFromNonMember",label:"非会员隐藏"},{default:c((e=>[1===e.row.isMember?(m(),V(le,{key:0,type:1===e.row.hideFromNonMember?"danger":"info"},{default:c((()=>[b(C(1===e.row.hideFromNonMember?"隐藏":"不隐藏"),1)])),_:2},1032,["type"])):(m(),i("span",Y,"-"))])),_:1}),p(ae,{prop:"createdAt",label:"创建时间",width:"200"},{default:c((e=>[b(C(M(I)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),p(ae,{label:"操作",width:"200"},{default:c((e=>[p(X,{link:"",type:"primary",size:"small",onClick:a=>function(e){T.value=e.id;const{name:a,status:l,des:t,order:r,coverImg:s,isMember:o,hideFromNonMember:u}=e;x((()=>{Object.assign(E,{name:a,status:l,des:t,order:r,coverImg:s,isMember:o,hideFromNonMember:u})})),K.value=!0}(e.row)},{default:c((()=>t[20]||(t[20]=[b(" 编辑 ")]))),_:2},1032,["onClick"]),p(te,{title:"确认删除此分类么?",width:"200","icon-color":"red",onConfirm:a=>async function(e){await U.deleteCats({id:e.id}),z.success("删除分类成功"),G()}(e.row)},{reference:c((()=>[p(X,{link:"",type:"danger",size:"small"},{default:c((()=>t[21]||(t[21]=[b(" 删除分类 ")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[ne,M(Q)]]),p(oe,{class:"mt-5 flex justify-end"},{default:c((()=>[p(se,{"current-page":R.page,"onUpdate:currentPage":t[4]||(t[4]=e=>R.page=e),"page-size":R.size,"onUpdate:pageSize":t[5]||(t[5]=e=>R.size=e),class:"mr-5","page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:M(P),onSizeChange:G,onCurrentChange:G},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),p(de,{modelValue:M(K),"onUpdate:modelValue":t[13]||(t[13]=e=>j(K)?K.value=e:null),"close-on-click-modal":!1,title:M($),width:"570",onClose:t[14]||(t[14]=e=>{return a=M(S),T.value=0,void(null==a||a.resetFields());var a})},{footer:c((()=>[v("span",A,[p(X,{onClick:t[11]||(t[11]=e=>K.value=!1)},{default:c((()=>t[22]||(t[22]=[b("取消")]))),_:1}),p(X,{type:"primary",onClick:t[12]||(t[12]=e=>{var a;null==(a=M(S))||a.validate((async e=>{e&&(T.value?(await U.updateCats({id:T.value,...E}),z({type:"success",message:"更新分类成功！"})):(await U.createCats(E),z({type:"success",message:"创建新的分类成功！"})),K.value=!1,G())}))})},{default:c((()=>[b(C(M(B)),1)])),_:1})])])),default:c((()=>[p(Z,{ref_key:"formPackageRef",ref:S,"label-position":"right","label-width":"100px",model:E,rules:O},{default:c((()=>[p(H,{label:"分类名称",prop:"name"},{default:c((()=>[p(u,{modelValue:E.name,"onUpdate:modelValue":t[6]||(t[6]=e=>E.name=e),placeholder:"请填写分类名称"},null,8,["modelValue"])])),_:1}),p(H,{label:"分类状态",prop:"status"},{default:c((()=>[p(L,{modelValue:E.status,"onUpdate:modelValue":t[7]||(t[7]=e=>E.status=e),placeholder:"请选择分类状态"},{default:c((()=>[(m(!0),i(h,null,w(M(q),(e=>(m(),V(J,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(H,{label:"会员专属",prop:"isMember"},{default:c((()=>[p(ue,{modelValue:E.isMember,"onUpdate:modelValue":t[8]||(t[8]=e=>E.isMember=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1}),1===E.isMember?(m(),V(H,{key:0,label:"非会员隐藏",prop:"hideFromNonMember"},{default:c((()=>[p(ue,{modelValue:E.hideFromNonMember,"onUpdate:modelValue":t[9]||(t[9]=e=>E.hideFromNonMember=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})):F("",!0),p(H,{label:"排序ID",prop:"order"},{default:c((()=>[p(u,{modelValue:E.order,"onUpdate:modelValue":t[10]||(t[10]=e=>E.order=e),modelModifiers:{number:!0},placeholder:"请填写排序ID[数字越大越靠前]"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}});"function"==typeof N&&N(P);export{P as default};
