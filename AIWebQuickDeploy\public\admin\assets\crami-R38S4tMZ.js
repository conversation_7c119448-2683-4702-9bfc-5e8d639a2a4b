
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,r as t,$ as u,P as o,b as d,Q as n,a1 as r,c as s,e as i,f as p,w as m,a2 as c,T as f,j as v,h as g,_ as y,g as _,V as b,W as h,i as w,a5 as C,t as k,ae as V,Y as x,k as j}from"./index-BERX8Mlm.js";import{A as U}from"./package-BcYUNEpY.js";import{C as z}from"./index-gPQwgooA.js";import{u as M}from"./utcFormatTime-BtFjiA-p.js";const I={key:1},q={class:"dialog-footer"},$={style:{"max-height":"200px",overflow:"scroll"}},A={class:"dialog-footer"},R=a({__name:"crami",setup(a){const j=t(),R=t(0),N=t(!1),Y=t(!1),D=t(),L=t(0),F=t([]),P=t([]),S=t(!1),T=t([]),O=t([]),B=u({count:1,drawMjCount:0,model3Count:0,model4Count:0,packageId:void 0}),E=u({useId:"",status:"",page:1,size:15}),Q=u({packageId:[{required:!0,message:"请选择套餐类型",trigger:"change"}],days:[{required:!0,message:"请填写有效期天数",trigger:"blur"}],count:[{required:!0,message:"请填写想要生成的数量",trigger:"blur"}],drawMjCount:[{required:!0,message:"卡密携带绘画数量",trigger:"blur"}],model3Count:[{required:!0,message:"卡密携带基础模型对话数量",trigger:"blur"}],model4Count:[{required:!0,message:"卡密携带高级模型金额",trigger:"blur"}]}),W=t([]);async function G(){try{S.value=!0;const e=await U.queryAllCrami(E),{rows:l,count:a}=e.data;S.value=!1,R.value=a,W.value=l}catch(e){S.value=!1}}async function H(e){const l=await V.queryAllUser({size:30,username:e});P.value=l.data.rows}function J(){!async function(){const e=await U.queryAllPackage({size:100});F.value=e.data.rows}(),N.value=!0}async function K(e){T.value=e}async function X(){try{S.value=!0,await U.batchDelCrami({ids:T.value.map((e=>e.id))}),S.value=!1,x({type:"success",message:"删除卡密成功！"}),G()}catch(e){S.value=!1}}function Z(){Y.value=!0;const e=T.value.map((e=>`${e.code}<----\x3e${e.packageName||"自定义套餐"}`));O.value=e}function ee(){!function(e,l){const a=function(e){return e.join("\n")}(e),t=new Blob([a],{type:"text/plain"}),u=URL.createObjectURL(t),o=document.createElement("a");o.href=u,o.download=`${l}.txt`,document.body.appendChild(o),o.click(),document.body.removeChild(o),setTimeout((()=>URL.revokeObjectURL(u)),0)}(T.value.map((e=>`${e.code}<----\x3e${e.packageName||"自定义套餐"}`)),"卡密信息")}const le=o((()=>W.value.some((e=>e.email))));return d((()=>{G()})),(a,t)=>{const u=v,o=y,d=l,V=n("el-option"),ae=n("el-select"),te=n("el-form-item"),ue=n("el-button"),oe=n("el-form"),de=e,ne=n("el-table-column"),re=n("el-tag"),se=n("el-popconfirm"),ie=n("el-table"),pe=n("el-pagination"),me=n("el-row"),ce=n("el-switch"),fe=n("el-input"),ve=n("el-dialog"),ge=r("loading");return i(),s("div",null,[p(d,null,{title:m((()=>t[17]||(t[17]=[_("div",{class:"flex items-center gap-4"},"卡密设置",-1)]))),content:m((()=>t[18]||(t[18]=[_("div",{class:"text-sm/6"},[_("div",null,"可生成套餐类卡密与自定义卡密，套餐类卡密的设置项更多。"),_("div",null,"过期时间表示卡密的过期时间，不是用户充值后的有效期，设置为0表示永不过期。")],-1)]))),default:m((()=>[T.value.length?(i(),c(u,{key:0,outline:"",type:"danger",onClick:Z},{default:m((()=>t[19]||(t[19]=[g(" 显示选中卡密 ")]))),_:1})):f("",!0),T.value.length?(i(),c(u,{key:1,outline:"",type:"danger",onClick:X},{default:m((()=>t[20]||(t[20]=[g(" 批量删除卡密 ")]))),_:1})):f("",!0),T.value.length?(i(),c(u,{key:2,outline:"",type:"primary",onClick:ee},{default:m((()=>t[21]||(t[21]=[g(" 批量导出卡密 ")]))),_:1})):f("",!0),p(u,{outline:"",type:"success",onClick:J},{default:m((()=>[p(o,{name:"ic:baseline-plus"}),t[22]||(t[22]=g(" 批量生成卡密 "))])),_:1})])),_:1}),p(de,null,{default:m((()=>[p(oe,{ref_key:"formRef",ref:j,inline:!0,model:E},{default:m((()=>[p(te,{label:"用户名称",prop:"useId"},{default:m((()=>[p(ae,{modelValue:E.useId,"onUpdate:modelValue":t[0]||(t[0]=e=>E.useId=e),filterable:"",clearable:"",remote:"","reserve-keyword":"",placeholder:"用户姓名[模糊搜索]","remote-show-suffix":"","remote-method":H,style:{width:"160px"}},{default:m((()=>[(i(!0),s(b,null,h(P.value,(e=>(i(),c(V,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(te,{label:"卡密状态",prop:"status"},{default:m((()=>[p(ae,{modelValue:E.status,"onUpdate:modelValue":t[1]||(t[1]=e=>E.status=e),placeholder:"请选择卡密状态",clearable:"",style:{width:"160px"}},{default:m((()=>[(i(!0),s(b,null,h(w(z),(e=>(i(),c(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(te,null,{default:m((()=>[p(ue,{type:"primary",onClick:G},{default:m((()=>t[23]||(t[23]=[g(" 查询 ")]))),_:1}),p(ue,{onClick:t[2]||(t[2]=e=>{return null==(l=j.value)||l.resetFields(),void G();var l})},{default:m((()=>t[24]||(t[24]=[g(" 重置 ")]))),_:1})])),_:1}),t[25]||(t[25]=_("div",{style:{float:"right"}},null,-1))])),_:1},8,["model"])])),_:1}),p(de,{style:{width:"100%"}},{default:m((()=>[C((i(),c(ie,{border:"",data:W.value,style:{width:"100%"},size:"large",onSelectionChange:K},{default:m((()=>[p(ne,{type:"selection",width:"55"}),p(ne,{prop:"code",label:"卡密账号",width:"180"}),p(ne,{prop:"packageName",label:"套餐类型",width:"180"},{default:m((e=>[p(re,{type:e.row.packageName?"success":"danger"},{default:m((()=>[g(k(e.row.packageName||"自定义卡密"),1)])),_:2},1032,["type"])])),_:1}),p(ne,{prop:"code",label:"卡密状态",width:"180"},{default:m((e=>[p(re,{type:e.row.status?"danger":"success"},{default:m((()=>[g(k(e.row.status?"已使用":"未使用"),1)])),_:2},1032,["type"])])),_:1}),p(ne,{prop:"useId",label:"使用人ID",width:"90"}),le.value?(i(),c(ne,{key:0,prop:"email",label:"使用人邮箱",width:"180"})):f("",!0),p(ne,{prop:"model3Count",label:"基础模型额度"}),p(ne,{prop:"model4Count",label:"高级模型额度"}),p(ne,{prop:"drawMjCount",label:"绘画模型额度"}),p(ne,{prop:"days",label:"有效天数"},{default:m((e=>[g(k(e.row.days>0?`${e.row.days}天`:"永久"),1)])),_:1}),p(ne,{prop:"createdAt",label:"注册时间",width:"200"},{default:m((e=>[g(k(w(M)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),p(ne,{label:"操作"},{default:m((e=>[p(se,{title:"确认删除此卡密么?",width:"200","icon-color":"red",onConfirm:l=>async function(e){await U.delCrami({id:e.id}),x({type:"success",message:"删除卡密成功！"}),G()}(e.row)},{reference:m((()=>[p(ue,{link:"",type:"danger",size:"small"},{default:m((()=>t[26]||(t[26]=[g(" 删除卡密 ")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[ge,S.value]]),p(me,{class:"mt-5 flex justify-end"},{default:m((()=>[p(pe,{"current-page":E.page,"onUpdate:currentPage":t[3]||(t[3]=e=>E.page=e),"page-size":E.size,"onUpdate:pageSize":t[4]||(t[4]=e=>E.size=e),class:"mr-5","page-sizes":[15,50,100,200],layout:"total, sizes, prev, pager, next, jumper",total:R.value,onSizeChange:G,onCurrentChange:G},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),p(ve,{modelValue:N.value,"onUpdate:modelValue":t[13]||(t[13]=e=>N.value=e),title:"生成卡密",width:"450",onClose:t[14]||(t[14]=e=>{var l;null==(l=D.value)||l.resetFields()})},{footer:m((()=>[_("span",q,[p(ue,{onClick:t[11]||(t[11]=e=>N.value=!1)},{default:m((()=>t[27]||(t[27]=[g("放弃生成")]))),_:1}),p(ue,{type:"primary",onClick:t[12]||(t[12]=e=>async function(e){null==e||e.validate((async e=>{e&&(await U.createCrami({...B}),x({type:"success",message:"生成卡密成功！"}),N.value=!1,G())}))}(D.value))},{default:m((()=>t[28]||(t[28]=[g(" 确定生成 ")]))),_:1})])])),default:m((()=>[p(oe,{ref_key:"formCramiRef",ref:D,"label-position":"right","label-width":"100px",model:B,rules:Q},{default:m((()=>[p(me,null,{default:m((()=>[p(te,{label:"是否生成自定义卡密","label-width":"170px"},{default:m((()=>[p(ce,{modelValue:L.value,"onUpdate:modelValue":t[5]||(t[5]=e=>L.value=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1}),L.value?f("",!0):(i(),c(te,{key:0,label:"套餐类型",prop:"packageId"},{default:m((()=>[p(ae,{modelValue:B.packageId,"onUpdate:modelValue":t[6]||(t[6]=e=>B.packageId=e),placeholder:"请选择套餐类型",clearable:"",style:{width:"100%"}},{default:m((()=>[(i(!0),s(b,null,h(F.value,(e=>(i(),c(V,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})),L.value?(i(),s("div",I,[p(te,{label:"基础模型额度",prop:"model3Count"},{default:m((()=>[p(fe,{modelValue:B.model3Count,"onUpdate:modelValue":t[7]||(t[7]=e=>B.model3Count=e),modelModifiers:{number:!0},type:"number",placeholder:"卡密携带基础模型额度"},null,8,["modelValue"])])),_:1}),p(te,{label:"高级模型额度",prop:"model4Count"},{default:m((()=>[p(fe,{modelValue:B.model4Count,"onUpdate:modelValue":t[8]||(t[8]=e=>B.model4Count=e),modelModifiers:{number:!0},type:"number",placeholder:"卡密携带高级模型额度"},null,8,["modelValue"])])),_:1}),p(te,{label:"绘画模型额度",prop:"drawMjCount"},{default:m((()=>[p(fe,{modelValue:B.drawMjCount,"onUpdate:modelValue":t[9]||(t[9]=e=>B.drawMjCount=e),modelModifiers:{number:!0},type:"number",placeholder:"卡密携带绘画积分额度"},null,8,["modelValue"])])),_:1})])):f("",!0),p(te,{label:"生成数量",prop:"count"},{default:m((()=>[p(fe,{modelValue:B.count,"onUpdate:modelValue":t[10]||(t[10]=e=>B.count=e),modelModifiers:{number:!0},type:"number",placeholder:"本次生成的张数"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),p(ve,{modelValue:Y.value,"onUpdate:modelValue":t[16]||(t[16]=e=>Y.value=e),title:"卡密列表"},{footer:m((()=>[_("span",A,[p(ue,{onClick:t[15]||(t[15]=e=>Y.value=!1)},{default:m((()=>t[29]||(t[29]=[g("关闭弹窗")]))),_:1})])])),default:m((()=>[_("div",$,[(i(!0),s(b,null,h(O.value,((e,l)=>(i(),s("div",{key:l},k(e),1)))),128))])])),_:1},8,["modelValue"])])}}});"function"==typeof j&&j(R);export{R as default};
