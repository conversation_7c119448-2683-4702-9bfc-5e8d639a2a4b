
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,af as t,a as n,P as r,a2 as o,T as s,i,e as a,w as u,f as p,aE as c,c as l,V as f,W as m,h as y,t as d,Z as h}from"./index-BERX8Mlm.js";import $ from"./index-CZaxHimt.js";import{_ as g}from"./item.vue_vue_type_script_setup_true_lang-aMXYc_KV.js";var x,w={};var v=function(){if(x)return w;x=1,Object.defineProperty(w,"__esModule",{value:!0}),w.TokenData=void 0,w.parse=p,w.compile=function(t,n={}){const{encode:r=encodeURIComponent,delimiter:o=e}=n,s=c((t instanceof u?t:p(t,n)).tokens,o,r);return function(e={}){const[t,...n]=s(e);if(n.length)throw new TypeError(`Missing parameters: ${n.join(", ")}`);return t}},w.match=function(n,r={}){const{decode:o=decodeURIComponent,delimiter:s=e}=r,{regexp:i,keys:a}=l(n,r),u=a.map((e=>!1===o?t:"param"===e.type?o:e=>e.split(s).map(o)));return function(e){const t=i.exec(e);if(!t)return!1;const n=t[0],r=Object.create(null);for(let o=1;o<t.length;o++){if(void 0===t[o])continue;const e=a[o-1],n=u[o-1];r[e.name]=n(t[o])}return{path:n,params:r}}},w.pathToRegexp=l,w.stringify=function(e){return e.tokens.map((function e(t,o,s){if("text"===t.type)return t.value.replace(/[{}()\[\]+?!:*]/g,"\\$&");if("group"===t.type)return`{${t.tokens.map(e).join("")}}`;const i=function(e){const[t,...o]=e;return!!n.test(t)&&o.every((e=>r.test(e)))}(t.name)&&function(e){return"text"!==(null==e?void 0:e.type)||!r.test(e.value[0])}(s[o+1]),a=i?t.name:JSON.stringify(t.name);if("param"===t.type)return`:${a}`;if("wildcard"===t.type)return`*${a}`;throw new TypeError(`Unexpected token: ${t}`)})).join("")};const e="/",t=e=>e,n=/^[$_\p{ID_Start}]$/u,r=/^[$\u200c\u200d\p{ID_Continue}]$/u,o="https://git.new/pathToRegexpError",s={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function i(e){return e.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}class a{constructor(e){this.tokens=e}peek(){if(!this._peek){const e=this.tokens.next();this._peek=e.value}return this._peek}tryConsume(e){const t=this.peek();if(t.type===e)return this._peek=void 0,t.value}consume(e){const t=this.tryConsume(e);if(void 0!==t)return t;const{type:n,index:r}=this.peek();throw new TypeError(`Unexpected ${n} at ${r}, expected ${e}: ${o}`)}text(){let e,t="";for(;e=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)t+=e;return t}}class u{constructor(e){this.tokens=e}}function p(e,i={}){const{encodePath:p=t}=i,c=new a(function*(e){const t=[...e];let i=0;function a(){let e="";if(n.test(t[++i]))for(e+=t[i];r.test(t[++i]);)e+=t[i];else if('"'===t[i]){let n=i;for(;i<t.length;){if('"'===t[++i]){i++,n=0;break}e+="\\"===t[i]?t[++i]:t[i]}if(n)throw new TypeError(`Unterminated quote at ${n}: ${o}`)}if(!e)throw new TypeError(`Missing parameter name at ${i}: ${o}`);return e}for(;i<t.length;){const e=t[i],n=s[e];if(n)yield{type:n,index:i++,value:e};else if("\\"===e)yield{type:"ESCAPED",index:i++,value:t[i++]};else if(":"===e){const e=a();yield{type:"PARAM",index:i,value:e}}else if("*"===e){const e=a();yield{type:"WILDCARD",index:i,value:e}}else yield{type:"CHAR",index:i,value:t[i++]}}return{type:"END",index:i,value:""}}(e));const l=function e(t){const n=[];for(;;){const r=c.text();r&&n.push({type:"text",value:p(r)});const o=c.tryConsume("PARAM");if(o){n.push({type:"param",name:o});continue}const s=c.tryConsume("WILDCARD");if(s){n.push({type:"wildcard",name:s});continue}if(!c.tryConsume("{"))return c.consume(t),n;n.push({type:"group",tokens:e("}")})}}("END");return new u(l)}function c(e,n,r){const o=e.map((e=>function(e,n,r){if("text"===e.type)return()=>[e.value];if("group"===e.type){const t=c(e.tokens,n,r);return e=>{const[n,...r]=t(e);return r.length?[""]:[n]}}const o=r||t;if("wildcard"===e.type&&!1!==r)return t=>{const r=t[e.name];if(null==r)return["",e.name];if(!Array.isArray(r)||0===r.length)throw new TypeError(`Expected "${e.name}" to be a non-empty array`);return[r.map(((t,n)=>{if("string"!=typeof t)throw new TypeError(`Expected "${e.name}/${n}" to be a string`);return o(t)})).join(n)]};return t=>{const n=t[e.name];if(null==n)return["",e.name];if("string"!=typeof n)throw new TypeError(`Expected "${e.name}" to be a string`);return[o(n)]}}(e,n,r)));return e=>{const t=[""];for(const n of o){const[r,...o]=n(e);t[0]+=r,t.push(...o)}return t}}function l(t,n={}){const{delimiter:r=e,end:o=!0,sensitive:s=!1,trailing:a=!0}=n,c=[],l=[],y=s?"":"i",d=(Array.isArray(t)?t:[t]).map((e=>e instanceof u?e:p(e,n)));for(const{tokens:e}of d)for(const t of f(e,0,[])){const e=m(t,r,c);l.push(e)}let h=`^(?:${l.join("|")})`;a&&(h+=`(?:${i(r)}$)?`),h+=o?"$":`(?=${i(r)}|$)`;return{regexp:new RegExp(h,y),keys:c}}function*f(e,t,n){if(t===e.length)return yield n;const r=e[t];if("group"===r.type){const o=n.slice();for(const n of f(r.tokens,0,o))yield*f(e,t+1,n)}else n.push(r);yield*f(e,t+1,n)}function m(e,t,n){let r="",s="",a=!0;for(let u=0;u<e.length;u++){const p=e[u];if("text"!==p.type)if("param"!==p.type&&"wildcard"!==p.type);else{if(!a&&!s)throw new TypeError(`Missing text after "${p.name}": ${o}`);"param"===p.type?r+=`(${y(t,a?"":s)}+)`:r+="([\\s\\S]+)",n.push(p),s="",a=!1}else r+=i(p.value),s+=p.value,a||(a=p.value.includes(t))}return r}function y(e,t){return t.length<2?e.length<2?`[^${i(e+t)}]`:`(?:(?!${i(e)})[^${i(t)}])`:e.length<2?`(?:(?!${i(t)})[^${i(e)}])`:`(?:(?!${i(t)}|${i(e)})[\\s\\S])`}return w.TokenData=u,w}();const k=h(e({__name:"index",setup(e){const h=t(),x=n(),w=r((()=>{const e=[];return x.settings.home.enable&&e.push({path:x.settings.home.fullPath,title:x.settings.home.title}),h.meta.breadcrumbNeste&&h.meta.breadcrumbNeste.forEach((t=>{!1===t.hide&&e.push({path:t.path,title:t.title})})),e}));return(e,t)=>"pc"===i(x).mode&&"filesystem"!==i(x).settings.app.routeBaseOn?(a(),o($,{key:0,class:"breadcrumb whitespace-nowrap px-2"},{default:u((()=>[p(c,{name:"breadcrumb"},{default:u((()=>[(a(!0),l(f,null,m(i(w),((e,t)=>{return a(),o(g,{key:`${t}_${e.path}_${e.title}`,to:t<i(w).length-1&&""!==e.path?(n=e.path,v.compile(n)(h.params)):""},{default:u((()=>[y(d(e.title),1)])),_:2},1032,["to"]);var n})),128))])),_:1})])),_:1})):s("",!0)}}),[["__scopeId","data-v-4b6f8f11"]]);export{k as default};
