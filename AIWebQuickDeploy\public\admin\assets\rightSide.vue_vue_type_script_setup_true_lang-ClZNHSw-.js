
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./HDropdownMenu.vue_vue_type_script_setup_true_lang-Wc6l-Ngn.js";import{d as s,u as t,a,ag as r,r as n,a0 as o,c as _,e as l,a2 as i,T as u,f as p,i as m,w as c,g,h as d,t as v,_ as h}from"./index-BERX8Mlm.js";import{_ as f}from"./index.vue_vue_type_script_setup_true_lang-PUnUpH4H.js";import{_ as x}from"./index.vue_vue_type_script_setup_true_lang-DN03WRps.js";import{_ as b}from"./index.vue_vue_type_script_setup_true_lang-Do4XPH2t.js";import{_ as y}from"./index.vue_vue_type_script_setup_true_lang-DbfRBGyF.js";const j={class:"flex items-center"},k={class:"flex-center gap-1"},S=s({name:"Tools",__name:"rightSide",setup(s){const S=t(),w=a(),T=r(),q=n(!1);return o((()=>T.avatar),(()=>{q.value&&(q.value=!1)})),(s,t)=>{const a=h,r=e;return l(),_("div",j,[m(w).settings.toolbar.navSearch?(l(),i(b,{key:0})):u("",!0),m(w).settings.toolbar.fullscreen?(l(),i(x,{key:1})):u("",!0),m(w).settings.toolbar.pageReload?(l(),i(y,{key:2})):u("",!0),m(w).settings.toolbar.colorScheme?(l(),i(f,{key:3})):u("",!0),p(r,{items:[[{label:m(w).settings.home.title,handle:()=>m(S).push({path:m(w).settings.home.fullPath}),hide:!m(w).settings.home.enable},{label:"个人设置",handle:()=>m(S).push({name:"personalSetting"})}],[{label:"退出登录",handle:()=>m(T).logout()}]],class:"flex-center cursor-pointer px-2"},{default:c((()=>[g("div",k,[d(v(m(T).username)+" ",1),p(a,{name:"i-ep:caret-bottom"})])])),_:1},8,["items"])])}}});export{S as _};
