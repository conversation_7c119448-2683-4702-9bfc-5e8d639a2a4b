
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as s,Q as o,c as r,e as u,f as i,w as n,j as c,h as d,_ as f,g as m,Y as g,k as p}from"./index-BERX8Mlm.js";import{a as _}from"./config-BrbFL53_.js";const v=l({__name:"localStorage",setup(l){const p=a({localStorageStatus:"",siteUrl:""}),v=t();async function x(){const e=await _.queryConfig({keys:["localStorageStatus","siteUrl"]});Object.assign(p,e.data)}const y=t({siteUrl:[{required:!0,message:"请输入网站地址",trigger:"blur"}]});function S(){var e;null==(e=v.value)||e.validate((async e=>{if(e){try{await _.setConfig({settings:(l=p,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),g.success("变更配置信息成功")}catch(a){}x()}else g.error("请填写完整信息");var l}))}return s((()=>{x()})),(l,a)=>{const t=f,s=c,g=e,_=o("el-switch"),x=o("el-form-item"),b=o("el-col"),U=o("el-row"),V=o("el-input"),j=o("el-form"),w=o("el-card");return u(),r("div",null,[i(g,null,{title:n((()=>a[2]||(a[2]=[m("div",{class:"flex items-center gap-4"},"本地存储参数设置",-1)]))),content:n((()=>a[3]||(a[3]=[m("div",{class:"text-sm/6"},[m("div",null," 开启后将优先使用本地存储方式保存数据，有些场景需开启跨域访问，可能需额外自行解决读写权限问题。 "),m("div",null,"文件存储目录为 /public/file，更新迁移时请做好数据维护及备份。")],-1)]))),default:n((()=>[i(s,{outline:"",onClick:S},{default:n((()=>[i(t,{name:"i-ri:file-text-line"}),a[4]||(a[4]=d(" 保存设置 "))])),_:1})])),_:1}),i(w,{style:{margin:"20px"}},{default:n((()=>[i(j,{ref_key:"formRef",ref:v,model:p,"label-width":"120px",rules:y.value},{default:n((()=>[i(U,null,{default:n((()=>[i(b,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[i(x,{label:"启用状态",prop:"localStorageStatus"},{default:n((()=>[i(_,{modelValue:p.localStorageStatus,"onUpdate:modelValue":a[0]||(a[0]=e=>p.localStorageStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(U,null,{default:n((()=>[i(b,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[i(x,{label:"网站地址",prop:"siteUrl"},{default:n((()=>[i(V,{modelValue:p.siteUrl,"onUpdate:modelValue":a[1]||(a[1]=e=>p.siteUrl=e),placeholder:"网站地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1})])}}});"function"==typeof p&&p(v);export{v as default};
