{"extends": "./tsconfig.paths.json", "compilerOptions": {"module": "commonjs", "moduleResolution": "node", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2022", "sourceMap": false, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts", "src/**/*.js", "src/modules/draw/generation/**/*.ts", "src/modules/draw/generation/**/*.js"], "exclude": ["node_modules", "dist"]}