#!/bin/bash

echo "🔧 99AI 配置修复工具"
echo "================================"

# 检查当前目录
if [ ! -f "AIWebQuickDeploy/package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

cd AIWebQuickDeploy

echo "📋 检查配置文件..."

# 检查 .env 文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，正在创建..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ 已从 .env.example 复制配置文件"
    else
        echo "❌ .env.example 文件也不存在，请手动创建 .env 文件"
        exit 1
    fi
else
    echo "✅ .env 文件存在"
fi

# 检查必要的环境变量
echo ""
echo "🔍 检查环境变量配置..."

check_env_var() {
    local var_name=$1
    local var_value=$(grep "^${var_name}=" .env | cut -d'=' -f2)
    
    if [ -z "$var_value" ]; then
        echo "⚠️  $var_name 未配置"
        return 1
    else
        echo "✅ $var_name = $var_value"
        return 0
    fi
}

# 检查关键配置
missing_config=0

echo "检查数据库配置:"
check_env_var "DB_HOST" || missing_config=1
check_env_var "DB_PORT" || missing_config=1
check_env_var "DB_USER" || missing_config=1
check_env_var "DB_DATABASE" || missing_config=1

echo ""
echo "检查Redis配置:"
check_env_var "REDIS_HOST" || missing_config=1
check_env_var "REDIS_PORT" || missing_config=1

echo ""
echo "检查服务配置:"
check_env_var "PORT" || missing_config=1

if [ $missing_config -eq 1 ]; then
    echo ""
    echo "⚠️  发现配置缺失，请编辑 .env 文件补充配置"
    echo "📝 配置文件位置: $(pwd)/.env"
    echo ""
    echo "🔧 必须配置的项目:"
    echo "   - DB_PASS: MySQL数据库密码"
    echo "   - REDIS_PASSWORD: Redis密码（如果有设置）"
    echo ""
    echo "💡 配置完成后，请重新启动服务"
else
    echo ""
    echo "✅ 所有必要配置都已设置"
fi

# 检查服务状态
echo ""
echo "📊 检查服务状态..."

# 检查MySQL连接
echo "检查MySQL连接..."
mysql_host=$(grep "^DB_HOST=" .env | cut -d'=' -f2)
mysql_port=$(grep "^DB_PORT=" .env | cut -d'=' -f2)

if command -v nc >/dev/null 2>&1; then
    if nc -z "$mysql_host" "$mysql_port" 2>/dev/null; then
        echo "✅ MySQL服务可访问 ($mysql_host:$mysql_port)"
    else
        echo "❌ MySQL服务不可访问 ($mysql_host:$mysql_port)"
        echo "   请确保MySQL服务已启动并且网络可达"
    fi
else
    echo "⚠️  无法检查MySQL连接（nc命令不可用）"
fi

# 检查Redis连接
echo "检查Redis连接..."
redis_host=$(grep "^REDIS_HOST=" .env | cut -d'=' -f2)
redis_port=$(grep "^REDIS_PORT=" .env | cut -d'=' -f2)

if command -v nc >/dev/null 2>&1; then
    if nc -z "$redis_host" "$redis_port" 2>/dev/null; then
        echo "✅ Redis服务可访问 ($redis_host:$redis_port)"
    else
        echo "❌ Redis服务不可访问 ($redis_host:$redis_port)"
        echo "   请确保Redis服务已启动并且网络可达"
    fi
else
    echo "⚠️  无法检查Redis连接（nc命令不可用）"
fi

echo ""
echo "🚀 修复建议:"
echo "1. 确保MySQL和Redis服务已启动"
echo "2. 检查防火墙设置"
echo "3. 验证数据库用户权限"
echo "4. 如果使用Docker，确保容器网络配置正确"

echo ""
echo "📝 快速启动命令:"
echo "   启动MySQL: sudo systemctl start mysql"
echo "   启动Redis: sudo systemctl start redis"
echo "   重启99AI: pm2 restart 99AI"

echo ""
echo "================================"
echo "配置检查完成！"
