
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

header[data-v-c74f61f1]{position:fixed;top:0;right:0;left:0;z-index:2000;display:flex;align-items:center;width:100%;height:var(--g-header-height);padding:0 20px;margin:0 auto;color:var(--g-header-color);background-color:var(--g-header-bg);box-shadow:-1px 0 0 0 var(--g-border-color),1px 0 0 0 var(--g-border-color),0 1px 0 0 var(--g-border-color);transition:background-color .3s}header .header-container[data-v-c74f61f1]{display:flex;gap:30px;align-items:center;justify-content:space-between;width:100%;height:100%;margin:0 auto}header .header-container[data-v-c74f61f1] a.title{position:relative;flex:0;width:inherit;height:inherit;padding:inherit;background-color:inherit}header .header-container[data-v-c74f61f1] a.title .logo{width:initial;height:40px}header .header-container[data-v-c74f61f1] a.title span{font-size:20px;color:var(--g-header-color);letter-spacing:1px}header .header-container .menu-container[data-v-c74f61f1]{flex:1;height:100%;padding:0 20px;overflow-x:auto;-webkit-mask-image:linear-gradient(to right,transparent,#000 20px,#000 calc(100% - 20px),transparent);mask-image:linear-gradient(to right,transparent,#000 20px,#000 calc(100% - 20px),transparent);scrollbar-width:none}header .header-container .menu-container[data-v-c74f61f1]::-webkit-scrollbar{display:none}header .header-container .menu-container .menu[data-v-c74f61f1]{display:inline-flex;height:100%}header .header-container .menu-container .menu[data-v-c74f61f1] .menu-item .menu-item-container{color:var(--g-header-menu-color)}header .header-container .menu-container .menu[data-v-c74f61f1] .menu-item .menu-item-container:hover{color:var(--g-header-menu-hover-color);background-color:var(--g-header-menu-hover-bg)}header .header-container .menu-container .menu[data-v-c74f61f1] .menu-item.active .menu-item-container{color:var(--g-header-menu-active-color);background-color:var(--g-header-menu-active-bg)}.header-enter-active[data-v-c74f61f1],.header-leave-active[data-v-c74f61f1]{transition:transform .3s}.header-enter-from[data-v-c74f61f1],.header-leave-to[data-v-c74f61f1]{transform:translateY(calc(var(--g-header-height) * -1))}
