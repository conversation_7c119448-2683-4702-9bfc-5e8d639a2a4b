
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,P as t,r,a0 as u,b as f,Q as s,c as d,e as p,f as o,w as n,j as i,h as c,_ as y,g as m,Y as z,k as L}from"./index-BERX8Mlm.js";import{a as g}from"./config-BrbFL53_.js";const _=l({__name:"ltzf",setup(l){const L=a({payLtzfStatus:"",payLtzfMchId:"",payLtzfSecret:"",payLtzfNotifyUrl:"",payLtzfReturnUrl:""}),_=t((()=>{const e="1"===L.payLtzfStatus;return{payLtzfStatus:[{required:!0,trigger:"change",message:"请选择当前支付开启状态"}],payLtzfSecret:[{required:e,trigger:"blur",message:"请填写商户秘钥"}],payLtzfMchId:[{required:e,trigger:"blur",message:"请填写商户号"}],payLtzfNotifyUrl:[{required:e,trigger:"blur",message:"请填写支付通知地址"}],payLtzfReturnUrl:[{required:e,trigger:"blur",message:"请填写支付回调地址"}]}})),h=r();async function v(){const e=await g.queryConfig({keys:["payLtzfSecret","payLtzfNotifyUrl","payLtzfReturnUrl","payLtzfMchId","payLtzfStatus"]});Object.assign(L,e.data)}function b(){var e;null==(e=h.value)||e.validate((async e=>{if(e){try{await g.setConfig({settings:(l=L,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),z.success("变更配置信息成功")}catch(a){}v()}else z.error("请填写完整信息");var l}))}return u((()=>L.payLtzfStatus),(()=>{setTimeout((()=>{var e;null==(e=h.value)||e.validateField(["payLtzfSecret","payLtzfMchId","payLtzfNotifyUrl","payLtzfReturnUrl"])}),0)})),f((()=>{v()})),(l,a)=>{const t=y,r=i,u=e,f=s("el-switch"),z=s("el-form-item"),g=s("el-col"),v=s("el-row"),x=s("el-input"),U=s("el-form"),S=s("el-card");return p(),d("div",null,[o(u,null,{title:n((()=>a[5]||(a[5]=[m("div",{class:"flex items-center gap-4"},"蓝兔支付设置",-1)]))),content:n((()=>a[6]||(a[6]=[m("div",{class:"text-sm/6"},[m("div",null,[m("a",{href:"https://www.ltzf.cn/?invite=beraqu",target:"_blank"},"蓝兔支付"),c(" 为第三方支付，接入请购买微信渠道。 ")]),m("div",null,"支付通知地址为： https://您的域名/api/pay/notify。")],-1)]))),default:n((()=>[o(r,{outline:"",onClick:b},{default:n((()=>[o(t,{name:"i-ri:file-text-line"}),a[7]||(a[7]=c(" 保存设置 "))])),_:1})])),_:1}),o(S,{style:{margin:"20px"}},{default:n((()=>[o(U,{ref_key:"formRef",ref:h,rules:_.value,model:L,"label-width":"120px"},{default:n((()=>[o(v,null,{default:n((()=>[o(g,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(z,{label:"启用当前支付",prop:"payLtzfMchId"},{default:n((()=>[o(f,{modelValue:L.payLtzfStatus,"onUpdate:modelValue":a[0]||(a[0]=e=>L.payLtzfStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:n((()=>[o(g,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(z,{label:"商户号",prop:"payLtzfMchId"},{default:n((()=>[o(x,{modelValue:L.payLtzfMchId,"onUpdate:modelValue":a[1]||(a[1]=e=>L.payLtzfMchId=e),placeholder:"请填写商户号",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:n((()=>[o(g,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(z,{label:"商户密钥",prop:"payLtzfSecret"},{default:n((()=>[o(x,{modelValue:L.payLtzfSecret,"onUpdate:modelValue":a[2]||(a[2]=e=>L.payLtzfSecret=e),placeholder:"请填写商户秘钥",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:n((()=>[o(g,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(z,{label:"支付通知地址",prop:"payLtzfSecret"},{default:n((()=>[o(x,{modelValue:L.payLtzfNotifyUrl,"onUpdate:modelValue":a[3]||(a[3]=e=>L.payLtzfNotifyUrl=e),placeholder:"请填写支付通知地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:n((()=>[o(g,{xs:24,md:20,lg:15,xl:12},{default:n((()=>[o(z,{label:"支付回调地址",prop:"payLtzfSecret"},{default:n((()=>[o(x,{modelValue:L.payLtzfReturnUrl,"onUpdate:modelValue":a[4]||(a[4]=e=>L.payLtzfReturnUrl=e),placeholder:"请填写支付成功后的回跳地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof L&&L(_);export{_ as default};
