
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,r as t,$ as o,P as d,b as r,Q as u,a1 as s,c as i,e as n,f as p,w as m,j as c,h as y,_ as f,g,V as k,W as b,i as _,a2 as h,a5 as v,t as w,T,ah as x,X as V,a7 as U,Y as N,a9 as M,aa as P,k as q}from"./index-BERX8Mlm.js";import{A as j}from"./models-Bn8M3XEv.js";import{u as F}from"./upload-DwmqW_vL.js";import{u as A}from"./utcFormatTime-BtFjiA-p.js";import{M as D,a as C,b as S,c as R,d as O,D as B}from"./index-gPQwgooA.js";const L={class:"w-full overflow-y-scroll whitespace-nowrap"},I=["src"],z={class:"input-with-text"},G={class:"input-with-text"},Y={key:0,class:"text-xs text-gray-400 mt-1"},J={class:"flex items-center"},E={class:"radio-row"},K={class:"radio-row"},W={class:"mr-5 flex justify-end"},X=a({__name:"key",setup(a){const q=t(),X=t(0),$=t(!1),Q=t(!1),H=t(!1),Z=o({keyType:"",model:"",page:1,size:10,status:null}),ee=d({get:()=>Z.status??void 0,set:e=>{Z.status=e??null}}),le=t(),ae=t(0),te=o({keyType:1,modelName:"",key:"",modelAvatar:"",status:!0,model:"",isTokenBased:!1,tokenFeeRatio:1e3,modelOrder:0,maxModelTokens:64e3,max_tokens:4096,proxyUrl:"",timeout:300,deduct:1,deductType:1,maxRounds:12,isFileUpload:0,isImageUpload:0,modelLimits:50,modelDescription:"",isNetworkSearch:!1,deepThinkingType:0,deductDeepThink:1,isMcpTool:!1,systemPrompt:"",systemPromptType:0,drawingType:0}),oe=d((()=>(e=>{if(!e)return"";let l=e.trim();return l.endsWith("/")&&(l=l.slice(0,-1)),/\/v\d+(?:beta|alpha)?/.test(l)?l:`${l}/v1`})(te.proxyUrl))),de=o({keyType:[{required:!0,message:"请选择调用模型类型",trigger:"blur"}],modelName:[{required:!0,message:"请填写您的模型名称",trigger:"blur"}],key:[{required:!1,message:"请填写您的调用模型key",trigger:"blur"}],status:[{required:!0,message:"请选择key的显示状态",trigger:"change"}],isFileUpload:[{required:!1,message:"请选择当前模型是否开启文件解析及支持种类",trigger:"change"}],isImageUpload:[{required:!1,message:"请选择当前模型是否开启图片解析及支持种类",trigger:"change"}],isTokenBased:[{required:!0,message:"请选择当前key是否基于token计费",trigger:"change"}],tokenFeeRatio:[{required:!1,message:"token计费比例",trigger:"change"}],model:[{required:!0,message:"请选择当前key需要绑定的模型",trigger:"change"}],modelOrder:[{required:!0,message:"请填写当前模型排序",trigger:"blur"}],maxModelTokens:[{required:!0,message:"请填写模型最大token数",trigger:"blur"}],max_tokens:[{required:!0,message:"请填写模型最大回复token数",trigger:"blur"}],proxyUrl:[{required:!1,message:"请填写指定代理地址",trigger:"blur"}],modelAvatar:[{required:!1,message:"请填写AI模型使用的头像, 不填写使用系统默认",trigger:"blur"}],timeout:[{required:!0,message:"请填写超时时间 默认 60 单位（秒）",trigger:"blur"}],deductType:[{required:!0,message:"请选择当前模型扣费类型",trigger:"change"}],deduct:[{required:!0,message:"请填写当前模型扣费金额（需要是正整数）",trigger:"blur"}],maxRounds:[{required:!0,message:"请填写允许用户选择的最大上下文轮次",trigger:"blur"}],modelLimits:[{required:!0,message:"请填写模型调用频率限制",trigger:"blur"}],modelDescription:[{required:!1,message:"请填写模型描述",trigger:"blur"}],isNetworkSearch:[{required:!1,message:"请填写是否开启网络搜索",trigger:"change"}],deepThinkingType:[{required:!1,message:"请选择深度思考模式",trigger:"change"}],deductDeepThink:[{required:!1,message:"请填写深度思考积分系数",trigger:"blur"}]});const re=d((()=>D[te.keyType])),ue=d((()=>ae.value?"修改模型":"新增模型")),se=d((()=>ae.value?"确认更新":"确认新增")),ie=t([]);async function ne(){try{Q.value=!0;const e=await j.queryModels({...Z,status:ee.value});Q.value=!1;const{rows:l,count:a}=e.data;X.value=a,ie.value=l}catch(e){Q.value=!1}}const pe=(e,l)=>{e&&e.data?te.modelAvatar=e.data:N.error("上传成功但未获取到URL")};async function me(){if(te.modelAvatar)try{N.info("正在重新上传模型头像...");const e=te.modelAvatar;!function(e,l,a){const t=new FormData;t.append("file",e),F.uploadFile(t,"system/models").then((e=>{l({data:e.data}),a&&N.success("重新上传模型头像成功")})).catch((e=>{N.error("文件上传失败"),a&&l===pe&&(te.modelAvatar=a)}))}(await async function(e){const l=await P.get(e,{responseType:"blob"});let a="downloaded_file";const t=l.headers["content-disposition"];if(t){const e=/filename="([^"]+)"/.exec(t);null!=e&&e[1]&&(a=e[1])}else a=function(e){const l=new URL(e),a=l.pathname;return a.substring(a.lastIndexOf("/")+1)}(e);return new File([l.data],a,{type:l.data.type})}(te.modelAvatar),pe,e)}catch(e){N.error("重新上传模型头像失败，请检查链接是否有效")}}const ce=e=>{const{file:l,onSuccess:a,onError:t}=e,o=new FormData;return o.append("file",l),F.uploadFile(o,"system/models").then((e=>(a&&(N.success("上传成功"),a(e)),e))).catch((e=>(t&&t(e),N.error("文件上传失败"),Promise.reject(e))))};function ye(){N({type:"warning",message:"此功能仅开发版支持！"})}const fe=e=>{const l=e.name.toLowerCase(),a=l.substring(l.lastIndexOf("."));return["image/png","image/jpeg","image/gif","image/webp","image/x-icon","image/vnd.microsoft.icon"].includes(e.type)||[".png",".jpg",".jpeg",".gif",".webp",".ico"].includes(a)?!(e.size/1024>3e3)||(N.error("当前限制文件最大不超过 3000KB!"),!1):(N.error("当前系统仅支持 PNG、JPEG、GIF、WebP 和 ICO 格式的图片!"),!1)};return r((()=>{ne()})),(a,t)=>{const o=f,d=c,r=l,P=u("el-option"),F=u("el-select"),D=u("el-form-item"),ge=u("el-button"),ke=u("el-form"),be=e,_e=u("el-tag"),he=u("el-table-column"),ve=u("el-popconfirm"),we=u("el-table"),Te=u("el-pagination"),xe=u("el-row"),Ve=u("el-switch"),Ue=u("el-icon"),Ne=u("el-tooltip"),Me=u("el-input"),Pe=u("el-upload"),qe=u("el-input-number"),je=u("el-radio"),Fe=u("el-radio-group"),Ae=u("el-dialog"),De=s("loading");return n(),i("div",null,[p(r,null,{title:m((()=>t[37]||(t[37]=[g("div",{class:"flex items-center gap-4"},"模型配置说明",-1)]))),content:m((()=>t[38]||(t[38]=[g("div",{class:"text-sm/6"},[g("div",null,"模型分为（基础对话｜创意模型｜特殊模型三类）。"),g("div",null," 基础对话：用户可以在用户端选择的模型，用于对话、问答、聊天等功能，仅支持 OpenAI Chat 格式，其他模型需自行使用分发程序适配。 "),g("div",null," 创意模型：用户端不展示，包含【Midjourney 绘图】【Dalle 绘图】【SDXL 绘图】【Suno音乐】，用于插件调用。 "),g("div",null,[y(" 其中，其中 Midjourney 对接 Midjourney-Proxy-Plus 格式，SDXL、LumaVideo 及 SunoMusic 适配 "),g("a",{href:"https://api.openai.com",target:"_blank"},"LightAi API"),y(" 格式。 ")]),g("div",null,"特殊模型：用户端不展示，包含【TTS朗读】【GPTs】。")],-1)]))),default:m((()=>[p(d,{outline:"",type:"success",onClick:t[0]||(t[0]=e=>$.value=!0)},{default:m((()=>[p(o,{name:"i-ri:file-text-line"}),t[39]||(t[39]=y(" 添加模型 "))])),_:1})])),_:1}),p(be,null,{default:m((()=>[p(ke,{ref_key:"formRef",ref:q,inline:!0,model:Z},{default:m((()=>[p(D,{label:"模型类型",prop:"model"},{default:m((()=>[p(F,{modelValue:Z.keyType,"onUpdate:modelValue":t[1]||(t[1]=e=>Z.keyType=e),filterable:"","allow-create":"",placeholder:"请选择或填写绑定的模型",clearable:"",style:{width:"160px"}},{default:m((()=>[(n(!0),i(k,null,b(_(C),(e=>(n(),h(P,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(D,{label:"使用模型",prop:"model"},{default:m((()=>[p(F,{modelValue:Z.model,"onUpdate:modelValue":t[2]||(t[2]=e=>Z.model=e),filterable:"","allow-create":"",placeholder:"请选择或填写绑定的模型",clearable:"",style:{width:"160px"}},{default:m((()=>[(n(!0),i(k,null,b(_(S),(e=>(n(),h(P,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(D,{label:"显示状态",prop:"status"},{default:m((()=>[p(F,{modelValue:ee.value,"onUpdate:modelValue":t[3]||(t[3]=e=>ee.value=e),placeholder:"请选择模型的显示状态",clearable:"",style:{width:"160px"}},{default:m((()=>[(n(!0),i(k,null,b(_(R),(e=>(n(),h(P,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(D,null,{default:m((()=>[p(ge,{type:"primary",onClick:ne},{default:m((()=>t[40]||(t[40]=[y(" 查询 ")]))),_:1}),p(ge,{onClick:t[4]||(t[4]=e=>{return null==(l=q.value)||l.resetFields(),void ne();var l})},{default:m((()=>t[41]||(t[41]=[y(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),p(be,{style:{width:"100%"}},{default:m((()=>[v((n(),h(we,{border:"",data:ie.value,style:{width:"100%"},size:"large"},{default:m((()=>[p(he,{prop:"keyType",label:"模型类型",width:"120"},{default:m((e=>[p(_e,{type:"success"},{default:m((()=>[y(w(_(O)[e.row.keyType]),1)])),_:2},1024)])),_:1}),p(he,{prop:"modelOrder",label:"模型排序",width:"90",align:"center"}),p(he,{prop:"modelLimits",label:"频率限制",width:"90",align:"center"}),p(he,{prop:"modelName",label:"模型名称",width:"180"}),p(he,{prop:"status",align:"center",label:"显示状态",width:"90"},{default:m((e=>[p(_e,{type:e.row.status?"success":"danger"},{default:m((()=>[y(w(e.row.status?"显示":"隐藏"),1)])),_:2},1032,["type"])])),_:1}),p(he,{prop:"key",label:"模型KEY",width:"460"},{default:m((e=>[g("div",L,w(e.row.key),1)])),_:1}),p(he,{prop:"model",align:"center",label:"绑定模型",width:"180"},{default:m((e=>[p(_e,{type:e.row.model.includes("gpt-4")?"success":"info"},{default:m((()=>[y(w(e.row.model),1)])),_:2},1032,["type"])])),_:1}),p(he,{prop:"isTokenBased",align:"center",label:"Token计费",width:"120"},{default:m((e=>[p(_e,{type:e.row.isTokenBased?"success":"danger"},{default:m((()=>[y(w(e.row.isTokenBased?"是":"否"),1)])),_:2},1032,["type"])])),_:1}),p(he,{prop:"deductType",align:"center",label:"扣费类型",width:"90"},{default:m((e=>[p(_e,{type:1===e.row.deductType?"success":2===e.row.deductType?"warning":"info"},{default:m((()=>[y(w(1===e.row.deductType?"普通积分":2===e.row.deductType?"高级积分":"绘画积分"),1)])),_:2},1032,["type"])])),_:1}),p(he,{prop:"deduct",align:"center",label:"单次扣除",width:"90"},{default:m((e=>[p(_e,{type:1===e.row.deductType?"success":"warning"},{default:m((()=>[y(w(`${e.row.deduct} 积分`),1)])),_:2},1032,["type"])])),_:1}),p(he,{prop:"useCount",align:"center",label:"调用次数",width:"90"}),p(he,{prop:"useToken",align:"center",label:"已使用Token",width:"120"}),p(he,{prop:"maxModelTokens",align:"center",label:"模型最大上下文",width:"140"},{default:m((e=>[p(ge,{type:"info",text:""},{default:m((()=>[y(w(e.row.maxModelTokens||"-"),1)])),_:2},1024)])),_:1}),p(he,{prop:"max_tokens",align:"center",label:"模型最大回复",width:"140"},{default:m((e=>[p(ge,{type:"info",text:""},{default:m((()=>[y(w(e.row.max_tokens||"-"),1)])),_:2},1024)])),_:1}),p(he,{prop:"proxyUrl",align:"center",label:"绑定的代理地址",width:"140"},{default:m((e=>[p(ge,{type:"info",text:""},{default:m((()=>[y(w(e.row.proxyUrl||"-"),1)])),_:2},1024)])),_:1}),p(he,{prop:"createdAt",align:"center",label:"添加时间",width:"120"},{default:m((e=>[y(w(_(A)(e.row.createdAt,"YYYY-MM-DD")),1)])),_:1}),p(he,{fixed:"right",label:"操作",width:"200"},{default:m((e=>[p(ge,{link:"",type:"primary",size:"small",onClick:l=>function(e){ae.value=e.id;const{keyType:l,modelName:a,key:t,status:o,model:d,modelOrder:r,maxModelTokens:u,max_tokens:s,proxyUrl:i,timeout:n,deductType:p,deduct:m,maxRounds:c,modelAvatar:y,isTokenBased:f,tokenFeeRatio:g,isFileUpload:k,isImageUpload:b,modelLimits:_,modelDescription:h,isNetworkSearch:v,deepThinkingType:w,deductDeepThink:T,isMcpTool:x,systemPrompt:V,systemPromptType:U,drawingType:N}=e;M((()=>{Object.assign(te,{keyType:l,modelName:a,key:t,status:Boolean(o),model:d,modelOrder:r,maxModelTokens:u,max_tokens:s,proxyUrl:i,timeout:n,deductType:p,deduct:m,maxRounds:c,modelAvatar:y,isTokenBased:Boolean(f),tokenFeeRatio:g,isFileUpload:k,isImageUpload:b,modelLimits:_,modelDescription:h,isNetworkSearch:Boolean(v),deepThinkingType:Number(w)||0,deductDeepThink:T,isMcpTool:Boolean(x),systemPrompt:V,systemPromptType:U,drawingType:Number(N)||0})})),$.value=!0}(e.row)},{default:m((()=>t[42]||(t[42]=[y(" 变更 ")]))),_:2},1032,["onClick"]),p(ve,{title:"确认删除此秘钥么?",width:"180","icon-color":"red",onConfirm:l=>async function(e){const{id:l}=e;await j.delModels({id:l}),N({type:"success",message:"操作完成！"}),ne()}(e.row)},{reference:m((()=>[p(ge,{link:"",type:"danger",size:"small"},{default:m((()=>t[43]||(t[43]=[y(" 删除秘钥 ")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[De,Q.value]]),p(xe,{class:"mt-5 flex justify-end"},{default:m((()=>[p(Te,{"current-page":Z.page,"onUpdate:currentPage":t[5]||(t[5]=e=>Z.page=e),"page-size":Z.size,"onUpdate:pageSize":t[6]||(t[6]=e=>Z.size=e),class:"mr-5","page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:X.value,onSizeChange:ne,onCurrentChange:ne},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),p(Ae,{modelValue:$.value,"onUpdate:modelValue":t[35]||(t[35]=e=>$.value=e),"close-on-click-modal":!1,title:ue.value,width:"770",class:"max-h-[90vh] overflow-y-auto rounded-md p-4",onClose:t[36]||(t[36]=e=>{return l=le.value,ae.value=0,void(null==l||l.resetFields());var l})},{footer:m((()=>[g("span",W,[p(ge,{onClick:t[33]||(t[33]=e=>$.value=!1)},{default:m((()=>t[74]||(t[74]=[y("取消")]))),_:1}),p(ge,{type:"primary",onClick:t[34]||(t[34]=e=>async function(e){null==e||e.validate((async e=>{if(e){const e=JSON.parse(JSON.stringify(te));if(delete e.id,ae.value&&(e.id=ae.value),1===Number(te.keyType)){const l=JSON.parse(JSON.stringify(te.key)).split("\n");e.key=l}await j.setModels(e),N({type:"success",message:"操作成功！"}),ae.value=0,$.value=!1,ne()}}))}(le.value))},{default:m((()=>[y(w(se.value),1)])),_:1})])])),default:m((()=>[v((n(),h(ke,{ref_key:"formPackageRef",ref:le,"label-position":"right","label-width":"120px",model:te,rules:de},{default:m((()=>[p(D,{label:"模型类型选择",prop:"keyType"},{default:m((()=>[p(F,{modelValue:te.keyType,"onUpdate:modelValue":t[7]||(t[7]=e=>te.keyType=e),placeholder:"请选择模型类型",style:{width:"100%"}},{default:m((()=>[(n(!0),i(k,null,b(_(C),(e=>(n(),h(P,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),[1].includes(Number(te.keyType))?(n(),h(D,{key:0,label:"用户端显示",prop:"status"},{default:m((()=>[p(Ve,{modelValue:te.status,"onUpdate:modelValue":t[8]||(t[8]=e=>te.status=e)},null,8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[44]||(t[44]=[g("div",{style:{width:"250px"}},"关闭将在用户端隐藏此模型、但不会影响后台的调用",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1})):T("",!0),p(D,{label:"模型显示名称",prop:"modelName"},{default:m((()=>[p(Me,{modelValue:te.modelName,"onUpdate:modelValue":t[9]||(t[9]=e=>te.modelName=e),placeholder:"请填写模型显示名称（用户端看到的）"},null,8,["modelValue"])])),_:1}),[1].includes(Number(te.keyType))?(n(),h(D,{key:1,label:"模型简介",prop:"key"},{default:m((()=>[p(Me,{modelValue:te.modelDescription,"onUpdate:modelValue":t[10]||(t[10]=e=>te.modelDescription=e),type:"text",placeholder:"请填写模型简介"},null,8,["modelValue"])])),_:1})):T("",!0),[1].includes(Number(te.keyType))?(n(),h(D,{key:2,label:"模型图标",prop:"modelAvatar"},{default:m((()=>[p(Me,{modelValue:te.modelAvatar,"onUpdate:modelValue":t[11]||(t[11]=e=>te.modelAvatar=e),placeholder:"请填写或上传网站模型图标",clearable:""},{append:m((()=>[p(Pe,{class:"avatar-uploader","http-request":ce,"show-file-list":!1,"on-success":pe,"before-upload":fe,style:{display:"flex","align-items":"center","justify-content":"center"}},{default:m((()=>[te.modelAvatar?(n(),i("img",{key:0,src:te.modelAvatar,style:{"max-width":"1.5rem","max-height":"1.5rem",margin:"5px 0","object-fit":"contain"}},null,8,I)):(n(),h(Ue,{key:1,style:{width:"1rem"}},{default:m((()=>[p(_(V))])),_:1}))])),_:1}),te.modelAvatar?(n(),h(Ue,{key:0,onClick:me,style:{"margin-left":"35px",width:"1rem"}},{default:m((()=>[p(_(U))])),_:1})):T("",!0)])),_:1},8,["modelValue"])])),_:1})):T("",!0),p(D,{label:"模型排序",prop:"modelOrder"},{default:m((()=>[g("div",z,[p(qe,{modelValue:te.modelOrder,"onUpdate:modelValue":t[12]||(t[12]=e=>te.modelOrder=e),max:999,min:0,step:10,class:"input-number",style:{"margin-right":"10px"}},null,8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[45]||(t[45]=[g("div",{style:{width:"250px"}},"模型排序，越小越靠前。",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])])),_:1}),p(D,{label:"模型调用频率",prop:"modelLimits"},{default:m((()=>[g("div",G,[p(qe,{modelValue:te.modelLimits,"onUpdate:modelValue":t[13]||(t[13]=e=>te.modelLimits=e),max:999,min:0,step:5,class:"input-number",style:{"margin-right":"10px"}},null,8,["modelValue"]),t[46]||(t[46]=g("span",{class:"unit-text"},"次/小时",-1))])])),_:1}),p(D,{label:"指定代理地址",prop:"proxyUrl"},{default:m((()=>[p(Me,{modelValue:te.proxyUrl,"onUpdate:modelValue":t[14]||(t[14]=e=>te.proxyUrl=e),placeholder:"例如 https://your-proxy.com，未指定 /v1 等版本时将自动添加 /v1"},null,8,["modelValue"]),oe.value?(n(),i("div",Y," 实际调用地址："+w(oe.value),1)):T("",!0)])),_:1}),p(D,{label:"模型密钥",prop:"key"},{default:m((()=>[p(Me,{modelValue:te.key,"onUpdate:modelValue":t[15]||(t[15]=e=>te.key=e),type:"text",placeholder:"请填写模型Key"},null,8,["modelValue"])])),_:1}),p(D,{label:"账号关联模型",prop:"model"},{default:m((()=>[p(F,{modelValue:te.model,"onUpdate:modelValue":t[16]||(t[16]=e=>te.model=e),filterable:"",clearable:"",placeholder:"请选用或填写绑定的模型","allow-create":""},{default:m((()=>[(n(!0),i(k,null,b(re.value,(e=>(n(),h(P,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(D,{label:"模型扣费类型",prop:"deductType"},{default:m((()=>[p(F,{modelValue:te.deductType,"onUpdate:modelValue":t[17]||(t[17]=e=>te.deductType=e),filterable:"","allow-create":"",clearable:"",placeholder:"请选用模型扣费类型"},{default:m((()=>[(n(!0),i(k,null,b(_(B),(e=>(n(),h(P,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(D,{label:"单次扣除金额",prop:"deduct"},{default:m((()=>[p(Me,{modelValue:te.deduct,"onUpdate:modelValue":t[18]||(t[18]=e=>te.deduct=e),modelModifiers:{number:!0},style:{width:"400px"},placeholder:"请填写单次调用此key的扣费金额！"},null,8,["modelValue"])])),_:1}),[2].includes(Number(te.keyType))?(n(),h(D,{key:3,label:"绘画类型",prop:"drawingType"},{default:m((()=>[g("div",J,[p(Fe,{modelValue:te.drawingType,"onUpdate:modelValue":t[19]||(t[19]=e=>te.drawingType=e),class:"w-[60%]"},{default:m((()=>[g("div",E,[p(je,{label:0},{default:m((()=>t[47]||(t[47]=[y("不是绘画")]))),_:1}),p(je,{label:1},{default:m((()=>t[48]||(t[48]=[y("dalle兼容")]))),_:1}),p(je,{label:2},{default:m((()=>t[49]||(t[49]=[y("gpt-image-1兼容")]))),_:1})]),g("div",K,[p(je,{label:3},{default:m((()=>t[50]||(t[50]=[y("midjourney")]))),_:1}),p(je,{label:4},{default:m((()=>t[51]||(t[51]=[y("chat正则提取")]))),_:1}),p(je,{label:5},{default:m((()=>t[52]||(t[52]=[y("豆包")]))),_:1})])])),_:1},8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[53]||(t[53]=[g("div",{style:{width:"250px"}}," 选择绘画类型：dalle兼容、gpt-image-1兼容、midjourney、chat正则提取、豆包等不同的绘画模型兼容格式 ",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])])),_:1})):T("",!0),[1].includes(Number(te.keyType))?(n(),h(D,{key:4,label:"深度思考",prop:"deepThinkingType"},{default:m((()=>[p(Fe,{modelValue:te.deepThinkingType,"onUpdate:modelValue":t[20]||(t[20]=e=>te.deepThinkingType=e)},{default:m((()=>[p(je,{label:0},{default:m((()=>t[54]||(t[54]=[y("不开启")]))),_:1}),p(je,{label:1},{default:m((()=>t[55]||(t[55]=[y("全局思考")]))),_:1}),p(je,{label:2},{default:m((()=>t[56]||(t[56]=[y("自带思考")]))),_:1})])),_:1},8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[57]||(t[57]=[g("div",{style:{width:"250px"}}," 选择深度思考模式：全局思考使用推理模型混合调用，自带思考使用模型本身的思考能力 ",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1})):T("",!0),te.deepThinkingType>0?(n(),h(D,{key:5,label:"深度思考系数",prop:"deductDeepThink"},{default:m((()=>[p(Me,{modelValue:te.deductDeepThink,"onUpdate:modelValue":t[21]||(t[21]=e=>te.deductDeepThink=e),modelModifiers:{number:!0},style:{width:"400px"},placeholder:"请填写深度思考积分系数"},null,8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[58]||(t[58]=[g("div",{style:{width:"150px"}},"开启深度思考后，扣除积分将在基础积分上乘以此系数",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1})):T("",!0),[1].includes(Number(te.keyType))?(n(),h(D,{key:6,label:"联网搜索",prop:"isNetworkSearch"},{default:m((()=>[p(Ve,{modelValue:te.isNetworkSearch,"onUpdate:modelValue":t[22]||(t[22]=e=>te.isNetworkSearch=e)},null,8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[59]||(t[59]=[g("div",{style:{width:"250px"}},"开启后模型将启用联网搜索功能，用户端将显示联网搜索按钮",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1})):T("",!0),[1].includes(Number(te.keyType))?(n(),h(D,{key:7,label:"上下文限制",prop:"maxRounds"},{default:m((()=>[p(Me,{modelValue:te.maxRounds,"onUpdate:modelValue":t[23]||(t[23]=e=>te.maxRounds=e),modelModifiers:{number:!0},placeholder:"请填写允许用户选择的最高上下文条数！"},null,8,["modelValue"])])),_:1})):T("",!0),[1].includes(Number(te.keyType))?(n(),h(D,{key:8,label:"上下文Tokens",prop:"maxModelTokens"},{default:m((()=>[p(Me,{modelValue:te.maxModelTokens,"onUpdate:modelValue":t[24]||(t[24]=e=>te.maxModelTokens=e),modelModifiers:{number:!0},placeholder:"请填写模型最大Token、不填写默认使用默认！"},null,8,["modelValue"])])),_:1})):T("",!0),[1].includes(Number(te.keyType))?(n(),h(D,{key:9,label:"回复Tokens",prop:"max_tokens"},{default:m((()=>[p(Me,{modelValue:te.max_tokens,"onUpdate:modelValue":t[25]||(t[25]=e=>te.max_tokens=e),modelModifiers:{number:!0},placeholder:"请填写模型最大回复、不填写默认使用默认！"},null,8,["modelValue"])])),_:1})):T("",!0),p(D,{label:"调用超时时间",prop:"timeout"},{default:m((()=>[p(Me,{modelValue:te.timeout,"onUpdate:modelValue":t[26]||(t[26]=e=>te.timeout=e),modelModifiers:{number:!0},placeholder:"请填写key的超时时间单位（秒）！"},null,8,["modelValue"])])),_:1}),p(D,{label:"图片解析",prop:"isImageUpload"},{default:m((()=>[p(Fe,{modelValue:te.isImageUpload,"onUpdate:modelValue":t[27]||(t[27]=e=>te.isImageUpload=e)},{default:m((()=>[p(je,{label:0},{default:m((()=>t[60]||(t[60]=[y(" 不使用 ")]))),_:1}),p(je,{label:1},{default:m((()=>t[61]||(t[61]=[y(" 逆向格式 ")]))),_:1}),p(je,{label:2},{default:m((()=>t[62]||(t[62]=[y(" GPT Vision ")]))),_:1}),p(je,{label:3,disabled:!0,onClick:ye},{default:m((()=>t[63]||(t[63]=[y(" 全局解析 ")]))),_:1})])),_:1},8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[64]||(t[64]=[g("div",{style:{width:"250px"}}," 选择是否开启图片解析及其格式，逆向格式【直接附带链接，仅支持逆向渠道】，GPT Vision【GPT Vision 的识图格式】，全局解析【支持所有格式的图片解析，仅开发版支持】 ",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1}),p(D,{label:"文件解析",prop:"isFileUpload"},{default:m((()=>[p(Fe,{modelValue:te.isFileUpload,"onUpdate:modelValue":t[28]||(t[28]=e=>te.isFileUpload=e)},{default:m((()=>[p(je,{label:0},{default:m((()=>t[65]||(t[65]=[y(" 不使用 ")]))),_:1}),p(je,{label:1},{default:m((()=>t[66]||(t[66]=[y(" 逆向格式 ")]))),_:1}),p(je,{label:2,disabled:!0,onClick:ye},{default:m((()=>t[67]||(t[67]=[y(" 向量解析 ")]))),_:1})])),_:1},8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[68]||(t[68]=[g("div",{style:{width:"250px"}}," 选择是否开启文件解析及其格式，逆向格式【直接附带链接，仅支持逆向渠道】，向量解析【内置的文件分析，支持全模型分析带文字的文件，请注意 tokens 消耗，仅开发版支持】 ",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1}),[1,3].includes(Number(te.keyType))?(n(),h(D,{key:10,label:"token 关联计费",prop:"isTokenBased"},{default:m((()=>[p(Ve,{modelValue:te.isTokenBased,"onUpdate:modelValue":t[29]||(t[29]=e=>te.isTokenBased=e)},null,8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[69]||(t[69]=[g("div",{style:{width:"250px"}}," 关联 token 的梯度计费模型，每次扣除的积分 = 单次扣除金额 *（token 消耗 / token 计费比例）结果向上取整【例如单次扣除金额为 3 积分，token 计费比例为 1000，用户调用消耗 2500 token，那么扣除的积分为 3 *（2500 / 1000）向上取整 9 积分】 ",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1})):T("",!0),[1,3].includes(Number(te.keyType))?(n(),h(D,{key:11,label:"token计费比例",prop:"tokenFeeRatio"},{default:m((()=>[p(Me,{modelValue:te.tokenFeeRatio,"onUpdate:modelValue":t[30]||(t[30]=e=>te.tokenFeeRatio=e),modelModifiers:{number:!0},placeholder:"请填写token计费比例",style:{width:"80%"}},null,8,["modelValue"])])),_:1})):T("",!0),p(D,{label:"预设类型",prop:"systemPromptType"},{default:m((()=>[p(Fe,{modelValue:te.systemPromptType,"onUpdate:modelValue":t[31]||(t[31]=e=>te.systemPromptType=e)},{default:m((()=>[p(je,{label:0},{default:m((()=>t[70]||(t[70]=[y("关闭预设")]))),_:1}),p(je,{label:1},{default:m((()=>t[71]||(t[71]=[y("附加模式")]))),_:1}),p(je,{label:2},{default:m((()=>t[72]||(t[72]=[y("覆盖模式")]))),_:1})])),_:1},8,["modelValue"]),p(Ne,{class:"box-item",effect:"dark",placement:"right"},{content:m((()=>t[73]||(t[73]=[g("div",{style:{width:"250px"}}," 选择模型预设的工作模式：关闭预设-不使用预设，附加模式-在用户输入基础上附加预设，覆盖模式-使用预设覆盖用户输入 ",-1)]))),default:m((()=>[p(Ue,{class:"ml-3 cursor-pointer"},{default:m((()=>[p(_(x))])),_:1})])),_:1})])),_:1}),p(D,{label:"模型预设",prop:"systemPrompt"},{default:m((()=>[p(Me,{modelValue:te.systemPrompt,"onUpdate:modelValue":t[32]||(t[32]=e=>te.systemPrompt=e),type:"textarea",rows:4,placeholder:"请输入模型模型预设内容"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])),[[De,H.value]])])),_:1},8,["modelValue","title"])])}}});"function"==typeof q&&q(X);export{X as default};
