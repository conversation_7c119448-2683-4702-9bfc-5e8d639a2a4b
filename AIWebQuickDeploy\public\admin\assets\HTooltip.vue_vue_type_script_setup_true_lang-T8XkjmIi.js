
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as t,Q as e,a2 as s,c as a,e as o,w as p,aj as l,h as r,t as d,R as n}from"./index-BERX8Mlm.js";const u={key:1},f=t({__name:"HTooltip",props:{text:{default:""},enable:{type:Boolean,default:!0}},setup:t=>(t,f)=>{const i=e("VTooltip");return t.enable?(o(),s(i,n({key:0,"popper-triggers":["hover"]},t.$attrs),{popper:p((()=>[l(t.$slots,"text",{},(()=>[r(d(t.text),1)]))])),default:p((()=>[l(t.$slots,"default")])),_:3},16)):(o(),a("div",u,[l(t.$slots,"default")]))}});export{f as _};
