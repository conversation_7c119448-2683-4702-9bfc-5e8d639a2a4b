
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{r as e,P as l,d as a,be as t,ap as n,bf as o,b as s,a0 as u,au as i,bd as r,bc as d,bs as f,bb as c,bh as v,V as p,bk as b,aS as m,aT as y,a2 as h,e as g,w as k,g as x,a6 as w,T as V,_ as I,i as S}from"./index-BERX8Mlm.js";import{s as T}from"./use-resolve-button-type-DnRVrBaM.js";let C=Symbol("GroupContext"),N=a({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(a,{emit:m,attrs:y,slots:h,expose:g}){var k;let x=null!=(k=a.id)?k:`headlessui-switch-${t()}`,w=n(C,null),[V,I]=function(a,t,n){let o=e(null==n?void 0:n.value),s=l((()=>void 0!==a.value));return[l((()=>s.value?a.value:o.value)),function(e){return s.value||(o.value=e),null==t?void 0:t(e)}]}(l((()=>a.modelValue)),(e=>m("update:modelValue",e)),l((()=>a.defaultChecked)));function S(){I(!V.value)}let N=e(null),B=null===w?N:w.switchRef,P=T(l((()=>({as:a.as,type:y.type}))),B);function _(e){e.preventDefault(),S()}function j(e){e.key===b.Space?(e.preventDefault(),S()):e.key===b.Enter&&function(e){var l,a;let t=null!=(l=null==e?void 0:e.form)?l:e.closest("form");if(t){for(let l of t.elements)if(l!==e&&("INPUT"===l.tagName&&"submit"===l.type||"BUTTON"===l.tagName&&"submit"===l.type||"INPUT"===l.nodeName&&"image"===l.type))return void l.click();null==(a=t.requestSubmit)||a.call(t)}}(e.currentTarget)}function U(e){e.preventDefault()}g({el:B,$el:B});let D=l((()=>{var e,l;return null==(l=null==(e=o(B))?void 0:e.closest)?void 0:l.call(e,"form")}));return s((()=>{u([D],(()=>{if(D.value&&void 0!==a.defaultChecked)return D.value.addEventListener("reset",e),()=>{var l;null==(l=D.value)||l.removeEventListener("reset",e)};function e(){I(a.defaultChecked)}}),{immediate:!0})})),()=>{let{name:e,value:l,form:t,tabIndex:n,...o}=a,s={checked:V.value},u={id:x,ref:B,role:"switch",type:P.value,tabIndex:-1===n?0:n,"aria-checked":V.value,"aria-labelledby":null==w?void 0:w.labelledby.value,"aria-describedby":null==w?void 0:w.describedby.value,onClick:_,onKeyup:j,onKeypress:U};return i(p,[null!=e&&null!=V.value?i(d,f({features:c.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:V.value,form:t,disabled:o.disabled,name:e,value:l})):null,r({ourProps:u,theirProps:{...y,...v(o,["modelValue","defaultChecked"])},slot:s,attrs:y,slots:h,name:"Switch"})])}}});const B={class:"absolute inset-0 h-full w-full flex items-center justify-center"},P=a({__name:"HToggle",props:m({disabled:{type:Boolean,default:!1},onIcon:{},offIcon:{}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const l=y(e,"modelValue");return(e,a)=>{const t=I;return g(),h(S(N),{modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),disabled:e.disabled,class:w(["relative h-5 w-10 inline-flex flex-shrink-0 cursor-pointer border-2 border-transparent rounded-full p-0 vertical-middle disabled-cursor-not-allowed disabled-opacity-50 focus-outline-none focus-visible-ring-2 focus-visible-ring-offset-2 focus-visible-ring-offset-white dark-focus-visible-ring-offset-gray-900",[l.value?"bg-ui-primary":"bg-stone-3 dark-bg-stone-7"]])},{default:k((()=>[x("span",{class:w(["pointer-events-none relative inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out dark-bg-dark",[l.value?"translate-x-5":"translate-x-0"]])},[x("span",B,[l.value&&e.onIcon||!l.value&&e.offIcon?(g(),h(t,{key:0,name:l.value?e.onIcon:e.offIcon,class:"h-3 w-3 text-stone-7 dark-text-stone-3"},null,8,["name"])):V("",!0)])],2)])),_:1},8,["modelValue","disabled","class"])}}});export{P as _};
