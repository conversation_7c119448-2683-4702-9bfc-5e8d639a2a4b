
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as s}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{d as t,u as a,r as e,Q as l,c as i,e as n,f as c,w as o,g as d,h as u,Z as r,k as p}from"./index-BERX8Mlm.js";const _={class:"setting-list"},f={class:"item"},m={class:"action"},b=t({__name:"setting",setup(t){const r=a();function p(){r.push({name:"personalEditPassword"})}return e({headimg:"",mobile:"",name:"",qq:"",wechat:""}),(t,a)=>{const e=l("el-button"),r=l("el-tab-pane"),b=l("el-tabs"),v=s;return n(),i("div",null,[c(v,null,{default:o((()=>[c(b,{"tab-position":"left",style:{height:"600px"}},{default:o((()=>[c(r,{label:"安全设置",class:"security"},{default:o((()=>[a[2]||(a[2]=d("h2",null,"安全设置",-1)),d("div",_,[d("div",f,[a[1]||(a[1]=d("div",{class:"content"},[d("div",{class:"title"},"账户密码"),d("div",{class:"desc"},"当前密码强度：强")],-1)),d("div",m,[c(e,{type:"primary",text:"",onClick:p},{default:o((()=>a[0]||(a[0]=[u(" 修改 ")]))),_:1})])])])])),_:1})])),_:1})])),_:1})])}}});"function"==typeof p&&p(b);const v=r(b,[["__scopeId","data-v-a9bbbcfb"]]);export{v as default};
