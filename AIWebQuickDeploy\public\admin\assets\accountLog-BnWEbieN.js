
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,r as t,$ as r,b as u,Q as s,a1 as o,c as d,e as n,f as i,w as p,g as c,V as m,W as f,a2 as g,i as _,h as v,a5 as w,t as h,ae as y,k as b}from"./index-BERX8Mlm.js";import{R as x,k as C,U as k,i as z}from"./index-gPQwgooA.js";import{u as j}from"./utcFormatTime-BtFjiA-p.js";const I=l({__name:"accountLog",setup(l){const b=t(),I=t(0),T=t(!1),U=r({userId:"",rechargeType:"",packageId:"",page:1,size:15});r({model3Count:[{required:!0,message:"请填写赠送基础模型额度",trigger:"blur"}],model4Count:[{required:!0,message:"请填写赠送高级模型额度",trigger:"blur"}],drawMjCount:[{required:!0,message:"请填写赠送绘画积分额度",trigger:"blur"}]});const V=t(),q=t([]);async function M(){try{T.value=!0;const e=await y.queryUserAccountLog(U),{rows:a,count:l}=e.data;T.value=!1,I.value=l,q.value=a}catch(e){T.value=!1}}async function A(e){const a=await y.queryAllUser({size:30,keyword:e});V.value=a.data.rows}return u((()=>M())),(l,t)=>{const r=a,u=s("el-option"),y=s("el-select"),D=s("el-form-item"),Y=s("el-button"),F=s("el-form"),L=e,R=s("el-avatar"),S=s("el-table-column"),$=s("el-tag"),P=s("el-table"),Q=s("el-pagination"),W=s("el-row"),B=o("loading");return n(),d("div",null,[i(r,null,{title:p((()=>t[5]||(t[5]=[c("div",{class:"flex items-center gap-4"},"账户明细",-1)]))),_:1}),i(L,null,{default:p((()=>[i(F,{ref_key:"formRef",ref:b,inline:!0,model:U},{default:p((()=>[i(D,{label:"用户名称",prop:"userId"},{default:p((()=>[i(y,{modelValue:U.userId,"onUpdate:modelValue":t[0]||(t[0]=e=>U.userId=e),filterable:"",clearable:"",remote:"","reserve-keyword":"",placeholder:"昵称|手机号|邮箱[模糊搜索]","remote-show-suffix":"","remote-method":A,style:{width:"200px"}},{default:p((()=>[(n(!0),d(m,null,f(V.value,(e=>(n(),g(u,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(D,{label:"充值类型",prop:"rechargeType"},{default:p((()=>[i(y,{modelValue:U.rechargeType,"onUpdate:modelValue":t[1]||(t[1]=e=>U.rechargeType=e),placeholder:"请选择充值类型",clearable:"",style:{width:"160px"}},{default:p((()=>[(n(!0),d(m,null,f(_(x),(e=>(n(),g(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(D,null,{default:p((()=>[i(Y,{type:"primary",onClick:M},{default:p((()=>t[6]||(t[6]=[v(" 查询 ")]))),_:1}),i(Y,{onClick:t[2]||(t[2]=e=>{return null==(a=b.value)||a.resetFields(),void M();var a})},{default:p((()=>t[7]||(t[7]=[v(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),i(L,{style:{width:"100%"}},{default:p((()=>[w((n(),g(P,{border:"",data:q.value,style:{width:"100%"},size:"large"},{default:p((()=>[i(S,{prop:"avatar",label:"用户头像",width:"120",fixed:""},{default:p((e=>[i(R,{src:e.row.avatar},null,8,["src"])])),_:1}),i(S,{prop:"username",label:"用户名称",width:"150",fixed:""}),i(S,{prop:"nickname",label:"用户昵称",width:"150"}),i(S,{prop:"userId",label:"用户ID",width:"80"}),i(S,{prop:"uid",label:"订单ID",width:"130"}),i(S,{prop:"email",label:"用户邮箱",width:"250",align:"left"}),i(S,{prop:"balanceInfo.useModel4Count",label:"充值类型",width:"160",align:"center"},{default:p((e=>[i($,{type:"success"},{default:p((()=>{var a,l;return[v(h((null==(a=e.row)?void 0:a.rechargeType)?_(C)[null==(l=e.row)?void 0:l.rechargeType]:"---"),1)]})),_:2},1024)])),_:1}),i(S,{prop:"model3Count",label:"基础模型额度",width:"120",align:"center"}),i(S,{prop:"model4Count",label:"高级模型额度",width:"120",align:"center"}),i(S,{prop:"drawMjCount",label:"绘画余额额度",width:"120",align:"center"}),i(S,{label:"额度有效期",width:"170",align:"center"},{default:p((e=>[i($,{type:"success"},{default:p((()=>{var a,l;return[v(h((null==(a=e.row)?void 0:a.days)<=0?"永久时效":`${null==(l=e.row)?void 0:l.days}天`),1)]})),_:2},1024)])),_:1}),i(S,{prop:"status",label:"用户状态",width:"120",align:"center"},{default:p((({row:e})=>[i($,{type:_(k)[e.status]},{default:p((()=>[v(h(_(z)[e.status]),1)])),_:2},1032,["type"])])),_:1}),i(S,{prop:"createdAt",label:"充值时间",width:"200",align:"center"},{default:p((e=>[v(h(_(j)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1})])),_:1},8,["data"])),[[B,T.value]]),i(W,{class:"mt-5 flex justify-end"},{default:p((()=>[i(Q,{"current-page":U.page,"onUpdate:currentPage":t[3]||(t[3]=e=>U.page=e),"page-size":U.size,"onUpdate:pageSize":t[4]||(t[4]=e=>U.size=e),class:"mr-5","page-sizes":[15,30,50,100],layout:"total, sizes, prev, pager, next, jumper",total:I.value,onSizeChange:M,onCurrentChange:M},null,8,["current-page","page-size","total"])])),_:1})])),_:1})])}}});"function"==typeof b&&b(I);export{I as default};
