
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as t,r as a,P as r,b as u,Q as d,c as n,e as s,f as o,w as i,j as g,h as m,_ as f,g as p,Y as S,k as c}from"./index-BERX8Mlm.js";import{a as M}from"./config-BrbFL53_.js";const _=l({__name:"register",setup(l){const c=t({registerSendStatus:"",registerSendModel3Count:"",registerSendModel4Count:"",registerSendDrawMjCount:"",firstRegisterSendStatus:0,firstRegisterSendRank:"",firstRegisterSendModel3Count:"",firstRegisterSendModel4Count:"",firstRegisterSendDrawMjCount:"",signInStatus:"",signInModel3Count:"",signInModel4Count:"",signInMjDrawToken:"",visitorModel3Num:null,visitorModel4Num:null,visitorMJNum:null}),_=a({visitorModel3Num:[{required:!0,trigger:"blur",message:"请填写每日限制的基础模型积分"}],visitorModel4Num:[{required:!0,trigger:"blur",message:"请填写每日限制的高级模型积分"}],visitorMJNum:[{required:!0,trigger:"blur",message:"请填写每日限制的绘画额度积分"}],signInStatus:[{required:!0,trigger:"blur",message:"请选择是否开启签到奖励"}],signInModel3Count:[{required:!0,trigger:"blur",message:"请填写赠送的基础模型额度"}],signInModel4Count:[{required:!0,trigger:"blur",message:"请填写赠送的高级模型额度"}],signInMjDrawToken:[{required:!0,trigger:"blur",message:"请填写赠送的绘画Token数量"}],registerSendStatus:[{required:!0,trigger:"change",message:"请确认是否开启注册赠送"}],firstRegisterSendStatus:[{required:!0,trigger:"change",message:"请确认是否开启优先注册赠送"}]}),b=a();async function V(){const e=await M.queryConfig({keys:["visitorModel4Num","visitorModel3Num","visitorMJNum","registerSendStatus","registerSendModel3Count","registerSendModel4Count","registerSendDrawMjCount","firstRegisterSendStatus","firstRegisterSendRank","firstRegisterSendModel3Count","firstRegisterSendModel4Count","firstRegisterSendDrawMjCount","signInModel3Count","signInModel4Count","signInMjDrawToken","signInStatus"]});e.data.firstRegisterSendStatus&&(e.data.firstRegisterSendStatus=Number(e.data.firstRegisterSendStatus)),e.data.registerSendStatus&&(e.data.registerSendStatus=Number(e.data.registerSendStatus)),Object.assign(c,e.data)}function C(){var e;null==(e=b.value)||e.validate((async e=>{if(e){try{await M.setConfig({settings:(l=c,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),S.success("变更配置信息成功")}catch(t){}V()}else S.error("请填写完整信息");var l}))}return r((()=>[{required:c.firstRegisterSendStatus,message:"开启优先注册赠送选项后需填写此项",trigger:"change"}])),r((()=>[{required:c.registerSendStatus,message:"开启注册赠送选项后需填写此项",trigger:"change"}])),u((()=>{V()})),(l,t)=>{const a=f,r=g,u=e,S=d("el-switch"),M=d("el-form-item"),V=d("el-col"),v=d("el-row"),x=d("el-input"),R=d("el-divider"),h=d("el-tooltip"),I=d("el-form"),j=d("el-card");return s(),n("div",null,[o(u,null,{title:i((()=>t[16]||(t[16]=[p("div",{class:"flex items-center gap-4"},"基础访问设置",-1)]))),content:i((()=>t[17]||(t[17]=[p("div",{class:"text-sm/6"},[p("div",null," 注册与访问设置支持为新用户定义默认赠送额度，涵盖对话次数、普通绘画次数和高级绘画次数。 "),p("div",null," 系统还为最初注册的前x名用户提供额外奖励，同时允许通过邀请机制为新用户及邀请者设置特定的奖励额度。 "),p("div",null," 此外，管理员可配置签到奖励和为访客分配可使用的额度，以鼓励日常活跃和吸引更多用户体验平台。 ")],-1)]))),default:i((()=>[o(r,{outline:"",text:"",onClick:C},{default:i((()=>[o(a,{name:"i-ri:file-text-line"}),t[18]||(t[18]=m(" 保存设置 "))])),_:1})])),_:1}),o(j,{style:{margin:"20px"}},{default:i((()=>[o(I,{ref_key:"formRef",ref:b,rules:_.value,model:c,"label-width":"220px"},{default:i((()=>[t[19]||(t[19]=p("h5",null,"注册赠送",-1)),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"是否开启注册赠送",prop:"registerSendStatus"},{default:i((()=>[o(S,{modelValue:c.registerSendStatus,"onUpdate:modelValue":t[0]||(t[0]=e=>c.registerSendStatus=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"注册赠送基础模型对话额度",prop:"registerSendModel3Count"},{default:i((()=>[o(x,{modelValue:c.registerSendModel3Count,"onUpdate:modelValue":t[1]||(t[1]=e=>c.registerSendModel3Count=e),placeholder:"首次注册赠基础模型对话额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"注册赠送高级模型对话额度",prop:"registerSendModel4Count"},{default:i((()=>[o(x,{modelValue:c.registerSendModel4Count,"onUpdate:modelValue":t[2]||(t[2]=e=>c.registerSendModel4Count=e),placeholder:"首次注册赠高级模型对话额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"注册赠送绘画额度",prop:"registerSendDrawMjCount"},{default:i((()=>[o(x,{modelValue:c.registerSendDrawMjCount,"onUpdate:modelValue":t[3]||(t[3]=e=>c.registerSendDrawMjCount=e),placeholder:"首次注册赠送MJ额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(R),t[20]||(t[20]=p("h5",null,"限定注册赠送",-1)),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"开启优先注册赠送",prop:"firstRegisterSendStatus"},{default:i((()=>[o(S,{modelValue:c.firstRegisterSendStatus,"onUpdate:modelValue":t[4]||(t[4]=e=>c.firstRegisterSendStatus=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"前多少名获得奖励",prop:"firstRegisterSendRank"},{default:i((()=>[o(x,{modelValue:c.firstRegisterSendRank,"onUpdate:modelValue":t[5]||(t[5]=e=>c.firstRegisterSendRank=e),placeholder:"设置优先注册前N名可以获得奖励",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"优先赠基础模型送对话额度",prop:"firstRegisterSendModel3Count"},{default:i((()=>[o(x,{modelValue:c.firstRegisterSendModel3Count,"onUpdate:modelValue":t[6]||(t[6]=e=>c.firstRegisterSendModel3Count=e),placeholder:"优先注册用户额外赠送基础模型对话额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"优先赠高级模型送对话额度",prop:"firstRegisterSendModel4Count"},{default:i((()=>[o(x,{modelValue:c.firstRegisterSendModel4Count,"onUpdate:modelValue":t[7]||(t[7]=e=>c.firstRegisterSendModel4Count=e),placeholder:"优先注册用户额外赠送高级模型对话额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"优先赠送绘画额度",prop:"firstRegisterSendDrawMjCount"},{default:i((()=>[o(x,{modelValue:c.firstRegisterSendDrawMjCount,"onUpdate:modelValue":t[8]||(t[8]=e=>c.firstRegisterSendDrawMjCount=e),placeholder:"优先注册用户额外赠送MJ额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(R),t[21]||(t[21]=p("h5",null,"签到奖励",-1)),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"开启签到奖励",prop:"signInStatus"},{default:i((()=>[o(h,{class:"box-item",effect:"dark",content:"如您启用签到奖励、则用户端则可以通过每日签到获取额度！",placement:"right"},{default:i((()=>[o(S,{modelValue:c.signInStatus,"onUpdate:modelValue":t[9]||(t[9]=e=>c.signInStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"赠送基础模型额度",prop:"signInModel3Count"},{default:i((()=>[o(x,{modelValue:c.signInModel3Count,"onUpdate:modelValue":t[10]||(t[10]=e=>c.signInModel3Count=e),type:"number",placeholder:"请填写签到赠送的基础模型额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"赠送高级模型额度",prop:"signInModel4Count"},{default:i((()=>[o(x,{modelValue:c.signInModel4Count,"onUpdate:modelValue":t[11]||(t[11]=e=>c.signInModel4Count=e),type:"number",placeholder:"请填写签到赠送的高级模型额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"赠送绘画额度",prop:"signInMjDrawToken"},{default:i((()=>[o(x,{modelValue:c.signInMjDrawToken,"onUpdate:modelValue":t[12]||(t[12]=e=>c.signInMjDrawToken=e),type:"number",placeholder:"请填写签到赠送绘画额度",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(R),t[22]||(t[22]=p("h5",null,"访客设置",-1)),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"基础模型额度",prop:"visitorModel3Num"},{default:i((()=>[o(x,{modelValue:c.visitorModel3Num,"onUpdate:modelValue":t[13]||(t[13]=e=>c.visitorModel3Num=e),type:"number",placeholder:"请填写每日限制基础模型积分",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"高级模型额度",prop:"visitorModel4Num"},{default:i((()=>[o(x,{modelValue:c.visitorModel4Num,"onUpdate:modelValue":t[14]||(t[14]=e=>c.visitorModel4Num=e),type:"number",placeholder:"请填写每日限制的高级模型积分",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(v,null,{default:i((()=>[o(V,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(M,{label:"绘画积分额度",prop:"visitorMJNum"},{default:i((()=>[o(x,{modelValue:c.visitorMJNum,"onUpdate:modelValue":t[15]||(t[15]=e=>c.visitorMJNum=e),type:"number",placeholder:"请填写每日限制的绘画额度积分",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof c&&c(_);export{_ as default};
