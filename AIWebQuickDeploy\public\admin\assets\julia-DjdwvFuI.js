
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

function e(e,t,n){return void 0===n&&(n=""),void 0===t&&(t="\\b"),new RegExp("^"+n+"(("+e.join(")|(")+"))"+t)}var t=["[<>]:","[<>=]=","<<=?",">>>?=?","=>","--?>","<--[->]?","\\/\\/","\\.{2,3}","[\\.\\\\%*+\\-<>!\\/^|&]=?","\\?","\\$","~",":"],n=e(["[<>]:","[<>=]=","[!=]==","<<=?",">>>?=?","=>?","--?>","<--[->]?","\\/\\/","[\\\\%*+\\-<>!\\/^|&\\u00F7\\u22BB]=?","\\?","\\$","~",":","\\u00D7","\\u2208","\\u2209","\\u220B","\\u220C","\\u2218","\\u221A","\\u221B","\\u2229","\\u222A","\\u2260","\\u2264","\\u2265","\\u2286","\\u2288","\\u228A","\\u22C5","\\b(in|isa)\\b(?!.?\\()"],""),r=/^[;,()[\]{}]/,a=/^[_A-Za-z\u00A1-\u2217\u2219-\uFFFF][\w\u00A1-\u2217\u2219-\uFFFF]*!*/,i=e(["\\\\[0-7]{1,3}","\\\\x[A-Fa-f0-9]{1,2}","\\\\[abefnrtv0%?'\"\\\\]","([^\\u0027\\u005C\\uD800-\\uDFFF]|[\\uD800-\\uDFFF][\\uDC00-\\uDFFF])"],"'"),s=["if","else","elseif","while","for","begin","let","end","do","try","catch","finally","return","break","continue","global","local","const","export","import","importall","using","function","where","macro","module","baremodule","struct","type","mutable","immutable","quote","typealias","abstract","primitive","bitstype"],u=["true","false","nothing","NaN","Inf"],o=e(["begin","function","type","struct","immutable","let","macro","for","while","quote","if","else","elseif","try","finally","catch","do"]),c=e(["end","else","elseif","catch","finally"]),l=e(s),m=e(u),f=/^@[_A-Za-z\u00A1-\uFFFF][\w\u00A1-\uFFFF]*!*/,p=/^:[_A-Za-z\u00A1-\uFFFF][\w\u00A1-\uFFFF]*!*/,h=/^(`|([_A-Za-z\u00A1-\uFFFF]*"("")?))/,d=e(t,"","@"),F=e(t,"",":");function k(e){return e.nestedArrays>0}function b(e,t){return void 0===t&&(t=0),e.scopes.length<=t?null:e.scopes[e.scopes.length-(t+1)]}function g(e,t){if(e.match("#=",!1))return t.tokenize=x,t.tokenize(e,t);var i=t.leavingExpr;if(e.sol()&&(i=!1),t.leavingExpr=!1,i&&e.match(/^'+/))return"operator";if(e.match(/\.{4,}/))return"error";if(e.match(/\.{1,3}/))return"operator";if(e.eatSpace())return null;var s,u=e.peek();if("#"===u)return e.skipToEnd(),"comment";if("["===u&&(t.scopes.push("["),t.nestedArrays++),"("===u&&(t.scopes.push("("),t.nestedGenerators++),k(t)&&"]"===u){for(;t.scopes.length&&"["!==b(t);)t.scopes.pop();t.scopes.pop(),t.nestedArrays--,t.leavingExpr=!0}if(function(e){return e.nestedGenerators>0}(t)&&")"===u){for(;t.scopes.length&&"("!==b(t);)t.scopes.pop();t.scopes.pop(),t.nestedGenerators--,t.leavingExpr=!0}if(k(t)){if("end"==t.lastToken&&e.match(":"))return"operator";if(e.match("end"))return"number"}if((s=e.match(o,!1))&&t.scopes.push(s[0]),e.match(c,!1)&&t.scopes.pop(),e.match(/^::(?![:\$])/))return t.tokenize=v,t.tokenize(e,t);if(!i&&(e.match(p)||e.match(F)))return"builtin";if(e.match(n))return"operator";if(e.match(/^\.?\d/,!1)){var z=RegExp(/^im\b/),y=!1;if(e.match(/^0x\.[0-9a-f_]+p[\+\-]?[_\d]+/i)&&(y=!0),e.match(/^0x[0-9a-f_]+/i)&&(y=!0),e.match(/^0b[01_]+/i)&&(y=!0),e.match(/^0o[0-7_]+/i)&&(y=!0),e.match(/^(?:(?:\d[_\d]*)?\.(?!\.)(?:\d[_\d]*)?|\d[_\d]*\.(?!\.)(?:\d[_\d]*))?([Eef][\+\-]?[_\d]+)?/i)&&(y=!0),e.match(/^\d[_\d]*(e[\+\-]?\d+)?/i)&&(y=!0),y)return e.match(z),t.leavingExpr=!0,"number"}if(e.match("'"))return t.tokenize=A,t.tokenize(e,t);if(e.match(h))return t.tokenize=function(e){'"""'===e.substr(-3)?e='"""':'"'===e.substr(-1)&&(e='"');function t(t,n){if(t.eat("\\"))t.next();else{if(t.match(e))return n.tokenize=g,n.leavingExpr=!0,"string";t.eat(/[`"]/)}return t.eatWhile(/[^\\`"]/),"string"}return t}(e.current()),t.tokenize(e,t);if(e.match(f)||e.match(d))return"meta";if(e.match(r))return null;if(e.match(l))return"keyword";if(e.match(m))return"builtin";var E=t.isDefinition||"function"==t.lastToken||"macro"==t.lastToken||"type"==t.lastToken||"struct"==t.lastToken||"immutable"==t.lastToken;return e.match(a)?E?"."===e.peek()?(t.isDefinition=!0,"variable"):(t.isDefinition=!1,"def"):(t.leavingExpr=!0,"variable"):(e.next(),"error")}function v(e,t){return e.match(/.*?(?=[,;{}()=\s]|$)/),e.match("{")?t.nestedParameters++:e.match("}")&&t.nestedParameters>0&&t.nestedParameters--,t.nestedParameters>0?e.match(/.*?(?={|})/)||e.next():0==t.nestedParameters&&(t.tokenize=g),"builtin"}function x(e,t){return e.match("#=")&&t.nestedComments++,e.match(/.*?(?=(#=|=#))/)||e.skipToEnd(),e.match("=#")&&(t.nestedComments--,0==t.nestedComments&&(t.tokenize=g)),"comment"}function A(e,t){var n,r=!1;if(e.match(i))r=!0;else if(n=e.match(/\\u([a-f0-9]{1,4})(?=')/i)){((a=parseInt(n[1],16))<=55295||a>=57344)&&(r=!0,e.next())}else if(n=e.match(/\\U([A-Fa-f0-9]{5,8})(?=')/)){var a;(a=parseInt(n[1],16))<=1114111&&(r=!0,e.next())}return r?(t.leavingExpr=!0,t.tokenize=g,"string"):(e.match(/^[^']+(?=')/)||e.skipToEnd(),e.match("'")&&(t.tokenize=g),"error")}const z={name:"julia",startState:function(){return{tokenize:g,scopes:[],lastToken:null,leavingExpr:!1,isDefinition:!1,nestedArrays:0,nestedComments:0,nestedGenerators:0,nestedParameters:0,firstParenPos:-1}},token:function(e,t){var n=t.tokenize(e,t),r=e.current();return r&&n&&(t.lastToken=r),n},indent:function(e,t,n){var r=0;return("]"===t||")"===t||/^end\b/.test(t)||/^else/.test(t)||/^catch\b/.test(t)||/^elseif\b/.test(t)||/^finally/.test(t))&&(r=-1),(e.scopes.length+r)*n.unit},languageData:{indentOnInput:/^\s*(end|else|catch|finally)\b$/,commentTokens:{line:"#",block:{open:"#=",close:"=#"}},closeBrackets:{brackets:["(","[","{",'"']},autocomplete:s.concat(u)}};export{z as julia};
