
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import e from"./HDropdown-DFGm5c_S.js";import{_ as o}from"./HTabList.vue_vue_type_script_setup_true_lang-BEyYCazB.js";import{d as t,a as n,a2 as i,e as a,w as r,f as s,_ as l,i as c}from"./index-BERX8Mlm.js";const m=t({name:"ColorScheme",__name:"index",setup(t){const m=n();function d(e){var o;const{startViewTransition:t}=(n=()=>{m.currentColorScheme&&m.setColorScheme("dark"===m.currentColorScheme?"light":"dark")},{startViewTransition:function(){if(document.startViewTransition&&!window.matchMedia("(prefers-reduced-motion: reduce)").matches)return document.startViewTransition((async()=>{await Promise.resolve(n())}));n()}});var n;null==(o=t())||o.ready.then((()=>{const o=e.clientX,t=e.clientY,n=[`circle(0px at ${o}px ${t}px)`,`circle(${Math.hypot(Math.max(o,innerWidth-o),Math.max(t,innerHeight-t))}px at ${o}px ${t}px)`];document.documentElement.animate({clipPath:"dark"!==m.settings.app.colorScheme?n:n.reverse()},{duration:300,easing:"ease-out",pseudoElement:"dark"!==m.settings.app.colorScheme?"::view-transition-new(root)":"::view-transition-old(root)"})}))}return(t,n)=>{const p=l,u=o,h=e;return a(),i(h,{class:"flex-center cursor-pointer px-2 py-1"},{dropdown:r((()=>[s(u,{modelValue:c(m).settings.app.colorScheme,"onUpdate:modelValue":n[0]||(n[0]=e=>c(m).settings.app.colorScheme=e),options:[{icon:"i-ri:sun-line",label:"",value:"light"},{icon:"i-ri:moon-line",label:"",value:"dark"},{icon:"i-codicon:color-mode",label:"",value:""}],class:"m-3"},null,8,["modelValue"])])),default:r((()=>[s(p,{name:{"":"i-codicon:color-mode",light:"i-ri:sun-line",dark:"i-ri:moon-line"}[c(m).settings.app.colorScheme],onClick:d},null,8,["name"])])),_:1})}}});export{m as _};
