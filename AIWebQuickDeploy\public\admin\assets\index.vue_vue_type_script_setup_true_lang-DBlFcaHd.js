
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,al as t,c as s,e as a,g as n,T as l,aj as i,i as o,h as c,t as d}from"./index-BERX8Mlm.js";const r={class:"page-header mb-5 flex flex-wrap items-center justify-between gap-5 bg-[var(--g-container-bg)] px-5 py-4 transition-background-color-300"},x={class:"main flex-[1_1_70%]"},m={class:"text-2xl"},p={class:"mt-2 text-sm text-stone-5 empty-hidden"},f={key:0,class:"ml-a flex-none"},g=e({name:"PageHeader",__name:"index",props:{title:{},content:{}},setup(e){const g=t();return(e,t)=>(a(),s("div",r,[n("div",x,[n("div",m,[i(e.$slots,"title",{},(()=>[c(d(e.title),1)]))]),n("div",p,[i(e.$slots,"content",{},(()=>[c(d(e.content),1)]))])]),o(g).default?(a(),s("div",f,[i(e.$slots,"default")])):l("",!0)]))}});export{g as _};
