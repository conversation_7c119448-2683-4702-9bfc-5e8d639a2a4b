
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,c as s,e as a,i as r,f as n,_ as o}from"./index-BERX8Mlm.js";import{u as i}from"./useMainPage-Dbp8uSF1.js";const t=e({name:"PageReload",__name:"index",setup(e){const t=i();return(e,i)=>{const c=o;return a(),s("span",{class:"flex-center cursor-pointer px-2 py-1",onClick:i[0]||(i[0]=e=>r(t).reload())},[n(c,{name:"i-iconoir:refresh-double"})])}}});export{t as _};
