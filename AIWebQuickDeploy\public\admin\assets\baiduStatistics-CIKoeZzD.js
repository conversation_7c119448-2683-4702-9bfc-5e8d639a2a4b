
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as d,b as u,Q as t,c as i,e as o,f as n,w as s,j as r,h as f,_ as c,g as p,Y as b,k as m}from"./index-BERX8Mlm.js";import{a as _}from"./config-BrbFL53_.js";const y=l({__name:"baiduStatistics",setup(l){const m=a({baiduCode:"",baiduSiteId:"",baiduToken:"",baiduApiKey:"",baiduSecretKey:"",baiduRefreshToken:""}),y=d({}),k=d();async function x(){const e=await _.queryConfig({keys:["baiduCode","baiduSiteId","baiduToken","baiduApiKey","baiduSecretKey","baiduRefreshToken"]});Object.assign(m,e.data)}function h(){var e;null==(e=k.value)||e.validate((async e=>{if(e){try{await _.setConfig({settings:(l=m,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),b.success("变更配置信息成功")}catch(a){}x()}else b.error("请填写完整信息");var l}))}return u((()=>{x()})),(l,a)=>{const d=c,u=r,b=e,_=t("el-input"),x=t("el-form-item"),g=t("el-col"),V=t("el-row"),K=t("el-form"),v=t("el-card");return o(),i("div",null,[n(b,null,{title:s((()=>a[6]||(a[6]=[p("div",{class:"flex items-center gap-4"},"百度统计设置",-1)]))),content:s((()=>a[7]||(a[7]=[p("div",{class:"text-sm/6"},[p("div",null,"百度统计主要用于展示，实际的统计数据将在网站首页显示。"),p("div",null,[f(" 为获取更精确的数据分析，请参考"),p("a",{href:"https://tongji.baidu.com/api/manual/Chapter2/openapi.html",target:"_blank"},"百度统计接口说明"),f("，申请专属于您网站的 siteId 、key 以及 token 等信息。 ")]),p("div",null," 百度统计提供的是一项免费服务，如果您选择不使用这项服务，只需将相关设置项留空即可。 ")],-1)]))),default:s((()=>[n(u,{outline:"",onClick:h},{default:s((()=>[n(d,{name:"i-ri:file-text-line"}),a[8]||(a[8]=f(" 保存设置 "))])),_:1})])),_:1}),n(v,{style:{margin:"20px"}},{default:s((()=>[n(K,{ref_key:"formRef",ref:k,rules:y.value,model:m,"label-width":"120px"},{default:s((()=>[n(V,null,{default:s((()=>[n(g,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(x,{label:"siteId",prop:"baiduSiteId"},{default:s((()=>[n(_,{modelValue:m.baiduSiteId,"onUpdate:modelValue":a[0]||(a[0]=e=>m.baiduSiteId=e),placeholder:"请填写百度site_id",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(V,null,{default:s((()=>[n(g,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(x,{label:"ApiKey",prop:"baiduApiKey"},{default:s((()=>[n(_,{modelValue:m.baiduApiKey,"onUpdate:modelValue":a[1]||(a[1]=e=>m.baiduApiKey=e),placeholder:"请填写百度apiKey",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(V,null,{default:s((()=>[n(g,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(x,{label:"SecretKey",prop:"baiduSecretKey"},{default:s((()=>[n(_,{modelValue:m.baiduSecretKey,"onUpdate:modelValue":a[2]||(a[2]=e=>m.baiduSecretKey=e),placeholder:"请填写百度 secretKey",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(V,null,{default:s((()=>[n(g,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(x,{label:"AccessToken",prop:"baiduToken"},{default:s((()=>[n(_,{modelValue:m.baiduToken,"onUpdate:modelValue":a[3]||(a[3]=e=>m.baiduToken=e),placeholder:"请填写百度 access_token",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(V,null,{default:s((()=>[n(g,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(x,{label:"RefreshToken",prop:"baiduRefreshToken"},{default:s((()=>[n(_,{modelValue:m.baiduRefreshToken,"onUpdate:modelValue":a[4]||(a[4]=e=>m.baiduRefreshToken=e),placeholder:"请填写百度 refresh_token",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(V,null,{default:s((()=>[n(g,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(x,{label:"统计代码",prop:"baiduCode"},{default:s((()=>[n(_,{modelValue:m.baiduCode,"onUpdate:modelValue":a[5]||(a[5]=e=>m.baiduCode=e),placeholder:"填写百度统计代码可统计每日访问量详情，如果没有使用用请查看详细文档！",type:"textarea",rows:12,clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof m&&m(y);export{y as default};
