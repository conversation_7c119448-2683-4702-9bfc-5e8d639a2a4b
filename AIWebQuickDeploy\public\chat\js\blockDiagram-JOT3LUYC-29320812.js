var ye=Object.defineProperty,be=Object.defineProperties;var we=Object.getOwnPropertyDescriptors;var Ot=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,Le=Object.prototype.propertyIsEnumerable;var Rt=(e,t,r)=>t in e?ye(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,st=(e,t)=>{for(var r in t||(t={}))me.call(t,r)&&Rt(e,r,t[r]);if(Ot)for(var r of Ot(t))Le.call(t,r)&&Rt(e,r,t[r]);return e},it=(e,t)=>be(e,we(t));var O=(e,t,r)=>new Promise((i,l)=>{var s=c=>{try{n(r.next(c))}catch(p){l(p)}},a=c=>{try{n(r.throw(c))}catch(p){l(p)}},n=c=>c.done?i(c.value):Promise.resolve(c.value).then(s,a);n((r=r.apply(e,t)).next())});import{_ as d,d as P,l as L,j as W,a9 as Se,F as ot,aa as q,ab as Gt,ac as ve,u as nt,ad as Ee,T as _e,k as ke,t as De,A as Te,Y as Ne,B as Ce,ae as Be,af as mt,e as Ie,i as zt}from"./chart-vendor-e1d59b84.js";import{c as Oe}from"./clone-92746810.js";import{G as Re}from"./graph-f794edc0.js";import"./utils-vendor-c35799af.js";import"./vue-vendor-d751b0f5.js";import"./_baseUniq-5ee25ed9.js";var Lt=function(){var e=d(function(C,y,g,f){for(g=g||{},f=C.length;f--;g[C[f]]=y);return g},"o"),t=[1,7],r=[1,13],i=[1,14],l=[1,15],s=[1,19],a=[1,16],n=[1,17],c=[1,18],p=[8,30],h=[8,21,28,29,30,31,32,40,44,47],x=[1,23],b=[1,24],m=[8,15,16,21,28,29,30,31,32,40,44,47],v=[8,15,16,21,27,28,29,30,31,32,40,44,47],N=[1,49],S={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:d(function(y,g,f,w,D,o,T){var u=o.length-1;switch(D){case 4:w.getLogger().debug("Rule: separator (NL) ");break;case 5:w.getLogger().debug("Rule: separator (Space) ");break;case 6:w.getLogger().debug("Rule: separator (EOF) ");break;case 7:w.getLogger().debug("Rule: hierarchy: ",o[u-1]),w.setHierarchy(o[u-1]);break;case 8:w.getLogger().debug("Stop NL ");break;case 9:w.getLogger().debug("Stop EOF ");break;case 10:w.getLogger().debug("Stop NL2 ");break;case 11:w.getLogger().debug("Stop EOF2 ");break;case 12:w.getLogger().debug("Rule: statement: ",o[u]),typeof o[u].length=="number"?this.$=o[u]:this.$=[o[u]];break;case 13:w.getLogger().debug("Rule: statement #2: ",o[u-1]),this.$=[o[u-1]].concat(o[u]);break;case 14:w.getLogger().debug("Rule: link: ",o[u],y),this.$={edgeTypeStr:o[u],label:""};break;case 15:w.getLogger().debug("Rule: LABEL link: ",o[u-3],o[u-1],o[u]),this.$={edgeTypeStr:o[u],label:o[u-1]};break;case 18:const E=parseInt(o[u]),I=w.generateId();this.$={id:I,type:"space",label:"",width:E,children:[]};break;case 23:w.getLogger().debug("Rule: (nodeStatement link node) ",o[u-2],o[u-1],o[u]," typestr: ",o[u-1].edgeTypeStr);const _=w.edgeStrToEdgeData(o[u-1].edgeTypeStr);this.$=[{id:o[u-2].id,label:o[u-2].label,type:o[u-2].type,directions:o[u-2].directions},{id:o[u-2].id+"-"+o[u].id,start:o[u-2].id,end:o[u].id,label:o[u-1].label,type:"edge",directions:o[u].directions,arrowTypeEnd:_,arrowTypeStart:"arrow_open"},{id:o[u].id,label:o[u].label,type:w.typeStr2Type(o[u].typeStr),directions:o[u].directions}];break;case 24:w.getLogger().debug("Rule: nodeStatement (abc88 node size) ",o[u-1],o[u]),this.$={id:o[u-1].id,label:o[u-1].label,type:w.typeStr2Type(o[u-1].typeStr),directions:o[u-1].directions,widthInColumns:parseInt(o[u],10)};break;case 25:w.getLogger().debug("Rule: nodeStatement (node) ",o[u]),this.$={id:o[u].id,label:o[u].label,type:w.typeStr2Type(o[u].typeStr),directions:o[u].directions,widthInColumns:1};break;case 26:w.getLogger().debug("APA123",this?this:"na"),w.getLogger().debug("COLUMNS: ",o[u]),this.$={type:"column-setting",columns:o[u]==="auto"?-1:parseInt(o[u])};break;case 27:w.getLogger().debug("Rule: id-block statement : ",o[u-2],o[u-1]),w.generateId(),this.$=it(st({},o[u-2]),{type:"composite",children:o[u-1]});break;case 28:w.getLogger().debug("Rule: blockStatement : ",o[u-2],o[u-1],o[u]);const A=w.generateId();this.$={id:A,type:"composite",label:"",children:o[u-1]};break;case 29:w.getLogger().debug("Rule: node (NODE_ID separator): ",o[u]),this.$={id:o[u]};break;case 30:w.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",o[u-1],o[u]),this.$={id:o[u-1],label:o[u].label,typeStr:o[u].typeStr,directions:o[u].directions};break;case 31:w.getLogger().debug("Rule: dirList: ",o[u]),this.$=[o[u]];break;case 32:w.getLogger().debug("Rule: dirList: ",o[u-1],o[u]),this.$=[o[u-1]].concat(o[u]);break;case 33:w.getLogger().debug("Rule: nodeShapeNLabel: ",o[u-2],o[u-1],o[u]),this.$={typeStr:o[u-2]+o[u],label:o[u-1]};break;case 34:w.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",o[u-3],o[u-2]," #3:",o[u-1],o[u]),this.$={typeStr:o[u-3]+o[u],label:o[u-2],directions:o[u-1]};break;case 35:case 36:this.$={type:"classDef",id:o[u-1].trim(),css:o[u].trim()};break;case 37:this.$={type:"applyClass",id:o[u-1].trim(),styleClass:o[u].trim()};break;case 38:this.$={type:"applyStyles",id:o[u-1].trim(),stylesStr:o[u].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:r,29:i,31:l,32:s,40:a,44:n,47:c},{8:[1,20]},e(p,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:r,29:i,31:l,32:s,40:a,44:n,47:c}),e(h,[2,16],{14:22,15:x,16:b}),e(h,[2,17]),e(h,[2,18]),e(h,[2,19]),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(m,[2,25],{27:[1,25]}),e(h,[2,26]),{19:26,26:12,32:s},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:r,29:i,31:l,32:s,40:a,44:n,47:c},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(v,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(p,[2,13]),{26:35,32:s},{32:[2,14]},{17:[1,36]},e(m,[2,24]),{11:37,13:4,14:22,15:x,16:b,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:r,29:i,31:l,32:s,40:a,44:n,47:c},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(v,[2,30]),{18:[1,43]},{18:[1,44]},e(m,[2,23]),{18:[1,45]},{30:[1,46]},e(h,[2,28]),e(h,[2,35]),e(h,[2,36]),e(h,[2,37]),e(h,[2,38]),{37:[1,47]},{34:48,35:N},{15:[1,50]},e(h,[2,27]),e(v,[2,33]),{39:[1,51]},{34:52,35:N,39:[2,31]},{32:[2,15]},e(v,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:d(function(y,g){if(g.recoverable)this.trace(y);else{var f=new Error(y);throw f.hash=g,f}},"parseError"),parse:d(function(y){var g=this,f=[0],w=[],D=[null],o=[],T=this.table,u="",E=0,I=0,_=2,A=1,et=o.slice.call(arguments,1),M=Object.create(this.lexer),J={yy:{}};for(var tt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,tt)&&(J.yy[tt]=this.yy[tt]);M.setInput(y,J.yy),J.yy.lexer=M,J.yy.parser=this,typeof M.yylloc=="undefined"&&(M.yylloc={});var Q=M.yylloc;o.push(Q);var fe=M.options&&M.options.ranges;typeof J.yy.parseError=="function"?this.parseError=J.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function xe(X){f.length=f.length-2*X,D.length=D.length-X,o.length=o.length-X}d(xe,"popStack");function Bt(){var X;return X=w.pop()||M.lex()||A,typeof X!="number"&&(X instanceof Array&&(w=X,X=w.pop()),X=g.symbols_[X]||X),X}d(Bt,"lex");for(var K,rt,j,bt,at={},ht,$,It,dt;;){if(rt=f[f.length-1],this.defaultActions[rt]?j=this.defaultActions[rt]:((K===null||typeof K=="undefined")&&(K=Bt()),j=T[rt]&&T[rt][K]),typeof j=="undefined"||!j.length||!j[0]){var wt="";dt=[];for(ht in T[rt])this.terminals_[ht]&&ht>_&&dt.push("'"+this.terminals_[ht]+"'");M.showPosition?wt="Parse error on line "+(E+1)+`:
`+M.showPosition()+`
Expecting `+dt.join(", ")+", got '"+(this.terminals_[K]||K)+"'":wt="Parse error on line "+(E+1)+": Unexpected "+(K==A?"end of input":"'"+(this.terminals_[K]||K)+"'"),this.parseError(wt,{text:M.match,token:this.terminals_[K]||K,line:M.yylineno,loc:Q,expected:dt})}if(j[0]instanceof Array&&j.length>1)throw new Error("Parse Error: multiple actions possible at state: "+rt+", token: "+K);switch(j[0]){case 1:f.push(K),D.push(M.yytext),o.push(M.yylloc),f.push(j[1]),K=null,I=M.yyleng,u=M.yytext,E=M.yylineno,Q=M.yylloc;break;case 2:if($=this.productions_[j[1]][1],at.$=D[D.length-$],at._$={first_line:o[o.length-($||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-($||1)].first_column,last_column:o[o.length-1].last_column},fe&&(at._$.range=[o[o.length-($||1)].range[0],o[o.length-1].range[1]]),bt=this.performAction.apply(at,[u,I,E,J.yy,j[1],D,o].concat(et)),typeof bt!="undefined")return bt;$&&(f=f.slice(0,-1*$*2),D=D.slice(0,-1*$),o=o.slice(0,-1*$)),f.push(this.productions_[j[1]][0]),D.push(at.$),o.push(at._$),It=T[f[f.length-2]][f[f.length-1]],f.push(It);break;case 3:return!0}}return!0},"parse")},B=function(){var C={EOF:1,parseError:d(function(g,f){if(this.yy.parser)this.yy.parser.parseError(g,f);else throw new Error(g)},"parseError"),setInput:d(function(y,g){return this.yy=g||this.yy||{},this._input=y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var y=this._input[0];this.yytext+=y,this.yyleng++,this.offset++,this.match+=y,this.matched+=y;var g=y.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),y},"input"),unput:d(function(y){var g=y.length,f=y.split(/(?:\r\n?|\n)/g);this._input=y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var w=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),f.length-1&&(this.yylineno-=f.length-1);var D=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:f?(f.length===w.length?this.yylloc.first_column:0)+w[w.length-f.length].length-f[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[D[0],D[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:d(function(y){this.unput(this.match.slice(y))},"less"),pastInput:d(function(){var y=this.matched.substr(0,this.matched.length-this.match.length);return(y.length>20?"...":"")+y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var y=this.match;return y.length<20&&(y+=this._input.substr(0,20-y.length)),(y.substr(0,20)+(y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var y=this.pastInput(),g=new Array(y.length+1).join("-");return y+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:d(function(y,g){var f,w,D;if(this.options.backtrack_lexer&&(D={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(D.yylloc.range=this.yylloc.range.slice(0))),w=y[0].match(/(?:\r\n?|\n).*/g),w&&(this.yylineno+=w.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:w?w[w.length-1].length-w[w.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+y[0].length},this.yytext+=y[0],this.match+=y[0],this.matches=y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(y[0].length),this.matched+=y[0],f=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),f)return f;if(this._backtrack){for(var o in D)this[o]=D[o];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var y,g,f,w;this._more||(this.yytext="",this.match="");for(var D=this._currentRules(),o=0;o<D.length;o++)if(f=this._input.match(this.rules[D[o]]),f&&(!g||f[0].length>g[0].length)){if(g=f,w=o,this.options.backtrack_lexer){if(y=this.test_match(f,D[o]),y!==!1)return y;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(y=this.test_match(g,D[w]),y!==!1?y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var g=this.next();return g||this.lex()},"lex"),begin:d(function(g){this.conditionStack.push(g)},"begin"),popState:d(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:d(function(g){this.begin(g)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:d(function(g,f,w,D){switch(w){case 0:return 10;case 1:return g.getLogger().debug("Found space-block"),31;case 2:return g.getLogger().debug("Found nl-block"),31;case 3:return g.getLogger().debug("Found space-block"),29;case 4:g.getLogger().debug(".",f.yytext);break;case 5:g.getLogger().debug("_",f.yytext);break;case 6:return 5;case 7:return f.yytext=-1,28;case 8:return f.yytext=f.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",f.yytext),28;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:g.getLogger().debug("LEX: POPPING STR:",f.yytext),this.popState();break;case 14:return g.getLogger().debug("LEX: STR end:",f.yytext),"STR";case 15:return f.yytext=f.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",f.yytext),21;case 16:return f.yytext="1",g.getLogger().debug("COLUMNS (LEX)",f.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 39:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 42:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 43:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";case 45:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";case 46:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 50:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 62:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 63:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 65:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 66:return this.pushState("NODE"),36;case 67:return this.pushState("NODE"),36;case 68:return this.pushState("NODE"),36;case 69:return this.pushState("NODE"),36;case 70:return this.pushState("NODE"),36;case 71:return this.pushState("NODE"),36;case 72:return this.pushState("NODE"),36;case 73:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),38;case 75:return g.getLogger().debug("Lex: NODE_ID",f.yytext),32;case 76:return g.getLogger().debug("Lex: EOF",f.yytext),8;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return g.getLogger().debug("LEX: NODE_DESCR:",f.yytext),"NODE_DESCR";case 84:g.getLogger().debug("LEX POPPING"),this.popState();break;case 85:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",f.yytext),"DIR";case 87:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",f.yytext),"DIR";case 88:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",f.yytext),"DIR";case 89:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",f.yytext),"DIR";case 90:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",f.yytext),"DIR";case 91:return f.yytext=f.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",f.yytext),"DIR";case 92:return f.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",f.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 94:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 95:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 96:return g.getLogger().debug("Lex: LINK",f.yytext),15;case 97:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 98:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 99:return g.getLogger().debug("Lex: START_LINK",f.yytext),this.pushState("LLABEL"),16;case 100:this.pushState("md_string");break;case 101:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),g.getLogger().debug("Lex: LINK","#"+f.yytext+"#"),15;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 104:return this.popState(),g.getLogger().debug("Lex: LINK",f.yytext),15;case 105:return g.getLogger().debug("Lex: COLON",f.yytext),f.yytext=f.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return C}();S.lexer=B;function k(){this.yy={}}return d(k,"Parser"),k.prototype=S,S.Parser=k,new k}();Lt.parser=Lt;var ze=Lt,G=new Map,kt=[],St=new Map,At="color",Mt="fill",Ae="bgFill",Zt=",",Me=P(),pt=new Map,Fe=d(e=>Ie.sanitizeText(e,Me),"sanitizeText"),We=d(function(e,t=""){let r=pt.get(e);r||(r={id:e,styles:[],textStyles:[]},pt.set(e,r)),t!=null&&t.split(Zt).forEach(i=>{const l=i.replace(/([^;]*);/,"$1").trim();if(RegExp(At).exec(i)){const a=l.replace(Mt,Ae).replace(At,Mt);r.textStyles.push(a)}r.styles.push(l)})},"addStyleClass"),Pe=d(function(e,t=""){const r=G.get(e);t!=null&&(r.styles=t.split(Zt))},"addStyle2Node"),Ye=d(function(e,t){e.split(",").forEach(function(r){let i=G.get(r);if(i===void 0){const l=r.trim();i={id:l,type:"na",children:[]},G.set(l,i)}i.classes||(i.classes=[]),i.classes.push(t)})},"setCssClass"),qt=d((e,t)=>{var l,s,a,n;const r=e.flat(),i=[];for(const c of r){if(c.label&&(c.label=Fe(c.label)),c.type==="classDef"){We(c.id,c.css);continue}if(c.type==="applyClass"){Ye(c.id,(l=c==null?void 0:c.styleClass)!=null?l:"");continue}if(c.type==="applyStyles"){c!=null&&c.stylesStr&&Pe(c.id,c==null?void 0:c.stylesStr);continue}if(c.type==="column-setting")t.columns=(s=c.columns)!=null?s:-1;else if(c.type==="edge"){const p=((a=St.get(c.id))!=null?a:0)+1;St.set(c.id,p),c.id=p+"-"+c.id,kt.push(c)}else{c.label||(c.type==="composite"?c.label="":c.label=c.id);const p=G.get(c.id);if(p===void 0?G.set(c.id,c):(c.type!=="na"&&(p.type=c.type),c.label!==c.id&&(p.label=c.label)),c.children&&qt(c.children,c),c.type==="space"){const h=(n=c.width)!=null?n:1;for(let x=0;x<h;x++){const b=Oe(c);b.id=b.id+"-"+x,G.set(b.id,b),i.push(b)}}else p===void 0&&i.push(c)}}t.children=i},"populateBlockDatabase"),Dt=[],ct={id:"root",type:"composite",children:[],columns:-1},He=d(()=>{L.debug("Clear called"),De(),ct={id:"root",type:"composite",children:[],columns:-1},G=new Map([["root",ct]]),Dt=[],pt=new Map,kt=[],St=new Map},"clear");function Jt(e){switch(L.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return L.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}d(Jt,"typeStr2Type");function Qt(e){switch(L.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}d(Qt,"edgeTypeStr2Type");function $t(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}d($t,"edgeStrToEdgeData");var Ft=0,Ke=d(()=>(Ft++,"id-"+Math.random().toString(36).substr(2,12)+"-"+Ft),"generateId"),Xe=d(e=>{ct.children=e,qt(e,ct),Dt=ct.children},"setHierarchy"),Ue=d(e=>{const t=G.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),je=d(()=>[...G.values()],"getBlocksFlat"),Ve=d(()=>Dt||[],"getBlocks"),Ge=d(()=>kt,"getEdges"),Ze=d(e=>G.get(e),"getBlock"),qe=d(e=>{G.set(e.id,e)},"setBlock"),Je=d(()=>console,"getLogger"),Qe=d(function(){return pt},"getClasses"),$e={getConfig:d(()=>ot().block,"getConfig"),typeStr2Type:Jt,edgeTypeStr2Type:Qt,edgeStrToEdgeData:$t,getLogger:Je,getBlocksFlat:je,getBlocks:Ve,getEdges:Ge,setHierarchy:Xe,getBlock:Ze,setBlock:qe,getColumns:Ue,getClasses:Qe,clear:He,generateId:Ke},tr=$e,gt=d((e,t)=>{const r=Ce,i=r(e,"r"),l=r(e,"g"),s=r(e,"b");return Te(i,l,s,t)},"fade"),er=d(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${gt(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${gt(e.mainBkg,.5)};
    fill: ${gt(e.clusterBkg,.5)};
    stroke: ${gt(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
`,"getStyles"),rr=er,ar=d((e,t,r,i)=>{t.forEach(l=>{ur[l](e,r,i)})},"insertMarkers"),sr=d((e,t,r)=>{L.trace("Making markers for ",r),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),ir=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),nr=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),lr=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",r+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),cr=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",r+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),or=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),hr=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),dr=d((e,t,r)=>{e.append("marker").attr("id",r+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",r+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),gr=d((e,t,r)=>{e.append("defs").append("marker").attr("id",r+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),ur={extension:sr,composition:ir,aggregation:nr,dependency:lr,lollipop:cr,point:or,circle:hr,cross:dr,barb:gr},pr=ar,Ut,jt,Vt,F=(Vt=(jt=(Ut=P())==null?void 0:Ut.block)==null?void 0:jt.padding)!=null?Vt:8;function te(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};const r=t%e,i=Math.floor(t/e);return{px:r,py:i}}d(te,"calculateBlockPosition");var fr=d(e=>{var i,l;let t=0,r=0;for(const s of e.children){const{width:a,height:n,x:c,y:p}=(i=s.size)!=null?i:{width:0,height:0,x:0,y:0};L.debug("getMaxChildSize abc95 child:",s.id,"width:",a,"height:",n,"x:",c,"y:",p,s.type),s.type!=="space"&&(a>t&&(t=a/((l=e.widthInColumns)!=null?l:1)),n>r&&(r=n))}return{width:t,height:r}},"getMaxChildSize");function ft(e,t,r=0,i=0){var a,n,c,p,h,x,b,m,v,N,S,B,k,C,y;L.debug("setBlockSizes abc95 (start)",e.id,(a=e==null?void 0:e.size)==null?void 0:a.x,"block width =",e==null?void 0:e.size,"sieblingWidth",r),(n=e==null?void 0:e.size)!=null&&n.width||(e.size={width:r,height:i,x:0,y:0});let l=0,s=0;if(((c=e.children)==null?void 0:c.length)>0){for(const E of e.children)ft(E,t);const g=fr(e);l=g.width,s=g.height,L.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",l,s);for(const E of e.children)E.size&&(L.debug(`abc95 Setting size of children of ${e.id} id=${E.id} ${l} ${s} ${JSON.stringify(E.size)}`),E.size.width=l*((p=E.widthInColumns)!=null?p:1)+F*(((h=E.widthInColumns)!=null?h:1)-1),E.size.height=s,E.size.x=0,E.size.y=0,L.debug(`abc95 updating size of ${e.id} children child:${E.id} maxWidth:${l} maxHeight:${s}`));for(const E of e.children)ft(E,t,l,s);const f=(x=e.columns)!=null?x:-1;let w=0;for(const E of e.children)w+=(b=E.widthInColumns)!=null?b:1;let D=e.children.length;f>0&&f<w&&(D=f);const o=Math.ceil(w/D);let T=D*(l+F)+F,u=o*(s+F)+F;if(T<r){L.debug(`Detected to small siebling: abc95 ${e.id} sieblingWidth ${r} sieblingHeight ${i} width ${T}`),T=r,u=i;const E=(r-D*F-F)/D,I=(i-o*F-F)/o;L.debug("Size indata abc88",e.id,"childWidth",E,"maxWidth",l),L.debug("Size indata abc88",e.id,"childHeight",I,"maxHeight",s),L.debug("Size indata abc88 xSize",D,"padding",F);for(const _ of e.children)_.size&&(_.size.width=E,_.size.height=I,_.size.x=0,_.size.y=0)}if(L.debug(`abc95 (finale calc) ${e.id} xSize ${D} ySize ${o} columns ${f}${e.children.length} width=${Math.max(T,((m=e.size)==null?void 0:m.width)||0)}`),T<(((v=e==null?void 0:e.size)==null?void 0:v.width)||0)){T=((N=e==null?void 0:e.size)==null?void 0:N.width)||0;const E=f>0?Math.min(e.children.length,f):e.children.length;if(E>0){const I=(T-E*F-F)/E;L.debug("abc95 (growing to fit) width",e.id,T,(S=e.size)==null?void 0:S.width,I);for(const _ of e.children)_.size&&(_.size.width=I)}}e.size={width:T,height:u,x:0,y:0}}L.debug("setBlockSizes abc94 (done)",e.id,(B=e==null?void 0:e.size)==null?void 0:B.x,(k=e==null?void 0:e.size)==null?void 0:k.width,(C=e==null?void 0:e.size)==null?void 0:C.y,(y=e==null?void 0:e.size)==null?void 0:y.height)}d(ft,"setBlockSizes");function Tt(e,t){var i,l,s,a,n,c,p,h,x,b,m,v,N,S,B,k,C,y,g,f,w,D;L.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${(i=e==null?void 0:e.size)==null?void 0:i.x} y: ${(l=e==null?void 0:e.size)==null?void 0:l.y} width: ${(s=e==null?void 0:e.size)==null?void 0:s.width}`);const r=(a=e.columns)!=null?a:-1;if(L.debug("layoutBlocks columns abc95",e.id,"=>",r,e),e.children&&e.children.length>0){const o=(p=(c=(n=e==null?void 0:e.children[0])==null?void 0:n.size)==null?void 0:c.width)!=null?p:0,T=e.children.length*o+(e.children.length-1)*F;L.debug("widthOfChildren 88",T,"posX");let u=0;L.debug("abc91 block?.size?.x",e.id,(h=e==null?void 0:e.size)==null?void 0:h.x);let E=(x=e==null?void 0:e.size)!=null&&x.x?((b=e==null?void 0:e.size)==null?void 0:b.x)+(-((m=e==null?void 0:e.size)==null?void 0:m.width)/2||0):-F,I=0;for(const _ of e.children){const A=e;if(!_.size)continue;const{width:et,height:M}=_.size,{px:J,py:tt}=te(r,u);if(tt!=I&&(I=tt,E=(v=e==null?void 0:e.size)!=null&&v.x?((N=e==null?void 0:e.size)==null?void 0:N.x)+(-((S=e==null?void 0:e.size)==null?void 0:S.width)/2||0):-F,L.debug("New row in layout for block",e.id," and child ",_.id,I)),L.debug(`abc89 layout blocks (child) id: ${_.id} Pos: ${u} (px, py) ${J},${tt} (${(B=A==null?void 0:A.size)==null?void 0:B.x},${(k=A==null?void 0:A.size)==null?void 0:k.y}) parent: ${A.id} width: ${et}${F}`),A.size){const Q=et/2;_.size.x=E+F+Q,L.debug(`abc91 layout blocks (calc) px, pyid:${_.id} startingPos=X${E} new startingPosX${_.size.x} ${Q} padding=${F} width=${et} halfWidth=${Q} => x:${_.size.x} y:${_.size.y} ${_.widthInColumns} (width * (child?.w || 1)) / 2 ${et*((C=_==null?void 0:_.widthInColumns)!=null?C:1)/2}`),E=_.size.x+Q,_.size.y=A.size.y-A.size.height/2+tt*(M+F)+M/2+F,L.debug(`abc88 layout blocks (calc) px, pyid:${_.id}startingPosX${E}${F}${Q}=>x:${_.size.x}y:${_.size.y}${_.widthInColumns}(width * (child?.w || 1)) / 2${et*((y=_==null?void 0:_.widthInColumns)!=null?y:1)/2}`)}_.children&&Tt(_),u+=(g=_==null?void 0:_.widthInColumns)!=null?g:1,L.debug("abc88 columnsPos",_,u)}}L.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${(f=e==null?void 0:e.size)==null?void 0:f.x} y: ${(w=e==null?void 0:e.size)==null?void 0:w.y} width: ${(D=e==null?void 0:e.size)==null?void 0:D.width}`)}d(Tt,"layoutBlocks");function Nt(e,{minX:t,minY:r,maxX:i,maxY:l}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:s,y:a,width:n,height:c}=e.size;s-n/2<t&&(t=s-n/2),a-c/2<r&&(r=a-c/2),s+n/2>i&&(i=s+n/2),a+c/2>l&&(l=a+c/2)}if(e.children)for(const s of e.children)({minX:t,minY:r,maxX:i,maxY:l}=Nt(s,{minX:t,minY:r,maxX:i,maxY:l}));return{minX:t,minY:r,maxX:i,maxY:l}}d(Nt,"findBounds");function ee(e){const t=e.getBlock("root");if(!t)return;ft(t,e,0,0),Tt(t),L.debug("getBlocks",JSON.stringify(t,null,2));const{minX:r,minY:i,maxX:l,maxY:s}=Nt(t),a=s-i,n=l-r;return{x:r,y:i,width:n,height:a}}d(ee,"layout");function vt(e,t){t&&e.attr("style",t)}d(vt,"applyStyle");function re(e){const t=W(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=t.append("xhtml:div"),i=e.label,l=e.isNode?"nodeLabel":"edgeLabel",s=r.append("span");return s.html(i),vt(s,e.labelStyle),s.attr("class",l),vt(r,e.labelStyle),r.style("display","inline-block"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}d(re,"addHtmlLabel");var xr=d((e,t,r,i)=>{let l=e||"";if(typeof l=="object"&&(l=l[0]),q(P().flowchart.htmlLabels)){l=l.replace(/\\n|\n/g,"<br />"),L.debug("vertexText"+l);const s={isNode:i,label:Be(mt(l)),labelStyle:t.replace("fill:","color:")};return re(s)}else{const s=document.createElementNS("http://www.w3.org/2000/svg","text");s.setAttribute("style",t.replace("color:","fill:"));let a=[];typeof l=="string"?a=l.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(l)?a=l:a=[];for(const n of a){const c=document.createElementNS("http://www.w3.org/2000/svg","tspan");c.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),c.setAttribute("dy","1em"),c.setAttribute("x","0"),r?c.setAttribute("class","title-row"):c.setAttribute("class","row"),c.textContent=n.trim(),s.appendChild(c)}return s}},"createLabel"),V=xr,yr=d((e,t,r,i,l)=>{t.arrowTypeStart&&Wt(e,"start",t.arrowTypeStart,r,i,l),t.arrowTypeEnd&&Wt(e,"end",t.arrowTypeEnd,r,i,l)},"addEdgeMarkers"),br={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},Wt=d((e,t,r,i,l,s)=>{const a=br[r];if(!a){L.warn(`Unknown arrow type: ${r}`);return}const n=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${i}#${l}_${s}-${a}${n})`)},"addEdgeMarker"),Et={},H={},wr=d((e,t)=>{const r=P(),i=q(r.flowchart.htmlLabels),l=t.labelType==="markdown"?Gt(e,t.label,{style:t.labelStyle,useHtmlLabels:i,addSvgBackground:!0},r):V(t.label,t.labelStyle),s=e.insert("g").attr("class","edgeLabel"),a=s.insert("g").attr("class","label");a.node().appendChild(l);let n=l.getBBox();if(i){const p=l.children[0],h=W(l);n=p.getBoundingClientRect(),h.attr("width",n.width),h.attr("height",n.height)}a.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),Et[t.id]=s,t.width=n.width,t.height=n.height;let c;if(t.startLabelLeft){const p=V(t.startLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),H[t.id]||(H[t.id]={}),H[t.id].startLeft=h,lt(c,t.startLabelLeft)}if(t.startLabelRight){const p=V(t.startLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=h.node().appendChild(p),x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),H[t.id]||(H[t.id]={}),H[t.id].startRight=h,lt(c,t.startLabelRight)}if(t.endLabelLeft){const p=V(t.endLabelLeft,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),h.node().appendChild(p),H[t.id]||(H[t.id]={}),H[t.id].endLeft=h,lt(c,t.endLabelLeft)}if(t.endLabelRight){const p=V(t.endLabelRight,t.labelStyle),h=e.insert("g").attr("class","edgeTerminals"),x=h.insert("g").attr("class","inner");c=x.node().appendChild(p);const b=p.getBBox();x.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),h.node().appendChild(p),H[t.id]||(H[t.id]={}),H[t.id].endRight=h,lt(c,t.endLabelRight)}return l},"insertEdgeLabel");function lt(e,t){P().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}d(lt,"setTerminalWidth");var mr=d((e,t)=>{L.debug("Moving label abc88 ",e.id,e.label,Et[e.id],t);let r=t.updatedPath?t.updatedPath:t.originalPath;const i=P(),{subGraphTitleTotalMargin:l}=ve(i);if(e.label){const s=Et[e.id];let a=e.x,n=e.y;if(r){const c=nt.calcLabelPosition(r);L.debug("Moving label "+e.label+" from (",a,",",n,") to (",c.x,",",c.y,") abc88"),t.updatedPath&&(a=c.x,n=c.y)}s.attr("transform",`translate(${a}, ${n+l/2})`)}if(e.startLabelLeft){const s=H[e.id].startLeft;let a=e.x,n=e.y;if(r){const c=nt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",r);a=c.x,n=c.y}s.attr("transform",`translate(${a}, ${n})`)}if(e.startLabelRight){const s=H[e.id].startRight;let a=e.x,n=e.y;if(r){const c=nt.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",r);a=c.x,n=c.y}s.attr("transform",`translate(${a}, ${n})`)}if(e.endLabelLeft){const s=H[e.id].endLeft;let a=e.x,n=e.y;if(r){const c=nt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",r);a=c.x,n=c.y}s.attr("transform",`translate(${a}, ${n})`)}if(e.endLabelRight){const s=H[e.id].endRight;let a=e.x,n=e.y;if(r){const c=nt.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",r);a=c.x,n=c.y}s.attr("transform",`translate(${a}, ${n})`)}},"positionEdgeLabel"),Lr=d((e,t)=>{const r=e.x,i=e.y,l=Math.abs(t.x-r),s=Math.abs(t.y-i),a=e.width/2,n=e.height/2;return l>=a||s>=n},"outsideNode"),Sr=d((e,t,r)=>{L.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,l=e.y,s=Math.abs(i-r.x),a=e.width/2;let n=r.x<t.x?a-s:a+s;const c=e.height/2,p=Math.abs(t.y-r.y),h=Math.abs(t.x-r.x);if(Math.abs(l-t.y)*a>Math.abs(i-t.x)*c){let x=r.y<t.y?t.y-c-l:l-c-t.y;n=h*x/p;const b={x:r.x<t.x?r.x+n:r.x-h+n,y:r.y<t.y?r.y+p-x:r.y-p+x};return n===0&&(b.x=t.x,b.y=t.y),h===0&&(b.x=t.x),p===0&&(b.y=t.y),L.debug(`abc89 topp/bott calc, Q ${p}, q ${x}, R ${h}, r ${n}`,b),b}else{r.x<t.x?n=t.x-a-i:n=i-a-t.x;let x=p*n/h,b=r.x<t.x?r.x+h-n:r.x-h+n,m=r.y<t.y?r.y+x:r.y-x;return L.debug(`sides calc abc89, Q ${p}, q ${x}, R ${h}, r ${n}`,{_x:b,_y:m}),n===0&&(b=t.x,m=t.y),h===0&&(b=t.x),p===0&&(m=t.y),{x:b,y:m}}},"intersection"),Pt=d((e,t)=>{L.debug("abc88 cutPathAtIntersect",e,t);let r=[],i=e[0],l=!1;return e.forEach(s=>{if(!Lr(t,s)&&!l){const a=Sr(t,i,s);let n=!1;r.forEach(c=>{n=n||c.x===a.x&&c.y===a.y}),r.some(c=>c.x===a.x&&c.y===a.y)||r.push(a),l=!0}else i=s,l||r.push(s)}),r},"cutPathAtIntersect"),vr=d(function(e,t,r,i,l,s,a){let n=r.points;L.debug("abc88 InsertEdge: edge=",r,"e=",t);let c=!1;const p=s.node(t.v);var h=s.node(t.w);h!=null&&h.intersect&&(p!=null&&p.intersect)&&(n=n.slice(1,r.points.length-1),n.unshift(p.intersect(n[0])),n.push(h.intersect(n[n.length-1]))),r.toCluster&&(L.debug("to cluster abc88",i[r.toCluster]),n=Pt(r.points,i[r.toCluster].node),c=!0),r.fromCluster&&(L.debug("from cluster abc88",i[r.fromCluster]),n=Pt(n.reverse(),i[r.fromCluster].node).reverse(),c=!0);const x=n.filter(y=>!Number.isNaN(y.y));let b=Ne;r.curve&&(l==="graph"||l==="flowchart")&&(b=r.curve);const{x:m,y:v}=Ee(r),N=_e().x(m).y(v).curve(b);let S;switch(r.thickness){case"normal":S="edge-thickness-normal";break;case"thick":S="edge-thickness-thick";break;case"invisible":S="edge-thickness-thick";break;default:S=""}switch(r.pattern){case"solid":S+=" edge-pattern-solid";break;case"dotted":S+=" edge-pattern-dotted";break;case"dashed":S+=" edge-pattern-dashed";break}const B=e.append("path").attr("d",N(x)).attr("id",r.id).attr("class"," "+S+(r.classes?" "+r.classes:"")).attr("style",r.style);let k="";(P().flowchart.arrowMarkerAbsolute||P().state.arrowMarkerAbsolute)&&(k=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,k=k.replace(/\(/g,"\\("),k=k.replace(/\)/g,"\\)")),yr(B,r,k,a,l);let C={};return c&&(C.updatedPath=n),C.originalPath=r.points,C},"insertEdge"),Er=d(e=>{const t=new Set;for(const r of e)switch(r){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(r);break}return t},"expandAndDeduplicateDirections"),_r=d((e,t,r)=>{const i=Er(e),l=2,s=t.height+2*r.padding,a=s/l,n=t.width+2*a+r.padding,c=r.padding/2;return i.has("right")&&i.has("left")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:a,y:0},{x:n/2,y:2*c},{x:n-a,y:0},{x:n,y:0},{x:n,y:-s/3},{x:n+2*c,y:-s/2},{x:n,y:-2*s/3},{x:n,y:-s},{x:n-a,y:-s},{x:n/2,y:-s-2*c},{x:a,y:-s},{x:0,y:-s},{x:0,y:-2*s/3},{x:-2*c,y:-s/2},{x:0,y:-s/3}]:i.has("right")&&i.has("left")&&i.has("up")?[{x:a,y:0},{x:n-a,y:0},{x:n,y:-s/2},{x:n-a,y:-s},{x:a,y:-s},{x:0,y:-s/2}]:i.has("right")&&i.has("left")&&i.has("down")?[{x:0,y:0},{x:a,y:-s},{x:n-a,y:-s},{x:n,y:0}]:i.has("right")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:n,y:-a},{x:n,y:-s+a},{x:0,y:-s}]:i.has("left")&&i.has("up")&&i.has("down")?[{x:n,y:0},{x:0,y:-a},{x:0,y:-s+a},{x:n,y:-s}]:i.has("right")&&i.has("left")?[{x:a,y:0},{x:a,y:-c},{x:n-a,y:-c},{x:n-a,y:0},{x:n,y:-s/2},{x:n-a,y:-s},{x:n-a,y:-s+c},{x:a,y:-s+c},{x:a,y:-s},{x:0,y:-s/2}]:i.has("up")&&i.has("down")?[{x:n/2,y:0},{x:0,y:-c},{x:a,y:-c},{x:a,y:-s+c},{x:0,y:-s+c},{x:n/2,y:-s},{x:n,y:-s+c},{x:n-a,y:-s+c},{x:n-a,y:-c},{x:n,y:-c}]:i.has("right")&&i.has("up")?[{x:0,y:0},{x:n,y:-a},{x:0,y:-s}]:i.has("right")&&i.has("down")?[{x:0,y:0},{x:n,y:0},{x:0,y:-s}]:i.has("left")&&i.has("up")?[{x:n,y:0},{x:0,y:-a},{x:n,y:-s}]:i.has("left")&&i.has("down")?[{x:n,y:0},{x:0,y:0},{x:n,y:-s}]:i.has("right")?[{x:a,y:-c},{x:a,y:-c},{x:n-a,y:-c},{x:n-a,y:0},{x:n,y:-s/2},{x:n-a,y:-s},{x:n-a,y:-s+c},{x:a,y:-s+c},{x:a,y:-s+c}]:i.has("left")?[{x:a,y:0},{x:a,y:-c},{x:n-a,y:-c},{x:n-a,y:-s+c},{x:a,y:-s+c},{x:a,y:-s},{x:0,y:-s/2}]:i.has("up")?[{x:a,y:-c},{x:a,y:-s+c},{x:0,y:-s+c},{x:n/2,y:-s},{x:n,y:-s+c},{x:n-a,y:-s+c},{x:n-a,y:-c}]:i.has("down")?[{x:n/2,y:0},{x:0,y:-c},{x:a,y:-c},{x:a,y:-s+c},{x:n-a,y:-s+c},{x:n-a,y:-c},{x:n,y:-c}]:[{x:0,y:0}]},"getArrowPoints");function ae(e,t){return e.intersect(t)}d(ae,"intersectNode");var kr=ae;function se(e,t,r,i){var l=e.x,s=e.y,a=l-i.x,n=s-i.y,c=Math.sqrt(t*t*n*n+r*r*a*a),p=Math.abs(t*r*a/c);i.x<l&&(p=-p);var h=Math.abs(t*r*n/c);return i.y<s&&(h=-h),{x:l+p,y:s+h}}d(se,"intersectEllipse");var ie=se;function ne(e,t,r){return ie(e,t,t,r)}d(ne,"intersectCircle");var Dr=ne;function le(e,t,r,i){var l,s,a,n,c,p,h,x,b,m,v,N,S,B,k;if(l=t.y-e.y,a=e.x-t.x,c=t.x*e.y-e.x*t.y,b=l*r.x+a*r.y+c,m=l*i.x+a*i.y+c,!(b!==0&&m!==0&&_t(b,m))&&(s=i.y-r.y,n=r.x-i.x,p=i.x*r.y-r.x*i.y,h=s*e.x+n*e.y+p,x=s*t.x+n*t.y+p,!(h!==0&&x!==0&&_t(h,x))&&(v=l*n-s*a,v!==0)))return N=Math.abs(v/2),S=a*p-n*c,B=S<0?(S-N)/v:(S+N)/v,S=s*c-l*p,k=S<0?(S-N)/v:(S+N)/v,{x:B,y:k}}d(le,"intersectLine");function _t(e,t){return e*t>0}d(_t,"sameSign");var Tr=le,Nr=ce;function ce(e,t,r){var i=e.x,l=e.y,s=[],a=Number.POSITIVE_INFINITY,n=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(v){a=Math.min(a,v.x),n=Math.min(n,v.y)}):(a=Math.min(a,t.x),n=Math.min(n,t.y));for(var c=i-e.width/2-a,p=l-e.height/2-n,h=0;h<t.length;h++){var x=t[h],b=t[h<t.length-1?h+1:0],m=Tr(e,r,{x:c+x.x,y:p+x.y},{x:c+b.x,y:p+b.y});m&&s.push(m)}return s.length?(s.length>1&&s.sort(function(v,N){var S=v.x-r.x,B=v.y-r.y,k=Math.sqrt(S*S+B*B),C=N.x-r.x,y=N.y-r.y,g=Math.sqrt(C*C+y*y);return k<g?-1:k===g?0:1}),s[0]):e}d(ce,"intersectPolygon");var Cr=d((e,t)=>{var r=e.x,i=e.y,l=t.x-r,s=t.y-i,a=e.width/2,n=e.height/2,c,p;return Math.abs(s)*a>Math.abs(l)*n?(s<0&&(n=-n),c=s===0?0:n*l/s,p=n):(l<0&&(a=-a),c=a,p=l===0?0:a*s/l),{x:r+c,y:i+p}},"intersectRect"),Br=Cr,R={node:kr,circle:Dr,ellipse:ie,polygon:Nr,rect:Br},Y=d((e,t,r,i)=>O(void 0,null,function*(){const l=P();let s;const a=t.useHtmlLabels||q(l.flowchart.htmlLabels);r?s=r:s="node default";const n=e.insert("g").attr("class",s).attr("id",t.domId||t.id),c=n.insert("g").attr("class","label").attr("style",t.labelStyle);let p;t.labelText===void 0?p="":p=typeof t.labelText=="string"?t.labelText:t.labelText[0];const h=c.node();let x;t.labelType==="markdown"?x=Gt(c,zt(mt(p),l),{useHtmlLabels:a,width:t.width||l.flowchart.wrappingWidth,classes:"markdown-node-label"},l):x=h.appendChild(V(zt(mt(p),l),t.labelStyle,!1,i));let b=x.getBBox();const m=t.padding/2;if(q(l.flowchart.htmlLabels)){const v=x.children[0],N=W(x),S=v.getElementsByTagName("img");if(S){const B=p.replace(/<img[^>]*>/g,"").trim()==="";yield Promise.all([...S].map(k=>new Promise(C=>{function y(){if(k.style.display="flex",k.style.flexDirection="column",B){const g=l.fontSize?l.fontSize:window.getComputedStyle(document.body).fontSize,f=5,w=parseInt(g,10)*f+"px";k.style.minWidth=w,k.style.maxWidth=w}else k.style.width="100%";C(k)}d(y,"setupImage"),setTimeout(()=>{k.complete&&y()}),k.addEventListener("error",y),k.addEventListener("load",y)})))}b=v.getBoundingClientRect(),N.attr("width",b.width),N.attr("height",b.height)}return a?c.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"):c.attr("transform","translate(0, "+-b.height/2+")"),t.centerLabel&&c.attr("transform","translate("+-b.width/2+", "+-b.height/2+")"),c.insert("rect",":first-child"),{shapeSvg:n,bbox:b,halfPadding:m,label:c}}),"labelHelper"),z=d((e,t)=>{const r=t.node().getBBox();e.width=r.width,e.height=r.height},"updateNodeBounds");function Z(e,t,r,i){return e.insert("polygon",":first-child").attr("points",i.map(function(l){return l.x+","+l.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+r/2+")")}d(Z,"insertPolygonShape");var Ir=d((e,t)=>O(void 0,null,function*(){t.useHtmlLabels||P().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:i,bbox:l,halfPadding:s}=yield Y(e,t,"node "+t.classes,!0);L.info("Classes = ",t.classes);const a=i.insert("rect",":first-child");return a.attr("rx",t.rx).attr("ry",t.ry).attr("x",-l.width/2-s).attr("y",-l.height/2-s).attr("width",l.width+t.padding).attr("height",l.height+t.padding),z(t,a),t.intersect=function(n){return R.rect(t,n)},i}),"note"),Or=Ir,Yt=d(e=>e?" "+e:"","formatClass"),U=d((e,t)=>`${t||"node default"}${Yt(e.classes)} ${Yt(e.class)}`,"getClassesFromNode"),Ht=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=l+s,n=[{x:a/2,y:0},{x:a,y:-a/2},{x:a/2,y:-a},{x:0,y:-a/2}];L.info("Question main (Circle)");const c=Z(r,a,a,n);return c.attr("style",t.style),z(t,c),t.intersect=function(p){return L.warn("Intersect called"),R.polygon(t,n,p)},r}),"question"),Rr=d((e,t)=>{const r=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=28,l=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}];return r.insert("polygon",":first-child").attr("points",l.map(function(a){return a.x+","+a.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(a){return R.circle(t,14,a)},r},"choice"),zr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=4,s=i.height+t.padding,a=s/l,n=i.width+2*a+t.padding,c=[{x:a,y:0},{x:n-a,y:0},{x:n,y:-s/2},{x:n-a,y:-s},{x:a,y:-s},{x:0,y:-s/2}],p=Z(r,n,s,c);return p.attr("style",t.style),z(t,p),t.intersect=function(h){return R.polygon(t,c,h)},r}),"hexagon"),Ar=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,void 0,!0),l=2,s=i.height+2*t.padding,a=s/l,n=i.width+2*a+t.padding,c=_r(t.directions,i,t),p=Z(r,n,s,c);return p.attr("style",t.style),z(t,p),t.intersect=function(h){return R.polygon(t,c,h)},r}),"block_arrow"),Mr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:-s/2,y:0},{x:l,y:0},{x:l,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return Z(r,l,s,a).attr("style",t.style),t.width=l+s,t.height=s,t.intersect=function(c){return R.polygon(t,a,c)},r}),"rect_left_inv_arrow"),Fr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:-2*s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:s/6,y:-s}],n=Z(r,l,s,a);return n.attr("style",t.style),z(t,n),t.intersect=function(c){return R.polygon(t,a,c)},r}),"lean_right"),Wr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:2*s/6,y:0},{x:l+s/6,y:0},{x:l-2*s/6,y:-s},{x:-s/6,y:-s}],n=Z(r,l,s,a);return n.attr("style",t.style),z(t,n),t.intersect=function(c){return R.polygon(t,a,c)},r}),"lean_left"),Pr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:-2*s/6,y:0},{x:l+2*s/6,y:0},{x:l-s/6,y:-s},{x:s/6,y:-s}],n=Z(r,l,s,a);return n.attr("style",t.style),z(t,n),t.intersect=function(c){return R.polygon(t,a,c)},r}),"trapezoid"),Yr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:s/6,y:0},{x:l-s/6,y:0},{x:l+2*s/6,y:-s},{x:-2*s/6,y:-s}],n=Z(r,l,s,a);return n.attr("style",t.style),z(t,n),t.intersect=function(c){return R.polygon(t,a,c)},r}),"inv_trapezoid"),Hr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:0,y:0},{x:l+s/2,y:0},{x:l,y:-s/2},{x:l+s/2,y:-s},{x:0,y:-s}],n=Z(r,l,s,a);return n.attr("style",t.style),z(t,n),t.intersect=function(c){return R.polygon(t,a,c)},r}),"rect_right_inv_arrow"),Kr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=l/2,a=s/(2.5+l/50),n=i.height+a+t.padding,c="M 0,"+a+" a "+s+","+a+" 0,0,0 "+l+" 0 a "+s+","+a+" 0,0,0 "+-l+" 0 l 0,"+n+" a "+s+","+a+" 0,0,0 "+l+" 0 l 0,"+-n,p=r.attr("label-offset-y",a).insert("path",":first-child").attr("style",t.style).attr("d",c).attr("transform","translate("+-l/2+","+-(n/2+a)+")");return z(t,p),t.intersect=function(h){const x=R.rect(t,h),b=x.x-t.x;if(s!=0&&(Math.abs(b)<t.width/2||Math.abs(b)==t.width/2&&Math.abs(x.y-t.y)>t.height/2-a)){let m=a*a*(1-b*b/(s*s));m!=0&&(m=Math.sqrt(m)),m=a-m,h.y-t.y>0&&(m=-m),x.y+=m}return x},r}),"cylinder"),Xr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i,halfPadding:l}=yield Y(e,t,"node "+t.classes+" "+t.class,!0),s=r.insert("rect",":first-child"),a=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,c=t.positioned?-a/2:-i.width/2-l,p=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",p).attr("width",a).attr("height",n),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(xt(s,t.props.borders,a,n),h.delete("borders")),h.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return z(t,s),t.intersect=function(h){return R.rect(t,h)},r}),"rect"),Ur=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i,halfPadding:l}=yield Y(e,t,"node "+t.classes,!0),s=r.insert("rect",":first-child"),a=t.positioned?t.width:i.width+t.padding,n=t.positioned?t.height:i.height+t.padding,c=t.positioned?-a/2:-i.width/2-l,p=t.positioned?-n/2:-i.height/2-l;if(s.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",c).attr("y",p).attr("width",a).attr("height",n),t.props){const h=new Set(Object.keys(t.props));t.props.borders&&(xt(s,t.props.borders,a,n),h.delete("borders")),h.forEach(x=>{L.warn(`Unknown node property ${x}`)})}return z(t,s),t.intersect=function(h){return R.rect(t,h)},r}),"composite"),jr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r}=yield Y(e,t,"label",!0);L.trace("Classes = ",t.class);const i=r.insert("rect",":first-child"),l=0,s=0;if(i.attr("width",l).attr("height",s),r.attr("class","label edgeLabel"),t.props){const a=new Set(Object.keys(t.props));t.props.borders&&(xt(i,t.props.borders,l,s),a.delete("borders")),a.forEach(n=>{L.warn(`Unknown node property ${n}`)})}return z(t,i),t.intersect=function(a){return R.rect(t,a)},r}),"labelRect");function xt(e,t,r,i){const l=[],s=d(n=>{l.push(n,0)},"addBorder"),a=d(n=>{l.push(0,n)},"skipBorder");t.includes("t")?(L.debug("add top border"),s(r)):a(r),t.includes("r")?(L.debug("add right border"),s(i)):a(i),t.includes("b")?(L.debug("add bottom border"),s(r)):a(r),t.includes("l")?(L.debug("add left border"),s(i)):a(i),e.attr("stroke-dasharray",l.join(" "))}d(xt,"applyNodePropertyBorders");var Vr=d((e,t)=>{let r;t.classes?r="node "+t.classes:r="node default";const i=e.insert("g").attr("class",r).attr("id",t.domId||t.id),l=i.insert("rect",":first-child"),s=i.insert("line"),a=i.insert("g").attr("class","label"),n=t.labelText.flat?t.labelText.flat():t.labelText;let c="";typeof n=="object"?c=n[0]:c=n,L.info("Label text abc79",c,n,typeof n=="object");const p=a.node().appendChild(V(c,t.labelStyle,!0,!0));let h={width:0,height:0};if(q(P().flowchart.htmlLabels)){const N=p.children[0],S=W(p);h=N.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}L.info("Text 2",n);const x=n.slice(1,n.length);let b=p.getBBox();const m=a.node().appendChild(V(x.join?x.join("<br/>"):x,t.labelStyle,!0,!0));if(q(P().flowchart.htmlLabels)){const N=m.children[0],S=W(m);h=N.getBoundingClientRect(),S.attr("width",h.width),S.attr("height",h.height)}const v=t.padding/2;return W(m).attr("transform","translate( "+(h.width>b.width?0:(b.width-h.width)/2)+", "+(b.height+v+5)+")"),W(p).attr("transform","translate( "+(h.width<b.width?0:-(b.width-h.width)/2)+", 0)"),h=a.node().getBBox(),a.attr("transform","translate("+-h.width/2+", "+(-h.height/2-v+3)+")"),l.attr("class","outer title-state").attr("x",-h.width/2-v).attr("y",-h.height/2-v).attr("width",h.width+t.padding).attr("height",h.height+t.padding),s.attr("class","divider").attr("x1",-h.width/2-v).attr("x2",h.width/2+v).attr("y1",-h.height/2-v+b.height+v).attr("y2",-h.height/2-v+b.height+v),z(t,l),t.intersect=function(N){return R.rect(t,N)},i},"rectWithTitle"),Gr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.height+t.padding,s=i.width+l/4+t.padding,a=r.insert("rect",":first-child").attr("style",t.style).attr("rx",l/2).attr("ry",l/2).attr("x",-s/2).attr("y",-l/2).attr("width",s).attr("height",l);return z(t,a),t.intersect=function(n){return R.rect(t,n)},r}),"stadium"),Zr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i,halfPadding:l}=yield Y(e,t,U(t,void 0),!0),s=r.insert("circle",":first-child");return s.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),L.info("Circle main"),z(t,s),t.intersect=function(a){return L.info("Circle intersect",t,i.width/2+l,a),R.circle(t,i.width/2+l,a)},r}),"circle"),qr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i,halfPadding:l}=yield Y(e,t,U(t,void 0),!0),s=5,a=r.insert("g",":first-child"),n=a.insert("circle"),c=a.insert("circle");return a.attr("class",t.class),n.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l+s).attr("width",i.width+t.padding+s*2).attr("height",i.height+t.padding+s*2),c.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+l).attr("width",i.width+t.padding).attr("height",i.height+t.padding),L.info("DoubleCircle main"),z(t,n),t.intersect=function(p){return L.info("DoubleCircle intersect",t,i.width/2+l+s,p),R.circle(t,i.width/2+l+s,p)},r}),"doublecircle"),Jr=d((e,t)=>O(void 0,null,function*(){const{shapeSvg:r,bbox:i}=yield Y(e,t,U(t,void 0),!0),l=i.width+t.padding,s=i.height+t.padding,a=[{x:0,y:0},{x:l,y:0},{x:l,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:l+8,y:0},{x:l+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],n=Z(r,l,s,a);return n.attr("style",t.style),z(t,n),t.intersect=function(c){return R.polygon(t,a,c)},r}),"subroutine"),Qr=d((e,t)=>{const r=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=r.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),z(t,i),t.intersect=function(l){return R.circle(t,7,l)},r},"start"),Kt=d((e,t,r)=>{const i=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let l=70,s=10;r==="LR"&&(l=10,s=70);const a=i.append("rect").attr("x",-1*l/2).attr("y",-1*s/2).attr("width",l).attr("height",s).attr("class","fork-join");return z(t,a),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(n){return R.rect(t,n)},i},"forkJoin"),$r=d((e,t)=>{const r=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=r.insert("circle",":first-child"),l=r.insert("circle",":first-child");return l.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),i.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),z(t,l),t.intersect=function(s){return R.circle(t,7,s)},r},"end"),ta=d((e,t)=>{var D;const r=t.padding/2,i=4,l=8;let s;t.classes?s="node "+t.classes:s="node default";const a=e.insert("g").attr("class",s).attr("id",t.domId||t.id),n=a.insert("rect",":first-child"),c=a.insert("line"),p=a.insert("line");let h=0,x=i;const b=a.insert("g").attr("class","label");let m=0;const v=(D=t.classData.annotations)==null?void 0:D[0],N=t.classData.annotations[0]?"«"+t.classData.annotations[0]+"»":"",S=b.node().appendChild(V(N,t.labelStyle,!0,!0));let B=S.getBBox();if(q(P().flowchart.htmlLabels)){const o=S.children[0],T=W(S);B=o.getBoundingClientRect(),T.attr("width",B.width),T.attr("height",B.height)}t.classData.annotations[0]&&(x+=B.height+i,h+=B.width);let k=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&(P().flowchart.htmlLabels?k+="&lt;"+t.classData.type+"&gt;":k+="<"+t.classData.type+">");const C=b.node().appendChild(V(k,t.labelStyle,!0,!0));W(C).attr("class","classTitle");let y=C.getBBox();if(q(P().flowchart.htmlLabels)){const o=C.children[0],T=W(C);y=o.getBoundingClientRect(),T.attr("width",y.width),T.attr("height",y.height)}x+=y.height+i,y.width>h&&(h=y.width);const g=[];t.classData.members.forEach(o=>{const T=o.getDisplayDetails();let u=T.displayText;P().flowchart.htmlLabels&&(u=u.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const E=b.node().appendChild(V(u,T.cssStyle?T.cssStyle:t.labelStyle,!0,!0));let I=E.getBBox();if(q(P().flowchart.htmlLabels)){const _=E.children[0],A=W(E);I=_.getBoundingClientRect(),A.attr("width",I.width),A.attr("height",I.height)}I.width>h&&(h=I.width),x+=I.height+i,g.push(E)}),x+=l;const f=[];if(t.classData.methods.forEach(o=>{const T=o.getDisplayDetails();let u=T.displayText;P().flowchart.htmlLabels&&(u=u.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const E=b.node().appendChild(V(u,T.cssStyle?T.cssStyle:t.labelStyle,!0,!0));let I=E.getBBox();if(q(P().flowchart.htmlLabels)){const _=E.children[0],A=W(E);I=_.getBoundingClientRect(),A.attr("width",I.width),A.attr("height",I.height)}I.width>h&&(h=I.width),x+=I.height+i,f.push(E)}),x+=l,v){let o=(h-B.width)/2;W(S).attr("transform","translate( "+(-1*h/2+o)+", "+-1*x/2+")"),m=B.height+i}let w=(h-y.width)/2;return W(C).attr("transform","translate( "+(-1*h/2+w)+", "+(-1*x/2+m)+")"),m+=y.height+i,c.attr("class","divider").attr("x1",-h/2-r).attr("x2",h/2+r).attr("y1",-x/2-r+l+m).attr("y2",-x/2-r+l+m),m+=l,g.forEach(o=>{var u;W(o).attr("transform","translate( "+-h/2+", "+(-1*x/2+m+l/2)+")");const T=o==null?void 0:o.getBBox();m+=((u=T==null?void 0:T.height)!=null?u:0)+i}),m+=l,p.attr("class","divider").attr("x1",-h/2-r).attr("x2",h/2+r).attr("y1",-x/2-r+l+m).attr("y2",-x/2-r+l+m),m+=l,f.forEach(o=>{var u;W(o).attr("transform","translate( "+-h/2+", "+(-1*x/2+m)+")");const T=o==null?void 0:o.getBBox();m+=((u=T==null?void 0:T.height)!=null?u:0)+i}),n.attr("style",t.style).attr("class","outer title-state").attr("x",-h/2-r).attr("y",-(x/2)-r).attr("width",h+t.padding).attr("height",x+t.padding),z(t,n),t.intersect=function(o){return R.rect(t,o)},a},"class_box"),Xt={rhombus:Ht,composite:Ur,question:Ht,rect:Xr,labelRect:jr,rectWithTitle:Vr,choice:Rr,circle:Zr,doublecircle:qr,stadium:Gr,hexagon:zr,block_arrow:Ar,rect_left_inv_arrow:Mr,lean_right:Fr,lean_left:Wr,trapezoid:Pr,inv_trapezoid:Yr,rect_right_inv_arrow:Hr,cylinder:Kr,start:Qr,end:$r,note:Or,subroutine:Jr,fork:Kt,join:Kt,class_box:ta},ut={},oe=d((e,t,r)=>O(void 0,null,function*(){let i,l;if(t.link){let s;P().securityLevel==="sandbox"?s="_top":t.linkTarget&&(s=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",s),l=yield Xt[t.shape](i,t,r)}else l=yield Xt[t.shape](e,t,r),i=l;return t.tooltip&&l.attr("title",t.tooltip),t.class&&l.attr("class","node default "+t.class),ut[t.id]=i,t.haveCallback&&ut[t.id].attr("class",ut[t.id].attr("class")+" clickable"),i}),"insertNode"),ea=d(e=>{const t=ut[e.id];L.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const r=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-r)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode");function Ct(e,t,r=!1){var b,m,v,N,S,B,k;const i=e;let l="default";(((b=i==null?void 0:i.classes)==null?void 0:b.length)||0)>0&&(l=((m=i==null?void 0:i.classes)!=null?m:[]).join(" ")),l=l+" flowchart-label";let s=0,a="",n;switch(i.type){case"round":s=5,a="rect";break;case"composite":s=0,a="composite",n=0;break;case"square":a="rect";break;case"diamond":a="question";break;case"hexagon":a="hexagon";break;case"block_arrow":a="block_arrow";break;case"odd":a="rect_left_inv_arrow";break;case"lean_right":a="lean_right";break;case"lean_left":a="lean_left";break;case"trapezoid":a="trapezoid";break;case"inv_trapezoid":a="inv_trapezoid";break;case"rect_left_inv_arrow":a="rect_left_inv_arrow";break;case"circle":a="circle";break;case"ellipse":a="ellipse";break;case"stadium":a="stadium";break;case"subroutine":a="subroutine";break;case"cylinder":a="cylinder";break;case"group":a="rect";break;case"doublecircle":a="doublecircle";break;default:a="rect"}const c=Se((v=i==null?void 0:i.styles)!=null?v:[]),p=i.label,h=(N=i.size)!=null?N:{width:0,height:0,x:0,y:0};return{labelStyle:c.labelStyle,shape:a,labelText:p,rx:s,ry:s,class:l,style:c.style,id:i.id,directions:i.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:r,intersect:void 0,type:i.type,padding:(k=n!=null?n:(B=(S=ot())==null?void 0:S.block)==null?void 0:B.padding)!=null?k:0}}d(Ct,"getNodeFromBlock");function he(e,t,r){return O(this,null,function*(){const i=Ct(t,r,!1);if(i.type==="group")return;const l=ot(),s=yield oe(e,i,{config:l}),a=s.node().getBBox(),n=r.getBlock(i.id);n.size={width:a.width,height:a.height,x:0,y:0,node:s},r.setBlock(n),s.remove()})}d(he,"calculateBlockSize");function de(e,t,r){return O(this,null,function*(){const i=Ct(t,r,!0);if(r.getBlock(i.id).type!=="space"){const s=ot();yield oe(e,i,{config:s}),t.intersect=i==null?void 0:i.intersect,ea(i)}})}d(de,"insertBlockPositioned");function yt(e,t,r,i){return O(this,null,function*(){for(const l of t)yield i(e,l,r),l.children&&(yield yt(e,l.children,r,i))})}d(yt,"performOperations");function ge(e,t,r){return O(this,null,function*(){yield yt(e,t,r,he)})}d(ge,"calculateBlockSizes");function ue(e,t,r){return O(this,null,function*(){yield yt(e,t,r,de)})}d(ue,"insertBlocks");function pe(e,t,r,i,l){return O(this,null,function*(){const s=new Re({multigraph:!0,compound:!0});s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const a of r)a.size&&s.setNode(a.id,{width:a.size.width,height:a.size.height,intersect:a.intersect});for(const a of t)if(a.start&&a.end){const n=i.getBlock(a.start),c=i.getBlock(a.end);if(n!=null&&n.size&&(c!=null&&c.size)){const p=n.size,h=c.size,x=[{x:p.x,y:p.y},{x:p.x+(h.x-p.x)/2,y:p.y+(h.y-p.y)/2},{x:h.x,y:h.y}];vr(e,{v:a.start,w:a.end,name:a.id},it(st({},a),{arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),void 0,"block",s,l),a.label&&(yield wr(e,it(st({},a),{label:a.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:a.arrowTypeEnd,arrowTypeStart:a.arrowTypeStart,points:x,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"})),mr(it(st({},a),{x:x[1].x,y:x[1].y}),{originalPath:x}))}}})}d(pe,"insertEdges");var ra=d(function(e,t){return t.db.getClasses()},"getClasses"),aa=d(function(e,t,r,i){return O(this,null,function*(){const{securityLevel:l,block:s}=ot(),a=i.db;let n;l==="sandbox"&&(n=W("#i"+t));const c=l==="sandbox"?W(n.nodes()[0].contentDocument.body):W("body"),p=l==="sandbox"?c.select(`[id="${t}"]`):W(`[id="${t}"]`);pr(p,["point","circle","cross"],i.type,t);const x=a.getBlocks(),b=a.getBlocksFlat(),m=a.getEdges(),v=p.insert("g").attr("class","block");yield ge(v,x,a);const N=ee(a);if(yield ue(v,x,a),yield pe(v,m,b,a,t),N){const S=N,B=Math.max(1,Math.round(.125*(S.width/S.height))),k=S.height+B+10,C=S.width+10,{useMaxWidth:y}=s;ke(p,k,C,!!y),L.debug("Here Bounds",N,S),p.attr("viewBox",`${S.x-5} ${S.y-5} ${S.width+10} ${S.height+10}`)}})},"draw"),sa={draw:aa,getClasses:ra},ga={parser:ze,db:tr,renderer:sa,styles:rr};export{ga as diagram};
