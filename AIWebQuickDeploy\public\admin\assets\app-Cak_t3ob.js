
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{ac as p}from"./index-BERX8Mlm.js";const a={queryCats:a=>p.get("app/queryAppCats",{params:a}),deleteCats:a=>p.post("app/delAppCats",a),createCats:a=>p.post("app/createAppCats",a),updateCats:a=>p.post("app/updateAppCats",a),queryApp:a=>p.get("app/queryApp",{params:a}),deleteApp:a=>p.post("app/delApp",a),createApp:a=>p.post("app/createApp",a),updateApp:a=>p.post("app/updateApp",a)};export{a as A};
