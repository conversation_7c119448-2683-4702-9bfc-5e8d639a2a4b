
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as i,Q as o,c as n,e as d,f as s,w as u,j as r,h as p,_ as f,g as m,Y as c,k as g}from"./index-BERX8Mlm.js";import{a as V}from"./config-BrbFL53_.js";const _=l({__name:"identityVerification",setup(l){const g=a({appCode:"",openIdentity:"",openPhoneValidation:"",phoneValidationMessageCount:"",identityVerificationMessageCount:"",isSensitiveWordFilter:""}),_=t({appCode:[{required:!1,trigger:"blur",message:"请填写身份认证appCode"}],openIdentity:[{required:!0,trigger:"blur",message:"请选择是否开启身份认证"}],openPhoneValidation:[{required:!0,trigger:"blur",message:"请选择是否开启手机号验证"}],phoneValidationMessageCount:[{required:!1,trigger:"blur",message:"请填写消息数量"}],identityVerificationMessageCount:[{required:!1,trigger:"blur",message:"请填写消息数量"}],isSensitiveWordFilter:[{required:!0,trigger:"blur",message:"请选择是否开启敏感词过滤"}]}),v=t();async function y(){const e=await V.queryConfig({keys:["appCode","openIdentity","openPhoneValidation","phoneValidationMessageCount","identityVerificationMessageCount","isSensitiveWordFilter"]});Object.assign(g,e.data)}function C(){var e;null==(e=v.value)||e.validate((async e=>{if(e){try{await V.setConfig({settings:(l=g,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),c.success("变更配置信息成功")}catch(a){}y()}else c.error("请填写完整信息");var l}))}return i((()=>{y()})),(l,a)=>{const t=f,i=r,c=e,V=o("el-switch"),y=o("el-tooltip"),h=o("el-form-item"),b=o("el-col"),x=o("el-row"),M=o("el-input"),w=o("el-form"),k=o("el-card");return d(),n("div",null,[s(c,null,{title:u((()=>a[6]||(a[6]=[m("div",{class:"flex items-center gap-4"},"认证设置",-1)]))),content:u((()=>a[7]||(a[7]=[m("div",{class:"text-sm/6"},[m("div",null,[p(" 实名认证接口使用阿里云"),m("a",{href:"https://market.aliyun.com/apimarket/detail/cmapi026109#sku=yuncode20109000025",target:"_blank"},"实名认证"),p("，需自行开通并获取 appCode。 ")]),m("div",null," 开启实名认证/手机号验证后，当用户对话条数超过对应阈值时，将会触发实名认证/手机号验证。 "),m("div",null,"触发优先级：手机号验证 > 实名认证"),m("div",null,"开启敏感词过滤后，将会对用户发送以及 AI 回复的消息进行敏感词过滤。")],-1)]))),default:u((()=>[s(i,{outline:"",onClick:C},{default:u((()=>[s(t,{name:"i-ri:file-text-line"}),a[8]||(a[8]=p(" 保存设置 "))])),_:1})])),_:1}),s(k,{style:{margin:"20px"}},{default:u((()=>[s(w,{ref_key:"formRef",ref:v,rules:_.value,model:g,"label-width":"150px"},{default:u((()=>[s(x,null,{default:u((()=>[s(b,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[s(h,{label:"开启实名验证",prop:"openIdentity"},{default:u((()=>[s(y,{content:"开启将打开实名验证",placement:"top","show-after":500},{default:u((()=>[s(V,{modelValue:g.openIdentity,"onUpdate:modelValue":a[0]||(a[0]=e=>g.openIdentity=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),s(x,null,{default:u((()=>[s(b,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[s(h,{label:"appCode",prop:"appCode"},{default:u((()=>[s(M,{modelValue:g.appCode,"onUpdate:modelValue":a[1]||(a[1]=e=>g.appCode=e),placeholder:"请填写实名认证 appCode",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(x,null,{default:u((()=>[s(b,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[s(h,{label:"开启手机号验证",prop:"openPhoneValidation"},{default:u((()=>[s(V,{modelValue:g.openPhoneValidation,"onUpdate:modelValue":a[2]||(a[2]=e=>g.openPhoneValidation=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(x,null,{default:u((()=>[s(b,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[s(h,{label:"手机号验证阈值",prop:"phoneValidationMessageCount"},{default:u((()=>[s(M,{modelValue:g.phoneValidationMessageCount,"onUpdate:modelValue":a[3]||(a[3]=e=>g.phoneValidationMessageCount=e),placeholder:"请填写消息数量",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(x,null,{default:u((()=>[s(b,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[s(h,{label:"实名认证阈值",prop:"identityVerificationMessageCount"},{default:u((()=>[s(M,{modelValue:g.identityVerificationMessageCount,"onUpdate:modelValue":a[4]||(a[4]=e=>g.identityVerificationMessageCount=e),placeholder:"请填写消息数量",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),s(x,null,{default:u((()=>[s(b,{xs:24,md:20,lg:15,xl:12},{default:u((()=>[s(h,{label:"开启敏感词过滤",prop:"isSensitiveWordFilter"},{default:u((()=>[s(V,{modelValue:g.isSensitiveWordFilter,"onUpdate:modelValue":a[5]||(a[5]=e=>g.isSensitiveWordFilter=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof g&&g(_);export{_ as default};
