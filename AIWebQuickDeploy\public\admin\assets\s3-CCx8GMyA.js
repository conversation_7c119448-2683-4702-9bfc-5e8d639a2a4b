
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as s,P as t,b as u,Q as o,c as d,e as c,f as n,w as r,j as m,h as i,_ as p,g as f,i as _,Y as g,k as y}from"./index-BERX8Mlm.js";import{a as S}from"./config-BrbFL53_.js";const x=l({__name:"s3",setup(l){const y=a({s3Status:"",s3AccessKeyId:"",s3SecretAccessKey:"",s3Region:"",s3Bucket:"",s3Endpoint:"",s3CustomDomain:""}),x=s();async function V(){const e=await S.queryConfig({keys:["s3Status","s3AccessKeyId","s3SecretAccessKey","s3Region","s3Bucket","s3Endpoint","s3CustomDomain"]});Object.assign(y,e.data)}function b(){var e;null==(e=x.value)||e.validate((async e=>{if(e){try{await S.setConfig({settings:(l=y,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),g.success("变更配置信息成功")}catch(a){}V()}else g.error("请填写完整信息");var l}))}const A=t((()=>[{required:1===Number(y.s3Status),message:"开启配置后请填写此项",trigger:"change"}]));return u((()=>{V()})),(l,a)=>{const s=p,t=m,u=e,g=o("el-switch"),S=o("el-form-item"),V=o("el-col"),h=o("el-row"),K=o("el-input"),v=o("el-form"),w=o("el-card");return c(),d("div",null,[n(u,null,{title:r((()=>a[7]||(a[7]=[f("div",{class:"flex items-center gap-4"},"S3存储参数设置",-1)]))),content:r((()=>a[8]||(a[8]=[f("div",{class:"text-sm/6"},[f("div",null,[i(" 支持亚马逊S3存储协议，兼容AWS S3、MinIO、Wasabi、Backblaze B2等S3兼容服务。 更多配置详见"),f("a",{href:"https://aws.amazon.com/s3/",target:"_blank"},"AWS S3文档"),i(" 。如果同时开启多个存储服务，服务优先级：本地存储 > S3存储 > 腾讯云COS > 阿里云OSS。 ")])],-1)]))),default:r((()=>[n(t,{outline:"",onClick:b},{default:r((()=>[n(s,{name:"i-ri:file-text-line"}),a[9]||(a[9]=i(" 保存设置 "))])),_:1})])),_:1}),n(w,{style:{margin:"20px"}},{default:r((()=>[n(v,{ref_key:"formRef",ref:x,model:y,"label-width":"120px"},{default:r((()=>[n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"服务启用状态",prop:"s3Status"},{default:r((()=>[n(g,{modelValue:y.s3Status,"onUpdate:modelValue":a[0]||(a[0]=e=>y.s3Status=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"AccessKeyId",prop:"s3AccessKeyId",rules:_(A)},{default:r((()=>[n(K,{modelValue:y.s3AccessKeyId,"onUpdate:modelValue":a[1]||(a[1]=e=>y.s3AccessKeyId=e),placeholder:"请填写Access Key ID",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"SecretAccessKey",prop:"s3SecretAccessKey",rules:_(A)},{default:r((()=>[n(K,{modelValue:y.s3SecretAccessKey,"onUpdate:modelValue":a[2]||(a[2]=e=>y.s3SecretAccessKey=e),placeholder:"请填写Secret Access Key",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"存储桶名称",prop:"s3Bucket",rules:_(A)},{default:r((()=>[n(K,{modelValue:y.s3Bucket,"onUpdate:modelValue":a[3]||(a[3]=e=>y.s3Bucket=e),placeholder:"请填写存储桶名称",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"所属地域",prop:"s3Region"},{default:r((()=>[n(K,{modelValue:y.s3Region,"onUpdate:modelValue":a[4]||(a[4]=e=>y.s3Region=e),placeholder:"请填写所属地域(us-east-1)，可留空使用默认地域",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"自定义端点",prop:"s3Endpoint"},{default:r((()=>[n(K,{modelValue:y.s3Endpoint,"onUpdate:modelValue":a[5]||(a[5]=e=>y.s3Endpoint=e),placeholder:"自定义S3端点，如MinIO服务地址(https://minio.example.com)，AWS S3可留空",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(h,null,{default:r((()=>[n(V,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[n(S,{label:"自定义域名",prop:"s3CustomDomain"},{default:r((()=>[n(K,{modelValue:y.s3CustomDomain,"onUpdate:modelValue":a[6]||(a[6]=e=>y.s3CustomDomain=e),placeholder:"自定义访问域名，如CDN域名(cdn.example.com)，可留空使用默认域名",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})])}}});"function"==typeof y&&y(x);export{x as default};
