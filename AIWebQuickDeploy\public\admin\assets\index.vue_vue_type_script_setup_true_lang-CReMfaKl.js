
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./sub.vue_vue_type_script_setup_true_lang-SYX2vZ5k.js";import{r as a,_ as l}from"./item.vue_vue_type_script_setup_true_lang-D5h42eqV.js";import{d as n,r as u,P as t,a0 as i,aq as o,$ as s,c,e as v,a6 as r,i as d,V as p,W as h,T as m,a2 as f,a9 as y}from"./index-BERX8Mlm.js";const x=n({name:"MainMenu",__name:"index",props:{menu:{},value:{},accordion:{type:Boolean,default:!0},defaultOpeneds:{default:()=>[]},mode:{default:"vertical"},collapse:{type:Boolean,default:!1},showCollapseName:{type:Boolean,default:!1}},setup(n){const x=n,_=u(x.value),k=u({}),M=u({}),g=u(x.defaultOpeneds.slice(0)),P=u([]),O=t((()=>"horizontal"===x.mode||"vertical"===x.mode&&x.collapse));function N(e,a=[]){e.forEach((e=>{const l=e.path??JSON.stringify(e);if(e.children){const n=[...a,l];M.value[l]={index:l,indexPath:n,active:!1},N(e.children,n)}else k.value[l]={index:l,indexPath:a}}))}const S=(e,a)=>{g.value.includes(e)||(x.accordion&&(g.value=g.value.filter((e=>a.includes(e)))),g.value.push(e))},q=e=>{Array.isArray(e)?y((()=>{q(e.at(-1)),e.length>1&&q(e.slice(0,-1))})):Object.keys(M.value).forEach((a=>{M.value[a].indexPath.includes(e)&&(g.value=g.value.filter((a=>a!==e)))}))};function w(e){var a,l;for(const n in M.value)M.value[n].active=!1;null==(a=M.value[e])||a.indexPath.forEach((e=>{M.value[e].active=!0})),null==(l=k.value[e])||l.indexPath.forEach((e=>{M.value[e].active=!0}))}const C=e=>{("horizontal"===x.mode||x.collapse)&&(g.value=[]),w(e)};function E(){const e=_.value&&k.value[_.value];w(_.value),e&&!x.collapse&&e.indexPath.forEach((e=>{const a=M.value[e];a&&S(e,a.indexPath)}))}return i((()=>x.menu),(e=>{N(e),E()}),{deep:!0,immediate:!0}),i((()=>x.value),(e=>{k.value[e]||(_.value="");const a=k.value[e]||_.value&&k.value[_.value]||k.value[x.value];_.value=a?a.index:e,E()})),i((()=>x.collapse),(e=>{e&&(g.value=[]),E()})),o(a,s({props:x,items:k,subMenus:M,activeIndex:_,openedMenus:g,mouseInMenu:P,isMenuPopup:O,openMenu:S,closeMenu:q,handleMenuItemClick:C,handleSubMenuClick:(e,a)=>{g.value.includes(e)?q(e):S(e,a)}})),(a,n)=>(v(),c("div",{class:r(["flex flex-col of-hidden transition-all",{"w-[200px]":!d(O)&&"vertical"===x.mode,"w-[64px]":d(O)&&"vertical"===x.mode,"h-[80px]":"horizontal"===x.mode,"flex-row! w-auto":d(O)&&"horizontal"===x.mode}])},[(v(!0),c(p,null,h(a.menu,(a=>{var n,u;return v(),c(p,{key:a.path??JSON.stringify(a)},[!1!==(null==(n=a.meta)?void 0:n.menu)?(v(),c(p,{key:0},[(null==(u=a.children)?void 0:u.length)?(v(),f(e,{key:0,menu:a,"unique-key":[a.path??JSON.stringify(a)]},null,8,["menu","unique-key"])):(v(),f(l,{key:1,item:a,"unique-key":[a.path??JSON.stringify(a)],onClick:e=>C(a.path??JSON.stringify(a))},null,8,["item","unique-key","onClick"]))],64)):m("",!0)],64)})),128))],2))}});export{x as _};
