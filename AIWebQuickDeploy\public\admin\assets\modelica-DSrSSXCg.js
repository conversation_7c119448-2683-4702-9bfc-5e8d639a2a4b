
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

function e(e){for(var t={},n=e.split(" "),r=0;r<n.length;++r)t[n[r]]=!0;return t}var t=e("algorithm and annotation assert block break class connect connector constant constrainedby der discrete each else elseif elsewhen encapsulated end enumeration equation expandable extends external false final flow for function if import impure in initial inner input loop model not operator or outer output package parameter partial protected public pure record redeclare replaceable return stream then true type when while within"),n=e("abs acos actualStream asin atan atan2 cardinality ceil cos cosh delay div edge exp floor getInstanceName homotopy inStream integer log log10 mod pre reinit rem semiLinear sign sin sinh spatialDistribution sqrt tan tanh"),r=e("Real Boolean Integer String"),o=[].concat(Object.keys(t),Object.keys(n),Object.keys(r)),i=/[;=\(:\),{}.*<>+\-\/^\[\]]/,l=/(:=|<=|>=|==|<>|\.\+|\.\-|\.\*|\.\/|\.\^)/,a=/[0-9]/,u=/[_a-zA-Z]/;function s(e,t){return e.skipToEnd(),t.tokenize=null,"comment"}function c(e,t){for(var n,r=!1;n=e.next();){if(r&&"/"==n){t.tokenize=null;break}r="*"==n}return"comment"}function k(e,t){for(var n,r=!1;null!=(n=e.next());){if('"'==n&&!r){t.tokenize=null,t.sol=!1;break}r=!r&&"\\"==n}return"string"}function p(e,o){for(e.eatWhile(a);e.eat(a)||e.eat(u););var i=e.current();return!o.sol||"package"!=i&&"model"!=i&&"when"!=i&&"connector"!=i?o.sol&&"end"==i&&o.level>0&&o.level--:o.level++,o.tokenize=null,o.sol=!1,t.propertyIsEnumerable(i)?"keyword":n.propertyIsEnumerable(i)?"builtin":r.propertyIsEnumerable(i)?"atom":"variable"}function f(e,t){for(;e.eat(/[^']/););return t.tokenize=null,t.sol=!1,e.eat("'")?"variable":"error"}function m(e,t){return e.eatWhile(a),e.eat(".")&&e.eatWhile(a),(e.eat("e")||e.eat("E"))&&(e.eat("-")||e.eat("+"),e.eatWhile(a)),t.tokenize=null,t.sol=!1,"number"}const d={name:"modelica",startState:function(){return{tokenize:null,level:0,sol:!0}},token:function(e,t){if(null!=t.tokenize)return t.tokenize(e,t);if(e.sol()&&(t.sol=!0),e.eatSpace())return t.tokenize=null,null;var n=e.next();if("/"==n&&e.eat("/"))t.tokenize=s;else if("/"==n&&e.eat("*"))t.tokenize=c;else{if(l.test(n+e.peek()))return e.next(),t.tokenize=null,"operator";if(i.test(n))return t.tokenize=null,"operator";if(u.test(n))t.tokenize=p;else if("'"==n&&e.peek()&&"'"!=e.peek())t.tokenize=f;else if('"'==n)t.tokenize=k;else{if(!a.test(n))return t.tokenize=null,"error";t.tokenize=m}}return t.tokenize(e,t)},indent:function(e,t,n){if(null!=e.tokenize)return null;var r=e.level;return/(algorithm)/.test(t)&&r--,/(equation)/.test(t)&&r--,/(initial algorithm)/.test(t)&&r--,/(initial equation)/.test(t)&&r--,/(end)/.test(t)&&r--,r>0?n.unit*r:0},languageData:{commentTokens:{line:"//",block:{open:"/*",close:"*/"}},autocomplete:o}};export{d as modelica};
