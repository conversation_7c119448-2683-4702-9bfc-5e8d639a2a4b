#!/bin/bash

echo "🔍 99AI 项目构建环境诊断"
echo "================================"

# 检查系统信息
echo "📊 系统信息:"
echo "操作系统: $(uname -s)"
echo "架构: $(uname -m)"
echo "内核版本: $(uname -r)"

# 检查内存
echo ""
echo "💾 内存信息:"
if command -v free >/dev/null 2>&1; then
    free -h
elif command -v vm_stat >/dev/null 2>&1; then
    # macOS
    vm_stat | head -5
else
    echo "无法获取内存信息"
fi

# 检查磁盘空间
echo ""
echo "💿 磁盘空间:"
df -h . 2>/dev/null || echo "无法获取磁盘信息"

# 检查Node.js
echo ""
echo "🟢 Node.js 环境:"
if command -v node >/dev/null 2>&1; then
    echo "Node.js版本: $(node --version)"
    echo "npm版本: $(npm --version)"
else
    echo "❌ Node.js 未安装"
fi

# 检查pnpm
echo ""
echo "📦 包管理器:"
if command -v pnpm >/dev/null 2>&1; then
    echo "pnpm版本: $(pnpm --version)"
else
    echo "❌ pnpm 未安装，请运行: npm install -g pnpm"
fi

# 检查项目依赖
echo ""
echo "📋 项目依赖检查:"

check_dependencies() {
    local dir=$1
    local name=$2
    
    if [ -d "$dir" ]; then
        echo "检查 $name..."
        cd "$dir"
        
        if [ -f "package.json" ]; then
            echo "  ✅ package.json 存在"
        else
            echo "  ❌ package.json 不存在"
        fi
        
        if [ -d "node_modules" ]; then
            echo "  ✅ node_modules 存在"
        else
            echo "  ⚠️  node_modules 不存在，需要运行 pnpm install"
        fi
        
        if [ -f "pnpm-lock.yaml" ]; then
            echo "  ✅ pnpm-lock.yaml 存在"
        else
            echo "  ⚠️  pnpm-lock.yaml 不存在"
        fi
        
        cd ..
    else
        echo "❌ $dir 目录不存在"
    fi
}

check_dependencies "admin" "管理后台"
check_dependencies "chat" "用户界面"
check_dependencies "service" "后端服务"

# 检查构建产物
echo ""
echo "🏗️  构建产物检查:"
for dir in "admin/dist" "chat/dist" "service/dist"; do
    if [ -d "$dir" ]; then
        echo "  ✅ $dir 存在"
    else
        echo "  ❌ $dir 不存在"
    fi
done

# 检查TypeScript配置
echo ""
echo "📝 TypeScript 配置:"
for file in "admin/tsconfig.json" "chat/tsconfig.json" "service/tsconfig.json"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file 存在"
    else
        echo "  ❌ $file 不存在"
    fi
done

# 提供建议
echo ""
echo "💡 建议的解决方案:"
echo "1. 如果内存不足，尝试运行: ./build-lite.sh"
echo "2. 如果要分步构建，尝试运行: ./build-step-by-step.sh admin"
echo "3. 如果要增加内存限制，尝试运行: ./build-fixed.sh"
echo "4. 如果pnpm未安装，运行: npm install -g pnpm"
echo "5. 如果依赖未安装，在各目录运行: pnpm install"

echo ""
echo "🔧 常见问题解决:"
echo "- 内存不足: 关闭其他应用程序，或使用 build-lite.sh"
echo "- 类型检查失败: 使用 build-lite.sh 跳过类型检查"
echo "- 依赖问题: 删除 node_modules 重新安装"
echo "- 权限问题: 确保有写入权限"

echo ""
echo "================================"
echo "诊断完成！"
