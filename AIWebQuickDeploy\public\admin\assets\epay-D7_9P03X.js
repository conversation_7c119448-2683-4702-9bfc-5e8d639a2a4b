
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as a}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as e,$ as l,P as p,r as y,a0 as t,b as r,Q as u,c as d,e as i,f as n,w as o,j as s,h as c,_ as f,g as m,V as E,W as g,t as _,Y as b,k as U}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";const h=e({__name:"epay",setup(e){const U=l({payEpayStatus:"",payEpayPid:"",payEpaySecret:"",payEpayNotifyUrl:"",payEpayReturnUrl:"",payEpayApiPayUrl:"",payEpayApiQueryUrl:"",payEpayRedirect:"",payEpayChannel:[]}),h=p((()=>{const a="1"===U.payEpayStatus;return{payEpayStatus:[{required:!0,trigger:"change",message:"请选择当前支付开启状态"}],payEpaySecret:[{required:a,trigger:"blur",message:"请填写支付秘钥"}],payEpayPid:[{required:a,trigger:"blur",message:"请填写商户PID"}],payEpayNotifyUrl:[{required:a,trigger:"blur",message:"请填写支付通知地址"}],payEpayReturnUrl:[{required:a,trigger:"blur",message:"请填写支付回调地址"}],payEpayApiPayUrl:[{required:a,trigger:"blur",message:"请填写平台支付API请求地址"}],payEpayApiQueryUrl:[{required:a,trigger:"blur",message:"请填写平台API商户查询地址"}],payEpayRedirect:[{required:a,trigger:"change",message:"请选择是否开启跳转支付"}],payEpayChannel:[{required:a,trigger:"change",message:"请选择至少一个支付渠道"}]}})),v=y(),V=[{label:"微信支付",value:"wxpay"},{label:"支付宝支付",value:"alipay"}];async function P(){const a=await x.queryConfig({keys:["payEpaySecret","payEpayNotifyUrl","payEpayReturnUrl","payEpayPid","payEpayStatus","payEpayApiPayUrl","payEpayApiQueryUrl","payEpayRedirect","payEpayChannel"]}),e=a.data.payEpayChannel?JSON.parse(a.data.payEpayChannel):[];Object.assign(U,a.data,{payEpayChannel:e})}function S(){var a;null==(a=v.value)||a.validate((async a=>{if(a){try{await x.setConfig({settings:(e=U,Object.keys(e).map((a=>({configKey:a,configVal:A(a,e[a])}))))}),b.success("变更配置信息成功")}catch(l){}P()}else b.error("请填写完整信息");var e}))}function A(a,e){return["payEpayChannel"].includes(a)?e?e?JSON.stringify(e):void 0:[]:e}return t((()=>U.payEpayStatus),(()=>{setTimeout((()=>{var a;null==(a=v.value)||a.validateField(["payEpaySecret","payEpayPid","payEpayNotifyUrl","payEpayReturnUrl","payEpayApiPayUrl","payEpayApiQueryUrl","payEpayRedirect","payEpayChannel"])}),0)})),r((()=>{P()})),(e,l)=>{const p=f,y=s,t=a,r=u("el-switch"),b=u("el-form-item"),x=u("el-col"),P=u("el-row"),A=u("el-input"),C=u("el-divider"),R=u("el-tooltip"),q=u("el-checkbox"),k=u("el-checkbox-group"),w=u("el-form"),N=u("el-card");return i(),d("div",null,[n(t,null,{title:o((()=>l[9]||(l[9]=[m("div",{class:"flex items-center gap-4"},"易支付设置",-1)]))),content:o((()=>l[10]||(l[10]=[m("div",{class:"text-sm/6"},[m("div",null,"通用易支付渠道，请按文档配置即可。"),m("div",null,"支付通知地址为： https://您的域名/api/pay/notify。")],-1)]))),default:o((()=>[n(y,{outline:"",onClick:S},{default:o((()=>[n(p,{name:"i-ri:file-text-line"}),l[11]||(l[11]=c(" 保存设置 "))])),_:1})])),_:1}),n(N,{style:{margin:"20px"}},{default:o((()=>[n(w,{ref_key:"formRef",ref:v,rules:h.value,model:U,"label-width":"120px"},{default:o((()=>[n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"启用当前支付",prop:"payEpayPid"},{default:o((()=>[n(r,{modelValue:U.payEpayStatus,"onUpdate:modelValue":l[0]||(l[0]=a=>U.payEpayStatus=a),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"商户PID",prop:"payEpayPid"},{default:o((()=>[n(A,{modelValue:U.payEpayPid,"onUpdate:modelValue":l[1]||(l[1]=a=>U.payEpayPid=a),placeholder:"请填写商户PID",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"商户秘钥",prop:"payEpaySecret"},{default:o((()=>[n(A,{modelValue:U.payEpaySecret,"onUpdate:modelValue":l[2]||(l[2]=a=>U.payEpaySecret=a),placeholder:"请填写商户秘钥",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"支付通知地址",prop:"payEpaySecret"},{default:o((()=>[n(A,{modelValue:U.payEpayNotifyUrl,"onUpdate:modelValue":l[3]||(l[3]=a=>U.payEpayNotifyUrl=a),placeholder:"请填写支付通知地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"支付回调地址",prop:"payEpaySecret"},{default:o((()=>[n(A,{modelValue:U.payEpayReturnUrl,"onUpdate:modelValue":l[4]||(l[4]=a=>U.payEpayReturnUrl=a),placeholder:"请填写支付成功后的回跳地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(C),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"支付请求地址",prop:"payEpayApiPayUrl"},{default:o((()=>[n(A,{modelValue:U.payEpayApiPayUrl,"onUpdate:modelValue":l[5]||(l[5]=a=>U.payEpayApiPayUrl=a),placeholder:"请填写平台支付请求地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"商户查询地址",prop:"payEpayApiQueryUrl"},{default:o((()=>[n(A,{modelValue:U.payEpayApiQueryUrl,"onUpdate:modelValue":l[6]||(l[6]=a=>U.payEpayApiQueryUrl=a),placeholder:"请填写平台查询商户地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(C),n(P,null,{default:o((()=>[n(x,{xs:24,md:20,lg:15,xl:12},{default:o((()=>[n(b,{label:"是否开启跳转支付",prop:"payEpayRedirect","label-width":"130px"},{default:o((()=>[n(R,{class:"box-item",effect:"dark",content:"请注意、仅mapi支持不跳转支付、其他都需要为跳转支付、不开启跳转支付表示购买页面显示二维码直接扫码购买、跳转支付表示前往新页面！",placement:"right"},{default:o((()=>[n(r,{modelValue:U.payEpayRedirect,"onUpdate:modelValue":l[7]||(l[7]=a=>U.payEpayRedirect=a),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),n(P,null,{default:o((()=>[n(x,{xs:24,md:24,lg:24,xl:24},{default:o((()=>[n(b,{label:"开启支付渠道",prop:"payEpayChannel"},{default:o((()=>[n(k,{modelValue:U.payEpayChannel,"onUpdate:modelValue":l[8]||(l[8]=a=>U.payEpayChannel=a),size:"small"},{default:o((()=>[(i(),d(E,null,g(V,(a=>n(q,{key:a.value,border:"",label:a.value},{default:o((()=>[c(_(a.label),1)])),_:2},1032,["label"]))),64))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof U&&U(h);export{h as default};
