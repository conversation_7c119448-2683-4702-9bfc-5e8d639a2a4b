
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

const e=[{value:0,label:"待激活"},{value:1,label:"正常"},{value:2,label:"已封禁"},{value:3,label:"黑名单"}],a={0:"待激活",1:"正常",2:"已封禁",3:"黑名单"},l={0:"info",1:"success",2:"danger",3:"danger"},i={1:"注册赠送",2:"受邀请赠送",3:"邀请人赠送",4:"购买套餐赠送",5:"管理员赠送",6:"扫码支付",7:"绘画失败退款",8:"签到奖励"},o=[{value:1,label:"注册赠送"},{value:2,label:"受邀请赠送"},{value:3,label:"邀请人赠送"},{value:4,label:"购买套餐赠送"},{value:5,label:"管理员赠送"},{value:6,label:"扫码支付"},{value:7,label:"绘画失败退款"},{value:8,label:"签到奖励"}],t={0:"关闭",1:"开启"},s=[{value:0,label:"禁用"},{value:1,label:"启动"}],n=[{value:0,label:"未使用"},{value:1,label:"已使用"}],g=[{value:0,label:"禁用"},{value:1,label:"启用"}],u=[{value:"0",label:"未启用"},{value:"1",label:"已启用"}],p={"-1":"欠费锁定",0:"未启用",1:"已启用",3:"待审核",4:"拒绝共享",5:"通过共享"},m=["gpt-4o","gpt-4o-mini","chatgpt-4o-latest","gpt-4.5-preview","o1","o1-mini","o3-mini","o3","gpt-4-all","gpt-4o-all","gpt-4o-image","gpt-4o-image-vip","sora_image","gpt-4.1","gpt-4.1-nano","gpt-4.1-mini","o1-mini-all","o1-all","o1-pro-all","o3-mini-all","o3-mini-high-all","o4-mini","claude-3-sonnet-20240229","claude-3-opus-20240229","claude-3-haiku-20240307","claude-3-5-sonnet-latest","claude-3-7-sonnet-20250219","claude-3-7-sonnet-thinking","claude-sonnet-4-20250514","claude-opus-4-20250514","claude-sonnet-4-20250514-thinking","claude-opus-4-20250514-thinking","grok-2-1212","grok-2-vision-1212","grok-2-latest","grok-2-vision-latest","grok-3","grok-3-reasoner","grok-3-deepsearch","gemini-pro","gemini-pro-vision","gemini-pro-1.5","gemini-1.5-flash","gemini-1.5-pro-latest","gemini-1.5-flash-002","gemini-2.0-flash-exp","gemini-2.0-flash-lite","gemini-2.0-flash-thinking-exp","gemini-2.5-pro-exp-03-25","deepseek-r1","deepseek-reasoner","deepseek-v3","deepseek-chat","deepseek-reasoner-all","ERNIE-Bot","ERNIE-Bot-4","ERNIE-3.5-8K","ERNIE-Bot-turbo","qwen-turbo","qwen-plus","qwen-max","qwen-max-longcontext","hunyuan","chatglm_turbo","chatglm_pro","chatglm_std","chatglm_lite","glm-3-turbo","glm-4","glm-4v","Baichuan2-53B","Baichuan2-Turbo","Baichuan2-Turbo-192k","yi-34b-chat-0205","yi-34b-chat-200k","yi-vl-plus","360GPT_S2_V9","SparkDesk","SparkDesk-v1.1","SparkDesk-v2.1","SparkDesk-v3.1","SparkDesk-v3.5","moonshot-v1-8k","moonshot-v1-32k","moonshot-v1-128k","dall-e-3","gpt-image-1","midjourney","stable-diffusion","suno-music","luma-video","flux-draw","cog-video","gpt-4o-image","gpt-4o-image-vip","grok-2-image-latest","seededit","seedream-3.0","tts-1","gpts"],r=[{value:0,label:"未支付"},{value:1,label:"已支付"},{value:2,label:"支付失败"},{value:3,label:"支付超时"}],v={0:"未支付",1:"已支付",2:"支付失败",3:"支付超时"},k=[{value:"epay",label:"易支付"},{value:"hupi",label:"虎皮椒"},{value:"wechat",label:"微信支付"},{value:"mpay",label:"码支付"},{value:"ltzf",label:"蓝兔支付"}],h={epay:"易支付",hupi:"虎皮椒",wechat:"微信支付",mpay:"码支付",ltzf:"蓝兔支付"},d=[{value:"百度云检测",label:"百度云检测"},{value:"自定义检测",label:"自定义检测"}],c=[{value:1,label:"基础对话"},{value:3,label:"特殊模型"}],b={1:"基础对话",2:"创意模型",3:"特殊模型"},f={1:["gpt-4o","gpt-4o-mini","chatgpt-4o-latest","gpt-4.5-preview","o1","o3","o1-mini","o3-mini","o4-mini","gpt-4-all","gpt-4o-all","gpt-4o-image","gpt-4o-image-vip","gpt-4.1","gpt-4.1-nano","gpt-4.1-mini","o1-mini-all","o1-all","o1-pro-all","o3-mini-all","o3-mini-high-all","claude-3-sonnet-20240229","claude-3-opus-20240229","claude-3-haiku-20240307","claude-3-5-sonnet-latest","claude-3-7-sonnet-20250219","claude-3-7-sonnet-thinking","claude-sonnet-4-20250514","claude-opus-4-20250514","claude-sonnet-4-20250514-thinking","claude-opus-4-20250514-thinking","grok-2-1212","grok-2-vision-1212","grok-2-latest","grok-2-vision-latest","grok-3","grok-3-reasoner","grok-3-deepsearch","gemini-pro","gemini-pro-vision","gemini-pro-1.5","gemini-1.5-flash","gemini-1.5-pro-latest","gemini-1.5-flash-002","gemini-2.0-flash-exp","gemini-2.0-flash-lite","gemini-2.0-flash-thinking-exp","gemini-2.5-pro-exp-03-25","deepseek-r1","deepseek-reasoner","deepseek-v3","deepseek-chat","deepseek-reasoner-all","ERNIE-Bot","ERNIE-Bot-4","ERNIE-3.5-8K","ERNIE-Bot-turbo","qwen-turbo","qwen-plus","qwen-max","qwen-max-longcontext","hunyuan","chatglm_turbo","chatglm_pro","chatglm_std","chatglm_lite","glm-3-turbo","glm-4","glm-4v","Baichuan2-53B","Baichuan2-Turbo","Baichuan2-Turbo-192k","yi-34b-chat-0205","yi-34b-chat-200k","yi-vl-plus","360GPT_S2_V9","SparkDesk","SparkDesk-v1.1","SparkDesk-v2.1","SparkDesk-v3.1","SparkDesk-v3.5","moonshot-v1-8k","moonshot-v1-32k","moonshot-v1-128k"],2:["dall-e-3","gpt-image-1","midjourney","stable-diffusion","suno-music","luma-video","flux-draw","cog-video","gpt-4o-image","gpt-4o-image-vip","sora_image","grok-2-image-latest","seededit","seedream-3.0"],3:["tts-1","gpts","deepseek-r1","deepseek-reasoner","flowith"]},E=[{value:1,label:"普通积分"},{value:2,label:"高级积分"},{value:3,label:"绘画积分"}];export{n as C,E as D,g as E,t as I,f as M,k as P,p as Q,o as R,d as T,l as U,c as a,m as b,u as c,b as d,r as e,h as f,v as g,s as h,a as i,e as j,i as k};
