
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as t,b as d,Q as o,c as u,e as i,f as n,w as s,j as m,h as r,_ as c,g as f,i as p,ah as _,Y as v,k as b}from"./index-BERX8Mlm.js";import{a as x}from"./config-BrbFL53_.js";const g={class:"font-bold text-lg mb-4"},h={class:"font-bold text-lg mt-8 mb-4"},V={class:"font-bold text-lg mt-8 mb-4"},H=l({__name:"points",setup(l){const b=a({isHideModel3Point:"",isHideModel4Point:"",isHideDrawMjPoint:"",isHideDefaultPreset:"",model3Name:"",model4Name:"",drawMjName:"",showWatermark:"",isHideTts:"1",pluginFirst:"1",isHidePlugin:"0",showCrami:"0",clearCacheEnabled:"0",streamCacheEnabled:"0",homeWelcomeContent:"",enableHtmlRender:"0"}),H=t({model3Name:[{required:!0,message:"请输入普通积分名称",trigger:"blur"}],model4Name:[{required:!0,message:"请输入高级积分名称",trigger:"blur"}],drawMjName:[{required:!0,message:"请输入绘画积分名称",trigger:"blur"}]}),w=t();async function M(){const e=await x.queryConfig({keys:["isHideModel3Point","isHideModel4Point","isHideDrawMjPoint","isHideDefaultPreset","model3Name","model4Name","drawMjName","showWatermark","isHideTts","pluginFirst","isHidePlugin","showCrami","clearCacheEnabled","streamCacheEnabled","homeWelcomeContent","enableHtmlRender"]});Object.assign(b,e.data)}function P(){var e;null==(e=w.value)||e.validate((async e=>{if(e){try{await x.setConfig({settings:(l=b,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),v.success("变更配置信息成功")}catch(a){}M()}else v.error("请填写完整信息");var l}))}return d((()=>{M()})),(l,a)=>{const t=c,d=m,v=e,x=o("el-divider"),M=o("el-switch"),C=o("el-icon"),y=o("el-tooltip"),N=o("el-form-item"),j=o("el-col"),k=o("el-row"),U=o("el-input"),D=o("el-form"),E=o("el-card");return i(),u("div",null,[n(v,null,{title:s((()=>a[13]||(a[13]=[f("div",{class:"flex items-center gap-4"},"网站显示配置",-1)]))),content:s((()=>a[14]||(a[14]=[f("div",{class:"text-sm/6"},[f("div",null," 网站显示配置用于控制用户界面的各种元素和功能展示，包括积分显示、菜单选项、功能按钮等。 "),f("div",null,"合理配置这些选项可以优化用户体验，简化界面，突出核心功能。")],-1)]))),default:s((()=>[n(d,{outline:"",onClick:P},{default:s((()=>[n(t,{name:"i-ri:file-text-line"}),a[15]||(a[15]=r(" 保存设置 "))])),_:1})])),_:1}),n(E,{style:{margin:"20px"}},{default:s((()=>[n(D,{ref_key:"formRef",ref:w,rules:H.value,model:b,"label-width":"150px"},{default:s((()=>[f("h3",g,[a[16]||(a[16]=r("性能与缓存设置 ")),n(x)]),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"清除缓存",prop:"clearCacheEnabled"},{default:s((()=>[n(M,{modelValue:b.clearCacheEnabled,"onUpdate:modelValue":a[0]||(a[0]=e=>b.clearCacheEnabled=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),n(y,{class:"box-item",effect:"dark",placement:"right"},{content:s((()=>a[17]||(a[17]=[f("div",{style:{width:"250px"}},[f("p",null,"开启后，将启用缓存清除功能，有助于解决页面数据显示异常问题")],-1)]))),default:s((()=>[n(C,{class:"ml-3 cursor-pointer"},{default:s((()=>[n(p(_))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"流式对话缓存",prop:"streamCacheEnabled"},{default:s((()=>[n(M,{modelValue:b.streamCacheEnabled,"onUpdate:modelValue":a[1]||(a[1]=e=>b.streamCacheEnabled=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),n(y,{class:"box-item",effect:"dark",placement:"right"},{content:s((()=>a[18]||(a[18]=[f("div",{style:{width:"250px"}},[f("p",null," 开启后，会对 AI 对话进行缓存输出，优化输出平滑性。关闭则完全依赖 API 流式输出 ")],-1)]))),default:s((()=>[n(C,{class:"ml-3 cursor-pointer"},{default:s((()=>[n(p(_))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),f("h3",h,[a[19]||(a[19]=r("功能与界面设置 ")),n(x)]),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"显示全局水印",prop:"showWatermark"},{default:s((()=>[n(M,{modelValue:b.showWatermark,"onUpdate:modelValue":a[2]||(a[2]=e=>b.showWatermark=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),n(y,{class:"box-item",effect:"dark",placement:"right"},{content:s((()=>a[20]||(a[20]=[f("div",{style:{width:"250px"}},[f("p",null,"开启后将在对话页面显示用户名水印，增强内容安全性")],-1)]))),default:s((()=>[n(C,{class:"ml-3 cursor-pointer"},{default:s((()=>[n(p(_))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"渲染HTML内容",prop:"enableHtmlRender"},{default:s((()=>[n(M,{modelValue:b.enableHtmlRender,"onUpdate:modelValue":a[3]||(a[3]=e=>b.enableHtmlRender=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),n(y,{class:"box-item",effect:"dark",placement:"right"},{content:s((()=>a[21]||(a[21]=[f("div",{style:{width:"250px"}},[f("p",null,"开启后，允许在用户内容中渲染HTML标签，但可能影响页面样式，建议按需开启")],-1)]))),default:s((()=>[n(C,{class:"ml-3 cursor-pointer"},{default:s((()=>[n(p(_))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"隐藏首页默认预设",prop:"isHideDefaultPreset"},{default:s((()=>[n(M,{modelValue:b.isHideDefaultPreset,"onUpdate:modelValue":a[4]||(a[4]=e=>b.isHideDefaultPreset=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),n(y,{class:"box-item",effect:"dark",placement:"right"},{content:s((()=>a[22]||(a[22]=[f("div",{style:{width:"250px"}},[f("p",null,"开启后，首页将不显示默认预设提示，使界面更简洁")],-1)]))),default:s((()=>[n(C,{class:"ml-3 cursor-pointer"},{default:s((()=>[n(p(_))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"隐藏朗读按钮",prop:"isHideTts"},{default:s((()=>[n(M,{modelValue:b.isHideTts,"onUpdate:modelValue":a[5]||(a[5]=e=>b.isHideTts=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"]),n(y,{class:"box-item",effect:"dark",placement:"right"},{content:s((()=>a[23]||(a[23]=[f("div",{style:{width:"250px"}},[f("p",null,"开启后，用户界面将不显示文本朗读按钮，可简化界面")],-1)]))),default:s((()=>[n(C,{class:"ml-3 cursor-pointer"},{default:s((()=>[n(p(_))])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"首页欢迎提示",prop:"homeWelcomeContent"},{default:s((()=>[n(U,{modelValue:b.homeWelcomeContent,"onUpdate:modelValue":a[6]||(a[6]=e=>b.homeWelcomeContent=e),type:"text",placeholder:"请输入首页欢迎提示内容，将在用户首次访问时显示"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),f("h3",V,[a[24]||(a[24]=r("积分显示设置 ")),n(x)]),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"隐藏普通积分",prop:"isHideModel3Point"},{default:s((()=>[n(M,{modelValue:b.isHideModel3Point,"onUpdate:modelValue":a[7]||(a[7]=e=>b.isHideModel3Point=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"普通积分名称",prop:"model3Name"},{default:s((()=>[n(U,{modelValue:b.model3Name,"onUpdate:modelValue":a[8]||(a[8]=e=>b.model3Name=e),placeholder:"普通积分名称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"隐藏高级积分",prop:"isHideModel4Point"},{default:s((()=>[n(M,{modelValue:b.isHideModel4Point,"onUpdate:modelValue":a[9]||(a[9]=e=>b.isHideModel4Point=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"高级积分名称",prop:"model4Name"},{default:s((()=>[n(U,{modelValue:b.model4Name,"onUpdate:modelValue":a[10]||(a[10]=e=>b.model4Name=e),placeholder:"高级积分名称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"隐藏绘画积分",prop:"isHideDrawMjPoint"},{default:s((()=>[n(M,{modelValue:b.isHideDrawMjPoint,"onUpdate:modelValue":a[11]||(a[11]=e=>b.isHideDrawMjPoint=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(k,null,{default:s((()=>[n(j,{xs:24,md:20,lg:15,xl:12},{default:s((()=>[n(N,{label:"绘画积分名称",prop:"drawMjName"},{default:s((()=>[n(U,{modelValue:b.drawMjName,"onUpdate:modelValue":a[12]||(a[12]=e=>b.drawMjName=e),placeholder:"绘画积分名称",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof b&&b(H);export{H as default};
