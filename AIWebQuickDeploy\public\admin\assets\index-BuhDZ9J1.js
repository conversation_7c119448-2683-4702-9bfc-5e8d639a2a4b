
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,a as t,az as a,r as i,a2 as n,e as s,w as r,c as l,T as o,i as u,g as p,f as _,a4 as c,V as d,W as m,a6 as v,_ as f,t as g,aB as h,Z as y}from"./index-BERX8Mlm.js";import{_ as x}from"./index.vue_vue_type_script_setup_true_lang-AClYjrVV.js";import{_ as j}from"./rightSide.vue_vue_type_script_setup_true_lang-ClZNHSw-.js";import{u as b}from"./useMenu-CK91fAX9.js";import"./HDropdownMenu.vue_vue_type_script_setup_true_lang-Wc6l-Ngn.js";import"./index.vue_vue_type_script_setup_true_lang-PUnUpH4H.js";import"./HDropdown-DFGm5c_S.js";import"./HTabList.vue_vue_type_script_setup_true_lang-BEyYCazB.js";import"./use-resolve-button-type-DnRVrBaM.js";import"./index.vue_vue_type_script_setup_true_lang-DN03WRps.js";import"./index-DhWfG07N.js";import"./index.vue_vue_type_script_setup_true_lang-Do4XPH2t.js";import"./HKbd-LjWkyhwy.js";import"./index.vue_vue_type_script_setup_true_lang-DbfRBGyF.js";import"./useMainPage-Dbp8uSF1.js";const k={key:0},w={class:"header-container"},H={class:"menu flex of-hidden transition-all"},M=["title","onClick"],T={class:"inline-flex flex-1 items-center justify-center gap-1"},q={class:"w-full flex-1 truncate text-sm transition-height transition-opacity transition-width"},z=y(e({name:"LayoutHeader",__name:"index",setup(e){const y=t(),z=a(),{switchTo:B}=b(),C=i();function D(e){(e.deltaY||0!==e.detail)&&C.value.scrollBy({left:(e.deltaY||e.detail)>0?50:-50})}return(e,t)=>{const a=f;return s(),n(h,{name:"header"},{default:r((()=>["pc"===u(y).mode&&"head"===u(y).settings.menu.menuMode?(s(),l("header",k,[p("div",w,[_(x,{class:"title"}),p("div",{ref_key:"menuRef",ref:C,class:"menu-container",onWheel:c(D,["prevent"])},[p("div",H,[(s(!0),l(d,null,m(u(z).allMenus,((e,t)=>{var i,r,_,c,d,m,f,h;return s(),l("div",{key:t,class:v(["menu-item relative transition-all",{active:t===u(z).actived}])},[e.children&&0!==e.children.length?(s(),l("div",{key:0,class:v(["group menu-item-container h-full w-full flex cursor-pointer items-center justify-between gap-1 px-3 text-[var(--g-header-menu-color)] transition-all hover-bg-[var(--g-header-menu-hover-bg)] hover-text-[var(--g-header-menu-hover-color)]",{"text-[var(--g-header-menu-active-color)]! bg-[var(--g-header-menu-active-bg)]!":t===u(z).actived}]),title:"function"==typeof(null==(i=e.meta)?void 0:i.title)?null==(r=e.meta)?void 0:r.title():null==(_=e.meta)?void 0:_.title,onClick:e=>u(B)(t)},[p("div",T,[(null==(c=e.meta)?void 0:c.icon)?(s(),n(a,{key:0,name:null==(d=e.meta)?void 0:d.icon,size:20,class:"menu-item-container-icon transition-transform group-hover-scale-120",async:""},null,8,["name"])):o("",!0),p("span",q,g("function"==typeof(null==(m=e.meta)?void 0:m.title)?null==(f=e.meta)?void 0:f.title():null==(h=e.meta)?void 0:h.title),1)])],10,M)):o("",!0)],2)})),128))])],544),_(j)])])):o("",!0)])),_:1})}}}),[["__scopeId","data-v-c74f61f1"]]);export{z as default};
