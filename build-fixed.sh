#!/bin/bash

set -e

# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=8192"

echo "开始构建项目..."
echo "Node.js内存限制已设置为8GB"

echo "构建管理后台..."
cd admin/
pnpm install
# 分步构建，避免内存溢出
echo "格式化代码..."
pnpm format
echo "TypeScript类型检查..."
pnpm run lint
echo "构建前端资源..."
pnpm run build:test || {
    echo "管理后台构建失败，尝试跳过类型检查..."
    # 如果类型检查失败，尝试跳过类型检查直接构建
    npx vite build
}
cd ..

echo "构建用户界面..."
cd chat/
pnpm install
pnpm format
pnpm run build || {
    echo "用户界面构建失败，尝试跳过类型检查..."
    npx vite build
}
cd ..

echo "构建后端服务..."
cd service/
pnpm install
pnpm run build
cd ..

echo "清理旧文件..."
rm -rf ./AIWebQuickDeploy/dist/* ./AIWebQuickDeploy/public/admin/* ./AIWebQuickDeploy/public/chat/*
mkdir -p ./AIWebQuickDeploy/dist ./AIWebQuickDeploy/public/admin ./AIWebQuickDeploy/public/chat

echo "复制配置文件..."
cp service/pm2.conf.json ./AIWebQuickDeploy/pm2.conf.json
cp service/package.json ./AIWebQuickDeploy/package.json

# 检查文件是否存在再复制
if [ -f "service/.env.example" ]; then
    cp service/.env.example ./AIWebQuickDeploy/.env.example
fi

if [ -f "service/.env.docker" ]; then
    cp service/.env.docker ./AIWebQuickDeploy/.env.docker
fi

if [ -f "service/Dockerfile" ]; then
    cp service/Dockerfile ./AIWebQuickDeploy/Dockerfile
fi

if [ -f "service/docker-compose.yml" ]; then
    cp service/docker-compose.yml ./AIWebQuickDeploy/docker-compose.yml
fi

if [ -f "service/.dockerignore" ]; then
    cp service/.dockerignore ./AIWebQuickDeploy/.dockerignore
fi

echo "复制构建产物..."
if [ -d "service/dist" ]; then
    cp -a service/dist/* ./AIWebQuickDeploy/dist/
else
    echo "警告: service/dist 目录不存在"
fi

if [ -d "admin/dist" ]; then
    cp -r admin/dist/* ./AIWebQuickDeploy/public/admin/
else
    echo "警告: admin/dist 目录不存在"
fi

if [ -d "chat/dist" ]; then
    cp -r chat/dist/* ./AIWebQuickDeploy/public/chat/
else
    echo "警告: chat/dist 目录不存在"
fi

echo "✅ 打包完成！"
echo "部署文件位于: ./AIWebQuickDeploy/"
