
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as l}from"./HDialog.vue_vue_type_script_setup_true_lang-BfrjeNSs.js";import s from"./HKbd-LjWkyhwy.js";import{d as a,r as t,a as e,b as n,aG as u,a2 as o,e as d,w as i,g as c,c as m,T as _,f,h as r,t as p,i as g,ab as b}from"./index-BERX8Mlm.js";const y={class:"px-4"},v={class:"grid gap-2 sm-grid-cols-2"},x={class:"list-none pl-4 text-sm"},h={class:"py-1"},k={key:0,class:"py-1"},A={key:0},H={class:"list-none pl-4 text-sm"},j={class:"py-1"},S={key:1},V={class:"list-none pl-4 text-sm"},I={class:"py-1"},w={class:"py-1"},D={class:"py-1"},G={class:"py-1"},K={class:"py-1"},M=a({name:"HotkeysIntro",__name:"index",setup(a){const M=t(!1),T=e();return n((()=>{u.on("global-hotkeys-intro-toggle",(()=>{M.value=!M.value}))})),(a,t)=>{const e=s,n=l;return d(),o(n,{modelValue:g(M),"onUpdate:modelValue":t[0]||(t[0]=l=>b(M)?M.value=l:null),title:"快捷键介绍"},{default:i((()=>[c("div",y,[c("div",v,[c("div",null,[t[5]||(t[5]=c("h2",{class:"m-0 text-lg font-bold"},"全局",-1)),c("ul",x,[c("li",h,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[1]||(t[1]=[r("I")]))),_:1}),t[2]||(t[2]=r(" 查看系统信息 "))]),g(T).settings.toolbar.navSearch&&g(T).settings.navSearch.enableHotkeys?(d(),m("li",k,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[3]||(t[3]=[r("S")]))),_:1}),t[4]||(t[4]=r(" 唤起导航搜索 "))])):_("",!0)])]),g(T).settings.menu.enableHotkeys&&["side","head"].includes(g(T).settings.menu.menuMode)?(d(),m("div",A,[t[8]||(t[8]=c("h2",{class:"m-0 text-lg font-bold"},"主导航",-1)),c("ul",H,[c("li",j,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[6]||(t[6]=[r("`")]))),_:1}),t[7]||(t[7]=r(" 激活下一个主导航 "))])])])):_("",!0),g(T).settings.tabbar.enable&&g(T).settings.tabbar.enableHotkeys?(d(),m("div",S,[t[19]||(t[19]=c("h2",{class:"m-0 text-lg font-bold"},"标签栏",-1)),c("ul",V,[c("li",I,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[9]||(t[9]=[r("←")]))),_:1}),t[10]||(t[10]=r(" 切换到上一个标签页 "))]),c("li",w,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[11]||(t[11]=[r("→")]))),_:1}),t[12]||(t[12]=r(" 切换到下一个标签页 "))]),c("li",D,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[13]||(t[13]=[r("W")]))),_:1}),t[14]||(t[14]=r(" 关闭当前标签页 "))]),c("li",G,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[15]||(t[15]=[r("1~9")]))),_:1}),t[16]||(t[16]=r(" 切换到第 n 个标签页 "))]),c("li",K,[f(e,null,{default:i((()=>[r(p("mac"===g(T).os?"⌥":"Alt"),1)])),_:1}),f(e,null,{default:i((()=>t[17]||(t[17]=[r("0")]))),_:1}),t[18]||(t[18]=r(" 切换到最后一个标签页 "))])])])):_("",!0)])])])),_:1},8,["modelValue"])}}});export{M as _};
