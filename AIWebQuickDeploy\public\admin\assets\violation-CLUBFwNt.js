
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{A as a}from"./badWords-FUVqvqZb.js";import{d as t,ad as r,r as s,$ as u,b as n,Q as o,a1 as i,c as d,e as p,f,w as c,g as m,V as _,W as v,i as w,a2 as y,h as g,a5 as b,t as h,ab as x,ae as C,Y as V,k}from"./index-BERX8Mlm.js";import{u as z}from"./utcFormatTime-BtFjiA-p.js";import{T as I,U as j,i as O,j as U}from"./index-gPQwgooA.js";const S=["src"],Y={class:"answer"},A=["innerHTML"],M=t({__name:"violation",setup(t){const k=new r.Renderer;r.setOptions({renderer:k,gfm:!0,pedantic:!1});const M=s(!1),T=s(),q=s(),D=s(0),F=u({userId:"",typeOriginCn:"",page:1,size:10}),H=s([]),J=s(!1),L=u({status:"0",id:0});async function N(){(await C.updateUserStatus(L)).success&&V({type:"success",message:"变更记录状态成功！"}),J.value=!1,R()}async function R(){M.value=!0;try{const e=await a.queryViolation(F);M.value=!1;const{rows:l,count:t}=e.data;D.value=t,H.value=l}catch(e){M.value=!1}}async function W(e){const l=await C.queryAllUser({size:30,username:e});T.value=l.data.rows}return n((()=>{R()})),(a,t)=>{const s=l,u=o("el-option"),n=o("el-select"),C=o("el-form-item"),V=o("el-button"),k=o("el-form"),P=e,Q=o("el-table-column"),$=o("el-tag"),B=o("el-popover"),E=o("el-table"),G=o("el-pagination"),K=o("el-row"),X=o("el-dialog"),Z=i("loading");return p(),d("div",null,[f(s,null,{title:c((()=>t[7]||(t[7]=[m("div",{class:"flex items-center gap-4"},"违规检测记录",-1)]))),_:1}),f(P,null,{default:c((()=>[f(k,{ref_key:"formRef",ref:q,inline:!0,model:F},{default:c((()=>[f(C,{label:"用户名称",prop:"userId"},{default:c((()=>[f(n,{modelValue:F.userId,"onUpdate:modelValue":t[0]||(t[0]=e=>F.userId=e),filterable:"",clearable:"",remote:"","reserve-keyword":"",placeholder:"用户姓名[模糊搜索]","remote-show-suffix":"","remote-method":W,style:{width:"160px"}},{default:c((()=>[(p(!0),d(_,null,v(w(T),(e=>(p(),y(u,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),f(C,{label:"检测平台",prop:"typeOriginCn"},{default:c((()=>[f(n,{modelValue:F.typeOriginCn,"onUpdate:modelValue":t[1]||(t[1]=e=>F.typeOriginCn=e),placeholder:"请选择检测平台",clearable:"",style:{width:"160px"}},{default:c((()=>[(p(!0),d(_,null,v(w(I),(e=>(p(),y(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),f(C,null,{default:c((()=>[f(V,{type:"primary",onClick:R},{default:c((()=>t[8]||(t[8]=[g(" 查询 ")]))),_:1}),f(V,{onClick:t[2]||(t[2]=e=>{return null==(l=w(q))||l.resetFields(),void R();var l})},{default:c((()=>t[9]||(t[9]=[g(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),f(P,{style:{width:"100%"}},{default:c((()=>[b((p(),y(E,{border:"",data:w(H),style:{width:"100%"},size:"large","tooltip-options":{}},{default:c((()=>[f(Q,{fixed:"",prop:"userInfo.username",label:"用户名称",width:"150"}),f(Q,{prop:"userInfo.avatar",label:"头像",width:"120"},{default:c((e=>{var l,a;return[m("img",{src:null==(a=null==(l=e.row)?void 0:l.userInfo)?void 0:a.avatar,style:{height:"50px"}},null,8,S)]})),_:1}),f(Q,{prop:"userInfo.email",label:"邮箱",width:"200"}),f(Q,{prop:"status",label:"用户状态",width:"120",align:"center"},{default:c((({row:e})=>[f($,{type:w(j)[e.userInfo.status]},{default:c((()=>[g(h(w(O)[e.userInfo.status]),1)])),_:2},1032,["type"])])),_:1}),f(Q,{prop:"userInfo.violationCount",label:"累计次数",width:"90",align:"center"}),f(Q,{label:"违规类型"},{default:c((e=>[f($,{type:"danger"},{default:c((()=>{var l,a;return[g(h((null==(l=e.row)?void 0:l.typeCn)?JSON.parse(null==(a=e.row)?void 0:a.typeCn).join("  |  "):""),1)]})),_:2},1024)])),_:1}),f(Q,{label:"违规关键词"},{default:c((e=>{var l,a;return[g(h((null==(l=e.row)?void 0:l.words)?JSON.parse(null==(a=e.row)?void 0:a.words).join("  |  "):""),1)]})),_:1}),f(Q,{prop:"typeOriginCn",label:"违规检测来源",width:"120"},{default:c((e=>[f($,{type:"success"},{default:c((()=>[g(h(e.row.typeOriginCn),1)])),_:2},1024)])),_:1}),f(Q,{prop:"answer",label:"违规内容",width:"200"},{default:c((e=>[f(B,{placement:"top",width:400,trigger:"click"},{reference:c((()=>[m("div",Y,h(e.row.content),1)])),default:c((()=>[m("div",{class:"answer_container",innerHTML:w(r)(e.row.content||"")},null,8,A)])),_:2},1024)])),_:1}),f(Q,{prop:"createdAt",label:"违规时间",width:"200"},{default:c((e=>[g(h(w(z)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),f(Q,{fixed:"right",label:"操作",width:"120",align:"center"},{default:c((e=>[f(V,{link:"",type:"primary",size:"small",onClick:l=>{return a=e.row,J.value=!0,L.status=a.status.toString(),void(L.id=a.userId);var a}},{default:c((()=>t[10]||(t[10]=[g(" 变更用户状态 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Z,w(M)]]),f(K,{class:"mt-5 flex justify-end"},{default:c((()=>[f(G,{"current-page":F.page,"onUpdate:currentPage":t[3]||(t[3]=e=>F.page=e),"page-size":F.size,"onUpdate:pageSize":t[4]||(t[4]=e=>F.size=e),class:"mr-5","page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:w(D),onSizeChange:R,onCurrentChange:R},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),f(X,{modelValue:w(J),"onUpdate:modelValue":t[6]||(t[6]=e=>x(J)?J.value=e:null),title:"变更用户状态",width:"500px"},{default:c((()=>[f(k,{model:L,inline:!0},{default:c((()=>[f(C,{label:"用户状态","label-width":"90px"},{default:c((()=>[f(n,{modelValue:L.status,"onUpdate:modelValue":t[5]||(t[5]=e=>L.status=e),placeholder:"请选择用户状态",clearable:"",style:{width:"160px"}},{default:c((()=>[(p(!0),d(_,null,v(w(U),(e=>(p(),y(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),f(C,null,{default:c((()=>[f(V,{type:"primary",onClick:N},{default:c((()=>t[11]||(t[11]=[g(" 确认变更 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}});"function"==typeof k&&k(M);export{M as default};
