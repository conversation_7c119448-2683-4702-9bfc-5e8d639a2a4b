var an=Object.defineProperty,cn=Object.defineProperties;var ln=Object.getOwnPropertyDescriptors;var G=Object.getOwnPropertySymbols;var Kt=Object.prototype.hasOwnProperty,Wt=Object.prototype.propertyIsEnumerable;var ut=(t,e,n)=>e in t?an(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,v=(t,e)=>{for(var n in e||(e={}))Kt.call(e,n)&&ut(t,n,e[n]);if(G)for(var n of G(e))Wt.call(e,n)&&ut(t,n,e[n]);return t},L=(t,e)=>cn(t,ln(e));var ft=(t,e)=>{var n={};for(var r in t)Kt.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&G)for(var r of G(t))e.indexOf(r)<0&&Wt.call(t,r)&&(n[r]=t[r]);return n};var A=(t,e,n)=>(ut(t,typeof e!="symbol"?e+"":e,n),n);var pt=(t,e,n)=>new Promise((r,s)=>{var i=c=>{try{a(n.next(c))}catch(l){s(l)}},o=c=>{try{a(n.throw(c))}catch(l){s(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,o);a((n=n.apply(t,e)).next())});import{u as un,t as fn,i as vt,a as j,b as pn,d as dn}from"./utils-vendor-c35799af.js";import{u as N,d as F,w as V,z as ot,j as B,q as ce,A as mn,B as le,C as ue,m as fe,F as yn,i as bn}from"./vue-vendor-d751b0f5.js";function dt(t){if(t===null||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e!==null&&e!==Object.prototype&&Object.getPrototypeOf(e)!==null||Symbol.iterator in t?!1:Symbol.toStringTag in t?Object.prototype.toString.call(t)==="[object Module]":!0}function xt(t,e,n=".",r){if(!dt(e))return xt(t,{},n,r);const s=Object.assign({},e);for(const i in t){if(i==="__proto__"||i==="constructor")continue;const o=t[i];o!=null&&(r&&r(s,i,o,n)||(Array.isArray(o)&&Array.isArray(s[i])?s[i]=[...o,...s[i]]:dt(o)&&dt(s[i])?s[i]=xt(o,s[i],(n?`${n}.`:"")+i.toString(),r):s[i]=o))}return s}function hn(t){return(...e)=>e.reduce((n,r)=>xt(n,r,"",t),{})}const pe=hn(),de=1/60*1e3,gn=typeof performance!="undefined"?()=>performance.now():()=>Date.now(),me=typeof window!="undefined"?t=>window.requestAnimationFrame(t):t=>setTimeout(()=>t(gn()),de);function On(t){let e=[],n=[],r=0,s=!1,i=!1;const o=new WeakSet,a={schedule:(c,l=!1,u=!1)=>{const f=u&&s,y=f?e:n;return l&&o.add(c),y.indexOf(c)===-1&&(y.push(c),f&&s&&(r=e.length)),c},cancel:c=>{const l=n.indexOf(c);l!==-1&&n.splice(l,1),o.delete(c)},process:c=>{if(s){i=!0;return}if(s=!0,[e,n]=[n,e],n.length=0,r=e.length,r)for(let l=0;l<r;l++){const u=e[l];u(c),o.has(u)&&(a.schedule(u),t())}s=!1,i&&(i=!1,a.process(c))}};return a}const vn=40;let Tt=!0,$=!1,St=!1;const P={delta:0,timestamp:0},H=["read","update","preRender","render","postRender"],it=H.reduce((t,e)=>(t[e]=On(()=>$=!0),t),{}),Mt=H.reduce((t,e)=>{const n=it[e];return t[e]=(r,s=!1,i=!1)=>($||Sn(),n.schedule(r,s,i)),t},{}),xn=H.reduce((t,e)=>(t[e]=it[e].cancel,t),{});H.reduce((t,e)=>(t[e]=()=>it[e].process(P),t),{});const Tn=t=>it[t].process(P),ye=t=>{$=!1,P.delta=Tt?de:Math.max(Math.min(t-P.timestamp,vn),1),P.timestamp=t,St=!0,H.forEach(Tn),St=!1,$&&(Tt=!1,me(ye))},Sn=()=>{$=!0,Tt=!0,St||me(ye)},be=()=>P;function he(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(t);s<r.length;s++)e.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(t,r[s])&&(n[r[s]]=t[r[s]]);return n}var Mn=function(){},Zt=function(){};const Et=(t,e,n)=>Math.min(Math.max(n,t),e),mt=.001,En=.01,Yt=10,An=.05,jn=1;function Vn({duration:t=800,bounce:e=.25,velocity:n=0,mass:r=1}){let s,i;Mn(t<=Yt*1e3);let o=1-e;o=Et(An,jn,o),t=Et(En,Yt,t/1e3),o<1?(s=l=>{const u=l*o,f=u*t,y=u-n,h=At(l,o),d=Math.exp(-f);return mt-y/h*d},i=l=>{const f=l*o*t,y=f*n+n,h=Math.pow(o,2)*Math.pow(l,2)*t,d=Math.exp(-f),S=At(Math.pow(l,2),o);return(-s(l)+mt>0?-1:1)*((y-h)*d)/S}):(s=l=>{const u=Math.exp(-l*t),f=(l-n)*t+1;return-mt+u*f},i=l=>{const u=Math.exp(-l*t),f=(n-l)*(t*t);return u*f});const a=5/t,c=Fn(s,i,a);if(t=t*1e3,isNaN(c))return{stiffness:100,damping:10,duration:t};{const l=Math.pow(c,2)*r;return{stiffness:l,damping:o*2*Math.sqrt(r*l),duration:t}}}const Rn=12;function Fn(t,e,n){let r=n;for(let s=1;s<Rn;s++)r=r-t(r)/e(r);return r}function At(t,e){return t*Math.sqrt(1-e*e)}const Dn=["duration","bounce"],Cn=["stiffness","damping","mass"];function Gt(t,e){return e.some(n=>t[n]!==void 0)}function _n(t){let e=Object.assign({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},t);if(!Gt(t,Cn)&&Gt(t,Dn)){const n=Vn(t);e=Object.assign(Object.assign(Object.assign({},e),n),{velocity:0,mass:1}),e.isResolvedFromDuration=!0}return e}function Ct(t){var{from:e=0,to:n=1,restSpeed:r=2,restDelta:s}=t,i=he(t,["from","to","restSpeed","restDelta"]);const o={done:!1,value:e};let{stiffness:a,damping:c,mass:l,velocity:u,duration:f,isResolvedFromDuration:y}=_n(i),h=Qt,d=Qt;function S(){const T=u?-(u/1e3):0,x=n-e,g=c/(2*Math.sqrt(a*l)),m=Math.sqrt(a/l)/1e3;if(s===void 0&&(s=Math.min(Math.abs(n-e)/100,.4)),g<1){const b=At(m,g);h=O=>{const M=Math.exp(-g*m*O);return n-M*((T+g*m*x)/b*Math.sin(b*O)+x*Math.cos(b*O))},d=O=>{const M=Math.exp(-g*m*O);return g*m*M*(Math.sin(b*O)*(T+g*m*x)/b+x*Math.cos(b*O))-M*(Math.cos(b*O)*(T+g*m*x)-b*x*Math.sin(b*O))}}else if(g===1)h=b=>n-Math.exp(-m*b)*(x+(T+m*x)*b);else{const b=m*Math.sqrt(g*g-1);h=O=>{const M=Math.exp(-g*m*O),R=Math.min(b*O,300);return n-M*((T+g*m*x)*Math.sinh(R)+b*x*Math.cosh(R))/b}}}return S(),{next:T=>{const x=h(T);if(y)o.done=T>=f;else{const g=d(T)*1e3,m=Math.abs(g)<=r,b=Math.abs(n-x)<=s;o.done=m&&b}return o.value=o.done?n:x,o},flipTarget:()=>{u=-u,[e,n]=[n,e],S()}}}Ct.needsInterpolation=(t,e)=>typeof t=="string"||typeof e=="string";const Qt=t=>0,ge=(t,e,n)=>{const r=e-t;return r===0?1:(n-t)/r},_t=(t,e,n)=>-n*t+n*e+t,Oe=(t,e)=>n=>Math.max(Math.min(n,e),t),k=t=>t%1?Number(t.toFixed(5)):t,q=/(-)?([\d]*\.?[\d])+/g,jt=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,wn=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function K(t){return typeof t=="string"}const W={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},U=Object.assign(Object.assign({},W),{transform:Oe(0,1)}),Q=Object.assign(Object.assign({},W),{default:1}),wt=t=>({test:e=>K(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),_=wt("deg"),z=wt("%"),p=wt("px"),Xt=Object.assign(Object.assign({},z),{parse:t=>z.parse(t)/100,transform:t=>z.transform(t*100)}),It=(t,e)=>n=>!!(K(n)&&wn.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),ve=(t,e,n)=>r=>{if(!K(r))return r;const[s,i,o,a]=r.match(q);return{[t]:parseFloat(s),[e]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},I={test:It("hsl","hue"),parse:ve("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:r=1})=>"hsla("+Math.round(t)+", "+z.transform(k(e))+", "+z.transform(k(n))+", "+k(U.transform(r))+")"},In=Oe(0,255),yt=Object.assign(Object.assign({},W),{transform:t=>Math.round(In(t))}),C={test:It("rgb","red"),parse:ve("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:r=1})=>"rgba("+yt.transform(t)+", "+yt.transform(e)+", "+yt.transform(n)+", "+k(U.transform(r))+")"};function Bn(t){let e="",n="",r="",s="";return t.length>5?(e=t.substr(1,2),n=t.substr(3,2),r=t.substr(5,2),s=t.substr(7,2)):(e=t.substr(1,1),n=t.substr(2,1),r=t.substr(3,1),s=t.substr(4,1),e+=e,n+=n,r+=r,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Vt={test:It("#"),parse:Bn,transform:C.transform},E={test:t=>C.test(t)||Vt.test(t)||I.test(t),parse:t=>C.test(t)?C.parse(t):I.test(t)?I.parse(t):Vt.parse(t),transform:t=>K(t)?t:t.hasOwnProperty("red")?C.transform(t):I.transform(t)},xe="${c}",Te="${n}";function Ln(t){var e,n,r,s;return isNaN(t)&&K(t)&&((n=(e=t.match(q))===null||e===void 0?void 0:e.length)!==null&&n!==void 0?n:0)+((s=(r=t.match(jt))===null||r===void 0?void 0:r.length)!==null&&s!==void 0?s:0)>0}function Se(t){typeof t=="number"&&(t=`${t}`);const e=[];let n=0;const r=t.match(jt);r&&(n=r.length,t=t.replace(jt,xe),e.push(...r.map(E.parse)));const s=t.match(q);return s&&(t=t.replace(q,Te),e.push(...s.map(W.parse))),{values:e,numColors:n,tokenised:t}}function Me(t){return Se(t).values}function Ee(t){const{values:e,numColors:n,tokenised:r}=Se(t),s=e.length;return i=>{let o=r;for(let a=0;a<s;a++)o=o.replace(a<n?xe:Te,a<n?E.transform(i[a]):k(i[a]));return o}}const Pn=t=>typeof t=="number"?0:t;function Nn(t){const e=Me(t);return Ee(t)(e.map(Pn))}const Z={test:Ln,parse:Me,createTransformer:Ee,getAnimatableNone:Nn},kn=new Set(["brightness","contrast","saturate","opacity"]);function Un(t){let[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[r]=n.match(q)||[];if(!r)return t;const s=n.replace(r,"");let i=kn.has(e)?1:0;return r!==n&&(i*=100),e+"("+i+s+")"}const zn=/([a-z-]*)\(.*?\)/g,Rt=Object.assign(Object.assign({},Z),{getAnimatableNone:t=>{const e=t.match(zn);return e?e.map(Un).join(" "):t}});function bt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Jt({hue:t,saturation:e,lightness:n,alpha:r}){t/=360,e/=100,n/=100;let s=0,i=0,o=0;if(!e)s=i=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;s=bt(c,a,t+1/3),i=bt(c,a,t),o=bt(c,a,t-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}const $n=(t,e,n)=>{const r=t*t,s=e*e;return Math.sqrt(Math.max(0,n*(s-r)+r))},qn=[Vt,C,I],te=t=>qn.find(e=>e.test(t)),Ae=(t,e)=>{let n=te(t),r=te(e),s=n.parse(t),i=r.parse(e);n===I&&(s=Jt(s),n=C),r===I&&(i=Jt(i),r=C);const o=Object.assign({},s);return a=>{for(const c in o)c!=="alpha"&&(o[c]=$n(s[c],i[c],a));return o.alpha=_t(s.alpha,i.alpha,a),n.transform(o)}},Hn=t=>typeof t=="number",Kn=(t,e)=>n=>e(t(n)),je=(...t)=>t.reduce(Kn);function Ve(t,e){return Hn(t)?n=>_t(t,e,n):E.test(t)?Ae(t,e):Fe(t,e)}const Re=(t,e)=>{const n=[...t],r=n.length,s=t.map((i,o)=>Ve(i,e[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}},Wn=(t,e)=>{const n=Object.assign(Object.assign({},t),e),r={};for(const s in n)t[s]!==void 0&&e[s]!==void 0&&(r[s]=Ve(t[s],e[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}};function ee(t){const e=Z.parse(t),n=e.length;let r=0,s=0,i=0;for(let o=0;o<n;o++)r||typeof e[o]=="number"?r++:e[o].hue!==void 0?i++:s++;return{parsed:e,numNumbers:r,numRGB:s,numHSL:i}}const Fe=(t,e)=>{const n=Z.createTransformer(e),r=ee(t),s=ee(e);return r.numHSL===s.numHSL&&r.numRGB===s.numRGB&&r.numNumbers>=s.numNumbers?je(Re(r.parsed,s.parsed),n):o=>`${o>0?e:t}`},Zn=(t,e)=>n=>_t(t,e,n);function Yn(t){if(typeof t=="number")return Zn;if(typeof t=="string")return E.test(t)?Ae:Fe;if(Array.isArray(t))return Re;if(typeof t=="object")return Wn}function Gn(t,e,n){const r=[],s=n||Yn(t[0]),i=t.length-1;for(let o=0;o<i;o++){let a=s(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]:e;a=je(c,a)}r.push(a)}return r}function Qn([t,e],[n]){return r=>n(ge(t,e,r))}function Xn(t,e){const n=t.length,r=n-1;return s=>{let i=0,o=!1;if(s<=t[0]?o=!0:s>=t[r]&&(i=r-1,o=!0),!o){let c=1;for(;c<n&&!(t[c]>s||c===r);c++);i=c-1}const a=ge(t[i],t[i+1],s);return e[i](a)}}function De(t,e,{clamp:n=!0,ease:r,mixer:s}={}){const i=t.length;Zt(i===e.length),Zt(!r||!Array.isArray(r)||r.length===i-1),t[0]>t[i-1]&&(t=[].concat(t),e=[].concat(e),t.reverse(),e.reverse());const o=Gn(e,r,s),a=i===2?Qn(t,o):Xn(t,o);return n?c=>a(Et(t[0],t[i-1],c)):a}const at=t=>e=>1-t(1-e),Bt=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Jn=t=>e=>Math.pow(e,t),Ce=t=>e=>e*e*((t+1)*e-t),tr=t=>{const e=Ce(t);return n=>(n*=2)<1?.5*e(n):.5*(2-Math.pow(2,-10*(n-1)))},_e=1.525,er=4/11,nr=8/11,rr=9/10,we=t=>t,Lt=Jn(2),sr=at(Lt),Ie=Bt(Lt),Be=t=>1-Math.sin(Math.acos(t)),Le=at(Be),or=Bt(Le),Pt=Ce(_e),ir=at(Pt),ar=Bt(Pt),cr=tr(_e),lr=4356/361,ur=35442/1805,fr=16061/1805,nt=t=>{if(t===1||t===0)return t;const e=t*t;return t<er?7.5625*e:t<nr?9.075*e-9.9*t+3.4:t<rr?lr*e-ur*t+fr:10.8*t*t-20.52*t+10.72},pr=at(nt),dr=t=>t<.5?.5*(1-nt(1-t*2)):.5*nt(t*2-1)+.5;function mr(t,e){return t.map(()=>e||Ie).splice(0,t.length-1)}function yr(t){const e=t.length;return t.map((n,r)=>r!==0?r/(e-1):0)}function br(t,e){return t.map(n=>n*e)}function J({from:t=0,to:e=1,ease:n,offset:r,duration:s=300}){const i={done:!1,value:t},o=Array.isArray(e)?e:[t,e],a=br(r&&r.length===o.length?r:yr(o),s);function c(){return De(a,o,{ease:Array.isArray(n)?n:mr(o,n)})}let l=c();return{next:u=>(i.value=l(u),i.done=u>=s,i),flipTarget:()=>{o.reverse(),l=c()}}}function hr({velocity:t=0,from:e=0,power:n=.8,timeConstant:r=350,restDelta:s=.5,modifyTarget:i}){const o={done:!1,value:e};let a=n*t;const c=e+a,l=i===void 0?c:i(c);return l!==c&&(a=l-e),{next:u=>{const f=-a*Math.exp(-u/r);return o.done=!(f>s||f<-s),o.value=o.done?l:l+f,o},flipTarget:()=>{}}}const ne={keyframes:J,spring:Ct,decay:hr};function gr(t){if(Array.isArray(t.to))return J;if(ne[t.type])return ne[t.type];const e=new Set(Object.keys(t));return e.has("ease")||e.has("duration")&&!e.has("dampingRatio")?J:e.has("dampingRatio")||e.has("stiffness")||e.has("mass")||e.has("damping")||e.has("restSpeed")||e.has("restDelta")?Ct:J}function Pe(t,e,n=0){return t-e-n}function Or(t,e,n=0,r=!0){return r?Pe(e+-t,e,n):e-(t-e)+n}function vr(t,e,n,r){return r?t>=e+n:t<=-n}const xr=t=>{const e=({delta:n})=>t(n);return{start:()=>Mt.update(e,!0),stop:()=>xn.update(e)}};function Nt(t){var e,n,{from:r,autoplay:s=!0,driver:i=xr,elapsed:o=0,repeat:a=0,repeatType:c="loop",repeatDelay:l=0,onPlay:u,onStop:f,onComplete:y,onRepeat:h,onUpdate:d}=t,S=he(t,["from","autoplay","driver","elapsed","repeat","repeatType","repeatDelay","onPlay","onStop","onComplete","onRepeat","onUpdate"]);let{to:T}=S,x,g=0,m=S.duration,b,O=!1,M=!0,R;const Y=gr(S);!((n=(e=Y).needsInterpolation)===null||n===void 0)&&n.call(e,r,T)&&(R=De([0,100],[r,T],{clamp:!1}),r=0,T=100);const D=Y(Object.assign(Object.assign({},S),{from:r,to:T}));function nn(){g++,c==="reverse"?(M=g%2===0,o=Or(o,m,l,M)):(o=Pe(o,m,l),c==="mirror"&&D.flipTarget()),O=!1,h&&h()}function rn(){x.stop(),y&&y()}function sn(lt){if(M||(lt=-lt),o+=lt,!O){const Ht=D.next(Math.max(0,o));b=Ht.value,R&&(b=R(b)),O=M?Ht.done:o<=0}d==null||d(b),O&&(g===0&&(m!=null||(m=o)),g<a?vr(o,m,l,M)&&nn():rn())}function on(){u==null||u(),x=i(sn),x.start()}return s&&on(),{stop:()=>{f==null||f(),x.stop()}}}function Ne(t,e){return e?t*(1e3/e):0}function Tr({from:t=0,velocity:e=0,min:n,max:r,power:s=.8,timeConstant:i=750,bounceStiffness:o=500,bounceDamping:a=10,restDelta:c=1,modifyTarget:l,driver:u,onUpdate:f,onComplete:y,onStop:h}){let d;function S(m){return n!==void 0&&m<n||r!==void 0&&m>r}function T(m){return n===void 0?r:r===void 0||Math.abs(n-m)<Math.abs(r-m)?n:r}function x(m){d==null||d.stop(),d=Nt(Object.assign(Object.assign({},m),{driver:u,onUpdate:b=>{var O;f==null||f(b),(O=m.onUpdate)===null||O===void 0||O.call(m,b)},onComplete:y,onStop:h}))}function g(m){x(Object.assign({type:"spring",stiffness:o,damping:a,restDelta:c},m))}if(S(t))g({from:t,velocity:e,to:T(t)});else{let m=s*e+t;typeof l!="undefined"&&(m=l(m));const b=T(m),O=b===n?-1:1;let M,R;const Y=D=>{M=R,R=D,e=Ne(D-M,be().delta),(O===1&&D>b||O===-1&&D<b)&&g({from:D,to:b,velocity:e})};x({type:"decay",from:t,velocity:e,timeConstant:i,power:s,restDelta:c,modifyTarget:l,onUpdate:S(m)?Y:void 0})}return{stop:()=>d==null?void 0:d.stop()}}const ke=(t,e)=>1-3*e+3*t,Ue=(t,e)=>3*e-6*t,ze=t=>3*t,rt=(t,e,n)=>((ke(e,n)*t+Ue(e,n))*t+ze(e))*t,$e=(t,e,n)=>3*ke(e,n)*t*t+2*Ue(e,n)*t+ze(e),Sr=1e-7,Mr=10;function Er(t,e,n,r,s){let i,o,a=0;do o=e+(n-e)/2,i=rt(o,r,s)-t,i>0?n=o:e=o;while(Math.abs(i)>Sr&&++a<Mr);return o}const Ar=8,jr=.001;function Vr(t,e,n,r){for(let s=0;s<Ar;++s){const i=$e(e,n,r);if(i===0)return e;const o=rt(e,n,r)-t;e-=o/i}return e}const tt=11,X=1/(tt-1);function Rr(t,e,n,r){if(t===e&&n===r)return we;const s=new Float32Array(tt);for(let o=0;o<tt;++o)s[o]=rt(o*X,t,n);function i(o){let a=0,c=1;const l=tt-1;for(;c!==l&&s[c]<=o;++c)a+=X;--c;const u=(o-s[c])/(s[c+1]-s[c]),f=a+u*X,y=$e(f,t,n);return y>=jr?Vr(o,f,t,n):y===0?f:Er(o,a,a+X,t,n)}return o=>o===0||o===1?o:rt(i(o),e,r)}const et={};class Fr{constructor(){A(this,"subscriptions",new Set)}add(e){return this.subscriptions.add(e),()=>this.subscriptions.delete(e)}notify(e,n,r){if(this.subscriptions.size)for(const s of this.subscriptions)s(e,n,r)}clear(){this.subscriptions.clear()}}function re(t){return!Number.isNaN(Number.parseFloat(t))}class Dr{constructor(e){A(this,"current");A(this,"prev");A(this,"timeDelta",0);A(this,"lastUpdated",0);A(this,"updateSubscribers",new Fr);A(this,"stopAnimation");A(this,"canTrackVelocity",!1);A(this,"updateAndNotify",e=>{this.prev=this.current,this.current=e;const{delta:n,timestamp:r}=be();this.lastUpdated!==r&&(this.timeDelta=n,this.lastUpdated=r),Mt.postRender(this.scheduleVelocityCheck),this.updateSubscribers.notify(this.current)});A(this,"scheduleVelocityCheck",()=>Mt.postRender(this.velocityCheck));A(this,"velocityCheck",({timestamp:e})=>{this.canTrackVelocity||(this.canTrackVelocity=re(this.current)),e!==this.lastUpdated&&(this.prev=this.current)});this.prev=this.current=e,this.canTrackVelocity=re(this.current)}onChange(e){return this.updateSubscribers.add(e)}clearListeners(){this.updateSubscribers.clear()}set(e){this.updateAndNotify(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Ne(Number.parseFloat(this.current)-Number.parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(n=>{const{stop:r}=e(n);this.stopAnimation=r}).then(()=>this.clearAnimation())}stop(){this.stopAnimation&&this.stopAnimation(),this.clearAnimation()}isAnimating(){return!!this.stopAnimation}clearAnimation(){this.stopAnimation=null}destroy(){this.updateSubscribers.clear(),this.stop()}}function Cr(t){return new Dr(t)}const{isArray:_r}=Array;function qe(){const t=F({}),e=r=>{const s=i=>{t.value[i]&&(t.value[i].stop(),t.value[i].destroy(),delete t.value[i])};r?_r(r)?r.forEach(s):s(r):Object.keys(t.value).forEach(s)},n=(r,s,i)=>{if(t.value[r])return t.value[r];const o=Cr(s);return o.onChange(a=>i[r]=a),t.value[r]=o,o};return fn(e),{motionValues:t,get:n,stop:e}}function wr(t){return Array.isArray(t)}function w(){return{type:"spring",stiffness:500,damping:25,restDelta:.5,restSpeed:10}}function ht(t){return{type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restDelta:.01,restSpeed:10}}function Ir(t){return{type:"spring",stiffness:550,damping:t===0?100:30,restDelta:.01,restSpeed:10}}function gt(){return{type:"keyframes",ease:"linear",duration:300}}function Br(t){return{type:"keyframes",duration:800,values:t}}const se={default:Ir,x:w,y:w,z:w,rotate:w,rotateX:w,rotateY:w,rotateZ:w,scaleX:ht,scaleY:ht,scale:ht,backgroundColor:gt,color:gt,opacity:gt};function kt(t,e){let n;return wr(e)?n=Br:n=se[t]||se.default,v({to:e},n(e))}const oe=L(v({},W),{transform:Math.round}),He={color:E,backgroundColor:E,outlineColor:E,fill:E,stroke:E,borderColor:E,borderTopColor:E,borderRightColor:E,borderBottomColor:E,borderLeftColor:E,borderWidth:p,borderTopWidth:p,borderRightWidth:p,borderBottomWidth:p,borderLeftWidth:p,borderRadius:p,radius:p,borderTopLeftRadius:p,borderTopRightRadius:p,borderBottomRightRadius:p,borderBottomLeftRadius:p,width:p,maxWidth:p,height:p,maxHeight:p,size:p,top:p,right:p,bottom:p,left:p,padding:p,paddingTop:p,paddingRight:p,paddingBottom:p,paddingLeft:p,margin:p,marginTop:p,marginRight:p,marginBottom:p,marginLeft:p,rotate:_,rotateX:_,rotateY:_,rotateZ:_,scale:Q,scaleX:Q,scaleY:Q,scaleZ:Q,skew:_,skewX:_,skewY:_,distance:p,translateX:p,translateY:p,translateZ:p,x:p,y:p,z:p,perspective:p,transformPerspective:p,opacity:U,originX:Xt,originY:Xt,originZ:p,zIndex:oe,filter:Rt,WebkitFilter:Rt,fillOpacity:U,strokeOpacity:U,numOctaves:oe},Ut=t=>He[t];function Ft(t,e){return e&&typeof t=="number"&&e.transform?e.transform(t):t}function Lr(t,e){let n=Ut(t);return n!==Rt&&(n=Z),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Pr={linear:we,easeIn:Lt,easeInOut:Ie,easeOut:sr,circIn:Be,circInOut:or,circOut:Le,backIn:Pt,backInOut:ar,backOut:ir,anticipate:cr,bounceIn:pr,bounceInOut:dr,bounceOut:nt};function ie(t){if(Array.isArray(t)){const[e,n,r,s]=t;return Rr(e,n,r,s)}else if(typeof t=="string")return Pr[t];return t}function Nr(t){return Array.isArray(t)&&typeof t[0]!="number"}function ae(t,e){return t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&Z.test(e)&&!e.startsWith("url("))}function kr(t){return Array.isArray(t.to)&&t.to[0]===null&&(t.to=[...t.to],t.to[0]=t.from),t}function Ur(s){var i=s,{ease:t,times:e,delay:n}=i,r=ft(i,["ease","times","delay"]);const o=v({},r);return e&&(o.offset=e),t&&(o.ease=Nr(t)?t.map(ie):ie(t)),n&&(o.elapsed=-n),o}function zr(t,e,n){return Array.isArray(e.to)&&(t.duration||(t.duration=800)),kr(e),$r(t)||(t=v(v({},t),kt(n,e.to))),v(v({},e),Ur(t))}function $r(o){var a=o,{delay:t,repeat:e,repeatType:n,repeatDelay:r,from:s}=a,i=ft(a,["delay","repeat","repeatType","repeatDelay","from"]);return!!Object.keys(i).length}function qr(t,e){return t[e]||t.default||t}function Hr(t,e,n,r,s){const i=qr(r,t);let o=i.from===null||i.from===void 0?e.get():i.from;const a=ae(t,n);o==="none"&&a&&typeof n=="string"&&(o=Lr(t,n));const c=ae(t,o);function l(f){const y={from:o,to:n,velocity:r.velocity?r.velocity:e.getVelocity(),onUpdate:h=>e.set(h)};return i.type==="inertia"||i.type==="decay"?Tr(v(v({},y),i)):Nt(L(v({},zr(i,y,t)),{onUpdate:h=>{y.onUpdate(h),i.onUpdate&&i.onUpdate(h)},onComplete:()=>{s&&s(),f&&f()}}))}function u(f){return e.set(n),s&&s(),f&&f(),{stop:()=>{}}}return!c||!a||i.type===!1?u:l}function Kr(){const{motionValues:t,stop:e,get:n}=qe();return{motionValues:t,stop:e,push:(s,i,o,a={},c)=>{const l=o[s],u=n(s,l,o);if(a&&a.immediate){u.set(i);return}const f=Hr(s,u,i,a,c);u.start(f)}}}function Wr(t,e={},{motionValues:n,push:r,stop:s}=Kr()){const i=N(e),o=F(!1);V(n,f=>{o.value=Object.values(f).filter(y=>y.isAnimating()).length>0},{immediate:!0,deep:!0});const a=f=>{if(!i||!i[f])throw new Error(`The variant ${f} does not exist.`);return i[f]},c=f=>{typeof f=="string"&&(f=a(f));const y=Object.entries(f).map(([d,S])=>{if(d!=="transition")return new Promise(T=>r(d,S,t,f.transition||kt(d,f[d]),T))}).filter(Boolean);function h(){return pt(this,null,function*(){var d,S;yield Promise.all(y),(S=(d=f.transition)==null?void 0:d.onComplete)==null||S.call(d)})}return Promise.all([h()])};return{isAnimating:o,apply:c,set:f=>{const y=vt(f)?f:a(f);Object.entries(y).forEach(([h,d])=>{h!=="transition"&&r(h,d,t,{immediate:!0})})},leave:f=>pt(this,null,function*(){let y;if(i&&(i.leave&&(y=i.leave),!i.leave&&i.initial&&(y=i.initial)),!y){f();return}yield c(y),f()}),stop:s}}const zt=typeof window!="undefined",Zr=()=>zt&&(window.onpointerdown===null||{VITE_GLOB_API_URL:"/api",BASE_URL:"./",MODE:"production",DEV:!1,PROD:!0,SSR:!1}.TEST),Yr=()=>zt&&(window.ontouchstart===null||{VITE_GLOB_API_URL:"/api",BASE_URL:"./",MODE:"production",DEV:!1,PROD:!0,SSR:!1}.TEST),Gr=()=>zt&&(window.onmousedown===null||{VITE_GLOB_API_URL:"/api",BASE_URL:"./",MODE:"production",DEV:!1,PROD:!0,SSR:!1}.TEST);function Qr({target:t,state:e,variants:n,apply:r}){const s=N(n),i=F(!1),o=F(!1),a=F(!1),c=B(()=>{let u=[...Object.keys(e.value||{})];return s&&(s.hovered&&(u=[...u,...Object.keys(s.hovered)]),s.tapped&&(u=[...u,...Object.keys(s.tapped)]),s.focused&&(u=[...u,...Object.keys(s.focused)])),u}),l=B(()=>{const u={};Object.assign(u,e.value),i.value&&s.hovered&&Object.assign(u,s.hovered),o.value&&s.tapped&&Object.assign(u,s.tapped),a.value&&s.focused&&Object.assign(u,s.focused);for(const f in u)c.value.includes(f)||delete u[f];return u});s.hovered&&(j(t,"mouseenter",()=>i.value=!0),j(t,"mouseleave",()=>{i.value=!1,o.value=!1})),s.tapped&&(Gr()&&(j(t,"mousedown",()=>o.value=!0),j(t,"mouseup",()=>o.value=!1)),Zr()&&(j(t,"pointerdown",()=>o.value=!0),j(t,"pointerup",()=>o.value=!1)),Yr()&&(j(t,"touchstart",()=>o.value=!0),j(t,"touchend",()=>o.value=!1))),s.focused&&(j(t,"focus",()=>a.value=!0),j(t,"blur",()=>a.value=!1)),V([i,o,a],()=>{r(l.value)})}function Xr({set:t,target:e,variants:n,variant:r}){const s=N(n);V(()=>e,()=>{s&&(s.initial&&(t("initial"),r.value="initial"),s.enter&&(r.value="enter"))},{immediate:!0,flush:"pre"})}function Jr({state:t,apply:e}){V(t,n=>{n&&e(n)},{immediate:!0})}function Ke({target:t,variants:e,variant:n}){const r=N(e);r&&(r.visible||r.visibleOnce)&&pn(t,([{isIntersecting:s}])=>{r.visible?s?n.value="visible":n.value="initial":r.visibleOnce&&(s&&n.value!=="visibleOnce"?n.value="visibleOnce":n.value||(n.value="initial"))})}function ts(t,e={syncVariants:!0,lifeCycleHooks:!0,visibilityHooks:!0,eventListeners:!0}){e.lifeCycleHooks&&Xr(t),e.syncVariants&&Jr(t),e.visibilityHooks&&Ke(t),e.eventListeners&&Qr(t)}function We(t={}){const e=ot(v({},t)),n=F({});return V(e,()=>{const r={};for(const[s,i]of Object.entries(e)){const o=Ut(s),a=Ft(i,o);r[s]=a}n.value=r},{immediate:!0,deep:!0}),{state:e,style:n}}function $t(t,e){V(()=>dn(t),n=>{n&&e(n)},{immediate:!0})}const es={x:"translateX",y:"translateY",z:"translateZ"};function Ze(t={},e=!0){const n=ot(v({},t)),r=F("");return V(n,s=>{let i="",o=!1;if(e&&(s.x||s.y||s.z)){const a=[s.x||0,s.y||0,s.z||0].map(c=>Ft(c,p)).join(",");i+=`translate3d(${a}) `,o=!0}for(const[a,c]of Object.entries(s)){if(e&&(a==="x"||a==="y"||a==="z"))continue;const l=Ut(a),u=Ft(c,l);i+=`${es[a]||a}(${u}) `}e&&!o&&(i+="translateZ(0px) "),r.value=i.trim()},{immediate:!0,deep:!0}),{state:n,transform:r}}const ns=["","X","Y","Z"],rs=["perspective","translate","scale","rotate","skew"],Ye=["transformPerspective","x","y","z"];rs.forEach(t=>{ns.forEach(e=>{const n=t+e;Ye.push(n)})});const ss=new Set(Ye);function qt(t){return ss.has(t)}const os=new Set(["originX","originY","originZ"]);function Ge(t){return os.has(t)}function is(t){const e={},n={};return Object.entries(t).forEach(([r,s])=>{qt(r)||Ge(r)?e[r]=s:n[r]=s}),{transform:e,style:n}}function ct(t){const{transform:e,style:n}=is(t),{transform:r}=Ze(e),{style:s}=We(n);return r.value&&(s.value.transform=r.value),s.value}function as(t,e){let n,r;const{state:s,style:i}=We();return $t(t,o=>{r=o;for(const a of Object.keys(He))o.style[a]===null||o.style[a]===""||qt(a)||Ge(a)||(s[a]=o.style[a]);n&&Object.entries(n).forEach(([a,c])=>o.style[a]=c),e&&e(s)}),V(i,o=>{if(!r){n=o;return}for(const a in o)r.style[a]=o[a]},{immediate:!0}),{style:s}}function cs(t){const e=t.trim().split(/\) |\)/);if(e.length===1)return{};const n=r=>r.endsWith("px")||r.endsWith("deg")?Number.parseFloat(r):Number.isNaN(Number(r))?Number(r):r;return e.reduce((r,s)=>{if(!s)return r;const[i,o]=s.split("("),c=o.split(",").map(u=>n(u.endsWith(")")?u.replace(")",""):u.trim())),l=c.length===1?c[0]:c;return L(v({},r),{[i]:l})},{})}function ls(t,e){Object.entries(cs(e)).forEach(([n,r])=>{const s=["x","y","z"];if(n==="translate3d"){if(r===0){s.forEach(i=>t[i]=0);return}r.forEach((i,o)=>t[s[o]]=i);return}if(r=Number.parseFloat(`${r}`),n==="translateX"){t.x=r;return}if(n==="translateY"){t.y=r;return}if(n==="translateZ"){t.z=r;return}t[n]=r})}function us(t,e){let n,r;const{state:s,transform:i}=Ze();return $t(t,o=>{r=o,o.style.transform&&ls(s,o.style.transform),n&&(o.style.transform=n),e&&e(s)}),V(i,o=>{if(!r){n=o;return}r.style.transform=o},{immediate:!0}),{transform:s}}function fs(t){return Object.entries(t)}function ps(t,e){const n=ot({}),r=o=>Object.entries(o).forEach(([a,c])=>n[a]=c),{style:s}=as(t,r),{transform:i}=us(t,r);return V(n,o=>{fs(o).forEach(([a,c])=>{const l=qt(a)?i:s;l[a]&&l[a]===c||(l[a]=c)})},{immediate:!0,deep:!0}),$t(t,()=>e&&r(e)),{motionProperties:n,style:s,transform:i}}function ds(t={}){const e=N(t),n=F();return{state:B(()=>{if(n.value)return e[n.value]}),variant:n}}function Qe(t,e={},n){const{motionProperties:r}=ps(t),{variant:s,state:i}=ds(e),o=Wr(r,e),a=v({target:t,variant:s,variants:e,state:i,motionProperties:r},o);return ts(a,n),a}const Xe=["delay","duration"],ms=["initial","enter","leave","visible","visible-once","visibleOnce","hovered","tapped","focused",...Xe];function ys(t){return Xe.includes(t)}function bs(t,e){var r;const n=t.props?t.props:t.data&&t.data.attrs?t.data.attrs:{};if(n){n.variants&&vt(n.variants)&&(e.value=v(v({},e.value),n.variants));for(let s of ms)if(!(!n||!n[s])){if(ys(s)&&typeof n[s]=="number"){for(const i of["enter","visible","visibleOnce"]){const o=e.value[i];o!=null&&((r=o.transition)!=null||(o.transition={}),o.transition[s]=n[s])}continue}if(vt(n[s])){const i=n[s];s==="visible-once"&&(s="visibleOnce"),e.value[s]=i}}}}function Ot(t,e=!1){return{created:(s,i,o)=>{const a=i.value&&typeof i.value=="string"?i.value:o.key;a&&et[a]&&et[a].stop();const c=e?structuredClone(le(t)||{}):t||{},l=F(c);typeof i.value=="object"&&(l.value=i.value),bs(o,l);const f=Qe(s,l,{eventListeners:!0,lifeCycleHooks:!0,syncVariants:!0,visibilityHooks:!1});s.motionInstance=f,a&&(et[a]=f)},mounted:(s,i,o)=>{s.motionInstance&&Ke(s.motionInstance)},getSSRProps(s,i){let{initial:o}=s.value||i&&(i==null?void 0:i.props)||{};o=N(o);const a=pe({},(t==null?void 0:t.initial)||{},o||{});return!a||Object.keys(a).length===0?void 0:{style:ct(a)}}}}const hs={initial:{opacity:0},enter:{opacity:1}},gs={initial:{opacity:0},visible:{opacity:1}},Os={initial:{opacity:0},visibleOnce:{opacity:1}},vs={initial:{scale:0,opacity:0},enter:{scale:1,opacity:1}},xs={initial:{scale:0,opacity:0},visible:{scale:1,opacity:1}},Ts={initial:{scale:0,opacity:0},visibleOnce:{scale:1,opacity:1}},Ss={initial:{x:-100,rotate:90,opacity:0},enter:{x:0,rotate:0,opacity:1}},Ms={initial:{x:-100,rotate:90,opacity:0},visible:{x:0,rotate:0,opacity:1}},Es={initial:{x:-100,rotate:90,opacity:0},visibleOnce:{x:0,rotate:0,opacity:1}},As={initial:{x:100,rotate:-90,opacity:0},enter:{x:0,rotate:0,opacity:1}},js={initial:{x:100,rotate:-90,opacity:0},visible:{x:0,rotate:0,opacity:1}},Vs={initial:{x:100,rotate:-90,opacity:0},visibleOnce:{x:0,rotate:0,opacity:1}},Rs={initial:{y:-100,rotate:-90,opacity:0},enter:{y:0,rotate:0,opacity:1}},Fs={initial:{y:-100,rotate:-90,opacity:0},visible:{y:0,rotate:0,opacity:1}},Ds={initial:{y:-100,rotate:-90,opacity:0},visibleOnce:{y:0,rotate:0,opacity:1}},Cs={initial:{y:100,rotate:90,opacity:0},enter:{y:0,rotate:0,opacity:1}},_s={initial:{y:100,rotate:90,opacity:0},visible:{y:0,rotate:0,opacity:1}},ws={initial:{y:100,rotate:90,opacity:0},visibleOnce:{y:0,rotate:0,opacity:1}},Is={initial:{x:-100,opacity:0},enter:{x:0,opacity:1}},Bs={initial:{x:-100,opacity:0},visible:{x:0,opacity:1}},Ls={initial:{x:-100,opacity:0},visibleOnce:{x:0,opacity:1}},Ps={initial:{x:100,opacity:0},enter:{x:0,opacity:1}},Ns={initial:{x:100,opacity:0},visible:{x:0,opacity:1}},ks={initial:{x:100,opacity:0},visibleOnce:{x:0,opacity:1}},Us={initial:{y:-100,opacity:0},enter:{y:0,opacity:1}},zs={initial:{y:-100,opacity:0},visible:{y:0,opacity:1}},$s={initial:{y:-100,opacity:0},visibleOnce:{y:0,opacity:1}},qs={initial:{y:100,opacity:0},enter:{y:0,opacity:1}},Hs={initial:{y:100,opacity:0},visible:{y:0,opacity:1}},Ks={initial:{y:100,opacity:0},visibleOnce:{y:0,opacity:1}},st={__proto__:null,fade:hs,fadeVisible:gs,fadeVisibleOnce:Os,pop:vs,popVisible:xs,popVisibleOnce:Ts,rollBottom:Cs,rollLeft:Ss,rollRight:As,rollTop:Rs,rollVisibleBottom:_s,rollVisibleLeft:Ms,rollVisibleOnceBottom:ws,rollVisibleOnceLeft:Es,rollVisibleOnceRight:Vs,rollVisibleOnceTop:Ds,rollVisibleRight:js,rollVisibleTop:Fs,slideBottom:qs,slideLeft:Is,slideRight:Ps,slideTop:Us,slideVisibleBottom:Hs,slideVisibleLeft:Bs,slideVisibleOnceBottom:Ks,slideVisibleOnceLeft:Ls,slideVisibleOnceRight:ks,slideVisibleOnceTop:$s,slideVisibleRight:Ns,slideVisibleTop:zs};function Ws(t){const e="àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;",n="aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrsssssttuuuuuuuuuwxyyzzz------",r=new RegExp(e.split("").join("|"),"g");return t.toString().replace(/[A-Z]/g,s=>`-${s}`).toLowerCase().replace(/\s+/g,"-").replace(r,s=>n.charAt(e.indexOf(s))).replace(/&/g,"-and-").replace(/[^\w\-]+/g,"").replace(/-{2,}/g,"-").replace(/^-+/,"").replace(/-+$/,"")}const Je=Symbol(""),tn={preset:{type:String,required:!1},instance:{type:Object,required:!1},variants:{type:Object,required:!1},initial:{type:Object,required:!1},enter:{type:Object,required:!1},leave:{type:Object,required:!1},visible:{type:Object,required:!1},visibleOnce:{type:Object,required:!1},hovered:{type:Object,required:!1},tapped:{type:Object,required:!1},focused:{type:Object,required:!1},delay:{type:[Number,String],required:!1},duration:{type:[Number,String],required:!1}};function Zs(t){return Object.prototype.toString.call(t)==="[object Object]"}function Dt(t){if(Array.isArray(t))return t.map(Dt);if(Zs(t)){const e={};for(const n in t)e[n]=Dt(t[n]);return e}return t}function en(t){const e=ot({}),n=bn(Je,{}),r=B(()=>t.preset==null?{}:n!=null&&t.preset in n?structuredClone(le(n)[t.preset]):t.preset in st?structuredClone(st[t.preset]):{}),s=B(()=>({initial:t.initial,enter:t.enter,leave:t.leave,visible:t.visible,visibleOnce:t.visibleOnce,hovered:t.hovered,tapped:t.tapped,focused:t.focused}));function i(c,l){var u;for(const f of["delay","duration"]){if(l[f]==null)continue;const y=Number.parseInt(l[f]);for(const h of["enter","visible","visibleOnce"]){const d=c[h];d!=null&&((u=d.transition)!=null||(d.transition={}),d.transition[f]=y)}}return c}const o=B(()=>{const c=pe({},s.value,r.value,t.variants||{});return i(v({},c),t)});function a(c,l,u){var y,h,d;(y=c.props)!=null||(c.props={}),(d=(h=c.props).style)!=null||(h.style={}),c.props.style=v(v({},c.props.style),u);const f=i(Dt(o.value),c.props);return c.props.onVnodeMounted=({el:S})=>{e[l]=Qe(S,f)},c.props.onVnodeUpdated=({el:S})=>{const T=ct(e[l].state);for(const[x,g]of Object.entries(T))S.style[x]=g},c}return{motionConfig:o,setNodeInstance:a}}const Ys=ce({name:"Motion",props:L(v({},tn),{is:{type:[String,Object],default:"div"}}),setup(t){const e=ue(),{motionConfig:n,setNodeInstance:r}=en(t);return()=>{const s=ct(n.value.initial||{}),i=fe(t.is,void 0,e);return r(i,0,s),i}}}),Gs=ce({name:"MotionGroup",props:L(v({},tn),{is:{type:[String,Object],required:!1}}),setup(t){const e=ue(),{motionConfig:n,setNodeInstance:r}=en(t);return()=>{var o;const s=ct(n.value.initial||{}),i=((o=e.default)==null?void 0:o.call(e))||[];for(let a=0;a<i.length;a++){const c=i[a];c.type===yn&&Array.isArray(c.children)?c.children.forEach(function l(u,f){if(u!=null){if(Array.isArray(u)){l(u,f);return}typeof u=="object"&&r(u,f,s)}}):r(c,a,s)}return t.is?fe(t.is,void 0,i):i}}}),to={install(t,e){if(t.directive("motion",Ot()),!e||e&&!e.excludePresets)for(const n in st){const r=st[n];t.directive(`motion-${Ws(n)}`,Ot(r,!0))}if(e&&e.directives)for(const n in e.directives){const r=e.directives[n];r.initial,t.directive(`motion-${n}`,Ot(r,!0))}t.provide(Je,e==null?void 0:e.directives),t.component("Motion",Ys),t.component("MotionGroup",Gs)}};function eo(t){const e=t;return e.apply!==void 0&&typeof e.apply=="function"&&e.set!==void 0&&typeof e.set=="function"&&e.target!==void 0&&mn(e.target)}function no(){return et}function ro(t,e){const{stop:n,get:r}=qe();return{values:t,stop:n,set:s=>Promise.all(Object.entries(s).map(([i,o])=>{const a=r(i,t[i],t);return a.start(c=>{const l=v({type:"spring"},e||kt(i,o));return Nt(v({from:a.get(),to:o,velocity:a.getVelocity(),onUpdate:u=>a.set(u),onComplete:c},l))})}))}}function so(t={}){const e=un("(prefers-reduced-motion: reduce)",t);return B(()=>e.value)}export{Ys as MotionComponent,Ot as MotionDirective,Gs as MotionGroupComponent,to as MotionPlugin,hs as fade,gs as fadeVisible,Os as fadeVisibleOnce,eo as isMotionInstance,vs as pop,xs as popVisible,Ts as popVisibleOnce,We as reactiveStyle,Ze as reactiveTransform,Cs as rollBottom,Ss as rollLeft,As as rollRight,Rs as rollTop,_s as rollVisibleBottom,Ms as rollVisibleLeft,ws as rollVisibleOnceBottom,Es as rollVisibleOnceLeft,Vs as rollVisibleOnceRight,Ds as rollVisibleOnceTop,js as rollVisibleRight,Fs as rollVisibleTop,qs as slideBottom,Is as slideLeft,Ps as slideRight,Us as slideTop,Hs as slideVisibleBottom,Bs as slideVisibleLeft,Ks as slideVisibleOnceBottom,Ls as slideVisibleOnceLeft,ks as slideVisibleOnceRight,$s as slideVisibleOnceTop,Ns as slideVisibleRight,zs as slideVisibleTop,Ws as slugify,as as useElementStyle,us as useElementTransform,Qe as useMotion,Wr as useMotionControls,ts as useMotionFeatures,ps as useMotionProperties,Kr as useMotionTransitions,ds as useMotionVariants,no as useMotions,so as useReducedMotion,ro as useSpring};
