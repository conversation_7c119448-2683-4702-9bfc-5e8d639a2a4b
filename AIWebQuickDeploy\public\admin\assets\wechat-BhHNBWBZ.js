
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,$ as l,P as t,r as p,a0 as r,b as u,Q as d,c as s,e as y,f as o,w as i,j as c,h,_ as n,g as m,Y as f,k as W}from"./index-BERX8Mlm.js";import{a as C}from"./config-BrbFL53_.js";const g=a({__name:"wechat",setup(a){const W=l({payWechatStatus:"",payWeChatMchId:"",payWeChatAppId:"",payWeChatSecret:"",payWeChatNotifyUrl:"",payWeChatPublicKey:"",payWeChatPrivateKey:""}),g=t((()=>{const e="1"===W.payWechatStatus;return{payWechatStatus:[{required:!0,trigger:"change",message:"请选择当前支付开启状态"}],payWeChatSecret:[{required:e,trigger:"blur",message:"请填写支付Secret秘钥"}],payWeChatMchId:[{required:e,trigger:"blur",message:"请填写商户号"}],payWeChatAppId:[{required:e,trigger:"blur",message:"请填写AppId"}],payWeChatNotifyUrl:[{required:e,trigger:"blur",message:"请填写支付通知地址"}],payWeChatPublicKey:[{required:e,trigger:"blur",message:"请填写支付公钥信息（cert.pem文件）"}],payWeChatPrivateKey:[{required:e,trigger:"blur",message:"请填写支付私钥地址（key.pem文件）"}]}})),_=p();async function b(){const e=await C.queryConfig({keys:["payWeChatSecret","payWeChatNotifyUrl","payWeChatAppId","payWechatStatus","payWeChatMchId","payWeChatPublicKey","payWeChatPrivateKey"]});Object.assign(W,e.data)}function v(){var e;null==(e=_.value)||e.validate((async e=>{if(e){try{await C.setConfig({settings:(a=W,Object.keys(a).map((e=>({configKey:e,configVal:a[e]}))))}),f.success("变更配置信息成功")}catch(l){}b()}else f.error("请填写完整信息");var a}))}return r((()=>W.payWechatStatus),(()=>{setTimeout((()=>{var e;null==(e=_.value)||e.validateField(["payWeChatSecret","payWeChatMchId","payWeChatAppId","payWeChatNotifyUrl","payWeChatPublicKey","payWeChatPrivateKey"])}),0)})),u((()=>{b()})),(a,l)=>{const t=n,p=c,r=e,u=d("el-switch"),f=d("el-form-item"),C=d("el-col"),b=d("el-row"),x=d("el-input"),V=d("el-form"),I=d("el-card");return y(),s("div",null,[o(r,null,{title:i((()=>l[7]||(l[7]=[m("div",{class:"flex items-center gap-4"},"微信支付设置",-1)]))),content:i((()=>l[8]||(l[8]=[m("div",{class:"text-sm/6"},[m("div",null," 同时开启多个支付，将以微信支付作为最高优先级，在pc端会调用 native 支付，在微信环境内将调用 Jsapi 支付。 "),m("div",null," 请确认微信支付已经申请了支付权限，支付通知地址为： https://您的域名/api/pay/notify。 ")],-1)]))),default:i((()=>[o(p,{outline:"",onClick:v},{default:i((()=>[o(t,{name:"i-ri:file-text-line"}),l[9]||(l[9]=h(" 保存设置 "))])),_:1})])),_:1}),o(I,{style:{margin:"20px"}},{default:i((()=>[o(V,{ref_key:"formRef",ref:_,rules:g.value,model:W,"label-width":"140px"},{default:i((()=>[o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"启用当前支付",prop:"payWechatStatus"},{default:i((()=>[o(u,{modelValue:W.payWechatStatus,"onUpdate:modelValue":l[0]||(l[0]=e=>W.payWechatStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"商户ID",prop:"payWeChatMchId"},{default:i((()=>[o(x,{modelValue:W.payWeChatMchId,"onUpdate:modelValue":l[1]||(l[1]=e=>W.payWeChatMchId=e),placeholder:"请填写商户ID",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"AppId",prop:"payWeChatAppId"},{default:i((()=>[o(x,{modelValue:W.payWeChatAppId,"onUpdate:modelValue":l[2]||(l[2]=e=>W.payWeChatAppId=e),placeholder:"请填写AppId",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"商户秘钥",prop:"payWeChatSecret"},{default:i((()=>[o(x,{modelValue:W.payWeChatSecret,"onUpdate:modelValue":l[3]||(l[3]=e=>W.payWeChatSecret=e),placeholder:"请填写Secret秘钥",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"支付通知地址",prop:"payWeChatNotifyUrl"},{default:i((()=>[o(x,{modelValue:W.payWeChatNotifyUrl,"onUpdate:modelValue":l[4]||(l[4]=e=>W.payWeChatNotifyUrl=e),placeholder:"请填写支付通知地址",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"公钥地址",prop:"payWeChatPublicKey"},{default:i((()=>[o(x,{modelValue:W.payWeChatPublicKey,"onUpdate:modelValue":l[5]||(l[5]=e=>W.payWeChatPublicKey=e),type:"textarea",rows:6,placeholder:"请填写支付公钥信息（cert.pem文件）",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(b,null,{default:i((()=>[o(C,{xs:24,md:20,lg:15,xl:12},{default:i((()=>[o(f,{label:"私钥地址",prop:"payWeChatPrivateKey"},{default:i((()=>[o(x,{modelValue:W.payWeChatPrivateKey,"onUpdate:modelValue":l[6]||(l[6]=e=>W.payWeChatPrivateKey=e),type:"textarea",rows:6,placeholder:"请填写支付私钥地址（key.pem文件）",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["rules","model"])])),_:1})])}}});"function"==typeof W&&W(g);export{g as default};
