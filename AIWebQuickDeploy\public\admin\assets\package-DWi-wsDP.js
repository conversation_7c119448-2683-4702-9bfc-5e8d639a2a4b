
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-mQp5T4Ar.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as a,r as t,$ as r,b as s,P as u,Q as d,a1 as o,c as n,e as i,f as p,w as m,j as c,h as f,_ as g,g as b,V as v,W as _,i as C,a2 as y,a5 as w,T as h,t as V,a4 as k,a6 as x,Y as j,a9 as M,k as q}from"./index-BERX8Mlm.js";import{A as z}from"./app-Cak_t3ob.js";import{A as U}from"./package-BcYUNEpY.js";import{h as S,I as A}from"./index-gPQwgooA.js";import{u as P}from"./utcFormatTime-BtFjiA-p.js";const F={key:0,class:"text-gray-400"},Y={class:"category-selector"},I={class:"selected-categories mb-2 min-h-[36px] flex items-center flex-wrap"},$={key:0,class:"text-gray-400 text-sm py-1"},D={class:"category-options p-2 border rounded-md max-h-40 overflow-y-auto"},T={class:"flex flex-wrap"},O={class:"mr-5 flex justify-end"},R=a({__name:"package",setup(a){const q=t(),R=t(0),Q=t(!1),W=t(!1),B=r({name:"",page:1,size:10,status:""}),E=t(),G=t(0);t(0);const H=r({name:null,des:null,coverImg:null,price:null,order:null,status:0,weight:null,days:null,model3Count:null,model4Count:null,drawMjCount:null,appCats:""}),J=r({name:[{required:!0,message:"请填写套餐名称",trigger:"blur"}],des:[{required:!0,message:"请填写套餐的详细描述",trigger:"blur"}],deductionType:[{required:!0,message:"请选择扣费形式",trigger:"change"}],coverImg:[{required:!0,message:"请填写或上传封面图地址",trigger:"blur"}],appCats:[{required:!1,message:"请填写套餐包含的应用，多个用逗号隔开",trigger:"blur"}],price:[{required:!0,message:"请填写套餐价格",trigger:"blur"}],order:[{required:!0,message:"请填写套餐排序",trigger:"blur"}],status:[{required:!0,message:"请选择套餐开启状态",trigger:"change"}],days:[{required:!0,message:"请填写套餐有效期天数",trigger:"blur"}],model3Count:[{required:!0,message:"请填写套餐中基础模型可使用次数",trigger:"blur"}],model4Count:[{required:!0,message:"请填写套餐中高级模型可使用次数",trigger:"blur"}],drawMjCount:[{required:!0,message:"请填写套餐中抽奖次数",trigger:"blur"}]}),K=t([]);async function L(){const e=await z.queryCats({size:100}),{rows:l}=e.data;X.value=l.filter((e=>1===e.isMember)).map((e=>({id:e.id,name:e.name,isMember:e.isMember})))}async function N(){try{W.value=!0;const e=await U.queryAllPackage(B);W.value=!1;const{rows:l,count:a}=e.data;R.value=a,K.value=l}catch(e){W.value=!1}}const X=t([]);s((()=>{L(),N()}));const Z=u((()=>G.value?"更新套餐":"新增套餐")),ee=u((()=>G.value?"确认更新":"确认新增"));function le(){G.value=0,L().then((()=>{})),Q.value=!0,M((()=>{var e;null==(e=E.value)||e.resetFields(),H.appCats=""}))}function ae(e){return!(!H.appCats||""===H.appCats)&&H.appCats.split(",").includes(String(e))}function te(e){if(!e||""===e)return"";if(0===X.value.length)return e;const l=X.value.find((l=>String(l.id)===String(e)));return l?l.name:e}return(a,t)=>{const r=g,s=c,u=l,z=d("el-option"),re=d("el-select"),se=d("el-form-item"),ue=d("el-button"),de=d("el-form"),oe=e,ne=d("el-table-column"),ie=d("el-tag"),pe=d("el-popconfirm"),me=d("el-table"),ce=d("el-pagination"),fe=d("el-row"),ge=d("el-input"),be=d("el-col"),ve=d("el-switch"),_e=d("el-dialog"),Ce=o("loading");return i(),n("div",null,[p(u,null,{title:m((()=>t[18]||(t[18]=[b("div",{class:"flex items-center gap-4"},"套餐设置",-1)]))),content:m((()=>t[19]||(t[19]=[b("div",{class:"text-sm/6"},[b("div",null," 套餐分为不限时套餐和限时套餐。不限时充值积分永不过期，限时套餐在规定时间未使用完毕将清空剩余积分。 "),b("div",null,"如果充值的套餐等级高于或等于当前套餐等级，则叠加充值额度并延长会员到期时间。"),b("div",null," 如果充值的套餐等级低于当前套餐等级，则只叠加充值额度，不延长会员到期时间也不改变会员等级。 "),f(" 请仔细阅读以上内容并合理配置，套餐有效时间设为-1即为不限时套餐。 ")],-1)]))),default:m((()=>[p(s,{outline:"",onClick:le},{default:m((()=>[p(r,{name:"ic:baseline-plus"}),t[20]||(t[20]=f(" 创建套餐 "))])),_:1})])),_:1}),p(oe,null,{default:m((()=>[p(de,{ref_key:"formRef",ref:q,inline:!0,model:B},{default:m((()=>[p(se,{label:"套餐状态",prop:"status"},{default:m((()=>[p(re,{modelValue:B.status,"onUpdate:modelValue":t[0]||(t[0]=e=>B.status=e),placeholder:"请选择套餐状态",clearable:"",style:{width:"160px"}},{default:m((()=>[(i(!0),n(v,null,_(C(S),(e=>(i(),y(z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),p(se,null,{default:m((()=>[p(ue,{type:"primary",onClick:N},{default:m((()=>t[21]||(t[21]=[f(" 查询 ")]))),_:1}),p(ue,{onClick:t[1]||(t[1]=e=>{return null==(l=q.value)||l.resetFields(),void N();var l})},{default:m((()=>t[22]||(t[22]=[f(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),p(oe,{style:{width:"100%"}},{default:m((()=>[w((i(),y(me,{border:"",data:K.value,style:{width:"100%"},size:"large"},{default:m((()=>[p(ne,{fixed:"",prop:"name",label:"套餐名称",width:"150"}),p(ne,{prop:"order",label:"排序ID",align:"center",width:"100"}),p(ne,{prop:"appCats",label:"套餐应用",width:"200",align:"center"},{default:m((e=>[(i(!0),n(v,null,_(e.row.appCats&&""!==e.row.appCats?e.row.appCats.split(",").filter((e=>e&&""!==e)):[],(e=>(i(),y(ie,{key:e,class:"mr-1",size:"small"},{default:m((()=>[f(V(te(e)),1)])),_:2},1024)))),128)),e.row.appCats&&""!==e.row.appCats?h("",!0):(i(),n("span",F," 无分类 "))])),_:1}),p(ne,{prop:"price",label:"套餐价格",width:"100",align:"center"}),p(ne,{prop:"weight",label:"套餐等级",width:"100",align:"center"}),p(ne,{prop:"status",label:"套餐状态",width:"100"},{default:m((e=>[p(ie,{type:"info"},{default:m((()=>[f(V(C(A)[e.row.status]),1)])),_:2},1024)])),_:1}),p(ne,{prop:"days",label:"套餐有效期",width:"120"},{default:m((e=>[f(V(e.row.days>0?`${e.row.days}天`:"永久"),1)])),_:1}),p(ne,{prop:"model3Count",label:"基础模型额度",width:"100"}),p(ne,{prop:"model4Count",label:"高级模型额度",width:"100"}),p(ne,{prop:"drawMjCount",label:"绘画额度",width:"100"}),p(ne,{prop:"des",label:"套餐描述",width:"300"}),p(ne,{prop:"createdAt",label:"创建时间",width:"200"},{default:m((e=>[f(V(C(P)(e.row.createdAt,"YYYY-MM-DD hh:mm:ss")),1)])),_:1}),p(ne,{fixed:"right",label:"操作",width:"200"},{default:m((e=>[p(ue,{link:"",type:"primary",size:"small",onClick:l=>{return a=e.row,G.value=a.id,0===X.value.length&&L(),M((()=>{var e;null==(e=E.value)||e.resetFields(),Object.assign(H,a),H.appCats=a.appCats||"",delete H.createdAt,delete H.updatedAt,delete H.deletedAt,delete H.id})),void(Q.value=!0);var a}},{default:m((()=>t[23]||(t[23]=[f(" 修改套餐 ")]))),_:2},1032,["onClick"]),p(pe,{title:"确认删除此套餐么?",width:"200","icon-color":"red",onConfirm:l=>async function(e){await U.delPackage({id:e}),j({type:"success",message:"删除套餐成功！"}),N()}(e.row.id)},{reference:m((()=>[p(ue,{link:"",type:"danger",size:"small"},{default:m((()=>t[24]||(t[24]=[f(" 删除套餐 ")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])),[[Ce,W.value]]),p(fe,{class:"mt-5 flex justify-end"},{default:m((()=>[p(ce,{"current-page":B.page,"onUpdate:currentPage":t[2]||(t[2]=e=>B.page=e),"page-size":B.size,"onUpdate:pageSize":t[3]||(t[3]=e=>B.size=e),class:"mr-5","page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:R.value,onSizeChange:N,onCurrentChange:N},null,8,["current-page","page-size","total"])])),_:1})])),_:1}),p(_e,{modelValue:Q.value,"onUpdate:modelValue":t[16]||(t[16]=e=>Q.value=e),"close-on-click-modal":!1,title:Z.value,width:"970",onClose:t[17]||(t[17]=e=>{return l=E.value,G.value=0,void(null==l||l.resetFields());var l})},{footer:m((()=>[b("span",O,[p(ue,{onClick:t[14]||(t[14]=e=>Q.value=!1)},{default:m((()=>t[26]||(t[26]=[f("取消")]))),_:1}),p(ue,{type:"primary",onClick:t[15]||(t[15]=e=>async function(e){null==e||e.validate((async e=>{if(e){const e={...H};G.value?(await U.updatePackage({id:G.value,...e}),j({type:"success",message:"更新套餐成功！"})):(await U.createPackage(e),j({type:"success",message:"创建新的套餐成功！"})),Q.value=!1,N()}}))}(E.value))},{default:m((()=>[f(V(ee.value),1)])),_:1})])])),default:m((()=>[p(de,{ref_key:"formPackageRef",ref:E,"label-position":"right","label-width":"120px",model:H,rules:J},{default:m((()=>[p(fe,null,{default:m((()=>[p(be,{span:11},{default:m((()=>[p(se,{label:"套餐详细名称",prop:"name"},{default:m((()=>[p(ge,{modelValue:H.name,"onUpdate:modelValue":t[4]||(t[4]=e=>H.name=e),placeholder:"请填写套餐名称"},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:3,offset:2},{default:m((()=>[p(se,{label:"套餐开启状态",prop:"status"},{default:m((()=>[p(ve,{modelValue:H.status,"onUpdate:modelValue":t[5]||(t[5]=e=>H.status=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:7,offset:1},{default:m((()=>[p(se,{label:"套餐等级",prop:"status"},{default:m((()=>[p(ge,{modelValue:H.weight,"onUpdate:modelValue":t[6]||(t[6]=e=>H.weight=e),modelModifiers:{number:!0},type:"number",placeholder:"设置套餐等级"},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:11},{default:m((()=>[p(se,{label:"设置套餐价格",prop:"price"},{default:m((()=>[p(ge,{modelValue:H.price,"onUpdate:modelValue":t[7]||(t[7]=e=>H.price=e),modelModifiers:{number:!0},placeholder:"请填写套餐价格(￥)最多两位小数",type:"number"},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:11,offset:2},{default:m((()=>[p(se,{label:"套餐有效时间",prop:"days"},{default:m((()=>[p(ge,{modelValue:H.days,"onUpdate:modelValue":t[8]||(t[8]=e=>H.days=e),modelModifiers:{number:!0},type:"number",placeholder:"自卡密生成之日有效天数、-1为永久"},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:11},{default:m((()=>[p(se,{label:"App分类",prop:"appCats"},{default:m((()=>[b("div",Y,[b("div",I,[(i(!0),n(v,null,_(H.appCats&&""!==H.appCats?H.appCats.split(",").filter((e=>e&&""!==e)):[],(e=>(i(),y(ie,{key:e,closable:"",class:"mr-1 mb-1",onClose:l=>function(e){if(!H.appCats)return;const l=H.appCats.split(","),a=l.indexOf(String(e));-1!==a&&(l.splice(a,1),H.appCats=l.join(","))}(e)},{default:m((()=>[f(V(te(e)),1)])),_:2},1032,["onClose"])))),128)),H.appCats&&""!==H.appCats?h("",!0):(i(),n("div",$," 请从下方选择分类 "))]),b("div",D,[t[25]||(t[25]=b("div",{class:"text-sm text-gray-500 mb-2"},"可选分类：",-1)),b("div",T,[(i(!0),n(v,null,_(X.value,(e=>(i(),y(ie,{key:e.id,class:x(["mr-1 mb-1 cursor-pointer",ae(String(e.id))?"is-disabled":""]),effect:ae(String(e.id))?"plain":"dark",onClick:k((l=>{return ae(String(e.id))?null:(a=String(e.id),e.name,void(H.appCats&&""!==H.appCats?H.appCats.split(",").includes(String(a))||(H.appCats=`${H.appCats},${a}`):H.appCats=String(a)));var a}),["stop"])},{default:m((()=>[f(V(e.name),1)])),_:2},1032,["class","effect","onClick"])))),128))])])])])),_:1})])),_:1}),p(be,{span:11,offset:2},{default:m((()=>[p(se,{label:"设置套餐排序",prop:"order"},{default:m((()=>[p(ge,{modelValue:H.order,"onUpdate:modelValue":t[9]||(t[9]=e=>H.order=e),modelModifiers:{number:!0},type:"number",placeholder:"排序数字越大越靠前|套餐权重等级则反之"},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:11},{default:m((()=>[p(se,{label:"设置套餐描述",prop:"des"},{default:m((()=>[p(ge,{modelValue:H.des,"onUpdate:modelValue":t[10]||(t[10]=e=>H.des=e),type:"textarea",placeholder:"请填写套餐详细介绍信息、用于对外展示、建议控制套餐价格字数以便于用户端对齐格式...",rows:6},null,8,["modelValue"])])),_:1})])),_:1}),p(be,{span:11,offset:2},{default:m((()=>[p(se,{label:"基础模型积分",prop:"model3Count"},{default:m((()=>[p(ge,{modelValue:H.model3Count,"onUpdate:modelValue":t[11]||(t[11]=e=>H.model3Count=e),modelModifiers:{number:!0},type:"number",placeholder:"基础模型积分"},null,8,["modelValue"])])),_:1}),p(se,{label:"高级模型积分",prop:"model4Count"},{default:m((()=>[p(ge,{modelValue:H.model4Count,"onUpdate:modelValue":t[12]||(t[12]=e=>H.model4Count=e),modelModifiers:{number:!0},type:"number",placeholder:"高级模型积分"},null,8,["modelValue"])])),_:1}),p(se,{label:"绘画模型积分",prop:"drawMjCount"},{default:m((()=>[p(ge,{modelValue:H.drawMjCount,"onUpdate:modelValue":t[13]||(t[13]=e=>H.drawMjCount=e),modelModifiers:{number:!0},type:"number",placeholder:"绘画模型积分"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}});"function"==typeof q&&q(R);export{R as default};
