{"language": "中文", "common": {"add": "添加", "addSuccess": "添加成功", "edit": "编辑", "editSuccess": "编辑成功", "delete": "删除", "deleteSuccess": "删除成功", "update": "修改", "saveSuccess": "保存成功", "updateUserSuccess": "修改用户信息成功", "reset": "重置", "action": "操作", "export": "导出", "exportSuccess": "导出成功", "import": "导入", "importSuccess": "导入成功", "clear": "清空", "clearSuccess": "清空成功", "yes": "是", "no": "否", "download": "下载", "noData": "暂无数据", "wrong": "好像出错了，请稍后再试。", "success": "操作成功", "failed": "操作失败", "verify": "验证", "unauthorizedTips": "未经授权，请先进行验证。", "confirm": "确认", "cancel": "取消"}, "chat": {"newChatButton": "新建聊天", "placeholder": "来说点什么吧...（Shift + Enter = 换行）", "placeholderMobile": "来说点什么...", "copy": "复制", "copied": "复制成功", "copyCode": "复制", "clearChat": "清空会话", "clearChatConfirm": "是否清空会话?", "exportImage": "保存会话到图片", "exportImageConfirm": "是否将会话保存为图片?", "exportSuccess": "保存成功", "exportFailed": "保存失败", "deleteMessage": "删除消息", "deleteMessageConfirm": "删除此条对话?", "deleteHistoryConfirm": "确定删除此记录?", "deleteSuccess": "删除成功", "clearHistoryConfirm": "确定清空聊天记录?", "preview": "预览", "showRawText": "显示原文", "size": "尺寸：", "generatedContentDisclaimer": "AI 生成内容仅供参考，不代表本平台立场。版权所有 ©", "square1": "方形（1:1）", "illustration": "配图（4:3）", "wallpaper": "壁纸（16:9）", "media": "媒体（3:4）", "poster": "海报（9:16）", "square": "方形", "landscape": "宽屏", "portrait": "垂直", "chatDialogue": "对话聊天", "startNewConversationPrompt": "点击下方按钮，开始一个新的对话吧", "newConversation": "新对话", "networkModeEnabledContextInvalid": "已开启联网模式、上下文状态失效！", "networkModeDisabled": "已关闭联网模式！", "pointsMall": "积分商城", "toggleTheme": "切换主题", "signInReward": "签到奖励", "networkMode": "联网模式", "searchHistoryConversations": "搜索历史对话", "announcement": "网站公告", "clear": "清空对话", "remaining": "剩余：", "ordinaryPoints": "普通积分", "advancedPoints": "高级积分", "drawingPoints": "绘画积分", "points": "积分", "clearConversation": "清空对话", "clearAllNonFavoriteConversations": "清空所有非收藏的对话？", "more": "更多", "collapse": "折叠", "myApps": "我的应用", "appSquare": "应用广场", "favorites": "收藏", "todayConversations": "今日对话", "historyConversations": "历史对话", "favoriteConversations": "收藏对话", "unfavorite": "取消收藏", "rename": "重命名", "deleteConversation": "删除对话", "me": "我", "onlineSearch": "联网搜索", "mindMap": "思维导图", "fileAnalysis": "文件", "delete": "删除", "regenerate": "重新生成", "pause": "暂停", "loading": "加载中...", "readAloud": "朗读", "vipCenter": "会员中心", "U1": "🔍 放大左上", "U2": "🔍 放大右上", "U3": "🔍 放大左下", "U4": "🔍 放大右下", "V1": "🪄 变换左上", "V2": "🪄 变换右上", "V3": "🪄 变换左下", "V4": "🪄 变换右下", "panLeft": "⬅️ 向左平移", "panRight": "➡️ 向右平移", "panUp": "⬆️ 向上平移", "panDown": "⬇️ 向下平移", "zoomIn15x": "↔️ 扩图1.5倍", "zoomIn2x": "↔️ 扩图2倍", "minorTransform": "🖌️ 微变换", "strongTransform": "🖌️ 强变换", "enlargeImagePrefix": "放大第", "enlargeImageSuffix": "张图片", "transformImagePrefix": "变换第", "transformImageSuffix": "张图片", "expandDrawing": "扩图绘制", "advancedTransform": "高级变换", "translateImage": "平移图片", "imageToImage": "以图生图", "faceConsistency": "人脸一致", "styleConsistency": "风格一致", "selectAppOrTopic": "选择应用或话题快速对话"}, "app": {"sampleTemplate": "示例模板", "exploreInfinitePossibilities": "探索无限可能，与 AI 一同开创智慧未来", "searchAppNameQuickFind": "搜索应用名称、快速查找应用...", "allCategories": "全部分类", "noModelConfigured": "管理员未配置特定应用模型、请联系管理员配置~"}, "setting": {"setting": "设置", "general": "总览", "advanced": "高级", "personalInfo": "个人信息", "avatarLink": "头像链接", "name": "用户名称", "sign": "用户签名", "role": "角色设定", "resetUserInfo": "重置用户信息", "chatHistory": "聊天记录", "theme": "主题", "language": "语言", "api": "API", "reverseProxy": "反向代理", "timeout": "超时", "socks": "Socks", "httpsProxy": "HTTPS Proxy", "balance": "API余额"}, "store": {"siderButton": "提示词商店", "local": "本地", "online": "在线", "title": "标题", "description": "描述", "clearStoreConfirm": "是否清空数据？", "importPlaceholder": "请粘贴 JSON 数据到此处", "addRepeatTitleTips": "标题重复，请重新输入", "addRepeatContentTips": "内容重复：{msg}，请重新输入", "editRepeatTitleTips": "标题冲突，请重新修改", "editRepeatContentTips": "内容冲突{msg} ，请重新修改", "importError": "键值不匹配", "importRepeatTitle": "标题重复跳过：{msg}", "importRepeatContent": "内容重复跳过：{msg}", "onlineImportWarning": "注意：请检查 JSON 文件来源！", "downloadError": "请检查网络状态与 JSON 文件有效性"}, "draw": {"use": "使用", "download": "下载", "delete": "删除", "zoom": "放大：", "U1": "左上", "U2": "右上", "U3": "左下", "U4": "右下", "regenerateOnce": "重新生成一次", "transform": "变换：", "V1": "左上", "V2": "右上", "V3": "左下", "V4": "右下", "pan": "平移：", "panLeft": "向左", "panRight": "向右", "panUp": "向上", "panDown": "向下", "transformZoom": "扩图变换：", "zoom1_5x": "扩图1.5倍", "zoom2x": "扩图2倍", "minorTransform": "微变换", "strongTransform": "强变换", "regionalRedraw": "区域重绘", "regionalRedraw1": "区域重绘（框选要改变的区域）", "submitTask": "提交任务", "selectSuiteForZoom": "操作：选中套图进行放大", "selectSuiteForTransform": "操作：选中套图进行变换", "regeneratingImage": "操作：正在对图片重新生成一次", "drawingInProgress": "操作：正在火速绘制中...", "tryDifferentPrompt": "执行：换个提示词重新试试吧！", "statusWaiting": "等待中", "statusDrawing": "绘制中", "statusSuccess": "成功", "statusFailure": "失败", "statusTimeout": "超时", "downloadImageTitle": "下载图片", "downloadImageContent": "下载当前图片", "downloadButtonText": "下载", "cancelButtonText": "取消", "deleteRecordTitle": "删除记录", "deleteRecordContent": "删除当前绘制记录？", "deleteButtonText": "删除", "submitZoomDrawingSuccess": "提交放大绘制任务成功、请等待绘制结束！", "submitRedrawSuccess": "提交重新绘制任务成功、请等待绘制结束！", "submitTransformDrawingSuccess": "提交变换绘制任务成功、请等待绘制结束！", "submitEnlargeDrawingSuccess": "提交扩图任务成功、请等待绘制结束！", "submitAdvancedTransformDrawingSuccess": "提交高级变换绘制任务成功、请等待绘制结束！", "submitRegionalRedrawSuccess": "提交区域重绘任务成功、请等待绘制结束！", "drawingRecordDeleted": "绘制记录已删除！", "queueing": "排队中...", "drawing": "正在绘制...", "storing": "图片存储中...", "drawingFailed": "绘制失败", "pointsRefunded": "积分已退还！", "submitDrawingTaskSuccess": "提交绘制任务成功、请等待绘制结束！", "defaultStyle": "默认风格", "expressiveStyle": "表现力风格", "cuteStyle": "可爱风格", "scenicStyle": "景观风格", "standardQuality": "普通", "generalQuality": "一般", "highDefinitionQuality": "高清", "ultraHighDefinitionQuality": "超高清", "enterDescription": "请输入描述词！", "optimizationFailed": "优化失败了！", "professionalDrawing": "专业绘图", "parameterExplanation": "参数释义：生成图片尺寸比例", "imageSize": "图片尺寸", "modelSelection": "模型选择", "tooltipMJ": "MJ: 偏真实通用模型", "tooltipNIJI": "NIJI: 偏动漫风格、适用于二次元模型", "version": "版本", "style": "风格", "parameters": "参数", "parametersTooltip": "合理使用参数绘制更为理想的结果！", "quality": "品质", "chaos": "混乱", "chaosDescription": "取值范围：0-100、 --chaos 或 --c", "chaosExplanation": "混乱级别，可以理解为让AI天马行空的空间", "chaosAdvice": "值越小越可靠、默认0最为精准", "stylization": "风格化", "stylizationDescription": "风格化：--stylize 或 --s，范围 1-1000", "parameterExplanation1": "参数释义：数值越高，画面表现也会更具丰富性和艺术性", "setting": "设定", "carryParameters": "携带参数", "autoCarryParameters": "是否自动携带参数", "carryOn": "打开：携带上述我们配置的参数", "carryOff": "关闭：使用指令中的我们自定义的参数", "imageToImage": "以图生图", "clickOrDrag": "点击或拖拽一个图片到这里作为输入", "supportFormats": "支持PNG和JPG格式", "remainingPoints": "剩余积分", "refresh": "刷新", "accountInfo": "账户信息", "points": "积分", "paintingSingleUse": "绘画单次消耗：", "imageGenerationSingleUse": "图生图单次消耗：", "enlargementSingleUse": "放大单次消耗：", "submitDrawingTask": "输入关键词，提交绘制任务", "optimize": "优化", "enterDrawingKeywords": "输入绘图关键词。例如：一只五颜六色的猫，可爱，卡通", "unnecessaryElements": "不需要的元素", "exclusionPrompt": "例：生成房间图片、但是不要床、你可以填bed！", "workingContents": "工作中的内容", "currentTasks": "当前系统进行中任务", "goToAIDrawingSquare": "点击前往 AI 绘画广场", "tasksInProgress": "个任务正在进行中、请耐心等候绘制完成、您可以前往其他页面稍后回来查看结果！", "myDrawings": "我的绘图", "aiDrawingSquare": "AI绘画广场", "sizeAdjustment": "尺寸调整", "keywordSearchPlaceholder": "prompt关键词搜索"}, "pay": {"membershipMarket": "会员商场", "sizeAdjustment": "尺寸调整", "memberPackage": "会员限时套餐", "permanentAddOnCard": "叠加永久次卡", "baseModelQuota": "普通积分", "advancedModelQuota": "高级积分", "MJDrawingQuota": "绘画积分", "packageValidity": "套餐有效期", "days": "天", "permanent": "永久", "points": "积分", "welcomeTipMobile": "尽情探索，欢迎光临我们的在线商店！", "welcomeTipDesktop": "尽情探索，欢迎光临我们的在线商店、感谢您选择我们、让我们一同开启愉悦的购物之旅！", "paymentNotEnabled": "管理员还未开启支付！", "purchaseSuccess": "购买成功、祝您使用愉快!", "paymentNotComplete": "您还没有支付成功哟！", "wechat": "微信", "alipay": "支付宝", "wechatPay": "微信支付", "alipayPay": "支付宝支付", "paymentSuccess": "恭喜你支付成功、祝您使用愉快！", "paymentTimeout": "支付超时，请重新下单!", "productPayment": "商品支付", "amountDue": "需要支付：", "packageName": "套餐名称：", "packageDescription": "套餐描述：", "siteAdminEnabledRedirect": "当前站长开通了跳转支付", "clickToPay": "点击前往支付", "completePaymentWithin": "请在", "timeToCompletePayment": "时间内完成支付！", "open": "打开", "scanToPay": "扫码支付"}, "mindmap": {"title": "思维导图", "yourNeeds": "您的需求？", "inputPlaceholder": "请输入您想要生成内容的简单描述、AI将为您输出一份完整的markdown内容及其思维导图!", "generateMindMapButton": "智能生成生成思维导图", "contentRequirements": "内容需求", "tryDemoButton": "试试示例", "usageCredits": "每次使用消耗基础积分： 1", "exportHTML": "导出HTML", "exportPNG": "导出PNG", "exportSVG": "导出SVG"}, "usercenter": {"defaultSignature": "我是一台基于深度学习和自然语言处理技术的 AI 机器人，旨在为用户提供高效、精准、个性化的智能服务。", "syncComplete": "已同步数据完成", "personalCenter": "个人中心", "logOut": "退出登录", "myUsageRecord": "我在本站的使用记录", "basicModelCredits": "基础模型积分:", "advancedModelCredits": "高级模型积分:", "basicModelUsage": "基础模型使用:", "advancedModelUsage": "高级模型使用:", "drawingUsageCredits": "绘画使用积分:", "bindWeChat": "绑定微信:", "clickToBindWeChat": "点击绑定微信", "weChatBound": "已绑定微信", "syncVisitorData": "点击同步访客数据", "points": "积分", "membershipExpiration": "会员过期时间：", "editInfoDescription": "编辑个人信息、查看更多详情", "myDetails": "我的详情", "myWallet": "我的钱包", "basicInfo": "基础信息", "userBasicSettings": "用户基础设置", "avatarPlaceholder": "请填写头像地址", "usernamePlaceholder": "请编辑您的用户名", "signaturePlaceholder": "请编辑您的签名", "passwordManagement": "密码管理", "inviteBenefits": "邀请得福利", "clickToLogin": "点击登入", "notLoggedIn": "未登录", "avatar": "头像", "username": "用户名称", "email": "用户邮箱", "inviteeStatus": "受邀人状态", "inviteTime": "邀请时间", "rewardStatus": "获得奖励状态", "certified": "已认证", "notActivated": "未激活", "rewardReceived": "已领取邀请奖励", "waitingConfirmation": "等待受邀人确认", "linkGeneratedSuccess": "生成邀请链接成功", "generateLinkFirst": "请先生成您的专属邀请链接！", "linkCopiedSuccess": "复制专属邀请链接成功！", "copyNotSupported": "当前设置不支持自动复制、手动复制吧！", "inviteForBenefits": "邀用户、得福利!", "myInviteCode": "我的邀请码", "generateInviteCode": "生成专属邀请码", "copyInviteLink": "复制专属邀请链接", "inviteOneUser": "邀请一位用户赠送", "basicModelCredits1": "积分基础模型额度+", "advancedModelCredits1": "积分高级模型额度+", "mjDrawingCredits": "MJ绘画积分额度", "receiveInvitation": "收到邀请用户获得", "creditsEnd": "积分", "invitationRecord": "邀请记录", "passwordMinLength": "密码最短长度为6位数", "passwordMaxLength": "密码最长长度为30位数", "enterPassword": "请输入密码", "reenterPassword": "请再次输入密码", "passwordsNotMatch": "两次密码输入不一致", "passwordUpdateSuccess": "密码更新成功、请重新登录系统！", "changeYourPassword": "变更您的密码", "oldPassword": "旧密码", "newPassword": "新密码", "confirmPassword": "确认密码", "reloginAfterPasswordChange": "更新密码完成后将重新登录！", "updateYourPassword": "更新您的密码", "passwordRequirements": "密码要求", "newPasswordInstructions": "要创建一个新的密码，你必须满足以下所有要求。", "minimumCharacters": "最少6个字符", "maximumCharacters": "最多30个字符", "requireNumber": "至少带有一个数字", "orderNumber": "订单编号", "rechargeType": "充值类型", "basicModelQuota": "普通积分", "advancedModelQuota": "高级积分", "mjDrawingQuota": "绘画积分", "validity": "有效期", "rechargeTime": "充值时间", "enterCardSecret": "请先填写卡密！", "cardRedeemSuccess": "卡密兑换成功、祝您使用愉快！", "userWalletBalance": "用户钱包余额", "basicModelBalance": "基础模型余额", "creditUsageNote": "每次对话根据模型消费不同积分！", "advancedModelBalance": "高级模型余额", "modelConsumptionNote": "每次对话根据模型消费不同积分！", "mjDrawingBalance": "MJ绘画余额", "drawingConsumptionNote": "根据画图动作消耗不同的积分！", "cardRecharge": "卡密充值", "enterCardDetails": "请粘贴或填写您的卡密信息！", "pleaseEnterCardDetails": "请输入卡密信息", "exchange": "兑换", "buyCardSecret": "购买卡密", "rechargeRecords": "充值记录", "packagePurchase": "套餐购买", "buyPackage": "购买套餐"}, "siderBar": {"signInReward": "签到奖励", "themeSwitch": "主题切换", "personalCenter": "个人中心", "loginAccount": "登录账户"}, "notice": {"doNotRemind24h": "我已知晓"}, "login": {"enterUsername": "请输入用户名", "usernameLength": "用户名长度应为 2 到 30 个字符", "enterPassword": "请输入密码", "passwordLength": "密码长度应为 6 到 30 个字符", "enterEmail": "请输入邮箱地址", "enterPhone": "请输入手机号码", "enterEmailOrPhone": "请输入邮箱地址或手机号码", "emailValid": "请输入正确的邮箱地址", "enterCaptcha": "请填写图形验证码", "emailPhone": "邮箱 / 手机号", "email": "邮箱", "phone": "手机号", "registrationSuccess": "账户注册成功、开始体验吧！", "loginSuccess": "账户登录成功、开始体验吧！", "registerTitle": "注册", "enterContact": "请填写您的", "enterCode": "请填写验证码", "sendVerificationCode": "发送验证码", "optionalInvitationCode": "邀请码[非必填]", "registerAccount": "注册账户", "alreadyHaveAccount": "已经有帐号？", "goToLogin": "去登录", "password": "密码", "enterYourPassword": "请输入您的账户密码", "rememberAccount": "记住帐号", "forgotPassword": "忘记密码?", "loginAccount": "登录账户", "noAccount": "还没有帐号？", "register": "去注册", "orUse": "或使用", "scanLogin": "扫码登录", "wechatLogin": "微信登录", "wechatScanFailed": "不使用微信扫码登录？试试", "useWechatScan": "使用微信扫码登录"}, "share": {"orderAmount": "订单金额", "productType": "商品类型", "status": "状态", "commissionRate": "佣金比例", "commission": "佣金", "orderTime": "订购时间", "purchasePackage": "购买套餐", "accounted": "已入账", "generateInviteCodeSuccess": "生成邀请码成功", "withdrawalTime": "提现时间", "withdrawalAmount": "提现金额", "withdrawalChannel": "提现渠道", "withdrawalStatus": "提现状态", "withdrawalRemarks": "提现备注", "auditor": "审核人", "alipay": "支付宝", "wechat": "微信", "paid": "已打款", "rejected": "被拒绝", "inReview": "审核中", "avatar": "头像", "username": "用户名", "email": "邮箱", "inviteeStatus": "受邀人状态", "registered": "已注册", "pendingActivation": "待激活", "registrationTime": "注册时间", "lastLogin": "最后登录", "requestInviteCodeFirst": "请先申请你的邀请码", "linkCopiedSuccess": "复制推荐链接成功", "title": "推介计划", "description": "加入我们，共享成功！欢迎来到我们的分销页面，成为我们的合作伙伴，一同开创美好未来！", "defaultSalesOutletName": "新秀推荐官", "myReferrals": "我的推介", "currencyUnit": "元", "remainingAmount": "剩余可提金额", "withdrawingAmount": "提现中金额", "withdrawNow": "立即提现", "minimumWithdrawalPrefix": "最低", "minimumWithdrawalSuffix": "元可提现", "purchaseOrderCount": "购买订单数量", "promotionLinkVisits": "推广链接访问次数", "registeredUsers": "注册用户", "referralEarnings": "推介收益", "referralEarningsDescription": "推介的用户注册购买产品后返佣金额", "percentage": "百分比", "applyForAdvancedAgent": "申请成为高级代理", "contactAdminForAdvancedAgent": "联系站长申请高级代理可享超高返佣", "joinAsPartner": "加入我们成为合伙人", "partnerDescription": "加入我们成为合伙人共同运营社区、合作双赢！", "winTogether": "合作共赢，携手共进", "referralLink": "推荐链接：", "apply": "申请", "referralRecordsTab": "推介记录", "withdrawalRecordsTab": "提现记录", "registeredUsersTab": "注册用户", "inviteFriends": "邀好友、赠套餐卡密、享充值返佣！", "inviteLink": "邀请链接", "copy": "复制", "inviteBenefits1": "邀请好友双方都可享受一定额度的永久次卡奖励", "inviteBenefits2Prefix": "邀请好友充值，您可获得充值金额的", "inviteBenefits2Suffix": "%返佣", "enterWithdrawalAmount": "请填写你的提款金额！", "selectWithdrawalChannel": "请选择你的提款渠道！", "enterContactInfo": "请填写您的联系方式并备注！", "optionalRemark": "如有特殊情况、请备注说明！", "withdrawalSuccess": "申请提现成功、请耐心等待审核！", "withdrawalApplicationForm": "提款申请表", "contactInformation": "联系方式", "withdrawalRemark": "提款备注", "enterWithdrawalRemark": "请填写你的提款备注！", "applyWithdrawal": "申 请 提 现"}, "goods": {"purchaseSuccess": "购买成功、祝您使用愉快!", "paymentNotSuccessful": "您还没有支付成功哟！", "orderConfirmationTitle": "订单确认", "orderConfirmationContent": "欢迎选购、确定购买", "thinkAgain": "我再想想", "confirmPurchase": "确认购买", "paymentNotEnabled": "管理员还未开启支付！", "selectProducts": "选购套餐", "basicModelQuota": "基础积分", "advancedModelQuota": "高级积分", "drawingQuota": "绘画积分", "buyPackage": "购买套餐"}, "rechargeTypes": {"1": "注册赠送", "2": "受邀请赠送", "3": "邀请他人赠送", "4": "购买卡密充值", "5": "管理员赠送", "6": "扫码购买充值", "7": "MJ绘画失败退款", "8": "签到奖励"}, "orderStatus": {"0": "未支付", "1": "已支付", "2": "支付失败", "3": "支付超时"}, "messages": {"logoutSuccess": "登出账户成功！"}}