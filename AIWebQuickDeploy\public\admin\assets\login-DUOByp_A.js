
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,af as a,u as l,ag as s,r,Q as o,c as t,e as n,g as u,a5 as m,a8 as i,i as d,f as c,w as g,t as p,_ as f,a3 as b,h as v,a4 as h,k as x}from"./index-BERX8Mlm.js";const _={class:"min-h-screen flex items-center justify-center bg-gray-100"},w={id:"login-box",class:"min-h-[60vh] min-w-[40vw] items-center rounded-lg bg-gray-50 p-6 shadow-lg"},y={class:"mb-6 text-center"},V={class:"text-xl text-gray-900 font-bold"},k={class:"mb-4 flex items-center justify-between"},S=e({__name:"login",setup(e){var x;const S=a(),z=l(),I=s(),U="AIWeb",j=atob("QUlXZWI=");if(!U.includes(j))throw document.body.innerHTML="<h1></h1>",new Error("");const q=r("login"),Q=r(!1),W=r((null==(x=S.query.redirect)?void 0:x.toString())??"/"),A=r(),C=r({username:localStorage.login_username||"",password:"",remember:!!localStorage.login_username}),E=r({username:[{required:!0,trigger:"blur",message:"请输入用户名"}],password:[{required:!0,trigger:"blur",message:"请输入密码"},{min:6,max:18,trigger:"blur",message:"密码长度为6到18位"}]});function F(){A.value&&A.value.validate((e=>{e&&(Q.value=!0,I.login(C.value).then((()=>{Q.value=!1,C.value.remember?localStorage.setItem("login_username",C.value.username):localStorage.removeItem("login_username"),z.push(W.value)})).catch((()=>{Q.value=!1})))}))}return(e,a)=>{const l=f,s=o("el-icon"),r=o("el-input"),x=o("el-form-item"),S=o("el-checkbox"),z=o("el-button"),I=o("el-form");return n(),t("div",_,[u("div",w,[m(c(I,{ref_key:"loginFormRef",ref:A,model:d(C),rules:d(E),class:"login-form mx-12 my-10",autocomplete:"on"},{default:g((()=>[u("div",y,[u("h3",V,"欢迎来到 "+p(d(U)),1)]),u("div",null,[c(x,{prop:"username",class:"py-2"},{default:g((()=>[c(r,{modelValue:d(C).username,"onUpdate:modelValue":a[0]||(a[0]=e=>d(C).username=e),placeholder:"用户名",text:"",tabindex:"1",autocomplete:"on",size:"large",class:"h-10"},{prefix:g((()=>[c(s,null,{default:g((()=>[c(l,{name:"ep:user"})])),_:1})])),_:1},8,["modelValue"])])),_:1}),c(x,{prop:"password",class:"py-2"},{default:g((()=>[c(r,{modelValue:d(C).password,"onUpdate:modelValue":a[1]||(a[1]=e=>d(C).password=e),type:"password",placeholder:"密码",tabindex:"2",autocomplete:"on","show-password":"",size:"large",class:"h-10",onKeyup:b(F,["enter"])},{prefix:g((()=>[c(s,null,{default:g((()=>[c(l,{name:"ep:lock"})])),_:1})])),_:1},8,["modelValue"])])),_:1})]),u("div",k,[c(S,{modelValue:d(C).remember,"onUpdate:modelValue":a[2]||(a[2]=e=>d(C).remember=e),size:"large"},{default:g((()=>a[3]||(a[3]=[v(" 记住我 ")]))),_:1},8,["modelValue"])]),c(z,{loading:d(Q),type:"primary",size:"large",class:"w-full",onClick:h(F,["prevent"])},{default:g((()=>a[4]||(a[4]=[v(" 登录 ")]))),_:1},8,["loading"])])),_:1},8,["model","rules"]),[[i,"login"===d(q)]])])])}}});"function"==typeof x&&x(S);export{S as default};
