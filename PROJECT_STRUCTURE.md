# 99AI 项目结构文档

## 📋 项目概述

99AI 是一个可商业化的 AI Web 平台，提供一站式的人工智能服务解决方案。项目采用前后端分离架构，支持私有化部署，内置多用户管理系统。

## 🏗️ 整体架构

```
99AI-main/
├── service/           # 后端服务 (NestJS + TypeScript)
├── admin/            # 管理后台 (Vue 3 + TypeScript)
├── chat/             # 用户聊天界面 (Vue 3 + TypeScript)
├── AIWebQuickDeploy/ # 快速部署包
├── docs/             # 项目文档
├── build.sh          # 构建脚本
├── README.md         # 项目说明
└── LICENSE           # 开源协议
```

## 🔧 技术栈

### 后端服务 (service/)
- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: MySQL (TypeORM)
- **缓存**: Redis (ioredis)
- **认证**: JWT + Passport
- **文档**: Swagger
- **进程管理**: PM2
- **AI集成**: OpenAI, Google GenAI, LangChain
- **支付**: 微信支付
- **文件处理**: 支持多种格式解析 (PDF, Word, Excel, PPT)

### 管理后台 (admin/)
- **框架**: Vue 3.5.x
- **构建工具**: Vite 6.x
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **样式**: UnoCSS + SCSS
- **图表**: ECharts
- **编辑器**: MD Editor V3

### 用户界面 (chat/)
- **框架**: Vue 3.5.x
- **构建工具**: Vite 4.x
- **样式**: TailwindCSS
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **代码高亮**: CodeMirror 6.x
- **图表渲染**: Mermaid, Markmap
- **文件处理**: PDF.js, Mammoth
- **桌面应用**: Electron (可选)

## 📁 详细目录结构

### 后端服务 (service/)
```
service/
├── src/
│   ├── app.module.ts          # 应用主模块
│   ├── main.ts               # 应用入口
│   ├── common/               # 公共模块
│   │   ├── auth/            # 认证相关
│   │   ├── constants/       # 常量定义
│   │   ├── decorators/      # 装饰器
│   │   ├── entity/          # 基础实体
│   │   ├── filters/         # 异常过滤器
│   │   ├── guards/          # 守卫
│   │   ├── interceptors/    # 拦截器
│   │   ├── logger/          # 日志模块
│   │   ├── middleware/      # 中间件
│   │   ├── result/          # 响应结果封装
│   │   ├── swagger/         # API文档配置
│   │   └── utils/           # 工具函数
│   ├── modules/             # 业务模块
│   │   ├── aiTool/         # AI工具模块
│   │   ├── app/            # 应用模块
│   │   ├── auth/           # 认证模块
│   │   ├── autoReply/      # 自动回复
│   │   ├── badWords/       # 敏感词过滤
│   │   ├── chat/           # 聊天模块
│   │   ├── chatGroup/      # 聊天分组
│   │   ├── chatLog/        # 聊天记录
│   │   ├── crami/          # 卡密系统
│   │   ├── database/       # 数据库模块
│   │   ├── globalConfig/   # 全局配置
│   │   ├── mailer/         # 邮件服务
│   │   ├── models/         # AI模型管理
│   │   ├── official/       # 官方功能
│   │   ├── order/          # 订单系统
│   │   ├── pay/            # 支付模块
│   │   ├── plugin/         # 插件系统
│   │   ├── rateLimit/      # 限流模块
│   │   ├── redisCache/     # Redis缓存
│   │   ├── share/          # 分享功能
│   │   ├── signin/         # 签到系统
│   │   ├── spa/            # 单页应用
│   │   ├── statistic/      # 统计分析
│   │   ├── task/           # 任务调度
│   │   ├── upload/         # 文件上传
│   │   ├── user/           # 用户管理
│   │   ├── userBalance/    # 用户余额
│   │   └── verification/   # 验证码
│   └── types/              # 类型定义
├── package.json            # 依赖配置
├── tsconfig.json          # TypeScript配置
├── nest-cli.json          # NestJS CLI配置
├── pm2.conf.json          # PM2配置
└── Dockerfile             # Docker配置
```

### 管理后台 (admin/)
```
admin/
├── src/
│   ├── App.vue              # 根组件
│   ├── main.ts             # 应用入口
│   ├── api/                # API接口
│   ├── assets/             # 静态资源
│   ├── components/         # 公共组件
│   │   ├── Auth/           # 权限组件
│   │   ├── FileUpload/     # 文件上传
│   │   ├── ImageUpload/    # 图片上传
│   │   ├── PageHeader/     # 页面头部
│   │   └── ...
│   ├── layouts/            # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   └── views/              # 页面组件
│       ├── app/            # 应用管理
│       ├── chat/           # 聊天管理
│       ├── models/         # 模型管理
│       ├── order/          # 订单管理
│       ├── pay/            # 支付管理
│       ├── system/         # 系统设置
│       └── users/          # 用户管理
├── package.json            # 依赖配置
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
└── uno.config.ts          # UnoCSS配置
```

### 用户界面 (chat/)
```
chat/
├── src/
│   ├── App.vue              # 根组件
│   ├── main.ts             # 应用入口
│   ├── api/                # API接口
│   ├── assets/             # 静态资源
│   ├── components/         # 公共组件
│   │   ├── Dialog/         # 对话框组件
│   │   ├── Login/          # 登录组件
│   │   ├── Message/        # 消息组件
│   │   ├── Settings/       # 设置组件
│   │   └── common/         # 通用组件
│   ├── hooks/              # 组合式函数
│   ├── locales/            # 国际化
│   ├── store/              # 状态管理
│   ├── styles/             # 样式文件
│   ├── utils/              # 工具函数
│   └── views/              # 页面组件
│       └── chat/           # 聊天页面
├── package.json            # 依赖配置
├── vite.config.ts         # Vite配置
├── tailwind.config.js     # TailwindCSS配置
└── electron-builder.yml   # Electron打包配置
```

## 🚀 核心功能模块

### 1. AI 对话系统
- **位置**: `service/src/modules/chat/`
- **功能**: 支持多种AI模型对话，流式响应，上下文管理
- **特性**: 支持文件上传、图像识别、代码生成

### 2. 模型管理
- **位置**: `service/src/modules/models/`
- **功能**: AI模型配置、参数调优、费用计算
- **支持**: OpenAI、Claude、Gemini等主流模型

### 3. 用户系统
- **位置**: `service/src/modules/user/`
- **功能**: 用户注册、登录、权限管理、余额系统
- **特性**: 多级权限、积分系统、VIP会员

### 4. 支付系统
- **位置**: `service/src/modules/pay/`
- **功能**: 微信支付、支付宝、卡密充值
- **特性**: 订单管理、退款处理、财务统计

### 5. 插件系统
- **位置**: `service/src/modules/plugin/`
- **功能**: 扩展AI能力，支持联网搜索、工具调用
- **特性**: 热插拔、配置化、API集成

## 📦 部署相关

### 快速部署包 (AIWebQuickDeploy/)
```
AIWebQuickDeploy/
├── dist/                   # 编译后的后端代码
├── public/                 # 前端静态文件
│   ├── admin/             # 管理后台
│   └── chat/              # 用户界面
├── docker-compose.yml     # Docker编排
├── Dockerfile             # Docker镜像
├── package.json           # 运行时依赖
└── pm2.conf.json         # PM2配置
```

### 文档目录 (docs/)
- `DEPLOYMENT.md` - 部署指南
- `DEVELOPMENT.md` - 开发指南  
- `FEATURES.md` - 功能介绍

## 🔄 开发流程

1. **后端开发**: 在 `service/` 目录下使用 NestJS 开发API
2. **前端开发**: 在 `admin/` 和 `chat/` 目录下使用 Vue 3 开发界面
3. **构建部署**: 使用 `build.sh` 脚本一键构建所有模块
4. **容器化**: 使用 Docker 进行容器化部署

## 📝 配置文件

- **后端配置**: `service/.env` - 数据库、Redis、AI模型等配置
- **前端配置**: `admin/src/settings.ts` - 管理后台配置
- **聊天配置**: `chat/src/config/` - 聊天界面配置
- **部署配置**: `docker-compose.yml` - 容器编排配置

## 🛠️ 开发环境要求

### 系统要求
- **Node.js**: ^18.18.0 || ^20.9.0 || >=21.1.0
- **包管理器**: pnpm (推荐) / npm / yarn
- **数据库**: MySQL 5.7+ / 8.0+
- **缓存**: Redis 6.0+
- **操作系统**: Windows / macOS / Linux

### 开发工具推荐
- **IDE**: VSCode / WebStorm
- **数据库工具**: Navicat / DBeaver
- **API测试**: Postman / Apifox
- **容器**: Docker Desktop

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
cd service && pnpm install
cd ../admin && pnpm install
cd ../chat && pnpm install
```

### 2. 配置环境变量
```bash
# 复制配置文件
cp service/.env.example service/.env
# 编辑配置文件，填入数据库、Redis等信息
```

### 3. 启动开发服务
```bash
# 启动后端服务
cd service && pnpm run dev

# 启动管理后台
cd admin && pnpm run dev

# 启动用户界面
cd chat && pnpm run dev
```

### 4. 访问应用
- 后端API: http://localhost:3009
- 管理后台: http://localhost:3000
- 用户界面: http://localhost:9002

## 📊 数据库设计

### 核心数据表
- `users` - 用户信息表
- `models` - AI模型配置表
- `chat_log` - 聊天记录表
- `chat_group` - 聊天分组表
- `user_balance` - 用户余额表
- `orders` - 订单表
- `config` - 系统配置表
- `app` - 应用预设表
- `crami` - 卡密表
- `plugin` - 插件配置表

## 🔐 安全特性

### 认证授权
- JWT Token 认证
- 角色权限控制 (RBAC)
- API 接口鉴权
- 敏感操作二次验证

### 数据安全
- 密码加密存储 (bcrypt)
- 敏感词过滤
- SQL注入防护
- XSS攻击防护
- CSRF防护

### 限流保护
- 接口访问频率限制
- 用户操作频率限制
- IP黑白名单
- 异常行为检测

## 🎯 核心特性

### AI能力
- **对话模型**: GPT-3.5/4, Claude, Gemini等
- **图像模型**: DALL-E, Midjourney, Stable Diffusion
- **音频模型**: Whisper, TTS
- **视频模型**: Luma, Runway
- **代码模型**: CodeLlama, GitHub Copilot

### 业务功能
- **多租户**: 支持多用户、多角色
- **计费系统**: 按次计费、包月套餐、积分系统
- **文件处理**: PDF、Word、Excel、PPT解析
- **联网搜索**: 实时信息获取
- **应用市场**: 预设应用模板
- **数据统计**: 使用情况分析

### 扩展能力
- **插件系统**: 支持第三方插件
- **API开放**: RESTful API接口
- **Webhook**: 事件回调机制
- **自定义**: 界面主题、功能配置

## 🔗 相关链接

- [官方文档](https://docs.lightai.cloud/)
- [在线演示](https://99ai.lightai.cloud/)
- [GitHub仓库](https://github.com/vastxie/99AI)
- [插件系统](https://github.com/vastxie/99AIPlugin)
- [开发版预览](https://asst.lightai.cloud)

## 📄 许可证

本项目采用 [Apache 2.0](LICENSE) 开源协议，支持商业使用。
