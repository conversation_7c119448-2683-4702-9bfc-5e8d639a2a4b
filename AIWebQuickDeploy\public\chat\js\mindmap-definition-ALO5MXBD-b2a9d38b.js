var tt=(A,w,u)=>new Promise((m,v)=>{var r=i=>{try{e(u.next(i))}catch(o){v(o)}},t=i=>{try{e(u.throw(i))}catch(o){v(o)}},e=i=>i.done?m(i.value):Promise.resolve(i.value).then(r,t);e((u=u.apply(A,w)).next())});import{_ as S,l as z,j as Dt,ab as mt,L as Ot,d as ot,I as At,a2 as It,D as et,i as rt,a3 as Ct,a4 as Rt,a5 as xt}from"./chart-vendor-e1d59b84.js";import{c as dt}from"./cytoscape.esm-73b8622f.js";import{c as ht,g as Mt}from"./utils-vendor-c35799af.js";import"./vue-vendor-d751b0f5.js";var vt={exports:{}},nt={exports:{}},st={exports:{}},ft;function wt(){return ft||(ft=1,function(A,w){(function(m,v){A.exports=v()})(ht,function(){return function(u){var m={};function v(r){if(m[r])return m[r].exports;var t=m[r]={i:r,l:!1,exports:{}};return u[r].call(t.exports,t,t.exports,v),t.l=!0,t.exports}return v.m=u,v.c=m,v.i=function(r){return r},v.d=function(r,t,e){v.o(r,t)||Object.defineProperty(r,t,{configurable:!1,enumerable:!0,get:e})},v.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return v.d(t,"a",t),t},v.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},v.p="",v(v.s=26)}([function(u,m,v){function r(){}r.QUALITY=1,r.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,r.DEFAULT_INCREMENTAL=!1,r.DEFAULT_ANIMATION_ON_LAYOUT=!0,r.DEFAULT_ANIMATION_DURING_LAYOUT=!1,r.DEFAULT_ANIMATION_PERIOD=50,r.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,r.DEFAULT_GRAPH_MARGIN=15,r.NODE_DIMENSIONS_INCLUDE_LABELS=!1,r.SIMPLE_NODE_SIZE=40,r.SIMPLE_NODE_HALF_SIZE=r.SIMPLE_NODE_SIZE/2,r.EMPTY_COMPOUND_NODE_SIZE=40,r.MIN_EDGE_LENGTH=1,r.WORLD_BOUNDARY=1e6,r.INITIAL_WORLD_BOUNDARY=r.WORLD_BOUNDARY/1e3,r.WORLD_CENTER_X=1200,r.WORLD_CENTER_Y=900,u.exports=r},function(u,m,v){var r=v(2),t=v(8),e=v(9);function i(g,a,y){r.call(this,y),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=y,this.bendpoints=[],this.source=g,this.target=a}i.prototype=Object.create(r.prototype);for(var o in r)i[o]=r[o];i.prototype.getSource=function(){return this.source},i.prototype.getTarget=function(){return this.target},i.prototype.isInterGraph=function(){return this.isInterGraph},i.prototype.getLength=function(){return this.length},i.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},i.prototype.getBendpoints=function(){return this.bendpoints},i.prototype.getLca=function(){return this.lca},i.prototype.getSourceInLca=function(){return this.sourceInLca},i.prototype.getTargetInLca=function(){return this.targetInLca},i.prototype.getOtherEnd=function(g){if(this.source===g)return this.target;if(this.target===g)return this.source;throw"Node is not incident with this edge"},i.prototype.getOtherEndInGraph=function(g,a){for(var y=this.getOtherEnd(g),n=a.getGraphManager().getRoot();;){if(y.getOwner()==a)return y;if(y.getOwner()==n)break;y=y.getOwner().getParent()}return null},i.prototype.updateLength=function(){var g=new Array(4);this.isOverlapingSourceAndTarget=t.getIntersection(this.target.getRect(),this.source.getRect(),g),this.isOverlapingSourceAndTarget||(this.lengthX=g[0]-g[2],this.lengthY=g[1]-g[3],Math.abs(this.lengthX)<1&&(this.lengthX=e.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=e.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},i.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=e.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=e.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},u.exports=i},function(u,m,v){function r(t){this.vGraphObject=t}u.exports=r},function(u,m,v){var r=v(2),t=v(10),e=v(13),i=v(0),o=v(16),g=v(4);function a(n,h,c,E){c==null&&E==null&&(E=h),r.call(this,E),n.graphManager!=null&&(n=n.graphManager),this.estimatedSize=t.MIN_VALUE,this.inclusionTreeDepth=t.MAX_VALUE,this.vGraphObject=E,this.edges=[],this.graphManager=n,c!=null&&h!=null?this.rect=new e(h.x,h.y,c.width,c.height):this.rect=new e}a.prototype=Object.create(r.prototype);for(var y in r)a[y]=r[y];a.prototype.getEdges=function(){return this.edges},a.prototype.getChild=function(){return this.child},a.prototype.getOwner=function(){return this.owner},a.prototype.getWidth=function(){return this.rect.width},a.prototype.setWidth=function(n){this.rect.width=n},a.prototype.getHeight=function(){return this.rect.height},a.prototype.setHeight=function(n){this.rect.height=n},a.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},a.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},a.prototype.getCenter=function(){return new g(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},a.prototype.getLocation=function(){return new g(this.rect.x,this.rect.y)},a.prototype.getRect=function(){return this.rect},a.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},a.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},a.prototype.setRect=function(n,h){this.rect.x=n.x,this.rect.y=n.y,this.rect.width=h.width,this.rect.height=h.height},a.prototype.setCenter=function(n,h){this.rect.x=n-this.rect.width/2,this.rect.y=h-this.rect.height/2},a.prototype.setLocation=function(n,h){this.rect.x=n,this.rect.y=h},a.prototype.moveBy=function(n,h){this.rect.x+=n,this.rect.y+=h},a.prototype.getEdgeListToNode=function(n){var h=[],c=this;return c.edges.forEach(function(E){if(E.target==n){if(E.source!=c)throw"Incorrect edge source!";h.push(E)}}),h},a.prototype.getEdgesBetween=function(n){var h=[],c=this;return c.edges.forEach(function(E){if(!(E.source==c||E.target==c))throw"Incorrect edge source and/or target";(E.target==n||E.source==n)&&h.push(E)}),h},a.prototype.getNeighborsList=function(){var n=new Set,h=this;return h.edges.forEach(function(c){if(c.source==h)n.add(c.target);else{if(c.target!=h)throw"Incorrect incidency!";n.add(c.source)}}),n},a.prototype.withChildren=function(){var n=new Set,h,c;if(n.add(this),this.child!=null)for(var E=this.child.getNodes(),T=0;T<E.length;T++)h=E[T],c=h.withChildren(),c.forEach(function(D){n.add(D)});return n},a.prototype.getNoOfChildren=function(){var n=0,h;if(this.child==null)n=1;else for(var c=this.child.getNodes(),E=0;E<c.length;E++)h=c[E],n+=h.getNoOfChildren();return n==0&&(n=1),n},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},a.prototype.scatter=function(){var n,h,c=-i.INITIAL_WORLD_BOUNDARY,E=i.INITIAL_WORLD_BOUNDARY;n=i.WORLD_CENTER_X+o.nextDouble()*(E-c)+c;var T=-i.INITIAL_WORLD_BOUNDARY,D=i.INITIAL_WORLD_BOUNDARY;h=i.WORLD_CENTER_Y+o.nextDouble()*(D-T)+T,this.rect.x=n,this.rect.y=h},a.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var n=this.getChild();if(n.updateBounds(!0),this.rect.x=n.getLeft(),this.rect.y=n.getTop(),this.setWidth(n.getRight()-n.getLeft()),this.setHeight(n.getBottom()-n.getTop()),i.NODE_DIMENSIONS_INCLUDE_LABELS){var h=n.getRight()-n.getLeft(),c=n.getBottom()-n.getTop();this.labelWidth>h&&(this.rect.x-=(this.labelWidth-h)/2,this.setWidth(this.labelWidth)),this.labelHeight>c&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-c)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-c),this.setHeight(this.labelHeight))}}},a.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==t.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},a.prototype.transform=function(n){var h=this.rect.x;h>i.WORLD_BOUNDARY?h=i.WORLD_BOUNDARY:h<-i.WORLD_BOUNDARY&&(h=-i.WORLD_BOUNDARY);var c=this.rect.y;c>i.WORLD_BOUNDARY?c=i.WORLD_BOUNDARY:c<-i.WORLD_BOUNDARY&&(c=-i.WORLD_BOUNDARY);var E=new g(h,c),T=n.inverseTransformPoint(E);this.setLocation(T.x,T.y)},a.prototype.getLeft=function(){return this.rect.x},a.prototype.getRight=function(){return this.rect.x+this.rect.width},a.prototype.getTop=function(){return this.rect.y},a.prototype.getBottom=function(){return this.rect.y+this.rect.height},a.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},u.exports=a},function(u,m,v){function r(t,e){t==null&&e==null?(this.x=0,this.y=0):(this.x=t,this.y=e)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.setX=function(t){this.x=t},r.prototype.setY=function(t){this.y=t},r.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},r.prototype.getCopy=function(){return new r(this.x,this.y)},r.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},u.exports=r},function(u,m,v){var r=v(2),t=v(10),e=v(0),i=v(6),o=v(3),g=v(1),a=v(13),y=v(12),n=v(11);function h(E,T,D){r.call(this,D),this.estimatedSize=t.MIN_VALUE,this.margin=e.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=E,T!=null&&T instanceof i?this.graphManager=T:T!=null&&T instanceof Layout&&(this.graphManager=T.graphManager)}h.prototype=Object.create(r.prototype);for(var c in r)h[c]=r[c];h.prototype.getNodes=function(){return this.nodes},h.prototype.getEdges=function(){return this.edges},h.prototype.getGraphManager=function(){return this.graphManager},h.prototype.getParent=function(){return this.parent},h.prototype.getLeft=function(){return this.left},h.prototype.getRight=function(){return this.right},h.prototype.getTop=function(){return this.top},h.prototype.getBottom=function(){return this.bottom},h.prototype.isConnected=function(){return this.isConnected},h.prototype.add=function(E,T,D){if(T==null&&D==null){var L=E;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(L)>-1)throw"Node already in graph!";return L.owner=this,this.getNodes().push(L),L}else{var O=E;if(!(this.getNodes().indexOf(T)>-1&&this.getNodes().indexOf(D)>-1))throw"Source or target not in graph!";if(!(T.owner==D.owner&&T.owner==this))throw"Both owners must be this graph!";return T.owner!=D.owner?null:(O.source=T,O.target=D,O.isInterGraph=!1,this.getEdges().push(O),T.edges.push(O),D!=T&&D.edges.push(O),O)}},h.prototype.remove=function(E){var T=E;if(E instanceof o){if(T==null)throw"Node is null!";if(!(T.owner!=null&&T.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var D=T.edges.slice(),L,O=D.length,d=0;d<O;d++)L=D[d],L.isInterGraph?this.graphManager.remove(L):L.source.owner.remove(L);var N=this.nodes.indexOf(T);if(N==-1)throw"Node not in owner node list!";this.nodes.splice(N,1)}else if(E instanceof g){var L=E;if(L==null)throw"Edge is null!";if(!(L.source!=null&&L.target!=null))throw"Source and/or target is null!";if(!(L.source.owner!=null&&L.target.owner!=null&&L.source.owner==this&&L.target.owner==this))throw"Source and/or target owner is invalid!";var s=L.source.edges.indexOf(L),l=L.target.edges.indexOf(L);if(!(s>-1&&l>-1))throw"Source and/or target doesn't know this edge!";L.source.edges.splice(s,1),L.target!=L.source&&L.target.edges.splice(l,1);var N=L.source.owner.getEdges().indexOf(L);if(N==-1)throw"Not in owner's edge list!";L.source.owner.getEdges().splice(N,1)}},h.prototype.updateLeftTop=function(){for(var E=t.MAX_VALUE,T=t.MAX_VALUE,D,L,O,d=this.getNodes(),N=d.length,s=0;s<N;s++){var l=d[s];D=l.getTop(),L=l.getLeft(),E>D&&(E=D),T>L&&(T=L)}return E==t.MAX_VALUE?null:(d[0].getParent().paddingLeft!=null?O=d[0].getParent().paddingLeft:O=this.margin,this.left=T-O,this.top=E-O,new y(this.left,this.top))},h.prototype.updateBounds=function(E){for(var T=t.MAX_VALUE,D=-t.MAX_VALUE,L=t.MAX_VALUE,O=-t.MAX_VALUE,d,N,s,l,f,p=this.nodes,I=p.length,C=0;C<I;C++){var R=p[C];E&&R.child!=null&&R.updateBounds(),d=R.getLeft(),N=R.getRight(),s=R.getTop(),l=R.getBottom(),T>d&&(T=d),D<N&&(D=N),L>s&&(L=s),O<l&&(O=l)}var x=new a(T,L,D-T,O-L);T==t.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),p[0].getParent().paddingLeft!=null?f=p[0].getParent().paddingLeft:f=this.margin,this.left=x.x-f,this.right=x.x+x.width+f,this.top=x.y-f,this.bottom=x.y+x.height+f},h.calculateBounds=function(E){for(var T=t.MAX_VALUE,D=-t.MAX_VALUE,L=t.MAX_VALUE,O=-t.MAX_VALUE,d,N,s,l,f=E.length,p=0;p<f;p++){var I=E[p];d=I.getLeft(),N=I.getRight(),s=I.getTop(),l=I.getBottom(),T>d&&(T=d),D<N&&(D=N),L>s&&(L=s),O<l&&(O=l)}var C=new a(T,L,D-T,O-L);return C},h.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},h.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},h.prototype.calcEstimatedSize=function(){for(var E=0,T=this.nodes,D=T.length,L=0;L<D;L++){var O=T[L];E+=O.calcEstimatedSize()}return E==0?this.estimatedSize=e.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=E/Math.sqrt(this.nodes.length),this.estimatedSize},h.prototype.updateConnected=function(){var E=this;if(this.nodes.length==0){this.isConnected=!0;return}var T=new n,D=new Set,L=this.nodes[0],O,d,N=L.withChildren();for(N.forEach(function(C){T.push(C),D.add(C)});T.length!==0;){L=T.shift(),O=L.getEdges();for(var s=O.length,l=0;l<s;l++){var f=O[l];if(d=f.getOtherEndInGraph(L,this),d!=null&&!D.has(d)){var p=d.withChildren();p.forEach(function(C){T.push(C),D.add(C)})}}}if(this.isConnected=!1,D.size>=this.nodes.length){var I=0;D.forEach(function(C){C.owner==E&&I++}),I==this.nodes.length&&(this.isConnected=!0)}},u.exports=h},function(u,m,v){var r,t=v(1);function e(i){r=v(5),this.layout=i,this.graphs=[],this.edges=[]}e.prototype.addRoot=function(){var i=this.layout.newGraph(),o=this.layout.newNode(null),g=this.add(i,o);return this.setRootGraph(g),this.rootGraph},e.prototype.add=function(i,o,g,a,y){if(g==null&&a==null&&y==null){if(i==null)throw"Graph is null!";if(o==null)throw"Parent node is null!";if(this.graphs.indexOf(i)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(i),i.parent!=null)throw"Already has a parent!";if(o.child!=null)throw"Already has a child!";return i.parent=o,o.child=i,i}else{y=g,a=o,g=i;var n=a.getOwner(),h=y.getOwner();if(!(n!=null&&n.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(h!=null&&h.getGraphManager()==this))throw"Target not in this graph mgr!";if(n==h)return g.isInterGraph=!1,n.add(g,a,y);if(g.isInterGraph=!0,g.source=a,g.target=y,this.edges.indexOf(g)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(g),!(g.source!=null&&g.target!=null))throw"Edge source and/or target is null!";if(!(g.source.edges.indexOf(g)==-1&&g.target.edges.indexOf(g)==-1))throw"Edge already in source and/or target incidency list!";return g.source.edges.push(g),g.target.edges.push(g),g}},e.prototype.remove=function(i){if(i instanceof r){var o=i;if(o.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(o==this.rootGraph||o.parent!=null&&o.parent.graphManager==this))throw"Invalid parent node!";var g=[];g=g.concat(o.getEdges());for(var a,y=g.length,n=0;n<y;n++)a=g[n],o.remove(a);var h=[];h=h.concat(o.getNodes());var c;y=h.length;for(var n=0;n<y;n++)c=h[n],o.remove(c);o==this.rootGraph&&this.setRootGraph(null);var E=this.graphs.indexOf(o);this.graphs.splice(E,1),o.parent=null}else if(i instanceof t){if(a=i,a==null)throw"Edge is null!";if(!a.isInterGraph)throw"Not an inter-graph edge!";if(!(a.source!=null&&a.target!=null))throw"Source and/or target is null!";if(!(a.source.edges.indexOf(a)!=-1&&a.target.edges.indexOf(a)!=-1))throw"Source and/or target doesn't know this edge!";var E=a.source.edges.indexOf(a);if(a.source.edges.splice(E,1),E=a.target.edges.indexOf(a),a.target.edges.splice(E,1),!(a.source.owner!=null&&a.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(a.source.owner.getGraphManager().edges.indexOf(a)==-1)throw"Not in owner graph manager's edge list!";var E=a.source.owner.getGraphManager().edges.indexOf(a);a.source.owner.getGraphManager().edges.splice(E,1)}},e.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},e.prototype.getGraphs=function(){return this.graphs},e.prototype.getAllNodes=function(){if(this.allNodes==null){for(var i=[],o=this.getGraphs(),g=o.length,a=0;a<g;a++)i=i.concat(o[a].getNodes());this.allNodes=i}return this.allNodes},e.prototype.resetAllNodes=function(){this.allNodes=null},e.prototype.resetAllEdges=function(){this.allEdges=null},e.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},e.prototype.getAllEdges=function(){if(this.allEdges==null){var i=[],o=this.getGraphs();o.length;for(var g=0;g<o.length;g++)i=i.concat(o[g].getEdges());i=i.concat(this.edges),this.allEdges=i}return this.allEdges},e.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},e.prototype.setAllNodesToApplyGravitation=function(i){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=i},e.prototype.getRoot=function(){return this.rootGraph},e.prototype.setRootGraph=function(i){if(i.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=i,i.parent==null&&(i.parent=this.layout.newNode("Root node"))},e.prototype.getLayout=function(){return this.layout},e.prototype.isOneAncestorOfOther=function(i,o){if(!(i!=null&&o!=null))throw"assert failed";if(i==o)return!0;var g=i.getOwner(),a;do{if(a=g.getParent(),a==null)break;if(a==o)return!0;if(g=a.getOwner(),g==null)break}while(!0);g=o.getOwner();do{if(a=g.getParent(),a==null)break;if(a==i)return!0;if(g=a.getOwner(),g==null)break}while(!0);return!1},e.prototype.calcLowestCommonAncestors=function(){for(var i,o,g,a,y,n=this.getAllEdges(),h=n.length,c=0;c<h;c++){if(i=n[c],o=i.source,g=i.target,i.lca=null,i.sourceInLca=o,i.targetInLca=g,o==g){i.lca=o.getOwner();continue}for(a=o.getOwner();i.lca==null;){for(i.targetInLca=g,y=g.getOwner();i.lca==null;){if(y==a){i.lca=y;break}if(y==this.rootGraph)break;if(i.lca!=null)throw"assert failed";i.targetInLca=y.getParent(),y=i.targetInLca.getOwner()}if(a==this.rootGraph)break;i.lca==null&&(i.sourceInLca=a.getParent(),a=i.sourceInLca.getOwner())}if(i.lca==null)throw"assert failed"}},e.prototype.calcLowestCommonAncestor=function(i,o){if(i==o)return i.getOwner();var g=i.getOwner();do{if(g==null)break;var a=o.getOwner();do{if(a==null)break;if(a==g)return a;a=a.getParent().getOwner()}while(!0);g=g.getParent().getOwner()}while(!0);return g},e.prototype.calcInclusionTreeDepths=function(i,o){i==null&&o==null&&(i=this.rootGraph,o=1);for(var g,a=i.getNodes(),y=a.length,n=0;n<y;n++)g=a[n],g.inclusionTreeDepth=o,g.child!=null&&this.calcInclusionTreeDepths(g.child,o+1)},e.prototype.includesInvalidEdge=function(){for(var i,o=this.edges.length,g=0;g<o;g++)if(i=this.edges[g],this.isOneAncestorOfOther(i.source,i.target))return!0;return!1},u.exports=e},function(u,m,v){var r=v(0);function t(){}for(var e in r)t[e]=r[e];t.MAX_ITERATIONS=2500,t.DEFAULT_EDGE_LENGTH=50,t.DEFAULT_SPRING_STRENGTH=.45,t.DEFAULT_REPULSION_STRENGTH=4500,t.DEFAULT_GRAVITY_STRENGTH=.4,t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,t.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,t.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,t.COOLING_ADAPTATION_FACTOR=.33,t.ADAPTATION_LOWER_NODE_LIMIT=1e3,t.ADAPTATION_UPPER_NODE_LIMIT=5e3,t.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,t.MAX_NODE_DISPLACEMENT=t.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,t.MIN_REPULSION_DIST=t.DEFAULT_EDGE_LENGTH/10,t.CONVERGENCE_CHECK_PERIOD=100,t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,t.MIN_EDGE_LENGTH=1,t.GRID_CALCULATION_CHECK_PERIOD=10,u.exports=t},function(u,m,v){var r=v(12);function t(){}t.calcSeparationAmount=function(e,i,o,g){if(!e.intersects(i))throw"assert failed";var a=new Array(2);this.decideDirectionsForOverlappingNodes(e,i,a),o[0]=Math.min(e.getRight(),i.getRight())-Math.max(e.x,i.x),o[1]=Math.min(e.getBottom(),i.getBottom())-Math.max(e.y,i.y),e.getX()<=i.getX()&&e.getRight()>=i.getRight()?o[0]+=Math.min(i.getX()-e.getX(),e.getRight()-i.getRight()):i.getX()<=e.getX()&&i.getRight()>=e.getRight()&&(o[0]+=Math.min(e.getX()-i.getX(),i.getRight()-e.getRight())),e.getY()<=i.getY()&&e.getBottom()>=i.getBottom()?o[1]+=Math.min(i.getY()-e.getY(),e.getBottom()-i.getBottom()):i.getY()<=e.getY()&&i.getBottom()>=e.getBottom()&&(o[1]+=Math.min(e.getY()-i.getY(),i.getBottom()-e.getBottom()));var y=Math.abs((i.getCenterY()-e.getCenterY())/(i.getCenterX()-e.getCenterX()));i.getCenterY()===e.getCenterY()&&i.getCenterX()===e.getCenterX()&&(y=1);var n=y*o[0],h=o[1]/y;o[0]<h?h=o[0]:n=o[1],o[0]=-1*a[0]*(h/2+g),o[1]=-1*a[1]*(n/2+g)},t.decideDirectionsForOverlappingNodes=function(e,i,o){e.getCenterX()<i.getCenterX()?o[0]=-1:o[0]=1,e.getCenterY()<i.getCenterY()?o[1]=-1:o[1]=1},t.getIntersection2=function(e,i,o){var g=e.getCenterX(),a=e.getCenterY(),y=i.getCenterX(),n=i.getCenterY();if(e.intersects(i))return o[0]=g,o[1]=a,o[2]=y,o[3]=n,!0;var h=e.getX(),c=e.getY(),E=e.getRight(),T=e.getX(),D=e.getBottom(),L=e.getRight(),O=e.getWidthHalf(),d=e.getHeightHalf(),N=i.getX(),s=i.getY(),l=i.getRight(),f=i.getX(),p=i.getBottom(),I=i.getRight(),C=i.getWidthHalf(),R=i.getHeightHalf(),x=!1,_=!1;if(g===y){if(a>n)return o[0]=g,o[1]=c,o[2]=y,o[3]=p,!1;if(a<n)return o[0]=g,o[1]=D,o[2]=y,o[3]=s,!1}else if(a===n){if(g>y)return o[0]=h,o[1]=a,o[2]=l,o[3]=n,!1;if(g<y)return o[0]=E,o[1]=a,o[2]=N,o[3]=n,!1}else{var U=e.height/e.width,X=i.height/i.width,M=(n-a)/(y-g),G=void 0,F=void 0,b=void 0,Y=void 0,k=void 0,H=void 0;if(-U===M?g>y?(o[0]=T,o[1]=D,x=!0):(o[0]=E,o[1]=c,x=!0):U===M&&(g>y?(o[0]=h,o[1]=c,x=!0):(o[0]=L,o[1]=D,x=!0)),-X===M?y>g?(o[2]=f,o[3]=p,_=!0):(o[2]=l,o[3]=s,_=!0):X===M&&(y>g?(o[2]=N,o[3]=s,_=!0):(o[2]=I,o[3]=p,_=!0)),x&&_)return!1;if(g>y?a>n?(G=this.getCardinalDirection(U,M,4),F=this.getCardinalDirection(X,M,2)):(G=this.getCardinalDirection(-U,M,3),F=this.getCardinalDirection(-X,M,1)):a>n?(G=this.getCardinalDirection(-U,M,1),F=this.getCardinalDirection(-X,M,3)):(G=this.getCardinalDirection(U,M,2),F=this.getCardinalDirection(X,M,4)),!x)switch(G){case 1:Y=c,b=g+-d/M,o[0]=b,o[1]=Y;break;case 2:b=L,Y=a+O*M,o[0]=b,o[1]=Y;break;case 3:Y=D,b=g+d/M,o[0]=b,o[1]=Y;break;case 4:b=T,Y=a+-O*M,o[0]=b,o[1]=Y;break}if(!_)switch(F){case 1:H=s,k=y+-R/M,o[2]=k,o[3]=H;break;case 2:k=I,H=n+C*M,o[2]=k,o[3]=H;break;case 3:H=p,k=y+R/M,o[2]=k,o[3]=H;break;case 4:k=f,H=n+-C*M,o[2]=k,o[3]=H;break}}return!1},t.getCardinalDirection=function(e,i,o){return e>i?o:1+o%4},t.getIntersection=function(e,i,o,g){if(g==null)return this.getIntersection2(e,i,o);var a=e.x,y=e.y,n=i.x,h=i.y,c=o.x,E=o.y,T=g.x,D=g.y,L=void 0,O=void 0,d=void 0,N=void 0,s=void 0,l=void 0,f=void 0,p=void 0,I=void 0;return d=h-y,s=a-n,f=n*y-a*h,N=D-E,l=c-T,p=T*E-c*D,I=d*l-N*s,I===0?null:(L=(s*p-l*f)/I,O=(N*f-d*p)/I,new r(L,O))},t.angleOfVector=function(e,i,o,g){var a=void 0;return e!==o?(a=Math.atan((g-i)/(o-e)),o<e?a+=Math.PI:g<i&&(a+=this.TWO_PI)):g<i?a=this.ONE_AND_HALF_PI:a=this.HALF_PI,a},t.doIntersect=function(e,i,o,g){var a=e.x,y=e.y,n=i.x,h=i.y,c=o.x,E=o.y,T=g.x,D=g.y,L=(n-a)*(D-E)-(T-c)*(h-y);if(L===0)return!1;var O=((D-E)*(T-a)+(c-T)*(D-y))/L,d=((y-h)*(T-a)+(n-a)*(D-y))/L;return 0<O&&O<1&&0<d&&d<1},t.HALF_PI=.5*Math.PI,t.ONE_AND_HALF_PI=1.5*Math.PI,t.TWO_PI=2*Math.PI,t.THREE_PI=3*Math.PI,u.exports=t},function(u,m,v){function r(){}r.sign=function(t){return t>0?1:t<0?-1:0},r.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},r.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},u.exports=r},function(u,m,v){function r(){}r.MAX_VALUE=2147483647,r.MIN_VALUE=-2147483648,u.exports=r},function(u,m,v){var r=function(){function a(y,n){for(var h=0;h<n.length;h++){var c=n[h];c.enumerable=c.enumerable||!1,c.configurable=!0,"value"in c&&(c.writable=!0),Object.defineProperty(y,c.key,c)}}return function(y,n,h){return n&&a(y.prototype,n),h&&a(y,h),y}}();function t(a,y){if(!(a instanceof y))throw new TypeError("Cannot call a class as a function")}var e=function(y){return{value:y,next:null,prev:null}},i=function(y,n,h,c){return y!==null?y.next=n:c.head=n,h!==null?h.prev=n:c.tail=n,n.prev=y,n.next=h,c.length++,n},o=function(y,n){var h=y.prev,c=y.next;return h!==null?h.next=c:n.head=c,c!==null?c.prev=h:n.tail=h,y.prev=y.next=null,n.length--,y},g=function(){function a(y){var n=this;t(this,a),this.length=0,this.head=null,this.tail=null,y!=null&&y.forEach(function(h){return n.push(h)})}return r(a,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(n,h){return i(h.prev,e(n),h,this)}},{key:"insertAfter",value:function(n,h){return i(h,e(n),h.next,this)}},{key:"insertNodeBefore",value:function(n,h){return i(h.prev,n,h,this)}},{key:"insertNodeAfter",value:function(n,h){return i(h,n,h.next,this)}},{key:"push",value:function(n){return i(this.tail,e(n),null,this)}},{key:"unshift",value:function(n){return i(null,e(n),this.head,this)}},{key:"remove",value:function(n){return o(n,this)}},{key:"pop",value:function(){return o(this.tail,this).value}},{key:"popNode",value:function(){return o(this.tail,this)}},{key:"shift",value:function(){return o(this.head,this).value}},{key:"shiftNode",value:function(){return o(this.head,this)}},{key:"get_object_at",value:function(n){if(n<=this.length()){for(var h=1,c=this.head;h<n;)c=c.next,h++;return c.value}}},{key:"set_object_at",value:function(n,h){if(n<=this.length()){for(var c=1,E=this.head;c<n;)E=E.next,c++;E.value=h}}}]),a}();u.exports=g},function(u,m,v){function r(t,e,i){this.x=null,this.y=null,t==null&&e==null&&i==null?(this.x=0,this.y=0):typeof t=="number"&&typeof e=="number"&&i==null?(this.x=t,this.y=e):t.constructor.name=="Point"&&e==null&&i==null&&(i=t,this.x=i.x,this.y=i.y)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.getLocation=function(){return new r(this.x,this.y)},r.prototype.setLocation=function(t,e,i){t.constructor.name=="Point"&&e==null&&i==null?(i=t,this.setLocation(i.x,i.y)):typeof t=="number"&&typeof e=="number"&&i==null&&(parseInt(t)==t&&parseInt(e)==e?this.move(t,e):(this.x=Math.floor(t+.5),this.y=Math.floor(e+.5)))},r.prototype.move=function(t,e){this.x=t,this.y=e},r.prototype.translate=function(t,e){this.x+=t,this.y+=e},r.prototype.equals=function(t){if(t.constructor.name=="Point"){var e=t;return this.x==e.x&&this.y==e.y}return this==t},r.prototype.toString=function(){return new r().constructor.name+"[x="+this.x+",y="+this.y+"]"},u.exports=r},function(u,m,v){function r(t,e,i,o){this.x=0,this.y=0,this.width=0,this.height=0,t!=null&&e!=null&&i!=null&&o!=null&&(this.x=t,this.y=e,this.width=i,this.height=o)}r.prototype.getX=function(){return this.x},r.prototype.setX=function(t){this.x=t},r.prototype.getY=function(){return this.y},r.prototype.setY=function(t){this.y=t},r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(t){this.width=t},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(t){this.height=t},r.prototype.getRight=function(){return this.x+this.width},r.prototype.getBottom=function(){return this.y+this.height},r.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},r.prototype.getCenterX=function(){return this.x+this.width/2},r.prototype.getMinX=function(){return this.getX()},r.prototype.getMaxX=function(){return this.getX()+this.width},r.prototype.getCenterY=function(){return this.y+this.height/2},r.prototype.getMinY=function(){return this.getY()},r.prototype.getMaxY=function(){return this.getY()+this.height},r.prototype.getWidthHalf=function(){return this.width/2},r.prototype.getHeightHalf=function(){return this.height/2},u.exports=r},function(u,m,v){var r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function t(){}t.lastID=0,t.createID=function(e){return t.isPrimitive(e)?e:(e.uniqueID!=null||(e.uniqueID=t.getString(),t.lastID++),e.uniqueID)},t.getString=function(e){return e==null&&(e=t.lastID),"Object#"+e},t.isPrimitive=function(e){var i=typeof e=="undefined"?"undefined":r(e);return e==null||i!="object"&&i!="function"},u.exports=t},function(u,m,v){function r(c){if(Array.isArray(c)){for(var E=0,T=Array(c.length);E<c.length;E++)T[E]=c[E];return T}else return Array.from(c)}var t=v(0),e=v(6),i=v(3),o=v(1),g=v(5),a=v(4),y=v(17),n=v(27);function h(c){n.call(this),this.layoutQuality=t.QUALITY,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=t.DEFAULT_INCREMENTAL,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new e(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,c!=null&&(this.isRemoteUse=c)}h.RANDOM_SEED=1,h.prototype=Object.create(n.prototype),h.prototype.getGraphManager=function(){return this.graphManager},h.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},h.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},h.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},h.prototype.newGraphManager=function(){var c=new e(this);return this.graphManager=c,c},h.prototype.newGraph=function(c){return new g(null,this.graphManager,c)},h.prototype.newNode=function(c){return new i(this.graphManager,c)},h.prototype.newEdge=function(c){return new o(null,null,c)},h.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},h.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var c;return this.checkLayoutSuccess()?c=!1:c=this.layout(),t.ANIMATE==="during"?!1:(c&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,c)},h.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},h.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var c=this.graphManager.getAllEdges(),E=0;E<c.length;E++)c[E];for(var T=this.graphManager.getRoot().getNodes(),E=0;E<T.length;E++)T[E];this.update(this.graphManager.getRoot())}},h.prototype.update=function(c){if(c==null)this.update2();else if(c instanceof i){var E=c;if(E.getChild()!=null)for(var T=E.getChild().getNodes(),D=0;D<T.length;D++)update(T[D]);if(E.vGraphObject!=null){var L=E.vGraphObject;L.update(E)}}else if(c instanceof o){var O=c;if(O.vGraphObject!=null){var d=O.vGraphObject;d.update(O)}}else if(c instanceof g){var N=c;if(N.vGraphObject!=null){var s=N.vGraphObject;s.update(N)}}},h.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=t.QUALITY,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=t.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},h.prototype.transform=function(c){if(c==null)this.transform(new a(0,0));else{var E=new y,T=this.graphManager.getRoot().updateLeftTop();if(T!=null){E.setWorldOrgX(c.x),E.setWorldOrgY(c.y),E.setDeviceOrgX(T.x),E.setDeviceOrgY(T.y);for(var D=this.getAllNodes(),L,O=0;O<D.length;O++)L=D[O],L.transform(E)}}},h.prototype.positionNodesRandomly=function(c){if(c==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var E,T,D=c.getNodes(),L=0;L<D.length;L++)E=D[L],T=E.getChild(),T==null||T.getNodes().length==0?E.scatter():(this.positionNodesRandomly(T),E.updateBounds())},h.prototype.getFlatForest=function(){for(var c=[],E=!0,T=this.graphManager.getRoot().getNodes(),D=!0,L=0;L<T.length;L++)T[L].getChild()!=null&&(D=!1);if(!D)return c;var O=new Set,d=[],N=new Map,s=[];for(s=s.concat(T);s.length>0&&E;){for(d.push(s[0]);d.length>0&&E;){var l=d[0];d.splice(0,1),O.add(l);for(var f=l.getEdges(),L=0;L<f.length;L++){var p=f[L].getOtherEnd(l);if(N.get(l)!=p)if(!O.has(p))d.push(p),N.set(p,l);else{E=!1;break}}}if(!E)c=[];else{var I=[].concat(r(O));c.push(I);for(var L=0;L<I.length;L++){var C=I[L],R=s.indexOf(C);R>-1&&s.splice(R,1)}O=new Set,N=new Map}}return c},h.prototype.createDummyNodesForBendpoints=function(c){for(var E=[],T=c.source,D=this.graphManager.calcLowestCommonAncestor(c.source,c.target),L=0;L<c.bendpoints.length;L++){var O=this.newNode(null);O.setRect(new Point(0,0),new Dimension(1,1)),D.add(O);var d=this.newEdge(null);this.graphManager.add(d,T,O),E.add(O),T=O}var d=this.newEdge(null);return this.graphManager.add(d,T,c.target),this.edgeToDummyNodes.set(c,E),c.isInterGraph()?this.graphManager.remove(c):D.remove(c),E},h.prototype.createBendpointsFromDummyNodes=function(){var c=[];c=c.concat(this.graphManager.getAllEdges()),c=[].concat(r(this.edgeToDummyNodes.keys())).concat(c);for(var E=0;E<c.length;E++){var T=c[E];if(T.bendpoints.length>0){for(var D=this.edgeToDummyNodes.get(T),L=0;L<D.length;L++){var O=D[L],d=new a(O.getCenterX(),O.getCenterY()),N=T.bendpoints.get(L);N.x=d.x,N.y=d.y,O.getOwner().remove(O)}this.graphManager.add(T,T.source,T.target)}}},h.transform=function(c,E,T,D){if(T!=null&&D!=null){var L=E;if(c<=50){var O=E/T;L-=(E-O)/50*(50-c)}else{var d=E*D;L+=(d-E)/50*(c-50)}return L}else{var N,s;return c<=50?(N=9*E/500,s=E/10):(N=9*E/50,s=-8*E),N*c+s}},h.findCenterOfTree=function(c){var E=[];E=E.concat(c);var T=[],D=new Map,L=!1,O=null;(E.length==1||E.length==2)&&(L=!0,O=E[0]);for(var d=0;d<E.length;d++){var N=E[d],s=N.getNeighborsList().size;D.set(N,N.getNeighborsList().size),s==1&&T.push(N)}var l=[];for(l=l.concat(T);!L;){var f=[];f=f.concat(l),l=[];for(var d=0;d<E.length;d++){var N=E[d],p=E.indexOf(N);p>=0&&E.splice(p,1);var I=N.getNeighborsList();I.forEach(function(x){if(T.indexOf(x)<0){var _=D.get(x),U=_-1;U==1&&l.push(x),D.set(x,U)}})}T=T.concat(l),(E.length==1||E.length==2)&&(L=!0,O=E[0])}return O},h.prototype.setGraphManager=function(c){this.graphManager=c},u.exports=h},function(u,m,v){function r(){}r.seed=1,r.x=0,r.nextDouble=function(){return r.x=Math.sin(r.seed++)*1e4,r.x-Math.floor(r.x)},u.exports=r},function(u,m,v){var r=v(4);function t(e,i){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}t.prototype.getWorldOrgX=function(){return this.lworldOrgX},t.prototype.setWorldOrgX=function(e){this.lworldOrgX=e},t.prototype.getWorldOrgY=function(){return this.lworldOrgY},t.prototype.setWorldOrgY=function(e){this.lworldOrgY=e},t.prototype.getWorldExtX=function(){return this.lworldExtX},t.prototype.setWorldExtX=function(e){this.lworldExtX=e},t.prototype.getWorldExtY=function(){return this.lworldExtY},t.prototype.setWorldExtY=function(e){this.lworldExtY=e},t.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},t.prototype.setDeviceOrgX=function(e){this.ldeviceOrgX=e},t.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},t.prototype.setDeviceOrgY=function(e){this.ldeviceOrgY=e},t.prototype.getDeviceExtX=function(){return this.ldeviceExtX},t.prototype.setDeviceExtX=function(e){this.ldeviceExtX=e},t.prototype.getDeviceExtY=function(){return this.ldeviceExtY},t.prototype.setDeviceExtY=function(e){this.ldeviceExtY=e},t.prototype.transformX=function(e){var i=0,o=this.lworldExtX;return o!=0&&(i=this.ldeviceOrgX+(e-this.lworldOrgX)*this.ldeviceExtX/o),i},t.prototype.transformY=function(e){var i=0,o=this.lworldExtY;return o!=0&&(i=this.ldeviceOrgY+(e-this.lworldOrgY)*this.ldeviceExtY/o),i},t.prototype.inverseTransformX=function(e){var i=0,o=this.ldeviceExtX;return o!=0&&(i=this.lworldOrgX+(e-this.ldeviceOrgX)*this.lworldExtX/o),i},t.prototype.inverseTransformY=function(e){var i=0,o=this.ldeviceExtY;return o!=0&&(i=this.lworldOrgY+(e-this.ldeviceOrgY)*this.lworldExtY/o),i},t.prototype.inverseTransformPoint=function(e){var i=new r(this.inverseTransformX(e.x),this.inverseTransformY(e.y));return i},u.exports=t},function(u,m,v){function r(n){if(Array.isArray(n)){for(var h=0,c=Array(n.length);h<n.length;h++)c[h]=n[h];return c}else return Array.from(n)}var t=v(15),e=v(7),i=v(0),o=v(8),g=v(9);function a(){t.call(this),this.useSmartIdealEdgeLengthCalculation=e.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=e.DEFAULT_EDGE_LENGTH,this.springConstant=e.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=e.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=e.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=e.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=e.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*e.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=e.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=e.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=e.MAX_ITERATIONS}a.prototype=Object.create(t.prototype);for(var y in t)a[y]=t[y];a.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=e.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},a.prototype.calcIdealEdgeLengths=function(){for(var n,h,c,E,T,D,L=this.getGraphManager().getAllEdges(),O=0;O<L.length;O++)n=L[O],n.idealLength=this.idealEdgeLength,n.isInterGraph&&(c=n.getSource(),E=n.getTarget(),T=n.getSourceInLca().getEstimatedSize(),D=n.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(n.idealLength+=T+D-2*i.SIMPLE_NODE_SIZE),h=n.getLca().getInclusionTreeDepth(),n.idealLength+=e.DEFAULT_EDGE_LENGTH*e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+E.getInclusionTreeDepth()-2*h))},a.prototype.initSpringEmbedder=function(){var n=this.getAllNodes().length;this.incremental?(n>e.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*e.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(n-e.ADAPTATION_LOWER_NODE_LIMIT)/(e.ADAPTATION_UPPER_NODE_LIMIT-e.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-e.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=e.MAX_NODE_DISPLACEMENT_INCREMENTAL):(n>e.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(e.COOLING_ADAPTATION_FACTOR,1-(n-e.ADAPTATION_LOWER_NODE_LIMIT)/(e.ADAPTATION_UPPER_NODE_LIMIT-e.ADAPTATION_LOWER_NODE_LIMIT)*(1-e.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=e.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},a.prototype.calcSpringForces=function(){for(var n=this.getAllEdges(),h,c=0;c<n.length;c++)h=n[c],this.calcSpringForce(h,h.idealLength)},a.prototype.calcRepulsionForces=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c,E,T,D,L=this.getAllNodes(),O;if(this.useFRGridVariant)for(this.totalIterations%e.GRID_CALCULATION_CHECK_PERIOD==1&&n&&this.updateGrid(),O=new Set,c=0;c<L.length;c++)T=L[c],this.calculateRepulsionForceOfANode(T,O,n,h),O.add(T);else for(c=0;c<L.length;c++)for(T=L[c],E=c+1;E<L.length;E++)D=L[E],T.getOwner()==D.getOwner()&&this.calcRepulsionForce(T,D)},a.prototype.calcGravitationalForces=function(){for(var n,h=this.getAllNodesToApplyGravitation(),c=0;c<h.length;c++)n=h[c],this.calcGravitationalForce(n)},a.prototype.moveNodes=function(){for(var n=this.getAllNodes(),h,c=0;c<n.length;c++)h=n[c],h.move()},a.prototype.calcSpringForce=function(n,h){var c=n.getSource(),E=n.getTarget(),T,D,L,O;if(this.uniformLeafNodeSizes&&c.getChild()==null&&E.getChild()==null)n.updateLengthSimple();else if(n.updateLength(),n.isOverlapingSourceAndTarget)return;T=n.getLength(),T!=0&&(D=this.springConstant*(T-h),L=D*(n.lengthX/T),O=D*(n.lengthY/T),c.springForceX+=L,c.springForceY+=O,E.springForceX-=L,E.springForceY-=O)},a.prototype.calcRepulsionForce=function(n,h){var c=n.getRect(),E=h.getRect(),T=new Array(2),D=new Array(4),L,O,d,N,s,l,f;if(c.intersects(E)){o.calcSeparationAmount(c,E,T,e.DEFAULT_EDGE_LENGTH/2),l=2*T[0],f=2*T[1];var p=n.noOfChildren*h.noOfChildren/(n.noOfChildren+h.noOfChildren);n.repulsionForceX-=p*l,n.repulsionForceY-=p*f,h.repulsionForceX+=p*l,h.repulsionForceY+=p*f}else this.uniformLeafNodeSizes&&n.getChild()==null&&h.getChild()==null?(L=E.getCenterX()-c.getCenterX(),O=E.getCenterY()-c.getCenterY()):(o.getIntersection(c,E,D),L=D[2]-D[0],O=D[3]-D[1]),Math.abs(L)<e.MIN_REPULSION_DIST&&(L=g.sign(L)*e.MIN_REPULSION_DIST),Math.abs(O)<e.MIN_REPULSION_DIST&&(O=g.sign(O)*e.MIN_REPULSION_DIST),d=L*L+O*O,N=Math.sqrt(d),s=this.repulsionConstant*n.noOfChildren*h.noOfChildren/d,l=s*L/N,f=s*O/N,n.repulsionForceX-=l,n.repulsionForceY-=f,h.repulsionForceX+=l,h.repulsionForceY+=f},a.prototype.calcGravitationalForce=function(n){var h,c,E,T,D,L,O,d;h=n.getOwner(),c=(h.getRight()+h.getLeft())/2,E=(h.getTop()+h.getBottom())/2,T=n.getCenterX()-c,D=n.getCenterY()-E,L=Math.abs(T)+n.getWidth()/2,O=Math.abs(D)+n.getHeight()/2,n.getOwner()==this.graphManager.getRoot()?(d=h.getEstimatedSize()*this.gravityRangeFactor,(L>d||O>d)&&(n.gravitationForceX=-this.gravityConstant*T,n.gravitationForceY=-this.gravityConstant*D)):(d=h.getEstimatedSize()*this.compoundGravityRangeFactor,(L>d||O>d)&&(n.gravitationForceX=-this.gravityConstant*T*this.compoundGravityConstant,n.gravitationForceY=-this.gravityConstant*D*this.compoundGravityConstant))},a.prototype.isConverged=function(){var n,h=!1;return this.totalIterations>this.maxIterations/3&&(h=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),n=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,n||h},a.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},a.prototype.calcNoOfChildrenForAllNodes=function(){for(var n,h=this.graphManager.getAllNodes(),c=0;c<h.length;c++)n=h[c],n.noOfChildren=n.getNoOfChildren()},a.prototype.calcGrid=function(n){var h=0,c=0;h=parseInt(Math.ceil((n.getRight()-n.getLeft())/this.repulsionRange)),c=parseInt(Math.ceil((n.getBottom()-n.getTop())/this.repulsionRange));for(var E=new Array(h),T=0;T<h;T++)E[T]=new Array(c);for(var T=0;T<h;T++)for(var D=0;D<c;D++)E[T][D]=new Array;return E},a.prototype.addNodeToGrid=function(n,h,c){var E=0,T=0,D=0,L=0;E=parseInt(Math.floor((n.getRect().x-h)/this.repulsionRange)),T=parseInt(Math.floor((n.getRect().width+n.getRect().x-h)/this.repulsionRange)),D=parseInt(Math.floor((n.getRect().y-c)/this.repulsionRange)),L=parseInt(Math.floor((n.getRect().height+n.getRect().y-c)/this.repulsionRange));for(var O=E;O<=T;O++)for(var d=D;d<=L;d++)this.grid[O][d].push(n),n.setGridCoordinates(E,T,D,L)},a.prototype.updateGrid=function(){var n,h,c=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),n=0;n<c.length;n++)h=c[n],this.addNodeToGrid(h,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},a.prototype.calculateRepulsionForceOfANode=function(n,h,c,E){if(this.totalIterations%e.GRID_CALCULATION_CHECK_PERIOD==1&&c||E){var T=new Set;n.surrounding=new Array;for(var D,L=this.grid,O=n.startX-1;O<n.finishX+2;O++)for(var d=n.startY-1;d<n.finishY+2;d++)if(!(O<0||d<0||O>=L.length||d>=L[0].length)){for(var N=0;N<L[O][d].length;N++)if(D=L[O][d][N],!(n.getOwner()!=D.getOwner()||n==D)&&!h.has(D)&&!T.has(D)){var s=Math.abs(n.getCenterX()-D.getCenterX())-(n.getWidth()/2+D.getWidth()/2),l=Math.abs(n.getCenterY()-D.getCenterY())-(n.getHeight()/2+D.getHeight()/2);s<=this.repulsionRange&&l<=this.repulsionRange&&T.add(D)}}n.surrounding=[].concat(r(T))}for(O=0;O<n.surrounding.length;O++)this.calcRepulsionForce(n,n.surrounding[O])},a.prototype.calcRepulsionRange=function(){return 0},u.exports=a},function(u,m,v){var r=v(1),t=v(7);function e(o,g,a){r.call(this,o,g,a),this.idealLength=t.DEFAULT_EDGE_LENGTH}e.prototype=Object.create(r.prototype);for(var i in r)e[i]=r[i];u.exports=e},function(u,m,v){var r=v(3);function t(i,o,g,a){r.call(this,i,o,g,a),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}t.prototype=Object.create(r.prototype);for(var e in r)t[e]=r[e];t.prototype.setGridCoordinates=function(i,o,g,a){this.startX=i,this.finishX=o,this.startY=g,this.finishY=a},u.exports=t},function(u,m,v){function r(t,e){this.width=0,this.height=0,t!==null&&e!==null&&(this.height=e,this.width=t)}r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(t){this.width=t},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(t){this.height=t},u.exports=r},function(u,m,v){var r=v(14);function t(){this.map={},this.keys=[]}t.prototype.put=function(e,i){var o=r.createID(e);this.contains(o)||(this.map[o]=i,this.keys.push(e))},t.prototype.contains=function(e){return r.createID(e),this.map[e]!=null},t.prototype.get=function(e){var i=r.createID(e);return this.map[i]},t.prototype.keySet=function(){return this.keys},u.exports=t},function(u,m,v){var r=v(14);function t(){this.set={}}t.prototype.add=function(e){var i=r.createID(e);this.contains(i)||(this.set[i]=e)},t.prototype.remove=function(e){delete this.set[r.createID(e)]},t.prototype.clear=function(){this.set={}},t.prototype.contains=function(e){return this.set[r.createID(e)]==e},t.prototype.isEmpty=function(){return this.size()===0},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAllTo=function(e){for(var i=Object.keys(this.set),o=i.length,g=0;g<o;g++)e.push(this.set[i[g]])},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAll=function(e){for(var i=e.length,o=0;o<i;o++){var g=e[o];this.add(g)}},u.exports=t},function(u,m,v){var r=function(){function o(g,a){for(var y=0;y<a.length;y++){var n=a[y];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(g,n.key,n)}}return function(g,a,y){return a&&o(g.prototype,a),y&&o(g,y),g}}();function t(o,g){if(!(o instanceof g))throw new TypeError("Cannot call a class as a function")}var e=v(11),i=function(){function o(g,a){t(this,o),(a!==null||a!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var y=void 0;g instanceof e?y=g.size():y=g.length,this._quicksort(g,0,y-1)}return r(o,[{key:"_quicksort",value:function(a,y,n){if(y<n){var h=this._partition(a,y,n);this._quicksort(a,y,h),this._quicksort(a,h+1,n)}}},{key:"_partition",value:function(a,y,n){for(var h=this._get(a,y),c=y,E=n;;){for(;this.compareFunction(h,this._get(a,E));)E--;for(;this.compareFunction(this._get(a,c),h);)c++;if(c<E)this._swap(a,c,E),c++,E--;else return E}}},{key:"_get",value:function(a,y){return a instanceof e?a.get_object_at(y):a[y]}},{key:"_set",value:function(a,y,n){a instanceof e?a.set_object_at(y,n):a[y]=n}},{key:"_swap",value:function(a,y,n){var h=this._get(a,y);this._set(a,y,this._get(a,n)),this._set(a,n,h)}},{key:"_defaultCompareFunction",value:function(a,y){return y>a}}]),o}();u.exports=i},function(u,m,v){var r=function(){function i(o,g){for(var a=0;a<g.length;a++){var y=g[a];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(o,y.key,y)}}return function(o,g,a){return g&&i(o.prototype,g),a&&i(o,a),o}}();function t(i,o){if(!(i instanceof o))throw new TypeError("Cannot call a class as a function")}var e=function(){function i(o,g){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,y=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;t(this,i),this.sequence1=o,this.sequence2=g,this.match_score=a,this.mismatch_penalty=y,this.gap_penalty=n,this.iMax=o.length+1,this.jMax=g.length+1,this.grid=new Array(this.iMax);for(var h=0;h<this.iMax;h++){this.grid[h]=new Array(this.jMax);for(var c=0;c<this.jMax;c++)this.grid[h][c]=0}this.tracebackGrid=new Array(this.iMax);for(var E=0;E<this.iMax;E++){this.tracebackGrid[E]=new Array(this.jMax);for(var T=0;T<this.jMax;T++)this.tracebackGrid[E][T]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return r(i,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var g=1;g<this.jMax;g++)this.grid[0][g]=this.grid[0][g-1]+this.gap_penalty,this.tracebackGrid[0][g]=[!1,!1,!0];for(var a=1;a<this.iMax;a++)this.grid[a][0]=this.grid[a-1][0]+this.gap_penalty,this.tracebackGrid[a][0]=[!1,!0,!1];for(var y=1;y<this.iMax;y++)for(var n=1;n<this.jMax;n++){var h=void 0;this.sequence1[y-1]===this.sequence2[n-1]?h=this.grid[y-1][n-1]+this.match_score:h=this.grid[y-1][n-1]+this.mismatch_penalty;var c=this.grid[y-1][n]+this.gap_penalty,E=this.grid[y][n-1]+this.gap_penalty,T=[h,c,E],D=this.arrayAllMaxIndexes(T);this.grid[y][n]=T[D[0]],this.tracebackGrid[y][n]=[D.includes(0),D.includes(1),D.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var g=[];for(g.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});g[0];){var a=g[0],y=this.tracebackGrid[a.pos[0]][a.pos[1]];y[0]&&g.push({pos:[a.pos[0]-1,a.pos[1]-1],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),y[1]&&g.push({pos:[a.pos[0]-1,a.pos[1]],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:"-"+a.seq2}),y[2]&&g.push({pos:[a.pos[0],a.pos[1]-1],seq1:"-"+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),a.pos[0]===0&&a.pos[1]===0&&this.alignments.push({sequence1:a.seq1,sequence2:a.seq2}),g.shift()}return this.alignments}},{key:"getAllIndexes",value:function(g,a){for(var y=[],n=-1;(n=g.indexOf(a,n+1))!==-1;)y.push(n);return y}},{key:"arrayAllMaxIndexes",value:function(g){return this.getAllIndexes(g,Math.max.apply(null,g))}}]),i}();u.exports=e},function(u,m,v){var r=function(){};r.FDLayout=v(18),r.FDLayoutConstants=v(7),r.FDLayoutEdge=v(19),r.FDLayoutNode=v(20),r.DimensionD=v(21),r.HashMap=v(22),r.HashSet=v(23),r.IGeometry=v(8),r.IMath=v(9),r.Integer=v(10),r.Point=v(12),r.PointD=v(4),r.RandomSeed=v(16),r.RectangleD=v(13),r.Transform=v(17),r.UniqueIDGeneretor=v(14),r.Quicksort=v(24),r.LinkedList=v(11),r.LGraphObject=v(2),r.LGraph=v(5),r.LEdge=v(1),r.LGraphManager=v(6),r.LNode=v(3),r.Layout=v(15),r.LayoutConstants=v(0),r.NeedlemanWunsch=v(25),u.exports=r},function(u,m,v){function r(){this.listeners=[]}var t=r.prototype;t.addListener=function(e,i){this.listeners.push({event:e,callback:i})},t.removeListener=function(e,i){for(var o=this.listeners.length;o>=0;o--){var g=this.listeners[o];g.event===e&&g.callback===i&&this.listeners.splice(o,1)}},t.emit=function(e,i){for(var o=0;o<this.listeners.length;o++){var g=this.listeners[o];e===g.event&&g.callback(i)}},u.exports=r}])})}(st)),st.exports}var pt;function St(){return pt||(pt=1,function(A,w){(function(m,v){A.exports=v(wt())})(ht,function(u){return function(m){var v={};function r(t){if(v[t])return v[t].exports;var e=v[t]={i:t,l:!1,exports:{}};return m[t].call(e.exports,e,e.exports,r),e.l=!0,e.exports}return r.m=m,r.c=v,r.i=function(t){return t},r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=7)}([function(m,v){m.exports=u},function(m,v,r){var t=r(0).FDLayoutConstants;function e(){}for(var i in t)e[i]=t[i];e.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,e.DEFAULT_RADIAL_SEPARATION=t.DEFAULT_EDGE_LENGTH,e.DEFAULT_COMPONENT_SEPERATION=60,e.TILE=!0,e.TILING_PADDING_VERTICAL=10,e.TILING_PADDING_HORIZONTAL=10,e.TREE_REDUCTION_ON_INCREMENTAL=!1,m.exports=e},function(m,v,r){var t=r(0).FDLayoutEdge;function e(o,g,a){t.call(this,o,g,a)}e.prototype=Object.create(t.prototype);for(var i in t)e[i]=t[i];m.exports=e},function(m,v,r){var t=r(0).LGraph;function e(o,g,a){t.call(this,o,g,a)}e.prototype=Object.create(t.prototype);for(var i in t)e[i]=t[i];m.exports=e},function(m,v,r){var t=r(0).LGraphManager;function e(o){t.call(this,o)}e.prototype=Object.create(t.prototype);for(var i in t)e[i]=t[i];m.exports=e},function(m,v,r){var t=r(0).FDLayoutNode,e=r(0).IMath;function i(g,a,y,n){t.call(this,g,a,y,n)}i.prototype=Object.create(t.prototype);for(var o in t)i[o]=t[o];i.prototype.move=function(){var g=this.graphManager.getLayout();this.displacementX=g.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=g.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementX=g.coolingFactor*g.maxNodeDisplacement*e.sign(this.displacementX)),Math.abs(this.displacementY)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementY=g.coolingFactor*g.maxNodeDisplacement*e.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),g.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},i.prototype.propogateDisplacementToChildren=function(g,a){for(var y=this.getChild().getNodes(),n,h=0;h<y.length;h++)n=y[h],n.getChild()==null?(n.moveBy(g,a),n.displacementX+=g,n.displacementY+=a):n.propogateDisplacementToChildren(g,a)},i.prototype.setPred1=function(g){this.pred1=g},i.prototype.getPred1=function(){return pred1},i.prototype.getPred2=function(){return pred2},i.prototype.setNext=function(g){this.next=g},i.prototype.getNext=function(){return next},i.prototype.setProcessed=function(g){this.processed=g},i.prototype.isProcessed=function(){return processed},m.exports=i},function(m,v,r){var t=r(0).FDLayout,e=r(4),i=r(3),o=r(5),g=r(2),a=r(1),y=r(0).FDLayoutConstants,n=r(0).LayoutConstants,h=r(0).Point,c=r(0).PointD,E=r(0).Layout,T=r(0).Integer,D=r(0).IGeometry,L=r(0).LGraph,O=r(0).Transform;function d(){t.call(this),this.toBeTiled={}}d.prototype=Object.create(t.prototype);for(var N in t)d[N]=t[N];d.prototype.newGraphManager=function(){var s=new e(this);return this.graphManager=s,s},d.prototype.newGraph=function(s){return new i(null,this.graphManager,s)},d.prototype.newNode=function(s){return new o(this.graphManager,s)},d.prototype.newEdge=function(s){return new g(null,null,s)},d.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.isSubLayout||(a.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=a.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=y.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=y.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=y.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=y.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=y.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=y.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/y.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=y.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},d.prototype.layout=function(){var s=n.DEFAULT_CREATE_BENDS_AS_NEEDED;return s&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},d.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(a.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var l=new Set(this.getAllNodes()),f=this.nodesWithGravity.filter(function(C){return l.has(C)});this.graphManager.setAllNodesToApplyGravitation(f)}}else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var l=new Set(this.getAllNodes()),f=this.nodesWithGravity.filter(function(p){return l.has(p)});this.graphManager.setAllNodesToApplyGravitation(f),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},d.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%y.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),l=this.nodesWithGravity.filter(function(I){return s.has(I)});this.graphManager.setAllNodesToApplyGravitation(l),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=y.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=y.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var f=!this.isTreeGrowing&&!this.isGrowthFinished,p=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(f,p),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},d.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),l={},f=0;f<s.length;f++){var p=s[f].rect,I=s[f].id;l[I]={id:I,x:p.getCenterX(),y:p.getCenterY(),w:p.width,h:p.height}}return l},d.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(y.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},d.prototype.calculateNodesToApplyGravitationTo=function(){var s=[],l,f=this.graphManager.getGraphs(),p=f.length,I;for(I=0;I<p;I++)l=f[I],l.updateConnected(),l.isConnected||(s=s.concat(l.getNodes()));return s},d.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var l=new Set,f;for(f=0;f<s.length;f++){var p=s[f];if(!l.has(p)){var I=p.getSource(),C=p.getTarget();if(I==C)p.getBendpoints().push(new c),p.getBendpoints().push(new c),this.createDummyNodesForBendpoints(p),l.add(p);else{var R=[];if(R=R.concat(I.getEdgeListToNode(C)),R=R.concat(C.getEdgeListToNode(I)),!l.has(R[0])){if(R.length>1){var x;for(x=0;x<R.length;x++){var _=R[x];_.getBendpoints().push(new c),this.createDummyNodesForBendpoints(_)}}R.forEach(function(U){l.add(U)})}}}if(l.size==s.length)break}},d.prototype.positionNodesRadially=function(s){for(var l=new h(0,0),f=Math.ceil(Math.sqrt(s.length)),p=0,I=0,C=0,R=new c(0,0),x=0;x<s.length;x++){x%f==0&&(C=0,I=p,x!=0&&(I+=a.DEFAULT_COMPONENT_SEPERATION),p=0);var _=s[x],U=E.findCenterOfTree(_);l.x=C,l.y=I,R=d.radialLayout(_,U,l),R.y>p&&(p=Math.floor(R.y)),C=Math.floor(R.x+a.DEFAULT_COMPONENT_SEPERATION)}this.transform(new c(n.WORLD_CENTER_X-R.x/2,n.WORLD_CENTER_Y-R.y/2))},d.radialLayout=function(s,l,f){var p=Math.max(this.maxDiagonalInTree(s),a.DEFAULT_RADIAL_SEPARATION);d.branchRadialLayout(l,null,0,359,0,p);var I=L.calculateBounds(s),C=new O;C.setDeviceOrgX(I.getMinX()),C.setDeviceOrgY(I.getMinY()),C.setWorldOrgX(f.x),C.setWorldOrgY(f.y);for(var R=0;R<s.length;R++){var x=s[R];x.transform(C)}var _=new c(I.getMaxX(),I.getMaxY());return C.inverseTransformPoint(_)},d.branchRadialLayout=function(s,l,f,p,I,C){var R=(p-f+1)/2;R<0&&(R+=180);var x=(R+f)%360,_=x*D.TWO_PI/360,U=I*Math.cos(_),X=I*Math.sin(_);s.setCenter(U,X);var M=[];M=M.concat(s.getEdges());var G=M.length;l!=null&&G--;for(var F=0,b=M.length,Y,k=s.getEdgesBetween(l);k.length>1;){var H=k[0];k.splice(0,1);var P=M.indexOf(H);P>=0&&M.splice(P,1),b--,G--}l!=null?Y=(M.indexOf(k[0])+1)%b:Y=0;for(var B=Math.abs(p-f)/G,$=Y;F!=G;$=++$%b){var K=M[$].getOtherEnd(s);if(K!=l){var Q=(f+F*B)%360,J=(Q+B)%360;d.branchRadialLayout(K,s,Q,J,I+C,C),F++}}},d.maxDiagonalInTree=function(s){for(var l=T.MIN_VALUE,f=0;f<s.length;f++){var p=s[f],I=p.getDiagonal();I>l&&(l=I)}return l},d.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},d.prototype.groupZeroDegreeMembers=function(){var s=this,l={};this.memberGroups={},this.idToDummyNode={};for(var f=[],p=this.graphManager.getAllNodes(),I=0;I<p.length;I++){var C=p[I],R=C.getParent();this.getNodeDegreeWithChildren(C)===0&&(R.id==null||!this.getToBeTiled(R))&&f.push(C)}for(var I=0;I<f.length;I++){var C=f[I],x=C.getParent().id;typeof l[x]=="undefined"&&(l[x]=[]),l[x]=l[x].concat(C)}Object.keys(l).forEach(function(_){if(l[_].length>1){var U="DummyCompound_"+_;s.memberGroups[U]=l[_];var X=l[_][0].getParent(),M=new o(s.graphManager);M.id=U,M.paddingLeft=X.paddingLeft||0,M.paddingRight=X.paddingRight||0,M.paddingBottom=X.paddingBottom||0,M.paddingTop=X.paddingTop||0,s.idToDummyNode[U]=M;var G=s.getGraphManager().add(s.newGraph(),M),F=X.getChild();F.add(M);for(var b=0;b<l[_].length;b++){var Y=l[_][b];F.remove(Y),G.add(Y)}}})},d.prototype.clearCompounds=function(){var s={},l={};this.performDFSOnCompounds();for(var f=0;f<this.compoundOrder.length;f++)l[this.compoundOrder[f].id]=this.compoundOrder[f],s[this.compoundOrder[f].id]=[].concat(this.compoundOrder[f].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[f].getChild()),this.compoundOrder[f].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,l)},d.prototype.clearZeroDegreeMembers=function(){var s=this,l=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(f){var p=s.idToDummyNode[f];l[f]=s.tileNodes(s.memberGroups[f],p.paddingLeft+p.paddingRight),p.rect.width=l[f].width,p.rect.height=l[f].height})},d.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var l=this.compoundOrder[s],f=l.id,p=l.paddingLeft,I=l.paddingTop;this.adjustLocations(this.tiledMemberPack[f],l.rect.x,l.rect.y,p,I)}},d.prototype.repopulateZeroDegreeMembers=function(){var s=this,l=this.tiledZeroDegreePack;Object.keys(l).forEach(function(f){var p=s.idToDummyNode[f],I=p.paddingLeft,C=p.paddingTop;s.adjustLocations(l[f],p.rect.x,p.rect.y,I,C)})},d.prototype.getToBeTiled=function(s){var l=s.id;if(this.toBeTiled[l]!=null)return this.toBeTiled[l];var f=s.getChild();if(f==null)return this.toBeTiled[l]=!1,!1;for(var p=f.getNodes(),I=0;I<p.length;I++){var C=p[I];if(this.getNodeDegree(C)>0)return this.toBeTiled[l]=!1,!1;if(C.getChild()==null){this.toBeTiled[C.id]=!1;continue}if(!this.getToBeTiled(C))return this.toBeTiled[l]=!1,!1}return this.toBeTiled[l]=!0,!0},d.prototype.getNodeDegree=function(s){s.id;for(var l=s.getEdges(),f=0,p=0;p<l.length;p++){var I=l[p];I.getSource().id!==I.getTarget().id&&(f=f+1)}return f},d.prototype.getNodeDegreeWithChildren=function(s){var l=this.getNodeDegree(s);if(s.getChild()==null)return l;for(var f=s.getChild().getNodes(),p=0;p<f.length;p++){var I=f[p];l+=this.getNodeDegreeWithChildren(I)}return l},d.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},d.prototype.fillCompexOrderByDFS=function(s){for(var l=0;l<s.length;l++){var f=s[l];f.getChild()!=null&&this.fillCompexOrderByDFS(f.getChild().getNodes()),this.getToBeTiled(f)&&this.compoundOrder.push(f)}},d.prototype.adjustLocations=function(s,l,f,p,I){l+=p,f+=I;for(var C=l,R=0;R<s.rows.length;R++){var x=s.rows[R];l=C;for(var _=0,U=0;U<x.length;U++){var X=x[U];X.rect.x=l,X.rect.y=f,l+=X.rect.width+s.horizontalPadding,X.rect.height>_&&(_=X.rect.height)}f+=_+s.verticalPadding}},d.prototype.tileCompoundMembers=function(s,l){var f=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(p){var I=l[p];f.tiledMemberPack[p]=f.tileNodes(s[p],I.paddingLeft+I.paddingRight),I.rect.width=f.tiledMemberPack[p].width,I.rect.height=f.tiledMemberPack[p].height})},d.prototype.tileNodes=function(s,l){var f=a.TILING_PADDING_VERTICAL,p=a.TILING_PADDING_HORIZONTAL,I={rows:[],rowWidth:[],rowHeight:[],width:0,height:l,verticalPadding:f,horizontalPadding:p};s.sort(function(x,_){return x.rect.width*x.rect.height>_.rect.width*_.rect.height?-1:x.rect.width*x.rect.height<_.rect.width*_.rect.height?1:0});for(var C=0;C<s.length;C++){var R=s[C];I.rows.length==0?this.insertNodeToRow(I,R,0,l):this.canAddHorizontal(I,R.rect.width,R.rect.height)?this.insertNodeToRow(I,R,this.getShortestRowIndex(I),l):this.insertNodeToRow(I,R,I.rows.length,l),this.shiftToLastRow(I)}return I},d.prototype.insertNodeToRow=function(s,l,f,p){var I=p;if(f==s.rows.length){var C=[];s.rows.push(C),s.rowWidth.push(I),s.rowHeight.push(0)}var R=s.rowWidth[f]+l.rect.width;s.rows[f].length>0&&(R+=s.horizontalPadding),s.rowWidth[f]=R,s.width<R&&(s.width=R);var x=l.rect.height;f>0&&(x+=s.verticalPadding);var _=0;x>s.rowHeight[f]&&(_=s.rowHeight[f],s.rowHeight[f]=x,_=s.rowHeight[f]-_),s.height+=_,s.rows[f].push(l)},d.prototype.getShortestRowIndex=function(s){for(var l=-1,f=Number.MAX_VALUE,p=0;p<s.rows.length;p++)s.rowWidth[p]<f&&(l=p,f=s.rowWidth[p]);return l},d.prototype.getLongestRowIndex=function(s){for(var l=-1,f=Number.MIN_VALUE,p=0;p<s.rows.length;p++)s.rowWidth[p]>f&&(l=p,f=s.rowWidth[p]);return l},d.prototype.canAddHorizontal=function(s,l,f){var p=this.getShortestRowIndex(s);if(p<0)return!0;var I=s.rowWidth[p];if(I+s.horizontalPadding+l<=s.width)return!0;var C=0;s.rowHeight[p]<f&&p>0&&(C=f+s.verticalPadding-s.rowHeight[p]);var R;s.width-I>=l+s.horizontalPadding?R=(s.height+C)/(I+l+s.horizontalPadding):R=(s.height+C)/s.width,C=f+s.verticalPadding;var x;return s.width<l?x=(s.height+C)/l:x=(s.height+C)/s.width,x<1&&(x=1/x),R<1&&(R=1/R),R<x},d.prototype.shiftToLastRow=function(s){var l=this.getLongestRowIndex(s),f=s.rowWidth.length-1,p=s.rows[l],I=p[p.length-1],C=I.width+s.horizontalPadding;if(s.width-s.rowWidth[f]>C&&l!=f){p.splice(-1,1),s.rows[f].push(I),s.rowWidth[l]=s.rowWidth[l]-C,s.rowWidth[f]=s.rowWidth[f]+C,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var R=Number.MIN_VALUE,x=0;x<p.length;x++)p[x].height>R&&(R=p[x].height);l>0&&(R+=s.verticalPadding);var _=s.rowHeight[l]+s.rowHeight[f];s.rowHeight[l]=R,s.rowHeight[f]<I.height+s.verticalPadding&&(s.rowHeight[f]=I.height+s.verticalPadding);var U=s.rowHeight[l]+s.rowHeight[f];s.height+=U-_,this.shiftToLastRow(s)}},d.prototype.tilingPreLayout=function(){a.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},d.prototype.tilingPostLayout=function(){a.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},d.prototype.reduceTrees=function(){for(var s=[],l=!0,f;l;){var p=this.graphManager.getAllNodes(),I=[];l=!1;for(var C=0;C<p.length;C++)f=p[C],f.getEdges().length==1&&!f.getEdges()[0].isInterGraph&&f.getChild()==null&&(I.push([f,f.getEdges()[0],f.getOwner()]),l=!0);if(l==!0){for(var R=[],x=0;x<I.length;x++)I[x][0].getEdges().length==1&&(R.push(I[x]),I[x][0].getOwner().remove(I[x][0]));s.push(R),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=s},d.prototype.growTree=function(s){for(var l=s.length,f=s[l-1],p,I=0;I<f.length;I++)p=f[I],this.findPlaceforPrunedNode(p),p[2].add(p[0]),p[2].add(p[1],p[1].source,p[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},d.prototype.findPlaceforPrunedNode=function(s){var l,f,p=s[0];p==s[1].source?f=s[1].target:f=s[1].source;var I=f.startX,C=f.finishX,R=f.startY,x=f.finishY,_=0,U=0,X=0,M=0,G=[_,X,U,M];if(R>0)for(var F=I;F<=C;F++)G[0]+=this.grid[F][R-1].length+this.grid[F][R].length-1;if(C<this.grid.length-1)for(var F=R;F<=x;F++)G[1]+=this.grid[C+1][F].length+this.grid[C][F].length-1;if(x<this.grid[0].length-1)for(var F=I;F<=C;F++)G[2]+=this.grid[F][x+1].length+this.grid[F][x].length-1;if(I>0)for(var F=R;F<=x;F++)G[3]+=this.grid[I-1][F].length+this.grid[I][F].length-1;for(var b=T.MAX_VALUE,Y,k,H=0;H<G.length;H++)G[H]<b?(b=G[H],Y=1,k=H):G[H]==b&&Y++;if(Y==3&&b==0)G[0]==0&&G[1]==0&&G[2]==0?l=1:G[0]==0&&G[1]==0&&G[3]==0?l=0:G[0]==0&&G[2]==0&&G[3]==0?l=3:G[1]==0&&G[2]==0&&G[3]==0&&(l=2);else if(Y==2&&b==0){var P=Math.floor(Math.random()*2);G[0]==0&&G[1]==0?P==0?l=0:l=1:G[0]==0&&G[2]==0?P==0?l=0:l=2:G[0]==0&&G[3]==0?P==0?l=0:l=3:G[1]==0&&G[2]==0?P==0?l=1:l=2:G[1]==0&&G[3]==0?P==0?l=1:l=3:P==0?l=2:l=3}else if(Y==4&&b==0){var P=Math.floor(Math.random()*4);l=P}else l=k;l==0?p.setCenter(f.getCenterX(),f.getCenterY()-f.getHeight()/2-y.DEFAULT_EDGE_LENGTH-p.getHeight()/2):l==1?p.setCenter(f.getCenterX()+f.getWidth()/2+y.DEFAULT_EDGE_LENGTH+p.getWidth()/2,f.getCenterY()):l==2?p.setCenter(f.getCenterX(),f.getCenterY()+f.getHeight()/2+y.DEFAULT_EDGE_LENGTH+p.getHeight()/2):p.setCenter(f.getCenterX()-f.getWidth()/2-y.DEFAULT_EDGE_LENGTH-p.getWidth()/2,f.getCenterY())},m.exports=d},function(m,v,r){var t={};t.layoutBase=r(0),t.CoSEConstants=r(1),t.CoSEEdge=r(2),t.CoSEGraph=r(3),t.CoSEGraphManager=r(4),t.CoSELayout=r(6),t.CoSENode=r(5),m.exports=t}])})}(nt)),nt.exports}(function(A,w){(function(m,v){A.exports=v(St())})(ht,function(u){return function(m){var v={};function r(t){if(v[t])return v[t].exports;var e=v[t]={i:t,l:!1,exports:{}};return m[t].call(e.exports,e,e.exports,r),e.l=!0,e.exports}return r.m=m,r.c=v,r.i=function(t){return t},r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(m,v){m.exports=u},function(m,v,r){var t=r(0).layoutBase.LayoutConstants,e=r(0).layoutBase.FDLayoutConstants,i=r(0).CoSEConstants,o=r(0).CoSELayout,g=r(0).CoSENode,a=r(0).layoutBase.PointD,y=r(0).layoutBase.DimensionD,n={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function h(D,L){var O={};for(var d in D)O[d]=D[d];for(var d in L)O[d]=L[d];return O}function c(D){this.options=h(n,D),E(this.options)}var E=function(L){L.nodeRepulsion!=null&&(i.DEFAULT_REPULSION_STRENGTH=e.DEFAULT_REPULSION_STRENGTH=L.nodeRepulsion),L.idealEdgeLength!=null&&(i.DEFAULT_EDGE_LENGTH=e.DEFAULT_EDGE_LENGTH=L.idealEdgeLength),L.edgeElasticity!=null&&(i.DEFAULT_SPRING_STRENGTH=e.DEFAULT_SPRING_STRENGTH=L.edgeElasticity),L.nestingFactor!=null&&(i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=L.nestingFactor),L.gravity!=null&&(i.DEFAULT_GRAVITY_STRENGTH=e.DEFAULT_GRAVITY_STRENGTH=L.gravity),L.numIter!=null&&(i.MAX_ITERATIONS=e.MAX_ITERATIONS=L.numIter),L.gravityRange!=null&&(i.DEFAULT_GRAVITY_RANGE_FACTOR=e.DEFAULT_GRAVITY_RANGE_FACTOR=L.gravityRange),L.gravityCompound!=null&&(i.DEFAULT_COMPOUND_GRAVITY_STRENGTH=e.DEFAULT_COMPOUND_GRAVITY_STRENGTH=L.gravityCompound),L.gravityRangeCompound!=null&&(i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=L.gravityRangeCompound),L.initialEnergyOnIncremental!=null&&(i.DEFAULT_COOLING_FACTOR_INCREMENTAL=e.DEFAULT_COOLING_FACTOR_INCREMENTAL=L.initialEnergyOnIncremental),L.quality=="draft"?t.QUALITY=0:L.quality=="proof"?t.QUALITY=2:t.QUALITY=1,i.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=t.NODE_DIMENSIONS_INCLUDE_LABELS=L.nodeDimensionsIncludeLabels,i.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=t.DEFAULT_INCREMENTAL=!L.randomize,i.ANIMATE=e.ANIMATE=t.ANIMATE=L.animate,i.TILE=L.tile,i.TILING_PADDING_VERTICAL=typeof L.tilingPaddingVertical=="function"?L.tilingPaddingVertical.call():L.tilingPaddingVertical,i.TILING_PADDING_HORIZONTAL=typeof L.tilingPaddingHorizontal=="function"?L.tilingPaddingHorizontal.call():L.tilingPaddingHorizontal};c.prototype.run=function(){var D,L,O=this.options;this.idToLNode={};var d=this.layout=new o,N=this;N.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var s=d.newGraphManager();this.gm=s;var l=this.options.eles.nodes(),f=this.options.eles.edges();this.root=s.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(l),d);for(var p=0;p<f.length;p++){var I=f[p],C=this.idToLNode[I.data("source")],R=this.idToLNode[I.data("target")];if(C!==R&&C.getEdgesBetween(R).length==0){var x=s.add(d.newEdge(),C,R);x.id=I.id()}}var _=function(M,G){typeof M=="number"&&(M=G);var F=M.data("id"),b=N.idToLNode[F];return{x:b.getRect().getCenterX(),y:b.getRect().getCenterY()}},U=function X(){for(var M=function(){O.fit&&O.cy.fit(O.eles,O.padding),D||(D=!0,N.cy.one("layoutready",O.ready),N.cy.trigger({type:"layoutready",layout:N}))},G=N.options.refresh,F,b=0;b<G&&!F;b++)F=N.stopped||N.layout.tick();if(F){d.checkLayoutSuccess()&&!d.isSubLayout&&d.doPostLayout(),d.tilingPostLayout&&d.tilingPostLayout(),d.isLayoutFinished=!0,N.options.eles.nodes().positions(_),M(),N.cy.one("layoutstop",N.options.stop),N.cy.trigger({type:"layoutstop",layout:N}),L&&cancelAnimationFrame(L),D=!1;return}var Y=N.layout.getPositionsData();O.eles.nodes().positions(function(k,H){if(typeof k=="number"&&(k=H),!k.isParent()){for(var P=k.id(),B=Y[P],$=k;B==null&&(B=Y[$.data("parent")]||Y["DummyCompound_"+$.data("parent")],Y[P]=B,$=$.parent()[0],$!=null););return B!=null?{x:B.x,y:B.y}:{x:k.position("x"),y:k.position("y")}}}),M(),L=requestAnimationFrame(X)};return d.addListener("layoutstarted",function(){N.options.animate==="during"&&(L=requestAnimationFrame(U))}),d.runLayout(),this.options.animate!=="during"&&(N.options.eles.nodes().not(":parent").layoutPositions(N,N.options,_),D=!1),this},c.prototype.getTopMostNodes=function(D){for(var L={},O=0;O<D.length;O++)L[D[O].id()]=!0;var d=D.filter(function(N,s){typeof N=="number"&&(N=s);for(var l=N.parent()[0];l!=null;){if(L[l.id()])return!1;l=l.parent()[0]}return!0});return d},c.prototype.processChildrenList=function(D,L,O){for(var d=L.length,N=0;N<d;N++){var s=L[N],l=s.children(),f,p=s.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(s.outerWidth()!=null&&s.outerHeight()!=null?f=D.add(new g(O.graphManager,new a(s.position("x")-p.w/2,s.position("y")-p.h/2),new y(parseFloat(p.w),parseFloat(p.h)))):f=D.add(new g(this.graphManager)),f.id=s.data("id"),f.paddingLeft=parseInt(s.css("padding")),f.paddingTop=parseInt(s.css("padding")),f.paddingRight=parseInt(s.css("padding")),f.paddingBottom=parseInt(s.css("padding")),this.options.nodeDimensionsIncludeLabels&&s.isParent()){var I=s.boundingBox({includeLabels:!0,includeNodes:!1}).w,C=s.boundingBox({includeLabels:!0,includeNodes:!1}).h,R=s.css("text-halign");f.labelWidth=I,f.labelHeight=C,f.labelPos=R}if(this.idToLNode[s.data("id")]=f,isNaN(f.rect.x)&&(f.rect.x=0),isNaN(f.rect.y)&&(f.rect.y=0),l!=null&&l.length>0){var x;x=O.getGraphManager().add(O.newGraph(),f),this.processChildrenList(x,l,O)}}},c.prototype.stop=function(){return this.stopped=!0,this};var T=function(L){L("layout","cose-bilkent",c)};typeof cytoscape!="undefined"&&T(cytoscape),m.exports=T}])})})(vt);var Gt=vt.exports;const _t=Mt(Gt);var at=function(){var A=S(function(O,d,N,s){for(N=N||{},s=O.length;s--;N[O[s]]=d);return N},"o"),w=[1,4],u=[1,13],m=[1,12],v=[1,15],r=[1,16],t=[1,20],e=[1,19],i=[6,7,8],o=[1,26],g=[1,24],a=[1,25],y=[6,7,11],n=[1,6,13,15,16,19,22],h=[1,33],c=[1,34],E=[1,6,7,11,13,15,16,19,22],T={trace:S(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:S(function(d,N,s,l,f,p,I){var C=p.length-1;switch(f){case 6:case 7:return l;case 8:l.getLogger().trace("Stop NL ");break;case 9:l.getLogger().trace("Stop EOF ");break;case 11:l.getLogger().trace("Stop NL2 ");break;case 12:l.getLogger().trace("Stop EOF2 ");break;case 15:l.getLogger().info("Node: ",p[C].id),l.addNode(p[C-1].length,p[C].id,p[C].descr,p[C].type);break;case 16:l.getLogger().trace("Icon: ",p[C]),l.decorateNode({icon:p[C]});break;case 17:case 21:l.decorateNode({class:p[C]});break;case 18:l.getLogger().trace("SPACELIST");break;case 19:l.getLogger().trace("Node: ",p[C].id),l.addNode(0,p[C].id,p[C].descr,p[C].type);break;case 20:l.decorateNode({icon:p[C]});break;case 25:l.getLogger().trace("node found ..",p[C-2]),this.$={id:p[C-1],descr:p[C-1],type:l.getType(p[C-2],p[C])};break;case 26:this.$={id:p[C],descr:p[C],type:l.nodeType.DEFAULT};break;case 27:l.getLogger().trace("node found ..",p[C-3]),this.$={id:p[C-3],descr:p[C-1],type:l.getType(p[C-2],p[C])};break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:w},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:w},{6:u,7:[1,10],9:9,12:11,13:m,14:14,15:v,16:r,17:17,18:18,19:t,22:e},A(i,[2,3]),{1:[2,2]},A(i,[2,4]),A(i,[2,5]),{1:[2,6],6:u,12:21,13:m,14:14,15:v,16:r,17:17,18:18,19:t,22:e},{6:u,9:22,12:11,13:m,14:14,15:v,16:r,17:17,18:18,19:t,22:e},{6:o,7:g,10:23,11:a},A(y,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:t,22:e}),A(y,[2,18]),A(y,[2,19]),A(y,[2,20]),A(y,[2,21]),A(y,[2,23]),A(y,[2,24]),A(y,[2,26],{19:[1,30]}),{20:[1,31]},{6:o,7:g,10:32,11:a},{1:[2,7],6:u,12:21,13:m,14:14,15:v,16:r,17:17,18:18,19:t,22:e},A(n,[2,14],{7:h,11:c}),A(E,[2,8]),A(E,[2,9]),A(E,[2,10]),A(y,[2,15]),A(y,[2,16]),A(y,[2,17]),{20:[1,35]},{21:[1,36]},A(n,[2,13],{7:h,11:c}),A(E,[2,11]),A(E,[2,12]),{21:[1,37]},A(y,[2,25]),A(y,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:S(function(d,N){if(N.recoverable)this.trace(d);else{var s=new Error(d);throw s.hash=N,s}},"parseError"),parse:S(function(d){var N=this,s=[0],l=[],f=[null],p=[],I=this.table,C="",R=0,x=0,_=2,U=1,X=p.slice.call(arguments,1),M=Object.create(this.lexer),G={yy:{}};for(var F in this.yy)Object.prototype.hasOwnProperty.call(this.yy,F)&&(G.yy[F]=this.yy[F]);M.setInput(d,G.yy),G.yy.lexer=M,G.yy.parser=this,typeof M.yylloc=="undefined"&&(M.yylloc={});var b=M.yylloc;p.push(b);var Y=M.options&&M.options.ranges;typeof G.yy.parseError=="function"?this.parseError=G.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function k(V){s.length=s.length-2*V,f.length=f.length-V,p.length=p.length-V}S(k,"popStack");function H(){var V;return V=l.pop()||M.lex()||U,typeof V!="number"&&(V instanceof Array&&(l=V,V=l.pop()),V=N.symbols_[V]||V),V}S(H,"lex");for(var P,B,$,K,Q={},J,j,ut,q;;){if(B=s[s.length-1],this.defaultActions[B]?$=this.defaultActions[B]:((P===null||typeof P=="undefined")&&(P=H()),$=I[B]&&I[B][P]),typeof $=="undefined"||!$.length||!$[0]){var it="";q=[];for(J in I[B])this.terminals_[J]&&J>_&&q.push("'"+this.terminals_[J]+"'");M.showPosition?it="Parse error on line "+(R+1)+`:
`+M.showPosition()+`
Expecting `+q.join(", ")+", got '"+(this.terminals_[P]||P)+"'":it="Parse error on line "+(R+1)+": Unexpected "+(P==U?"end of input":"'"+(this.terminals_[P]||P)+"'"),this.parseError(it,{text:M.match,token:this.terminals_[P]||P,line:M.yylineno,loc:b,expected:q})}if($[0]instanceof Array&&$.length>1)throw new Error("Parse Error: multiple actions possible at state: "+B+", token: "+P);switch($[0]){case 1:s.push(P),f.push(M.yytext),p.push(M.yylloc),s.push($[1]),P=null,x=M.yyleng,C=M.yytext,R=M.yylineno,b=M.yylloc;break;case 2:if(j=this.productions_[$[1]][1],Q.$=f[f.length-j],Q._$={first_line:p[p.length-(j||1)].first_line,last_line:p[p.length-1].last_line,first_column:p[p.length-(j||1)].first_column,last_column:p[p.length-1].last_column},Y&&(Q._$.range=[p[p.length-(j||1)].range[0],p[p.length-1].range[1]]),K=this.performAction.apply(Q,[C,x,R,G.yy,$[1],f,p].concat(X)),typeof K!="undefined")return K;j&&(s=s.slice(0,-1*j*2),f=f.slice(0,-1*j),p=p.slice(0,-1*j)),s.push(this.productions_[$[1]][0]),f.push(Q.$),p.push(Q._$),ut=I[s[s.length-2]][s[s.length-1]],s.push(ut);break;case 3:return!0}}return!0},"parse")},D=function(){var O={EOF:1,parseError:S(function(N,s){if(this.yy.parser)this.yy.parser.parseError(N,s);else throw new Error(N)},"parseError"),setInput:S(function(d,N){return this.yy=N||this.yy||{},this._input=d,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:S(function(){var d=this._input[0];this.yytext+=d,this.yyleng++,this.offset++,this.match+=d,this.matched+=d;var N=d.match(/(?:\r\n?|\n).*/g);return N?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),d},"input"),unput:S(function(d){var N=d.length,s=d.split(/(?:\r\n?|\n)/g);this._input=d+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-N),this.offset-=N;var l=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var f=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===l.length?this.yylloc.first_column:0)+l[l.length-s.length].length-s[0].length:this.yylloc.first_column-N},this.options.ranges&&(this.yylloc.range=[f[0],f[0]+this.yyleng-N]),this.yyleng=this.yytext.length,this},"unput"),more:S(function(){return this._more=!0,this},"more"),reject:S(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:S(function(d){this.unput(this.match.slice(d))},"less"),pastInput:S(function(){var d=this.matched.substr(0,this.matched.length-this.match.length);return(d.length>20?"...":"")+d.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:S(function(){var d=this.match;return d.length<20&&(d+=this._input.substr(0,20-d.length)),(d.substr(0,20)+(d.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:S(function(){var d=this.pastInput(),N=new Array(d.length+1).join("-");return d+this.upcomingInput()+`
`+N+"^"},"showPosition"),test_match:S(function(d,N){var s,l,f;if(this.options.backtrack_lexer&&(f={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(f.yylloc.range=this.yylloc.range.slice(0))),l=d[0].match(/(?:\r\n?|\n).*/g),l&&(this.yylineno+=l.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:l?l[l.length-1].length-l[l.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+d[0].length},this.yytext+=d[0],this.match+=d[0],this.matches=d,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(d[0].length),this.matched+=d[0],s=this.performAction.call(this,this.yy,this,N,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var p in f)this[p]=f[p];return!1}return!1},"test_match"),next:S(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var d,N,s,l;this._more||(this.yytext="",this.match="");for(var f=this._currentRules(),p=0;p<f.length;p++)if(s=this._input.match(this.rules[f[p]]),s&&(!N||s[0].length>N[0].length)){if(N=s,l=p,this.options.backtrack_lexer){if(d=this.test_match(s,f[p]),d!==!1)return d;if(this._backtrack){N=!1;continue}else return!1}else if(!this.options.flex)break}return N?(d=this.test_match(N,f[l]),d!==!1?d:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:S(function(){var N=this.next();return N||this.lex()},"lex"),begin:S(function(N){this.conditionStack.push(N)},"begin"),popState:S(function(){var N=this.conditionStack.length-1;return N>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:S(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:S(function(N){return N=this.conditionStack.length-1-Math.abs(N||0),N>=0?this.conditionStack[N]:"INITIAL"},"topState"),pushState:S(function(N){this.begin(N)},"pushState"),stateStackSize:S(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:S(function(N,s,l,f){switch(l){case 0:return N.getLogger().trace("Found comment",s.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:this.popState();break;case 5:N.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return N.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:N.getLogger().trace("end icon"),this.popState();break;case 10:return N.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return N.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return N.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return N.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:return this.begin("NODE"),19;case 15:return this.begin("NODE"),19;case 16:return this.begin("NODE"),19;case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:N.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return N.getLogger().trace("description:",s.yytext),"NODE_DESCR";case 26:this.popState();break;case 27:return this.popState(),N.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),N.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),N.getLogger().trace("node end ...",s.yytext),"NODE_DEND";case 30:return this.popState(),N.getLogger().trace("node end (("),"NODE_DEND";case 31:return this.popState(),N.getLogger().trace("node end (-"),"NODE_DEND";case 32:return this.popState(),N.getLogger().trace("node end (-"),"NODE_DEND";case 33:return this.popState(),N.getLogger().trace("node end (("),"NODE_DEND";case 34:return this.popState(),N.getLogger().trace("node end (("),"NODE_DEND";case 35:return N.getLogger().trace("Long description:",s.yytext),20;case 36:return N.getLogger().trace("Long description:",s.yytext),20}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return O}();T.lexer=D;function L(){this.yy={}}return S(L,"Parser"),L.prototype=T,T.Parser=L,new L}();at.parser=at;var Ft=at,Z=[],yt=0,lt={},bt=S(()=>{Z=[],yt=0,lt={}},"clear"),Ut=S(function(A){for(let w=Z.length-1;w>=0;w--)if(Z[w].level<A)return Z[w];return null},"getParent"),Pt=S(()=>Z.length>0?Z[0]:null,"getMindmap"),Yt=S((A,w,u,m)=>{var i,o,g,a;z.info("addNode",A,w,u,m);const v=ot();let r=(o=(i=v.mindmap)==null?void 0:i.padding)!=null?o:et.mindmap.padding;switch(m){case W.ROUNDED_RECT:case W.RECT:case W.HEXAGON:r*=2}const t={id:yt++,nodeId:rt(w,v),level:A,descr:rt(u,v),type:m,children:[],width:(a=(g=v.mindmap)==null?void 0:g.maxNodeWidth)!=null?a:et.mindmap.maxNodeWidth,padding:r},e=Ut(A);if(e)e.children.push(t),Z.push(t);else if(Z.length===0)Z.push(t);else throw new Error('There can be only one root. No parent could be found for ("'+t.descr+'")')},"addNode"),W={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Xt=S((A,w)=>{switch(z.debug("In get type",A,w),A){case"[":return W.RECT;case"(":return w===")"?W.ROUNDED_RECT:W.CLOUD;case"((":return W.CIRCLE;case")":return W.CLOUD;case"))":return W.BANG;case"{{":return W.HEXAGON;default:return W.DEFAULT}},"getType"),kt=S((A,w)=>{lt[A]=w},"setElementForId"),Ht=S(A=>{if(!A)return;const w=ot(),u=Z[Z.length-1];A.icon&&(u.icon=rt(A.icon,w)),A.class&&(u.class=rt(A.class,w))},"decorateNode"),$t=S(A=>{switch(A){case W.DEFAULT:return"no-border";case W.RECT:return"rect";case W.ROUNDED_RECT:return"rounded-rect";case W.CIRCLE:return"circle";case W.CLOUD:return"cloud";case W.BANG:return"bang";case W.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),Wt=S(()=>z,"getLogger"),Bt=S(A=>lt[A],"getElementById"),Vt={clear:bt,addNode:Yt,getMindmap:Pt,nodeType:W,getType:Xt,setElementForId:kt,decorateNode:Ht,type2Str:$t,getLogger:Wt,getElementById:Bt},Zt=Vt,Qt=12,jt=S(function(A,w,u,m){w.append("path").attr("id","node-"+u.id).attr("class","node-bkg node-"+A.type2Str(u.type)).attr("d",`M0 ${u.height-5} v${-u.height+2*5} q0,-5 5,-5 h${u.width-2*5} q5,0 5,5 v${u.height-5} H0 Z`),w.append("line").attr("class","node-line-"+m).attr("x1",0).attr("y1",u.height).attr("x2",u.width).attr("y2",u.height)},"defaultBkg"),zt=S(function(A,w,u){w.append("rect").attr("id","node-"+u.id).attr("class","node-bkg node-"+A.type2Str(u.type)).attr("height",u.height).attr("width",u.width)},"rectBkg"),Kt=S(function(A,w,u){const m=u.width,v=u.height,r=.15*m,t=.25*m,e=.35*m,i=.2*m;w.append("path").attr("id","node-"+u.id).attr("class","node-bkg node-"+A.type2Str(u.type)).attr("d",`M0 0 a${r},${r} 0 0,1 ${m*.25},${-1*m*.1}
      a${e},${e} 1 0,1 ${m*.4},${-1*m*.1}
      a${t},${t} 1 0,1 ${m*.35},${1*m*.2}

      a${r},${r} 1 0,1 ${m*.15},${1*v*.35}
      a${i},${i} 1 0,1 ${-1*m*.15},${1*v*.65}

      a${t},${r} 1 0,1 ${-1*m*.25},${m*.15}
      a${e},${e} 1 0,1 ${-1*m*.5},0
      a${r},${r} 1 0,1 ${-1*m*.25},${-1*m*.15}

      a${r},${r} 1 0,1 ${-1*m*.1},${-1*v*.35}
      a${i},${i} 1 0,1 ${m*.1},${-1*v*.65}

    H0 V0 Z`)},"cloudBkg"),Jt=S(function(A,w,u){const m=u.width,v=u.height,r=.15*m;w.append("path").attr("id","node-"+u.id).attr("class","node-bkg node-"+A.type2Str(u.type)).attr("d",`M0 0 a${r},${r} 1 0,0 ${m*.25},${-1*v*.1}
      a${r},${r} 1 0,0 ${m*.25},0
      a${r},${r} 1 0,0 ${m*.25},0
      a${r},${r} 1 0,0 ${m*.25},${1*v*.1}

      a${r},${r} 1 0,0 ${m*.15},${1*v*.33}
      a${r*.8},${r*.8} 1 0,0 0,${1*v*.34}
      a${r},${r} 1 0,0 ${-1*m*.15},${1*v*.33}

      a${r},${r} 1 0,0 ${-1*m*.25},${v*.15}
      a${r},${r} 1 0,0 ${-1*m*.25},0
      a${r},${r} 1 0,0 ${-1*m*.25},0
      a${r},${r} 1 0,0 ${-1*m*.25},${-1*v*.15}

      a${r},${r} 1 0,0 ${-1*m*.1},${-1*v*.33}
      a${r*.8},${r*.8} 1 0,0 0,${-1*v*.34}
      a${r},${r} 1 0,0 ${m*.1},${-1*v*.33}

    H0 V0 Z`)},"bangBkg"),qt=S(function(A,w,u){w.append("circle").attr("id","node-"+u.id).attr("class","node-bkg node-"+A.type2Str(u.type)).attr("r",u.width/2)},"circleBkg");function Et(A,w,u,m,v){return A.insert("polygon",":first-child").attr("points",m.map(function(r){return r.x+","+r.y}).join(" ")).attr("transform","translate("+(v.width-w)/2+", "+u+")")}S(Et,"insertPolygonShape");var te=S(function(A,w,u){const m=u.height,r=m/4,t=u.width-u.padding+2*r,e=[{x:r,y:0},{x:t-r,y:0},{x:t,y:-m/2},{x:t-r,y:-m},{x:r,y:-m},{x:0,y:-m/2}];Et(w,t,m,e,u)},"hexagonBkg"),ee=S(function(A,w,u){w.append("rect").attr("id","node-"+u.id).attr("class","node-bkg node-"+A.type2Str(u.type)).attr("height",u.height).attr("rx",u.padding).attr("ry",u.padding).attr("width",u.width)},"roundedRectBkg"),re=S(function(A,w,u,m,v){return tt(this,null,function*(){const r=v.htmlLabels,t=m%(Qt-1),e=w.append("g");u.section=t;let i="section-"+t;t<0&&(i+=" section-root"),e.attr("class",(u.class?u.class+" ":"")+"mindmap-node "+i);const o=e.append("g"),g=e.append("g"),a=u.descr.replace(/(<br\/*>)/g,`
`);yield mt(g,a,{useHtmlLabels:r,width:u.width,classes:"mindmap-node-label"},v),r||g.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const y=g.node().getBBox(),[n]=Ot(v.fontSize);if(u.height=y.height+n*1.1*.5+u.padding,u.width=y.width+2*u.padding,u.icon)if(u.type===A.nodeType.CIRCLE)u.height+=50,u.width+=50,e.append("foreignObject").attr("height","50px").attr("width",u.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+t+" "+u.icon),g.attr("transform","translate("+u.width/2+", "+(u.height/2-1.5*u.padding)+")");else{u.width+=50;const h=u.height;u.height=Math.max(h,60);const c=Math.abs(u.height-h);e.append("foreignObject").attr("width","60px").attr("height",u.height).attr("style","text-align: center;margin-top:"+c/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+t+" "+u.icon),g.attr("transform","translate("+(25+u.width/2)+", "+(c/2+u.padding/2)+")")}else if(r){const h=(u.width-y.width)/2,c=(u.height-y.height)/2;g.attr("transform","translate("+h+", "+c+")")}else{const h=u.width/2,c=u.padding/2;g.attr("transform","translate("+h+", "+c+")")}switch(u.type){case A.nodeType.DEFAULT:jt(A,o,u,t);break;case A.nodeType.ROUNDED_RECT:ee(A,o,u,t);break;case A.nodeType.RECT:zt(A,o,u,t);break;case A.nodeType.CIRCLE:o.attr("transform","translate("+u.width/2+", "+ +u.height/2+")"),qt(A,o,u,t);break;case A.nodeType.CLOUD:Kt(A,o,u,t);break;case A.nodeType.BANG:Jt(A,o,u,t);break;case A.nodeType.HEXAGON:te(A,o,u,t);break}return A.setElementForId(u.id,e),u.height})},"drawNode"),ie=S(function(A,w){const u=A.getElementById(w.id),m=w.x||0,v=w.y||0;u.attr("transform","translate("+m+","+v+")")},"positionNode");dt.use(_t);function ct(A,w,u,m,v){return tt(this,null,function*(){yield re(A,w,u,m,v),u.children&&(yield Promise.all(u.children.map((r,t)=>ct(A,w,r,m<0?t:m,v))))})}S(ct,"drawNodes");function Lt(A,w){w.edges().map((u,m)=>{const v=u.data();if(u[0]._private.bodyBounds){const r=u[0]._private.rscratch;z.trace("Edge: ",m,v),A.insert("path").attr("d",`M ${r.startX},${r.startY} L ${r.midX},${r.midY} L${r.endX},${r.endY} `).attr("class","edge section-edge-"+v.section+" edge-depth-"+v.depth)}})}S(Lt,"drawEdges");function gt(A,w,u,m){w.add({group:"nodes",data:{id:A.id.toString(),labelText:A.descr,height:A.height,width:A.width,level:m,nodeId:A.id,padding:A.padding,type:A.type},position:{x:A.x,y:A.y}}),A.children&&A.children.forEach(v=>{gt(v,w,u,m+1),w.add({group:"edges",data:{id:`${A.id}_${v.id}`,source:A.id,target:v.id,depth:m,section:v.section}})})}S(gt,"addNodes");function Tt(A,w){return new Promise(u=>{const m=Dt("body").append("div").attr("id","cy").attr("style","display:none"),v=dt({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});m.remove(),gt(A,v,w,0),v.nodes().forEach(function(r){r.layoutDimensions=()=>{const t=r.data();return{w:t.width,h:t.height}}}),v.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),v.ready(r=>{z.info("Ready",r),u(v)})})}S(Tt,"layoutMindmap");function Nt(A,w){w.nodes().map((u,m)=>{const v=u.data();v.x=u.position().x,v.y=u.position().y,ie(A,v);const r=A.getElementById(v.nodeId);z.info("Id:",m,"Position: (",u.position().x,", ",u.position().y,")",v),r.attr("transform",`translate(${u.position().x-v.width/2}, ${u.position().y-v.height/2})`),r.attr("attr",`apa-${m})`)})}S(Nt,"positionNodes");var ne=S((A,w,u,m)=>tt(void 0,null,function*(){var a,y,n,h;z.debug(`Rendering mindmap diagram
`+A);const v=m.db,r=v.getMindmap();if(!r)return;const t=ot();t.htmlLabels=!1;const e=At(w),i=e.append("g");i.attr("class","mindmap-edges");const o=e.append("g");o.attr("class","mindmap-nodes"),yield ct(v,o,r,-1,t);const g=yield Tt(r,t);Lt(i,g),Nt(v,g),It(void 0,e,(y=(a=t.mindmap)==null?void 0:a.padding)!=null?y:et.mindmap.padding,(h=(n=t.mindmap)==null?void 0:n.useMaxWidth)!=null?h:et.mindmap.useMaxWidth)}),"draw"),se={draw:ne},ae=S(A=>{let w="";for(let u=0;u<A.THEME_COLOR_LIMIT;u++)A["lineColor"+u]=A["lineColor"+u]||A["cScaleInv"+u],Ct(A["lineColor"+u])?A["lineColor"+u]=Rt(A["lineColor"+u],20):A["lineColor"+u]=xt(A["lineColor"+u],20);for(let u=0;u<A.THEME_COLOR_LIMIT;u++){const m=""+(17-3*u);w+=`
    .section-${u-1} rect, .section-${u-1} path, .section-${u-1} circle, .section-${u-1} polygon, .section-${u-1} path  {
      fill: ${A["cScale"+u]};
    }
    .section-${u-1} text {
     fill: ${A["cScaleLabel"+u]};
    }
    .node-icon-${u-1} {
      font-size: 40px;
      color: ${A["cScaleLabel"+u]};
    }
    .section-edge-${u-1}{
      stroke: ${A["cScale"+u]};
    }
    .edge-depth-${u-1}{
      stroke-width: ${m};
    }
    .section-${u-1} line {
      stroke: ${A["cScaleInv"+u]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return w},"genSections"),oe=S(A=>`
  .edge {
    stroke-width: 3;
  }
  ${ae(A)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${A.git0};
  }
  .section-root text {
    fill: ${A.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),he=oe,pe={db:Zt,renderer:se,parser:Ft,styles:he};export{pe as diagram};
