var Wt=(e,t,i)=>new Promise((n,r)=>{var a=c=>{try{s(i.next(c))}catch(u){r(u)}},o=c=>{try{s(i.throw(c))}catch(u){r(u)}},s=c=>c.done?n(c.value):Promise.resolve(c.value).then(a,o);s((i=i.apply(e,t)).next())});import{m as Yt,p as Xt,w as vt,n as mt,q as Be,d as Ke,e as Ze,o as Ge,v as Je,x as Qe,y as ti}from"./vue-vendor-d751b0f5.js";/*!
 * Viewer.js v1.11.7
 * https://fengyuanchen.github.io/viewerjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2024-11-24T04:32:19.116Z
 */function ei(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ht(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ie(n.key),n)}}function ii(e,t,i){return t&&Ht(e.prototype,t),i&&Ht(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function ni(e,t,i){return(t=Ie(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function qt(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),i.push.apply(i,n)}return i}function zt(e){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?qt(Object(i),!0).forEach(function(n){ni(e,n,i[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):qt(Object(i)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(i,n))})}return e}function ri(e,t){if(typeof e!="object"||!e)return e;var i=e[Symbol.toPrimitive];if(i!==void 0){var n=i.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ie(e){var t=ri(e,"string");return typeof t=="symbol"?t:t+""}function Ct(e){"@babel/helpers - typeof";return Ct=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ct(e)}var Ut={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialCoverage:.9,initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},ai='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>',Tt=typeof window!="undefined"&&typeof window.document!="undefined",X=Tt?window:{},J=Tt&&X.document.documentElement?"ontouchstart"in X.document.documentElement:!1,Vt=Tt?"PointerEvent"in X:!1,m="viewer",gt="move",Ne="switch",rt="zoom",ct="".concat(m,"-active"),oi="".concat(m,"-close"),pt="".concat(m,"-fade"),jt="".concat(m,"-fixed"),si="".concat(m,"-fullscreen"),$t="".concat(m,"-fullscreen-exit"),H="".concat(m,"-hide"),li="".concat(m,"-hide-md-down"),ui="".concat(m,"-hide-sm-down"),ci="".concat(m,"-hide-xs-down"),L="".concat(m,"-in"),at="".concat(m,"-invisible"),Q="".concat(m,"-loading"),hi="".concat(m,"-move"),Bt="".concat(m,"-open"),$="".concat(m,"-show"),A="".concat(m,"-transition"),tt="click",kt="dblclick",Kt="dragstart",Zt="focusin",Gt="keydown",z="load",q="error",fi=J?"touchend touchcancel":"mouseup",di=J?"touchmove":"mousemove",vi=J?"touchstart":"mousedown",Jt=Vt?"pointerdown":vi,Qt=Vt?"pointermove":di,te=Vt?"pointerup pointercancel":fi,ee="resize",P="transitionend",ie="wheel",ne="ready",re="show",ae="shown",oe="hide",se="hidden",le="view",st="viewed",ue="move",ce="moved",he="rotate",fe="rotated",de="scale",ve="scaled",me="zoom",ge="zoomed",pe="play",be="stop",wt="".concat(m,"Action"),Rt=/\s\s*/,ht=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function lt(e){return typeof e=="string"}var mi=Number.isNaN||X.isNaN;function D(e){return typeof e=="number"&&!mi(e)}function K(e){return typeof e=="undefined"}function et(e){return Ct(e)==="object"&&e!==null}var gi=Object.prototype.hasOwnProperty;function Z(e){if(!et(e))return!1;try{var t=e.constructor,i=t.prototype;return t&&i&&gi.call(i,"isPrototypeOf")}catch(n){return!1}}function E(e){return typeof e=="function"}function S(e,t){if(e&&E(t))if(Array.isArray(e)||D(e.length)){var i=e.length,n;for(n=0;n<i&&t.call(e,e[n],n,e)!==!1;n+=1);}else et(e)&&Object.keys(e).forEach(function(r){t.call(e,e[r],r,e)});return e}var k=Object.assign||function(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];return et(t)&&n.length>0&&n.forEach(function(a){et(a)&&Object.keys(a).forEach(function(o){t[o]=a[o]})}),t},pi=/^(?:width|height|left|top|marginLeft|marginTop)$/;function F(e,t){var i=e.style;S(t,function(n,r){pi.test(r)&&D(n)&&(n+="px"),i[r]=n})}function bi(e){return lt(e)?e.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):e}function B(e,t){return!e||!t?!1:e.classList?e.classList.contains(t):e.className.indexOf(t)>-1}function d(e,t){if(!(!e||!t)){if(D(e.length)){S(e,function(n){d(n,t)});return}if(e.classList){e.classList.add(t);return}var i=e.className.trim();i?i.indexOf(t)<0&&(e.className="".concat(i," ").concat(t)):e.className=t}}function p(e,t){if(!(!e||!t)){if(D(e.length)){S(e,function(i){p(i,t)});return}if(e.classList){e.classList.remove(t);return}e.className.indexOf(t)>=0&&(e.className=e.className.replace(t,""))}}function ut(e,t,i){if(t){if(D(e.length)){S(e,function(n){ut(n,t,i)});return}i?d(e,t):p(e,t)}}var wi=/([a-z\d])([A-Z])/g;function Mt(e){return e.replace(wi,"$1-$2").toLowerCase()}function G(e,t){return et(e[t])?e[t]:e.dataset?e.dataset[t]:e.getAttribute("data-".concat(Mt(t)))}function Lt(e,t,i){et(i)?e[t]=i:e.dataset?e.dataset[t]=i:e.setAttribute("data-".concat(Mt(t)),i)}var Ce=function(){var e=!1;if(Tt){var t=!1,i=function(){},n=Object.defineProperty({},"once",{get:function(){return e=!0,t},set:function(a){t=a}});X.addEventListener("test",i,n),X.removeEventListener("test",i,n)}return e}();function T(e,t,i){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=i;t.trim().split(Rt).forEach(function(a){if(!Ce){var o=e.listeners;o&&o[a]&&o[a][i]&&(r=o[a][i],delete o[a][i],Object.keys(o[a]).length===0&&delete o[a],Object.keys(o).length===0&&delete e.listeners)}e.removeEventListener(a,r,n)})}function v(e,t,i){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=i;t.trim().split(Rt).forEach(function(a){if(n.once&&!Ce){var o=e.listeners,s=o===void 0?{}:o;r=function(){delete s[a][i],e.removeEventListener(a,r,n);for(var u=arguments.length,l=new Array(u),h=0;h<u;h++)l[h]=arguments[h];i.apply(e,l)},s[a]||(s[a]={}),s[a][i]&&e.removeEventListener(a,s[a][i],n),s[a][i]=r,e.listeners=s}e.addEventListener(a,r,n)})}function N(e,t,i,n){var r;return E(Event)&&E(CustomEvent)?r=new CustomEvent(t,zt({bubbles:!0,cancelable:!0,detail:i},n)):(r=document.createEvent("CustomEvent"),r.initCustomEvent(t,!0,!0,i)),e.dispatchEvent(r)}function yi(e){var t=e.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}function bt(e){var t=e.rotate,i=e.scaleX,n=e.scaleY,r=e.translateX,a=e.translateY,o=[];D(r)&&r!==0&&o.push("translateX(".concat(r,"px)")),D(a)&&a!==0&&o.push("translateY(".concat(a,"px)")),D(t)&&t!==0&&o.push("rotate(".concat(t,"deg)")),D(i)&&i!==1&&o.push("scaleX(".concat(i,")")),D(n)&&n!==1&&o.push("scaleY(".concat(n,")"));var s=o.length?o.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Ei(e){return lt(e)?decodeURIComponent(e.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}var At=X.navigator&&/Version\/\d+(\.\d+)+?\s+Safari/i.test(X.navigator.userAgent);function je(e,t,i){var n=document.createElement("img");if(e.naturalWidth&&!At)return i(e.naturalWidth,e.naturalHeight),n;var r=document.body||document.documentElement;return n.onload=function(){i(n.width,n.height),At||r.removeChild(n)},S(t.inheritedAttributes,function(a){var o=e.getAttribute(a);o!==null&&n.setAttribute(a,o)}),n.src=e.src,At||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",r.appendChild(n)),n}function ft(e){switch(e){case 2:return ci;case 3:return ui;case 4:return li;default:return""}}function Ti(e){var t=zt({},e),i=[];return S(e,function(n,r){delete t[r],S(t,function(a){var o=Math.abs(n.startX-a.startX),s=Math.abs(n.startY-a.startY),c=Math.abs(n.endX-a.endX),u=Math.abs(n.endY-a.endY),l=Math.sqrt(o*o+s*s),h=Math.sqrt(c*c+u*u),f=(h-l)/l;i.push(f)})}),i.sort(function(n,r){return Math.abs(n)<Math.abs(r)}),i[0]}function dt(e,t){var i=e.pageX,n=e.pageY,r={endX:i,endY:n};return t?r:zt({timeStamp:Date.now(),startX:i,startY:n},r)}function Oi(e){var t=0,i=0,n=0;return S(e,function(r){var a=r.startX,o=r.startY;t+=a,i+=o,n+=1}),t/=n,i/=n,{pageX:t,pageY:i}}var Si={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var t=this.element.ownerDocument,i=t.body||t.documentElement;this.body=i,this.scrollbarWidth=window.innerWidth-t.documentElement.clientWidth,this.initialBodyPaddingRight=i.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(i).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var t=this.options,i=this.parent,n;t.inline&&(n={width:Math.max(i.offsetWidth,t.minWidth),height:Math.max(i.offsetHeight,t.minHeight)},this.parentData=n),(this.fulled||!n)&&(n=this.containerData),this.viewerData=k({},n)},renderViewer:function(){this.options.inline&&!this.fulled&&F(this.viewer,this.viewerData)},initList:function(){var t=this,i=this.element,n=this.options,r=this.list,a=[];r.innerHTML="",S(this.images,function(o,s){var c=o.src,u=o.alt||Ei(c),l=t.getImageURL(o);if(c||l){var h=document.createElement("li"),f=document.createElement("img");S(n.inheritedAttributes,function(g){var w=o.getAttribute(g);w!==null&&f.setAttribute(g,w)}),n.navbar&&(f.src=c||l),f.alt=u,f.setAttribute("data-original-url",l||c),h.setAttribute("data-index",s),h.setAttribute("data-viewer-action","view"),h.setAttribute("role","button"),n.keyboard&&h.setAttribute("tabindex",0),h.appendChild(f),r.appendChild(h),a.push(h)}}),this.items=a,S(a,function(o){var s=o.firstElementChild,c,u;Lt(s,"filled",!0),n.loading&&d(o,Q),v(s,z,c=function(h){T(s,q,u),n.loading&&p(o,Q),t.loadImage(h)},{once:!0}),v(s,q,u=function(){T(s,z,c),n.loading&&p(o,Q)},{once:!0})}),n.transition&&v(i,st,function(){d(r,A)},{once:!0})},renderList:function(){var t=this.index,i=this.items[t];if(i){var n=i.nextElementSibling,r=parseInt(window.getComputedStyle(n||i).marginLeft,10),a=i.offsetWidth,o=a+r;F(this.list,k({width:o*this.length-r},bt({translateX:(this.viewerData.width-a)/2-o*t})))}},resetList:function(){var t=this.list;t.innerHTML="",p(t,A),F(t,bt({translateX:0}))},initImage:function(t){var i=this,n=this.options,r=this.image,a=this.viewerData,o=this.footer.offsetHeight,s=a.width,c=Math.max(a.height-o,o),u=this.imageData||{},l;this.imageInitializing={abort:function(){l.onload=null}},l=je(r,n,function(h,f){var g=h/f,w=Math.max(0,Math.min(1,n.initialCoverage)),y=s,_=c;i.imageInitializing=!1,c*g>s?_=s/g:y=c*g,w=D(w)?w:.9,y=Math.min(y*w,h),_=Math.min(_*w,f);var I=(s-y)/2,C=(c-_)/2,O={left:I,top:C,x:I,y:C,width:y,height:_,oldRatio:1,ratio:y/h,aspectRatio:g,naturalWidth:h,naturalHeight:f},V=k({},O);n.rotatable&&(O.rotate=u.rotate||0,V.rotate=0),n.scalable&&(O.scaleX=u.scaleX||1,O.scaleY=u.scaleY||1,V.scaleX=1,V.scaleY=1),i.imageData=O,i.initialImageData=V,t&&t()})},renderImage:function(t){var i=this,n=this.image,r=this.imageData;if(F(n,k({width:r.width,height:r.height,marginLeft:r.x,marginTop:r.y},bt(r))),t)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&B(n,A)){var a=function(){i.imageRendering=!1,t()};this.imageRendering={abort:function(){T(n,P,a)}},v(n,P,a,{once:!0})}else t()},resetImage:function(){var t=this.image;t&&(this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null,this.title.innerHTML="")}},xi={bind:function(){var t=this.options,i=this.viewer,n=this.canvas,r=this.element.ownerDocument;v(i,tt,this.onClick=this.click.bind(this)),v(i,Kt,this.onDragStart=this.dragstart.bind(this)),v(n,Jt,this.onPointerDown=this.pointerdown.bind(this)),v(r,Qt,this.onPointerMove=this.pointermove.bind(this)),v(r,te,this.onPointerUp=this.pointerup.bind(this)),v(r,Gt,this.onKeyDown=this.keydown.bind(this)),v(window,ee,this.onResize=this.resize.bind(this)),t.zoomable&&t.zoomOnWheel&&v(i,ie,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleOnDblclick&&v(n,kt,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var t=this.options,i=this.viewer,n=this.canvas,r=this.element.ownerDocument;T(i,tt,this.onClick),T(i,Kt,this.onDragStart),T(n,Jt,this.onPointerDown),T(r,Qt,this.onPointerMove),T(r,te,this.onPointerUp),T(r,Gt,this.onKeyDown),T(window,ee,this.onResize),t.zoomable&&t.zoomOnWheel&&T(i,ie,this.onWheel,{passive:!1,capture:!0}),t.toggleOnDblclick&&T(n,kt,this.onDblclick)}},Di={click:function(t){var i=this.options,n=this.imageData,r=t.target,a=G(r,wt);switch(!a&&r.localName==="img"&&r.parentElement.localName==="li"&&(r=r.parentElement,a=G(r,wt)),J&&t.isTrusted&&r===this.canvas&&clearTimeout(this.clickCanvasTimeout),a){case"mix":this.played?this.stop():i.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.pointerMoved||this.hide();break;case"view":this.view(G(r,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(i.loop);break;case"play":this.play(i.fullscreen);break;case"next":this.next(i.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-n.scaleX||-1);break;case"flip-vertical":this.scaleY(-n.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(t){t.preventDefault(),this.viewed&&t.target===this.image&&(J&&t.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(t.isTrusted?t:t.detail&&t.detail.originalEvent))},load:function(){var t=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var i=this.element,n=this.options,r=this.image,a=this.index,o=this.viewerData;p(r,at),n.loading&&p(this.canvas,Q),r.style.cssText="height:0;"+"margin-left:".concat(o.width/2,"px;")+"margin-top:".concat(o.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage(function(){ut(r,hi,n.movable),ut(r,A,n.transition),t.renderImage(function(){t.viewed=!0,t.viewing=!1,E(n.viewed)&&v(i,st,n.viewed,{once:!0}),N(i,st,{originalImage:t.images[a],index:a,image:r},{cancelable:!1})})})},loadImage:function(t){var i=t.target,n=i.parentNode,r=n.offsetWidth||30,a=n.offsetHeight||50,o=!!G(i,"filled");je(i,this.options,function(s,c){var u=s/c,l=r,h=a;a*u>r?o?l=a*u:h=r/u:o?h=r/u:l=a*u,F(i,k({width:l,height:h},bt({translateX:(r-l)/2,translateY:(a-h)/2})))})},keydown:function(t){var i=this.options;if(i.keyboard){var n=t.keyCode||t.which||t.charCode;switch(n){case 13:this.viewer.contains(t.target)&&this.click(t);break}if(this.fulled)switch(n){case 27:this.played?this.stop():i.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.played&&this.playing?this.playing.prev():this.prev(i.loop);break;case 38:t.preventDefault(),this.zoom(i.zoomRatio,!0);break;case 39:this.played&&this.playing?this.playing.next():this.next(i.loop);break;case 40:t.preventDefault(),this.zoom(-i.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle());break}}},dragstart:function(t){t.target.localName==="img"&&t.preventDefault()},pointerdown:function(t){var i=this.options,n=this.pointers,r=t.buttons,a=t.button;if(this.pointerMoved=!1,!(!this.viewed||this.showing||this.viewing||this.hiding||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(D(r)&&r!==1||D(a)&&a!==0||t.ctrlKey))){t.preventDefault(),t.changedTouches?S(t.changedTouches,function(s){n[s.identifier]=dt(s)}):n[t.pointerId||0]=dt(t);var o=i.movable?gt:!1;i.zoomOnTouch&&i.zoomable&&Object.keys(n).length>1?o=rt:i.slideOnTouch&&(t.pointerType==="touch"||t.type==="touchstart")&&this.isSwitchable()&&(o=Ne),i.transition&&(o===gt||o===rt)&&p(this.image,A),this.action=o}},pointermove:function(t){var i=this.pointers,n=this.action;!this.viewed||!n||(t.preventDefault(),t.changedTouches?S(t.changedTouches,function(r){k(i[r.identifier]||{},dt(r,!0))}):k(i[t.pointerId||0]||{},dt(t,!0)),this.change(t))},pointerup:function(t){var i=this,n=this.options,r=this.action,a=this.pointers,o;t.changedTouches?S(t.changedTouches,function(s){o=a[s.identifier],delete a[s.identifier]}):(o=a[t.pointerId||0],delete a[t.pointerId||0]),r&&(t.preventDefault(),n.transition&&(r===gt||r===rt)&&d(this.image,A),this.action=!1,J&&r!==rt&&o&&Date.now()-o.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),n.toggleOnDblclick&&this.viewed&&t.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout(function(){N(i.image,kt,{originalEvent:t})},50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout(function(){i.imageClicked=!1},500)):(this.imageClicked=!1,n.backdrop&&n.backdrop!=="static"&&t.target===this.canvas&&(this.clickCanvasTimeout=setTimeout(function(){N(i.canvas,tt,{originalEvent:t})},50)))))},resize:function(){var t=this;if(!(!this.isShown||this.hiding)&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage()}),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement)){this.stop();return}S(this.player.getElementsByTagName("img"),function(i){v(i,z,t.loadImage.bind(t),{once:!0}),N(i,z)})}},wheel:function(t){var i=this;if(this.viewed&&(t.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50);var n=Number(this.options.zoomRatio)||.1,r=1;t.deltaY?r=t.deltaY>0?1:-1:t.wheelDelta?r=-t.wheelDelta/120:t.detail&&(r=t.detail>0?1:-1),this.zoom(-r*n,!0,null,t)}}},Ai={show:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.element,n=this.options;if(n.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(t),this;if(E(n.show)&&v(i,re,n.show,{once:!0}),N(i,re)===!1||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var r=this.viewer;if(p(r,H),r.setAttribute("role","dialog"),r.setAttribute("aria-labelledby",this.title.id),r.setAttribute("aria-modal",!0),r.removeAttribute("aria-hidden"),n.transition&&!t){var a=this.shown.bind(this);this.transitioning={abort:function(){T(r,P,a),p(r,L)}},d(r,A),r.initialOffsetWidth=r.offsetWidth,v(r,P,a,{once:!0}),d(r,L)}else d(r,L),this.shown();return this},hide:function(){var t=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,n=this.element,r=this.options;if(r.inline||this.hiding||!(this.isShown||this.showing))return this;if(E(r.hide)&&v(n,oe,r.hide,{once:!0}),N(n,oe)===!1)return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var a=this.viewer,o=this.image,s=function(){p(a,L),t.hidden()};if(r.transition&&!i){var c=function(h){h&&h.target===a&&(T(a,P,c),t.hidden())},u=function(){B(a,A)?(v(a,P,c),p(a,L)):s()};this.transitioning={abort:function(){t.viewed&&B(o,A)?T(o,P,u):B(a,A)&&T(a,P,c)}},this.viewed&&B(o,A)?(v(o,P,u,{once:!0}),this.zoomTo(0,!1,null,null,!0)):u()}else s();return this},view:function(){var t=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.initialViewIndex;if(i=Number(i)||0,this.hiding||this.played||i<0||i>=this.length||this.viewed&&i===this.index)return this;if(!this.isShown)return this.index=i,this.show();this.viewing&&this.viewing.abort();var n=this.element,r=this.options,a=this.title,o=this.canvas,s=this.items[i],c=s.querySelector("img"),u=G(c,"originalUrl"),l=c.getAttribute("alt"),h=document.createElement("img");if(S(r.inheritedAttributes,function(_){var I=c.getAttribute(_);I!==null&&h.setAttribute(_,I)}),h.src=u,h.alt=l,E(r.view)&&v(n,le,r.view,{once:!0}),N(n,le,{originalImage:this.images[i],index:i,image:h})===!1||!this.isShown||this.hiding||this.played)return this;var f=this.items[this.index];f&&(p(f,ct),f.removeAttribute("aria-selected")),d(s,ct),s.setAttribute("aria-selected",!0),r.focus&&s.focus(),this.image=h,this.viewed=!1,this.index=i,this.imageData={},d(h,at),r.loading&&d(o,Q),o.innerHTML="",o.appendChild(h),this.renderList(),a.innerHTML="";var g=function(){var I=t.imageData,C=Array.isArray(r.title)?r.title[1]:r.title;a.innerHTML=bi(E(C)?C.call(t,h,I):"".concat(l," (").concat(I.naturalWidth," × ").concat(I.naturalHeight,")"))},w,y;return v(n,st,g,{once:!0}),this.viewing={abort:function(){T(n,st,g),h.complete?t.imageRendering?t.imageRendering.abort():t.imageInitializing&&t.imageInitializing.abort():(h.src="",T(h,z,w),t.timeout&&clearTimeout(t.timeout))}},h.complete?this.load():(v(h,z,w=function(){T(h,q,y),t.load()},{once:!0}),v(h,q,y=function(){T(h,z,w),t.timeout&&(clearTimeout(t.timeout),t.timeout=!1),p(h,at),r.loading&&p(t.canvas,Q)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(function(){p(h,at),t.timeout=!1},1e3)),this},prev:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.index-1;return i<0&&(i=t?this.length-1:0),this.view(i),this},next:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.length-1,n=this.index+1;return n>i&&(n=t?0:i),this.view(n),this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,n=this.imageData;return this.moveTo(K(t)?t:n.x+Number(t),K(i)?i:n.y+Number(i)),this},moveTo:function(t){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=this.element,o=this.options,s=this.imageData;if(t=Number(t),n=Number(n),this.viewed&&!this.played&&o.movable){var c=s.x,u=s.y,l=!1;if(D(t)?l=!0:t=c,D(n)?l=!0:n=u,l){if(E(o.move)&&v(a,ue,o.move,{once:!0}),N(a,ue,{x:t,y:n,oldX:c,oldY:u,originalEvent:r})===!1)return this;s.x=t,s.y=n,s.left=t,s.top=n,this.moving=!0,this.renderImage(function(){i.moving=!1,E(o.moved)&&v(a,ce,o.moved,{once:!0}),N(a,ce,{x:t,y:n,oldX:c,oldY:u,originalEvent:r},{cancelable:!1})})}}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo:function(t){var i=this,n=this.element,r=this.options,a=this.imageData;if(t=Number(t),D(t)&&this.viewed&&!this.played&&r.rotatable){var o=a.rotate;if(E(r.rotate)&&v(n,he,r.rotate,{once:!0}),N(n,he,{degree:t,oldDegree:o})===!1)return this;a.rotate=t,this.rotating=!0,this.renderImage(function(){i.rotating=!1,E(r.rotated)&&v(n,fe,r.rotated,{once:!0}),N(n,fe,{degree:t,oldDegree:o},{cancelable:!1})})}return this},scaleX:function(t){return this.scale(t,this.imageData.scaleY),this},scaleY:function(t){return this.scale(this.imageData.scaleX,t),this},scale:function(t){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,r=this.element,a=this.options,o=this.imageData;if(t=Number(t),n=Number(n),this.viewed&&!this.played&&a.scalable){var s=o.scaleX,c=o.scaleY,u=!1;if(D(t)?u=!0:t=s,D(n)?u=!0:n=c,u){if(E(a.scale)&&v(r,de,a.scale,{once:!0}),N(r,de,{scaleX:t,scaleY:n,oldScaleX:s,oldScaleY:c})===!1)return this;o.scaleX=t,o.scaleY=n,this.scaling=!0,this.renderImage(function(){i.scaling=!1,E(a.scaled)&&v(r,ve,a.scaled,{once:!0}),N(r,ve,{scaleX:t,scaleY:n,oldScaleX:s,oldScaleY:c},{cancelable:!1})})}}return this},zoom:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,a=this.imageData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(a.width*t/a.naturalWidth,i,n,r),this},zoomTo:function(t){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,s=this.element,c=this.options,u=this.pointers,l=this.imageData,h=l.x,f=l.y,g=l.width,w=l.height,y=l.naturalWidth,_=l.naturalHeight;if(t=Math.max(0,t),D(t)&&this.viewed&&!this.played&&(o||c.zoomable)){if(!o){var I=Math.max(.01,c.minZoomRatio),C=Math.min(100,c.maxZoomRatio);t=Math.min(Math.max(t,I),C)}if(a)switch(a.type){case"wheel":c.zoomRatio>=.055&&t>.95&&t<1.05&&(t=1);break;case"pointermove":case"touchmove":case"mousemove":t>.99&&t<1.01&&(t=1);break}var O=y*t,V=_*t,R=O-g,b=V-w,j=l.ratio;if(E(c.zoom)&&v(s,me,c.zoom,{once:!0}),N(s,me,{ratio:t,oldRatio:j,originalEvent:a})===!1)return this;if(this.zooming=!0,a){var M=yi(this.viewer),W=u&&Object.keys(u).length>0?Oi(u):{pageX:a.pageX,pageY:a.pageY};l.x-=R*((W.pageX-M.left-h)/g),l.y-=b*((W.pageY-M.top-f)/w)}else Z(r)&&D(r.x)&&D(r.y)?(l.x-=R*((r.x-h)/g),l.y-=b*((r.y-f)/w)):(l.x-=R/2,l.y-=b/2);l.left=l.x,l.top=l.y,l.width=O,l.height=V,l.oldRatio=j,l.ratio=t,this.renderImage(function(){i.zooming=!1,E(c.zoomed)&&v(s,ge,c.zoomed,{once:!0}),N(s,ge,{ratio:t,oldRatio:j,originalEvent:a},{cancelable:!1})}),n&&this.tooltip()}return this},play:function(){var t=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(!this.isShown||this.played)return this;var n=this.element,r=this.options;if(E(r.play)&&v(n,pe,r.play,{once:!0}),N(n,pe)===!1)return this;var a=this.player,o=this.loadImage.bind(this),s=[],c=0,u=0;if(this.played=!0,this.onLoadWhenPlay=o,i&&this.requestFullscreen(i),d(a,$),S(this.items,function(f,g){var w=f.querySelector("img"),y=document.createElement("img");y.src=G(w,"originalUrl"),y.alt=w.getAttribute("alt"),y.referrerPolicy=w.referrerPolicy,c+=1,d(y,pt),ut(y,A,r.transition),B(f,ct)&&(d(y,L),u=g),s.push(y),v(y,z,o,{once:!0}),a.appendChild(y)}),D(r.interval)&&r.interval>0){var l=function(){clearTimeout(t.playing.timeout),p(s[u],L),u-=1,u=u>=0?u:c-1,d(s[u],L),t.playing.timeout=setTimeout(l,r.interval)},h=function(){clearTimeout(t.playing.timeout),p(s[u],L),u+=1,u=u<c?u:0,d(s[u],L),t.playing.timeout=setTimeout(h,r.interval)};c>1&&(this.playing={prev:l,next:h,timeout:setTimeout(h,r.interval)})}return this},stop:function(){var t=this;if(!this.played)return this;var i=this.element,n=this.options;if(E(n.stop)&&v(i,be,n.stop,{once:!0}),N(i,be)===!1)return this;var r=this.player;return clearTimeout(this.playing.timeout),this.playing=!1,this.played=!1,S(r.getElementsByTagName("img"),function(a){T(a,z,t.onLoadWhenPlay)}),p(r,$),r.innerHTML="",this.exitFullscreen(),this},full:function(){var t=this,i=this.options,n=this.viewer,r=this.image,a=this.list;return!this.isShown||this.played||this.fulled||!i.inline?this:(this.fulled=!0,this.open(),d(this.button,$t),i.transition&&(p(a,A),this.viewed&&p(r,A)),d(n,jt),n.setAttribute("role","dialog"),n.setAttribute("aria-labelledby",this.title.id),n.setAttribute("aria-modal",!0),n.removeAttribute("style"),F(n,{zIndex:i.zIndex}),i.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=k({},this.containerData),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){d(r,A),d(a,A)},0)})}),this)},exit:function(){var t=this,i=this.options,n=this.viewer,r=this.image,a=this.list;return!this.isShown||this.played||!this.fulled||!i.inline?this:(this.fulled=!1,this.close(),p(this.button,$t),i.transition&&(p(a,A),this.viewed&&p(r,A)),i.focus&&this.clearEnforceFocus(),n.removeAttribute("role"),n.removeAttribute("aria-labelledby"),n.removeAttribute("aria-modal"),p(n,jt),F(n,{zIndex:i.zIndexInline}),this.viewerData=k({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage(function(){t.renderImage(function(){i.transition&&setTimeout(function(){d(r,A),d(a,A)},0)})}),this)},tooltip:function(){var t=this,i=this.options,n=this.tooltipBox,r=this.imageData;return!this.viewed||this.played||!i.tooltip?this:(n.textContent="".concat(Math.round(r.ratio*100),"%"),this.tooltipping?clearTimeout(this.tooltipping):i.transition?(this.fading&&N(n,P),d(n,$),d(n,pt),d(n,A),n.removeAttribute("aria-hidden"),n.initialOffsetWidth=n.offsetWidth,d(n,L)):(d(n,$),n.removeAttribute("aria-hidden")),this.tooltipping=setTimeout(function(){i.transition?(v(n,P,function(){p(n,$),p(n,pt),p(n,A),n.setAttribute("aria-hidden",!0),t.fading=!1},{once:!0}),p(n,L),t.fading=!0):(p(n,$),n.setAttribute("aria-hidden",!0)),t.tooltipping=!1},1e3),this)},toggle:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null;return this.imageData.ratio===1?this.zoomTo(this.imageData.oldRatio,!0,null,t):this.zoomTo(1,!0,null,t),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=k({},this.initialImageData),this.renderImage()),this},update:function(){var t=this,i=this.element,n=this.options,r=this.isImg;if(r&&!i.parentNode)return this.destroy();var a=[];if(S(r?[i]:i.querySelectorAll("img"),function(u){E(n.filter)?n.filter.call(t,u)&&a.push(u):t.getImageURL(u)&&a.push(u)}),!a.length)return this;if(this.images=a,this.length=a.length,this.ready){var o=[];if(S(this.items,function(u,l){var h=u.querySelector("img"),f=a[l];f&&h?(f.src!==h.src||f.alt!==h.alt)&&o.push(l):o.push(l)}),F(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var s=o.indexOf(this.index);if(s>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-s,this.length-1),0));else{var c=this.items[this.index];d(c,ct),c.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var t=this.element,i=this.options;return t[m]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),i.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):i.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),i.inline||T(t,tt,this.onStart),t[m]=void 0,this):this}},_i={getImageURL:function(t){var i=this.options.url;return lt(i)?i=t.getAttribute(i):E(i)?i=i.call(this,t):i="",i},enforceFocus:function(){var t=this;this.clearEnforceFocus(),v(document,Zt,this.onFocusin=function(i){var n=t.viewer,r=i.target;if(!(r===document||r===n||n.contains(r))){for(;r;){if(r.getAttribute("tabindex")!==null||r.getAttribute("aria-modal")==="true")return;r=r.parentElement}n.focus()}})},clearEnforceFocus:function(){this.onFocusin&&(T(document,Zt,this.onFocusin),this.onFocusin=null)},open:function(){var t=this.body;d(t,Bt),this.scrollbarWidth>0&&(t.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px"))},close:function(){var t=this.body;p(t,Bt),this.scrollbarWidth>0&&(t.style.paddingRight=this.initialBodyPaddingRight)},shown:function(){var t=this.element,i=this.options,n=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,i.focus&&(n.focus(),this.enforceFocus()),E(i.shown)&&v(t,ae,i.shown,{once:!0}),N(t,ae)!==!1&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var t=this.element,i=this.options,n=this.viewer;i.fucus&&this.clearEnforceFocus(),this.close(),this.unbind(),d(n,H),n.removeAttribute("role"),n.removeAttribute("aria-labelledby"),n.removeAttribute("aria-modal"),n.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.hiding=!1,this.destroyed||(E(i.hidden)&&v(t,se,i.hidden,{once:!0}),N(t,se,null,{cancelable:!1}))},requestFullscreen:function(t){var i=this.element.ownerDocument;if(this.fulled&&!(i.fullscreenElement||i.webkitFullscreenElement||i.mozFullScreenElement||i.msFullscreenElement)){var n=i.documentElement;n.requestFullscreen?Z(t)?n.requestFullscreen(t):n.requestFullscreen():n.webkitRequestFullscreen?n.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):n.mozRequestFullScreen?n.mozRequestFullScreen():n.msRequestFullscreen&&n.msRequestFullscreen()}},exitFullscreen:function(){var t=this.element.ownerDocument;this.fulled&&(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&(t.exitFullscreen?t.exitFullscreen():t.webkitExitFullscreen?t.webkitExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.msExitFullscreen&&t.msExitFullscreen())},change:function(t){var i=this.options,n=this.pointers,r=n[Object.keys(n)[0]];if(r){var a=r.endX-r.startX,o=r.endY-r.startY;switch(this.action){case gt:(a!==0||o!==0)&&(this.pointerMoved=!0,this.move(a,o,t));break;case rt:this.zoom(Ti(n),!1,null,t);break;case Ne:{this.action="switched";var s=Math.abs(a);s>1&&s>Math.abs(o)&&(this.pointers={},a>1?this.prev(i.loop):a<-1&&this.next(i.loop));break}}S(n,function(c){c.startX=c.endX,c.startY=c.endY})}},isSwitchable:function(){var t=this.imageData,i=this.viewerData;return this.length>1&&t.x>=0&&t.y>=0&&t.width<=i.width&&t.height<=i.height}},Ii=X.Viewer,Ni=function(e){return function(){return e+=1,e}}(-1),it=function(){function e(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ei(this,e),!t||t.nodeType!==1)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=k({},Ut,Z(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.pointerMoved=!1,this.id=Ni(),this.init()}return ii(e,[{key:"init",value:function(){var i=this,n=this.element,r=this.options;if(!n[m]){n[m]=this,r.focus&&!r.keyboard&&(r.focus=!1);var a=n.localName==="img",o=[];if(S(a?[n]:n.querySelectorAll("img"),function(u){E(r.filter)?r.filter.call(i,u)&&o.push(u):i.getImageURL(u)&&o.push(u)}),this.isImg=a,this.length=o.length,this.images=o,this.initBody(),K(document.createElement(m).style.transition)&&(r.transition=!1),r.inline){var s=0,c=function(){if(s+=1,s===i.length){var l;i.initializing=!1,i.delaying={abort:function(){clearTimeout(l)}},l=setTimeout(function(){i.delaying=!1,i.build()},0)}};this.initializing={abort:function(){S(o,function(l){l.complete||(T(l,z,c),T(l,q,c))})}},S(o,function(u){if(u.complete)c();else{var l,h;v(u,z,l=function(){T(u,q,h),c()},{once:!0}),v(u,q,h=function(){T(u,z,l),c()},{once:!0})}})}else v(n,tt,this.onStart=function(u){var l=u.target;l.localName==="img"&&(!E(r.filter)||r.filter.call(i,l))&&i.view(i.images.indexOf(l))})}}},{key:"build",value:function(){if(!this.ready){var i=this.element,n=this.options,r=i.parentNode,a=document.createElement("div");a.innerHTML=ai;var o=a.querySelector(".".concat(m,"-container")),s=o.querySelector(".".concat(m,"-title")),c=o.querySelector(".".concat(m,"-toolbar")),u=o.querySelector(".".concat(m,"-navbar")),l=o.querySelector(".".concat(m,"-button")),h=o.querySelector(".".concat(m,"-canvas"));if(this.parent=r,this.viewer=o,this.title=s,this.toolbar=c,this.navbar=u,this.button=l,this.canvas=h,this.footer=o.querySelector(".".concat(m,"-footer")),this.tooltipBox=o.querySelector(".".concat(m,"-tooltip")),this.player=o.querySelector(".".concat(m,"-player")),this.list=o.querySelector(".".concat(m,"-list")),o.id="".concat(m).concat(this.id),s.id="".concat(m,"Title").concat(this.id),d(s,n.title?ft(Array.isArray(n.title)?n.title[0]:n.title):H),d(u,n.navbar?ft(n.navbar):H),ut(l,H,!n.button),n.keyboard&&l.setAttribute("tabindex",0),n.backdrop&&(d(o,"".concat(m,"-backdrop")),!n.inline&&n.backdrop!=="static"&&Lt(h,wt,"hide")),lt(n.className)&&n.className&&n.className.split(Rt).forEach(function(O){d(o,O)}),n.toolbar){var f=document.createElement("ul"),g=Z(n.toolbar),w=ht.slice(0,3),y=ht.slice(7,9),_=ht.slice(9);g||d(c,ft(n.toolbar)),S(g?n.toolbar:ht,function(O,V){var R=g&&Z(O),b=g?Mt(V):O,j=R&&!K(O.show)?O.show:O;if(!(!j||!n.zoomable&&w.indexOf(b)!==-1||!n.rotatable&&y.indexOf(b)!==-1||!n.scalable&&_.indexOf(b)!==-1)){var M=R&&!K(O.size)?O.size:O,W=R&&!K(O.click)?O.click:O,Y=document.createElement("li");n.keyboard&&Y.setAttribute("tabindex",0),Y.setAttribute("role","button"),d(Y,"".concat(m,"-").concat(b)),E(W)||Lt(Y,wt,b),D(j)&&d(Y,ft(j)),["small","large"].indexOf(M)!==-1?d(Y,"".concat(m,"-").concat(M)):b==="play"&&d(Y,"".concat(m,"-large")),E(W)&&v(Y,tt,W),f.appendChild(Y)}}),c.appendChild(f)}else d(c,H);if(!n.rotatable){var I=c.querySelectorAll('li[class*="rotate"]');d(I,at),S(I,function(O){c.appendChild(O)})}if(n.inline)d(l,si),F(o,{zIndex:n.zIndexInline}),window.getComputedStyle(r).position==="static"&&F(r,{position:"relative"}),r.insertBefore(o,i.nextSibling);else{d(l,oi),d(o,jt),d(o,pt),d(o,H),F(o,{zIndex:n.zIndex});var C=n.container;lt(C)&&(C=i.ownerDocument.querySelector(C)),C||(C=this.body),C.appendChild(o)}if(n.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,E(n.ready)&&v(i,ne,n.ready,{once:!0}),N(i,ne)===!1){this.ready=!1;return}this.ready&&n.inline&&this.view(this.index)}}}],[{key:"noConflict",value:function(){return window.Viewer=Ii,e}},{key:"setDefaults",value:function(i){k(Ut,Z(i)&&i)}}])}();k(it.prototype,Si,xi,Di,Ai,_i);var Ci=typeof global=="object"&&global&&global.Object===Object&&global;const ke=Ci;var ji=typeof self=="object"&&self&&self.Object===Object&&self,ki=ke||ji||Function("return this")();const Ot=ki;var Li=Ot.Symbol;const yt=Li;var Le=Object.prototype,zi=Le.hasOwnProperty,Vi=Le.toString,nt=yt?yt.toStringTag:void 0;function Ri(e){var t=zi.call(e,nt),i=e[nt];try{e[nt]=void 0;var n=!0}catch(a){}var r=Vi.call(e);return n&&(t?e[nt]=i:delete e[nt]),r}var Mi=Object.prototype,Pi=Mi.toString;function Fi(e){return Pi.call(e)}var Wi="[object Null]",Yi="[object Undefined]",we=yt?yt.toStringTag:void 0;function St(e){return e==null?e===void 0?Yi:Wi:we&&we in Object(e)?Ri(e):Fi(e)}function xt(e){return e!=null&&typeof e=="object"}var Xi="[object Symbol]";function Hi(e){return typeof e=="symbol"||xt(e)&&St(e)==Xi}var qi=Array.isArray;const Ui=qi;var $i=/\s/;function Bi(e){for(var t=e.length;t--&&$i.test(e.charAt(t)););return t}var Ki=/^\s+/;function Zi(e){return e&&e.slice(0,Bi(e)+1).replace(Ki,"")}function U(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var ye=0/0,Gi=/^[-+]0x[0-9a-f]+$/i,Ji=/^0b[01]+$/i,Qi=/^0o[0-7]+$/i,tn=parseInt;function Ee(e){if(typeof e=="number")return e;if(Hi(e))return ye;if(U(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=U(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Zi(e);var i=Ji.test(e);return i||Qi.test(e)?tn(e.slice(2),i?2:8):Gi.test(e)?ye:+e}function ze(e){return e}var en="[object AsyncFunction]",nn="[object Function]",rn="[object GeneratorFunction]",an="[object Proxy]";function Ve(e){if(!U(e))return!1;var t=St(e);return t==nn||t==rn||t==en||t==an}var on=Ot["__core-js_shared__"];const _t=on;var Te=function(){var e=/[^.]+$/.exec(_t&&_t.keys&&_t.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function sn(e){return!!Te&&Te in e}var ln=Function.prototype,un=ln.toString;function cn(e){if(e!=null){try{return un.call(e)}catch(t){}try{return e+""}catch(t){}}return""}var hn=/[\\^$.*+?()[\]{}|]/g,fn=/^\[object .+?Constructor\]$/,dn=Function.prototype,vn=Object.prototype,mn=dn.toString,gn=vn.hasOwnProperty,pn=RegExp("^"+mn.call(gn).replace(hn,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function bn(e){if(!U(e)||sn(e))return!1;var t=Ve(e)?pn:fn;return t.test(cn(e))}function wn(e,t){return e==null?void 0:e[t]}function yn(e,t){var i=wn(e,t);return bn(i)?i:void 0}function En(e,t,i){switch(i.length){case 0:return e.call(t);case 1:return e.call(t,i[0]);case 2:return e.call(t,i[0],i[1]);case 3:return e.call(t,i[0],i[1],i[2])}return e.apply(t,i)}var Tn=800,On=16,Sn=Date.now;function xn(e){var t=0,i=0;return function(){var n=Sn(),r=On-(n-i);if(i=n,r>0){if(++t>=Tn)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function Dn(e){return function(){return e}}var An=function(){try{var e=yn(Object,"defineProperty");return e({},"",{}),e}catch(t){}}();const Et=An;var _n=Et?function(e,t){return Et(e,"toString",{configurable:!0,enumerable:!1,value:Dn(t),writable:!0})}:ze;const In=_n;var Nn=xn(In);const Cn=Nn;var jn=9007199254740991,kn=/^(?:0|[1-9]\d*)$/;function Re(e,t){var i=typeof e;return t=t==null?jn:t,!!t&&(i=="number"||i!="symbol"&&kn.test(e))&&e>-1&&e%1==0&&e<t}function Me(e,t,i){t=="__proto__"&&Et?Et(e,t,{configurable:!0,enumerable:!0,value:i,writable:!0}):e[t]=i}function Pt(e,t){return e===t||e!==e&&t!==t}var Ln=Object.prototype,zn=Ln.hasOwnProperty;function Pe(e,t,i){var n=e[t];(!(zn.call(e,t)&&Pt(n,i))||i===void 0&&!(t in e))&&Me(e,t,i)}function Vn(e,t,i,n){var r=!i;i||(i={});for(var a=-1,o=t.length;++a<o;){var s=t[a],c=n?n(i[s],e[s],s,i,e):void 0;c===void 0&&(c=e[s]),r?Me(i,s,c):Pe(i,s,c)}return i}var Oe=Math.max;function Rn(e,t,i){return t=Oe(t===void 0?e.length-1:t,0),function(){for(var n=arguments,r=-1,a=Oe(n.length-t,0),o=Array(a);++r<a;)o[r]=n[t+r];r=-1;for(var s=Array(t+1);++r<t;)s[r]=n[r];return s[t]=i(o),En(e,this,s)}}function Fe(e,t){return Cn(Rn(e,t,ze),e+"")}var Mn=9007199254740991;function We(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Mn}function Dt(e){return e!=null&&We(e.length)&&!Ve(e)}function Ye(e,t,i){if(!U(i))return!1;var n=typeof t;return(n=="number"?Dt(i)&&Re(t,i.length):n=="string"&&t in i)?Pt(i[t],e):!1}function Pn(e){return Fe(function(t,i){var n=-1,r=i.length,a=r>1?i[r-1]:void 0,o=r>2?i[2]:void 0;for(a=e.length>3&&typeof a=="function"?(r--,a):void 0,o&&Ye(i[0],i[1],o)&&(a=r<3?void 0:a,r=1),t=Object(t);++n<r;){var s=i[n];s&&e(t,s,n,a)}return t})}var Fn=Object.prototype;function Ft(e){var t=e&&e.constructor,i=typeof t=="function"&&t.prototype||Fn;return e===i}function Wn(e,t){for(var i=-1,n=Array(e);++i<e;)n[i]=t(i);return n}var Yn="[object Arguments]";function Se(e){return xt(e)&&St(e)==Yn}var Xe=Object.prototype,Xn=Xe.hasOwnProperty,Hn=Xe.propertyIsEnumerable,qn=Se(function(){return arguments}())?Se:function(e){return xt(e)&&Xn.call(e,"callee")&&!Hn.call(e,"callee")};const Un=qn;function $n(){return!1}var He=typeof exports=="object"&&exports&&!exports.nodeType&&exports,xe=He&&typeof module=="object"&&module&&!module.nodeType&&module,Bn=xe&&xe.exports===He,De=Bn?Ot.Buffer:void 0,Kn=De?De.isBuffer:void 0,Zn=Kn||$n;const Gn=Zn;var Jn="[object Arguments]",Qn="[object Array]",tr="[object Boolean]",er="[object Date]",ir="[object Error]",nr="[object Function]",rr="[object Map]",ar="[object Number]",or="[object Object]",sr="[object RegExp]",lr="[object Set]",ur="[object String]",cr="[object WeakMap]",hr="[object ArrayBuffer]",fr="[object DataView]",dr="[object Float32Array]",vr="[object Float64Array]",mr="[object Int8Array]",gr="[object Int16Array]",pr="[object Int32Array]",br="[object Uint8Array]",wr="[object Uint8ClampedArray]",yr="[object Uint16Array]",Er="[object Uint32Array]",x={};x[dr]=x[vr]=x[mr]=x[gr]=x[pr]=x[br]=x[wr]=x[yr]=x[Er]=!0;x[Jn]=x[Qn]=x[hr]=x[tr]=x[fr]=x[er]=x[ir]=x[nr]=x[rr]=x[ar]=x[or]=x[sr]=x[lr]=x[ur]=x[cr]=!1;function Tr(e){return xt(e)&&We(e.length)&&!!x[St(e)]}function Or(e){return function(t){return e(t)}}var qe=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ot=qe&&typeof module=="object"&&module&&!module.nodeType&&module,Sr=ot&&ot.exports===qe,It=Sr&&ke.process,xr=function(){try{var e=ot&&ot.require&&ot.require("util").types;return e||It&&It.binding&&It.binding("util")}catch(t){}}();const Ae=xr;var _e=Ae&&Ae.isTypedArray,Dr=_e?Or(_e):Tr;const Ar=Dr;var _r=Object.prototype,Ir=_r.hasOwnProperty;function Ue(e,t){var i=Ui(e),n=!i&&Un(e),r=!i&&!n&&Gn(e),a=!i&&!n&&!r&&Ar(e),o=i||n||r||a,s=o?Wn(e.length,String):[],c=s.length;for(var u in e)(t||Ir.call(e,u))&&!(o&&(u=="length"||r&&(u=="offset"||u=="parent")||a&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||Re(u,c)))&&s.push(u);return s}function Nr(e,t){return function(i){return e(t(i))}}var Cr=Nr(Object.keys,Object);const jr=Cr;var kr=Object.prototype,Lr=kr.hasOwnProperty;function zr(e){if(!Ft(e))return jr(e);var t=[];for(var i in Object(e))Lr.call(e,i)&&i!="constructor"&&t.push(i);return t}function Vr(e){return Dt(e)?Ue(e):zr(e)}var Rr=Object.prototype,Mr=Rr.hasOwnProperty,Pr=Pn(function(e,t){if(Ft(t)||Dt(t)){Vn(t,Vr(t),e);return}for(var i in t)Mr.call(t,i)&&Pe(e,i,t[i])});const Fr=Pr;function Wr(e){var t=[];if(e!=null)for(var i in Object(e))t.push(i);return t}var Yr=Object.prototype,Xr=Yr.hasOwnProperty;function Hr(e){if(!U(e))return Wr(e);var t=Ft(e),i=[];for(var n in e)n=="constructor"&&(t||!Xr.call(e,n))||i.push(n);return i}function qr(e){return Dt(e)?Ue(e,!0):Hr(e)}var Ur=function(){return Ot.Date.now()};const Nt=Ur;var $r="Expected a function",Br=Math.max,Kr=Math.min;function Zr(e,t,i){var n,r,a,o,s,c,u=0,l=!1,h=!1,f=!0;if(typeof e!="function")throw new TypeError($r);t=Ee(t)||0,U(i)&&(l=!!i.leading,h="maxWait"in i,a=h?Br(Ee(i.maxWait)||0,t):a,f="trailing"in i?!!i.trailing:f);function g(b){var j=n,M=r;return n=r=void 0,u=b,o=e.apply(M,j),o}function w(b){return u=b,s=setTimeout(I,t),l?g(b):o}function y(b){var j=b-c,M=b-u,W=t-j;return h?Kr(W,a-M):W}function _(b){var j=b-c,M=b-u;return c===void 0||j>=t||j<0||h&&M>=a}function I(){var b=Nt();if(_(b))return C(b);s=setTimeout(I,y(b))}function C(b){return s=void 0,f&&n?g(b):(n=r=void 0,o)}function O(){s!==void 0&&clearTimeout(s),u=0,n=c=r=s=void 0}function V(){return s===void 0?o:C(Nt())}function R(){var b=Nt(),j=_(b);if(n=arguments,r=this,c=b,j){if(s===void 0)return w(c);if(h)return clearTimeout(s),s=setTimeout(I,t),g(c)}return s===void 0&&(s=setTimeout(I,t)),o}return R.cancel=O,R.flush=V,R}var $e=Object.prototype,Gr=$e.hasOwnProperty,Jr=Fe(function(e,t){e=Object(e);var i=-1,n=t.length,r=n>2?t[2]:void 0;for(r&&Ye(t[0],t[1],r)&&(n=1);++i<n;)for(var a=t[i],o=qr(a),s=-1,c=o.length;++s<c;){var u=o[s],l=e[u];(l===void 0||Pt(l,$e[u])&&!Gr.call(e,u))&&(e[u]=a[u])}return e});const Qr=Jr,ta=({images:e=[],options:t})=>{t=Fr(t,{inline:!1});const i=document.createElement("div"),n=Yt("div",{style:{display:"none"},class:["__viewer-token"]},e.map(s=>Yt("img",typeof s=="string"?{src:s}:s)));Xt(n,i);const r=i.firstElementChild;document.body.appendChild(r);const a=new it(r,t),o=a.destroy.bind(a);return a.destroy=function(){return o(),Xt(null,i),a},a.show(),r.addEventListener("hidden",function(){this.viewer===a&&a.destroy()}),a},ea=({name:e="viewer",debug:t=!1}={})=>{function i(l,h,f=!1,g=!1){return Wt(this,null,function*(){yield mt(),!(g&&!n(l))&&(f||!l[`$${e}`]?(o(l),l[`$${e}`]=new it(l,h),u("Viewer created")):(l[`$${e}`].update(),u("Viewer updated")))})}function n(l){const h=l.innerHTML.match(/<img([\w\W]+?)[\\/]?>/g),f=h?h.join(""):void 0;return l.__viewerImageDiffCache===f?(u("Element change detected, but image(s) has not changed"),!1):(u("Image change detected"),l.__viewerImageDiffCache=f,!0)}function r(l,h,f,g){s(l);const w=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;if(!w){u("Observer not supported");return}const y=new w(I=>{I.forEach(C=>{u(`Viewer mutation:${C.type}`),f(l,h,g,!0)})}),_={attributes:!0,childList:!0,characterData:!0,subtree:!0};y.observe(l,_),l.__viewerMutationObserver=y,u("Observer created")}function a(l,h,f,g){l.__viewerUnwatch=vt(()=>h.value,(w,y)=>{u("Change detected by watcher: ",h.value),g(l,w,!0,!1)},{deep:!0}),u("Watcher created, expression: ",h.value)}function o(l){!l[`$${e}`]||(l[`$${e}`].destroy(),delete l[`$${e}`],u("Viewer destroyed"))}function s(l){!l.__viewerMutationObserver||(l.__viewerMutationObserver.disconnect(),delete l.__viewerMutationObserver,u("observer destroyed"))}function c(l){!l.__viewerUnwatch||(l.__viewerUnwatch(),delete l.__viewerUnwatch,u("Watcher destroyed"))}function u(...l){}return{mounted(l,h,f){u("Viewer bind");const g=Zr(i,50);g(l,h.value),a(l,h,f,g),h.modifiers.static||r(l,h.value,g,h.modifiers.rebuild)},unmounted(l){u("Viewer unbind"),s(l),c(l),o(l)}}},ia=Be({name:"Viewer",props:{images:{type:Array,default:()=>[]},rebuild:{type:Boolean,default:!1},trigger:{type:Object,default:null},options:{type:Object,default:()=>null}},emits:["inited"],setup(e,{emit:t}){let i;const n=Ke();function r(){i=new it(n.value,e.options),t("inited",i)}function a(){i&&i.destroy()}function o(){a(),r()}function s(){i?(i.update(),t("inited",i)):r()}function c(){e.rebuild?o():s()}const u={deep:!0};return vt(()=>e.images,()=>{mt(()=>{c()})},u),vt(()=>e.trigger,()=>{mt(()=>{c()})},u),vt(()=>e.options,()=>{mt(()=>{o()})},u),Ze(()=>r()),Ge(()=>a()),{root:n,createViewer:r,rebuildViewer:o,updateViewer:s,destroyViewer:a}}}),na=(e,t)=>{const i=e.__vccOpts||e;for(const[n,r]of t)i[n]=r;return i},ra={ref:"root"};function aa(e,t,i,n,r,a){return Je(),Qe("div",ra,[ti(e.$slots,"default",{images:e.images,options:e.options})],512)}const oa=na(ia,[["render",aa]]),ua={install(e,{name:t="viewer",debug:i=!1,defaultOptions:n}={}){n&&it.setDefaults(n),e.config.globalProperties[`$${t}Api`]=ta,e.component(t,Qr(oa,{name:t})),e.directive(t,ea({name:t,debug:i}))},setDefaults(e){it.setDefaults(e)}};export{it as Viewer,ta as api,oa as component,ua as default,ea as directive};
