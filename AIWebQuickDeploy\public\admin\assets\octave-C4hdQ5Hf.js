
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

function e(e){return new RegExp("^(("+e.join(")|(")+"))\\b")}var n=new RegExp("^[\\+\\-\\*/&|\\^~<>!@'\\\\]"),t=new RegExp("^[\\(\\[\\{\\},:=;\\.]"),r=new RegExp("^((==)|(~=)|(<=)|(>=)|(<<)|(>>)|(\\.[\\+\\-\\*/\\^\\\\]))"),i=new RegExp("^((!=)|(\\+=)|(\\-=)|(\\*=)|(/=)|(&=)|(\\|=)|(\\^=))"),a=new RegExp("^((>>=)|(<<=))"),o=new RegExp("^[\\]\\)]"),c=new RegExp("^[_A-Za-z¡-￿][_A-Za-z0-9¡-￿]*"),m=e(["error","eval","function","abs","acos","atan","asin","cos","cosh","exp","log","prod","sum","log10","max","min","sign","sin","sinh","sqrt","tan","reshape","break","zeros","default","margin","round","ones","rand","syn","ceil","floor","size","clear","zeros","eye","mean","std","cov","det","eig","inv","norm","rank","trace","expm","logm","sqrtm","linspace","plot","title","xlabel","ylabel","legend","text","grid","meshgrid","mesh","num2str","fft","ifft","arrayfun","cellfun","input","fliplr","flipud","ismember"]),s=e(["return","case","switch","else","elseif","end","endif","endfunction","if","otherwise","do","for","while","try","catch","classdef","properties","events","methods","global","persistent","endfor","endwhile","printf","sprintf","disp","until","continue","pkg"]);function u(e,n){return e.sol()||"'"!==e.peek()?(n.tokenize=f,f(e,n)):(e.next(),n.tokenize=f,"operator")}function l(e,n){return e.match(/^.*%}/)?(n.tokenize=f,"comment"):(e.skipToEnd(),"comment")}function f(p,d){if(p.eatSpace())return null;if(p.match("%{"))return d.tokenize=l,p.skipToEnd(),"comment";if(p.match(/^[%#]/))return p.skipToEnd(),"comment";if(p.match(/^[0-9\.+-]/,!1)){if(p.match(/^[+-]?0x[0-9a-fA-F]+[ij]?/))return p.tokenize=f,"number";if(p.match(/^[+-]?\d*\.\d+([EeDd][+-]?\d+)?[ij]?/))return"number";if(p.match(/^[+-]?\d+([EeDd][+-]?\d+)?[ij]?/))return"number"}if(p.match(e(["nan","NaN","inf","Inf"])))return"number";var h=p.match(/^"(?:[^"]|"")*("|$)/)||p.match(/^'(?:[^']|'')*('|$)/);return h?h[1]?"string":"error":p.match(s)?"keyword":p.match(m)?"builtin":p.match(c)?"variable":p.match(n)||p.match(r)?"operator":p.match(t)||p.match(i)||p.match(a)?null:p.match(o)?(d.tokenize=u,null):(p.next(),"error")}const p={name:"octave",startState:function(){return{tokenize:f}},token:function(e,n){var t=n.tokenize(e,n);return"number"!==t&&"variable"!==t||(n.tokenize=u),t},languageData:{commentTokens:{line:"%"}}};export{p as octave};
