
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,aS as s,aT as a,P as l,Q as o,a2 as t,e as n,w as d,g as i,t as r,i as u,f as c,_ as p,c as b,V as f,W as g,a6 as m,R as v}from"./index-BERX8Mlm.js";const x={class:"w-full inline-flex"},w=["disabled"],k={class:"block truncate"},h={class:"pointer-events-none absolute end-0 inset-y-0 flex items-center pe-2.5"},y={class:"max-h-60 w-full scroll-py-1 overflow-y-auto p-1 lg-w-48 focus-outline-none"},V=["disabled","onClick"],_=e({__name:"HSelect",props:s({options:{},disabled:{type:Boolean,default:!1}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const s=e,_=a(e,"modelValue"),C=l({get:()=>s.options.find((e=>e.value===_.value))??s.options[0],set(e){_.value=e.value}});return(s,a)=>{const l=p,_=o("VMenu");return n(),t(_,v({triggers:["click"],"popper-triggers":["click"],delay:0,disabled:s.disabled},s.$attrs),{popper:d((()=>[i("div",y,[(n(!0),b(f,null,g(s.options,(s=>(n(),b("button",{key:s.value,disabled:s.disabled,class:m(["w-full cursor-pointer truncate border-size-0 rounded-md bg-inherit px-2 py-1.5 text-left text-sm disabled-cursor-not-allowed hover-not-disabled-bg-stone-1 dark-hover-not-disabled-bg-stone-9",{"font-bold":e.modelValue===s.value}]),onClick:e=>C.value=s},r(s.label),11,V)))),128))])])),default:d((()=>[i("div",x,[i("button",{class:"relative block w-full flex cursor-default items-center gap-x-2 border-0 rounded-md bg-white px-2.5 py-1.5 pe-9 text-left text-sm shadow-sm ring-1 ring-stone-2 ring-inset lg-w-48 disabled-cursor-not-allowed dark-bg-dark focus-outline-none focus-ring-2 dark-ring-stone-8 focus-ring-ui-primary",disabled:s.disabled},[i("span",k,r(u(C).label),1),i("span",h,[c(l,{name:"i-carbon:chevron-down",class:"h-5 w-5 flex-shrink-0 text-stone-5"})])],8,w)])])),_:1},16,["disabled"])}}});export{_ as default};
