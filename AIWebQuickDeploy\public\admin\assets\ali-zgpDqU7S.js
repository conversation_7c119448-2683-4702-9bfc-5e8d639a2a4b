
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{_ as e}from"./index.vue_vue_type_script_setup_true_lang-DBlFcaHd.js";import{d as l,$ as a,r as s,P as t,b as u,Q as c,c as o,e as d,f as i,w as r,j as n,h as m,_ as f,g as p,i as O,Y as _,k as g}from"./index-BERX8Mlm.js";import{a as y}from"./config-BrbFL53_.js";const S=l({__name:"ali",setup(l){const g=a({aliOssStatus:"",aliOssAccessKeyId:"",aliOssAccessKeySecret:"",aliOssRegion:"",aliOssBucket:"",aliOssAcceleratedDomain:""}),S=s();async function x(){const e=await y.queryConfig({keys:["aliOssAccessKeySecret","aliOssRegion","aliOssBucket","aliOssAccessKeyId","aliOssStatus","aliOssAcceleratedDomain"]});Object.assign(g,e.data)}function V(){var e;null==(e=S.value)||e.validate((async e=>{if(e){try{await y.setConfig({settings:(l=g,Object.keys(l).map((e=>({configKey:e,configVal:l[e]}))))}),_.success("变更配置信息成功")}catch(a){}x()}else _.error("请填写完整信息");var l}))}const b=t((()=>[{required:1===Number(g.aliOssStatus),message:"开启配置后请填写此项",trigger:"change"}]));return u((()=>{x()})),(l,a)=>{const s=f,t=n,u=e,_=c("el-switch"),y=c("el-form-item"),x=c("el-col"),h=c("el-row"),A=c("el-input"),v=c("el-form"),K=c("el-card");return d(),o("div",null,[i(u,null,{title:r((()=>a[6]||(a[6]=[p("div",{class:"flex items-center gap-4"},"阿里云OSS参数设置",-1)]))),content:r((()=>a[7]||(a[7]=[p("div",{class:"text-sm/6"},[p("div",null,[m(" 需前往阿里云申请对象存储服务，更多配置及申请详见"),p("a",{href:"https://oss.console.aliyun.com",target:"_blank"},"阿里云OSS"),m(" 。如果同时开启多个存储服务，服务优先级：本地存储 > S3存储 > 腾讯云COS > 阿里云OSS。 ")])],-1)]))),default:r((()=>[i(t,{outline:"",onClick:V},{default:r((()=>[i(s,{name:"i-ri:file-text-line"}),a[8]||(a[8]=m(" 保存设置 "))])),_:1})])),_:1}),i(K,{style:{margin:"20px"}},{default:r((()=>[i(v,{ref_key:"formRef",ref:S,model:g,"label-width":"120px"},{default:r((()=>[i(h,null,{default:r((()=>[i(x,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[i(y,{label:"服务启用状态",prop:"aliOssStatus"},{default:r((()=>[i(_,{modelValue:g.aliOssStatus,"onUpdate:modelValue":a[0]||(a[0]=e=>g.aliOssStatus=e),"active-value":"1","inactive-value":"0"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(h,null,{default:r((()=>[i(x,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[i(y,{label:"accessKeyId",prop:"aliOssAccessKeyId",rules:O(b)},{default:r((()=>[i(A,{modelValue:g.aliOssAccessKeyId,"onUpdate:modelValue":a[1]||(a[1]=e=>g.aliOssAccessKeyId=e),placeholder:"请填写SecretId",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),i(h,null,{default:r((()=>[i(x,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[i(y,{label:"keySecret",prop:"aliOssAccessKeySecret",rules:O(b)},{default:r((()=>[i(A,{modelValue:g.aliOssAccessKeySecret,"onUpdate:modelValue":a[2]||(a[2]=e=>g.aliOssAccessKeySecret=e),placeholder:"请填写SecretKey",clearable:"",type:"password","show-password":""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),i(h,null,{default:r((()=>[i(x,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[i(y,{label:"存储桶名称",prop:"aliOssBucket",rules:O(b)},{default:r((()=>[i(A,{modelValue:g.aliOssBucket,"onUpdate:modelValue":a[3]||(a[3]=e=>g.aliOssBucket=e),placeholder:"请填写存储桶名称",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),i(h,null,{default:r((()=>[i(x,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[i(y,{label:"所属地域",prop:"aliOssRegion",rules:O(b)},{default:r((()=>[i(A,{modelValue:g.aliOssRegion,"onUpdate:modelValue":a[4]||(a[4]=e=>g.aliOssRegion=e),placeholder:"请填写所属地域(oss-cn-shanghai)",clearable:""},null,8,["modelValue"])])),_:1},8,["rules"])])),_:1})])),_:1}),i(h,null,{default:r((()=>[i(x,{xs:24,md:20,lg:15,xl:12},{default:r((()=>[i(y,{label:"全球加速域名",prop:"aliOssAcceleratedDomain"},{default:r((()=>[i(A,{modelValue:g.aliOssAcceleratedDomain,"onUpdate:modelValue":a[5]||(a[5]=e=>g.aliOssAcceleratedDomain=e),placeholder:"如您是国外服务器可开启全球加速域名得到更快响应速度、同理也会更高计费！",clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})])}}});"function"==typeof g&&g(S);export{S as default};
