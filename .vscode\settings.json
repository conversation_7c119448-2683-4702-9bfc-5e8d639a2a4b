{"prettier.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "json", "jsonc", "json5", "yaml", "yml", "markdown"], "cSpell.words": ["aiweb", "ant<PERSON>", "axios", "Baichuan", "bumpp", "Chatbox", "chatglm", "chatgpt", "chatlog", "chatweb", "chenz<PERSON>yu", "chevereto", "cogvideox", "commitlint", "cref", "dall", "dalle", "<PERSON><PERSON><PERSON>", "deepsearch", "deepseek", "docker<PERSON>b", "duckduck<PERSON>", "EMAILCODE", "errmsg", "esno", "flowith", "GPTAPI", "gpts", "highlightjs", "hljs", "hun<PERSON>", "iconify", "ISDEV", "katex", "ka<PERSON><PERSON><PERSON><PERSON>", "langchain", "<PERSON><PERSON>", "linkify", "logprobs", "longcontext", "luma", "mapi", "Markmap", "mdhljs", "mediumtext", "micromessenger", "mila", "Mindmap", "MODELSMAPLIST", "MODELTYPELIST", "modelvalue", "newconfig", "niji", "Nmessage", "nodata", "OPENAI", "pinia", "Popconfirm", "PPTCREATE", "projectaddress", "qwen", "rushstack", "sdxl", "<PERSON><PERSON>", "seedream", "<PERSON><PERSON>", "sref", "suno", "tailwindcss", "<PERSON><PERSON>", "traptitech", "tsup", "Typecheck", "typeorm", "unplugin", "usercenter", "vastxie", "VITE", "vueuse", "wechat"], "vue.codeActions.enabled": false, "volar.experimental.tsconfigPaths": {"./chat": ["./src/chat/tsconfig.json"], "./admin": ["./src/admin/tsconfig.json"], "./service": ["./src/service/tsconfig.json"]}}