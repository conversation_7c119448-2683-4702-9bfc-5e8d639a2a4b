
/**
 * 由 Fantastic-admin 提供技术支持
 * Powered by Fantastic-admin
 * https://fantastic-admin.github.io
 */

import{d as e,a as s,r as t,P as o,Q as i,a2 as n,e as r,i as a,a6 as u,w as l,c as p,T as v,t as c}from"./index-BERX8Mlm.js";var g={pkg:{version:"4.3.0",dependencies:{"@element-plus/icons-vue":"^2.3.1","@headlessui/vue":"^1.7.23","@imengyu/vue3-context-menu":"^1.4.8","@vueuse/core":"^13.1.0","@vueuse/integrations":"^13.1.0",axios:"^1.9.0",dayjs:"^1.11.13",echarts:"^5.6.0",eruda:"^3.4.1","floating-vue":"5.2.2","hotkeys-js":"^3.13.10",less:"^4.3.0","lodash-es":"^4.17.21",marked:"^15.0.11","md-editor-v3":"^5.5.1",mitt:"^3.0.1",nprogress:"^0.2.0",overlayscrollbars:"^2.11.2","overlayscrollbars-vue":"^0.5.9","path-browserify":"^1.0.1","path-to-regexp":"^8.2.0",pinia:"^2.1.7",prettier:"^3.5.3","resize-observer-polyfill":"^1.5.1",uuid:"^11.1.0","v-viewer":"^3.0.21",vconsole:"^3.15.1",vue:"^3.5.13","vue-m-message":"^4.0.2","vue-router":"^4.5.1",vuedraggable:"^4.1.0"},devDependencies:{"@iconify/json":"^2.2.337","@iconify/vue":"^5.0.0","@types/lodash-es":"^4.17.12","@types/mockjs":"^1.0.10","@types/node":"^22.15.17","@types/path-browserify":"^1.0.3","@unocss/core":"66.1.1","@unocss/preset-mini":"66.1.1","@vitejs/plugin-legacy":"^6.1.1","@vitejs/plugin-vue":"^5.2.4","@vitejs/plugin-vue-jsx":"^4.1.2",archiver:"^7.0.1",autoprefixer:"^10.4.16",boxen:"^8.0.1",bumpp:"^10.1.0","element-plus":"^2.9.10",esno:"^4.8.0","fs-extra":"^11.3.0","http-server":"^14.1.1",inquirer:"^12.6.1",msw:"^2.0.0",picocolors:"^1.1.1",plop:"^4.0.1",sass:"^1.88.0",svgo:"^3.3.2",terser:"^5.39.0",typescript:"^5.8.3",unocss:"66.1.1","unplugin-auto-import":"^19.2.0","unplugin-turbo-console":"^2.1.3","unplugin-vue-components":"^28.5.0",vite:"^6.3.5","vite-plugin-banner":"^0.8.1","vite-plugin-checker":"^0.9.3","vite-plugin-compression2":"^1.3.3","vite-plugin-fake-server":"^2.2.0","vite-plugin-pages":"^0.33.0","vite-plugin-svg-icons":"^2.0.1","vite-plugin-vue-devtools":"^7.7.6","vite-plugin-vue-meta-layouts":"^0.5.1","vue-tsc":"^2.2.10"}}};const d={key:0,class:"block truncate font-bold"},h=e({name:"Logo",__name:"index",props:{showLogo:{type:Boolean,default:!0},showTitle:{type:Boolean,default:!0}},setup(e){const{pkg:h}=g,m=s(),y=t("AIWeb"),b=atob("QUlXZWI=");if(!y.value.includes(b))throw document.body.innerHTML="<h1></h1>",new Error("");const f=o((()=>m.settings.home.enable?m.settings.home.fullPath:""));return(e,s)=>{const t=i("RouterLink");return r(),n(t,{to:a(f),class:u(["h-[var(--g-sidebar-logo-height)] w-inherit flex-center gap-2 px-3 text-inherit no-underline",{"cursor-pointer":a(m).settings.home.enable}]),title:a(y)},{default:l((()=>[e.showTitle?(r(),p("span",d,c(a(y))+"-"+c(a(h).version),1)):v("",!0)])),_:1},8,["to","class","title"])}}});export{h as _};
